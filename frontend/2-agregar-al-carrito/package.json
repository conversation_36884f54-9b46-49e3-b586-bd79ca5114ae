{"name": "tus-libros-web", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@eryxcoop/appyx-comm": "^1.0.7", "@mui/icons-material": "^5.15.19", "@mui/material": "^5.15.19", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "./node_modules/.bin/react-scripts start", "build": "./node_modules/.bin/react-scripts build", "test": "./node_modules/.bin/react-scripts test", "eject": "./node_modules/.bin/react-scripts eject", "docker:build": "docker build -t tuslibros-frontend . ", "docker:start": "docker run -d --name tuslibros-frontend-container -p 3000:3000 tuslibros-frontend", "onboot": "docker update --restart unless-stopped tuslibros-frontend-container"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}