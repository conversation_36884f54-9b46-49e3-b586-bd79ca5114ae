run:
	docker-compose -f docker-compose.dev.yml up

shell:
	docker exec -it run0km-web /bin/bash

api-shell:
	docker exec -it run0km-web python manage.py shell_plus

dietrich-test:
	docker exec -it run0km-web python manage.py dietrich_task_test

build:
	docker-compose -f docker-compose.dev.yml build

down:
	docker-compose -f docker-compose.dev.yml down

test-db:
	docker-compose -f docker-compose.dev.yml up

test:
	cd run0km && echo python manage.py test --settings=run0km.test_settings

db:
	docker-compose -f docker-compose.dev.yml up database
