#!/usr/bin/env python3

import os
import tempfile
import unittest
import shutil
from unittest.mock import patch
import sys

# Import the functions from count_classes.py
from count_classes import count_classes_in_file, scan_directory, main

class TestCountClasses(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content):
        """Helper method to create test files with specific content"""
        file_path = os.path.join(self.test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_count_classes_in_empty_file(self):
        """Test counting classes in an empty file"""
        file_path = self.create_test_file('empty.py', '')
        self.assertEqual(count_classes_in_file(file_path), 0)
    
    def test_count_classes_in_file_no_classes(self):
        """Test counting classes in a file with no classes"""
        content = """
def function1():
    pass

def function2():
    pass
"""
        file_path = self.create_test_file('no_classes.py', content)
        self.assertEqual(count_classes_in_file(file_path), 0)
    
    def test_count_classes_in_file_with_classes(self):
        """Test counting classes in a file with multiple classes"""
        content = """
class Class1:
    def method1(self):
        pass

def function1():
    pass

class Class2:
    def method2(self):
        pass
"""
        file_path = self.create_test_file('with_classes.py', content)
        self.assertEqual(count_classes_in_file(file_path), 2)
    
    def test_count_classes_with_inheritance(self):
        """Test counting classes with inheritance"""
        content = """
class BaseClass:
    pass

class ChildClass(BaseClass):
    pass

class AnotherClass(ChildClass, object):
    pass
"""
        file_path = self.create_test_file('inheritance.py', content)
        self.assertEqual(count_classes_in_file(file_path), 3)
    
    def test_count_classes_with_decorators(self):
        """Test counting classes with decorators"""
        content = """
def decorator(cls):
    return cls

@decorator
class DecoratedClass:
    pass

class RegularClass:
    pass
"""
        file_path = self.create_test_file('decorators.py', content)
        self.assertEqual(count_classes_in_file(file_path), 2)
    
    def test_count_classes_in_comments(self):
        """Test that classes in comments are not counted"""
        content = """
# class CommentedClass:
#     pass

'''
class DocstringClass:
    pass
'''

class RealClass:
    pass
"""
        file_path = self.create_test_file('comments.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_scan_directory_empty(self):
        """Test scanning an empty directory"""
        total, file_counts = scan_directory(self.test_dir)
        self.assertEqual(total, 0)
        self.assertEqual(len(file_counts), 0)
    
    def test_scan_directory_with_files(self):
        """Test scanning a directory with multiple Python files"""
        # Create file 1
        content1 = "class Class1:\n    pass\n\nclass Class2:\n    pass"
        file1 = self.create_test_file('file1.py', content1)
        
        # Create file 2
        content2 = "class Class3:\n    pass"
        file2 = self.create_test_file('file2.py', content2)
        
        # Create a non-Python file
        non_py = self.create_test_file('not_python.txt', 'class NotPython:\n    pass')
        
        total, file_counts = scan_directory(self.test_dir)
        self.assertEqual(total, 3)
        self.assertEqual(len(file_counts), 2)
        self.assertEqual(file_counts[file1], 2)
        self.assertEqual(file_counts[file2], 1)
    
    def test_scan_directory_with_subdirectories(self):
        """Test scanning a directory with subdirectories"""
        # Create a file in the main directory
        content1 = "class MainClass:\n    pass"
        file1 = self.create_test_file('main.py', content1)
        
        # Create a subdirectory
        subdir = os.path.join(self.test_dir, 'subdir')
        os.makedirs(subdir)
        
        # Create a file in the subdirectory
        subfile_path = os.path.join(subdir, 'sub.py')
        with open(subfile_path, 'w', encoding='utf-8') as f:
            f.write("class SubClass:\n    pass")
        
        total, file_counts = scan_directory(self.test_dir)
        self.assertEqual(total, 2)
        self.assertEqual(len(file_counts), 2)
    
    @patch('sys.argv', ['count_classes.py', 'dummy_dir'])
    @patch('sys.exit')
    @patch('count_classes.scan_directory')
    def test_main_function(self, mock_scan, mock_exit, mock_argv):
        """Test the main function with mocked arguments"""
        mock_scan.return_value = (5, {'file1.py': 2, 'file2.py': 3})
        
        with patch('os.path.isdir', return_value=True):
            with patch('builtins.print') as mock_print:
                main()
                mock_scan.assert_called_once()
                mock_print.assert_called()
                mock_exit.assert_not_called()
    
    @patch('sys.argv', ['count_classes.py'])
    @patch('sys.exit')
    def test_main_no_args(self, mock_exit, mock_argv):
        """Test main function with no arguments"""
        with patch('builtins.print') as mock_print:
            main()
            mock_exit.assert_called_once_with(1)
            mock_print.assert_called_once()

if __name__ == '__main__':
    unittest.main()
