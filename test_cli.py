#!/usr/bin/env python3
"""
Tests para la interfaz de línea de comandos de python_stats.
"""

import unittest
import tempfile
import os
import subprocess
import sys
from unittest.mock import patch


class TestCLI(unittest.TestCase):
    """Tests para la interfaz de línea de comandos."""
    
    def setUp(self):
        """Configuración para cada test."""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Limpieza después de cada test."""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, rel_path, content):
        """Helper para crear archivos de test."""
        full_path = os.path.join(self.test_dir, rel_path)
        # Crear directorio si no existe
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return full_path

    def test_cli_single_file(self):
        """Test 1: CLI debe poder analizar un archivo individual."""
        # Arrange
        file_path = self.create_test_file('test.py', '''
class TestClass:
    pass

class AnotherClass:
    pass
''')
        
        # Act
        result = subprocess.run(
            [sys.executable, 'python_stats.py', file_path],
            capture_output=True,
            text=True
        )
        
        # Assert
        self.assertEqual(result.returncode, 0)
        self.assertIn('2', result.stdout)  # Debe mostrar 2 clases
        self.assertIn('clases', result.stdout.lower())

    def test_cli_directory(self):
        """Test 2: CLI debe poder analizar un directorio."""
        # Arrange
        self.create_test_file('main.py', 'class MainClass:\n    pass')
        self.create_test_file('subdir/sub.py', 'class SubClass:\n    pass')
        
        # Act
        result = subprocess.run(
            [sys.executable, 'python_stats.py', self.test_dir],
            capture_output=True,
            text=True
        )
        
        # Assert
        self.assertEqual(result.returncode, 0)
        self.assertIn('2', result.stdout)  # Debe mostrar 2 clases total

    def test_cli_help(self):
        """Test 3: CLI debe mostrar ayuda cuando se pide."""
        # Act
        result = subprocess.run(
            [sys.executable, 'python_stats.py', '--help'],
            capture_output=True,
            text=True
        )
        
        # Assert
        self.assertEqual(result.returncode, 0)
        self.assertIn('usage', result.stdout.lower())
        self.assertIn('python', result.stdout.lower())

    def test_cli_no_arguments(self):
        """Test 4: CLI debe mostrar error cuando no se dan argumentos."""
        # Act
        result = subprocess.run(
            [sys.executable, 'python_stats.py'],
            capture_output=True,
            text=True
        )
        
        # Assert
        self.assertNotEqual(result.returncode, 0)  # Debe fallar
        self.assertTrue(len(result.stderr) > 0 or 'usage' in result.stdout.lower())


if __name__ == '__main__':
    unittest.main()
