
Theme name: Muun
Theme data:
button_component {
  property_set {
    key {
      section_style: EMPHASIS_2
      button_style: "filled"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_1
      button_style: "filled"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: REGULAR
      button_style: "filled"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: REGULAR
      button_style: "outlined"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: REGULAR
      button_style: "text"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_1
      button_style: "text"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_2
      button_style: "text"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_1
      button_style: "outlined"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_2
      button_style: "outlined"
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
}
divider_component {
  property_set {
    key {
      section_style: REGULAR
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_1
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_2
    }
    color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
}
image_carousel_component {
  property_set {
    key {
      section_style: REGULAR
    }
    active_dot_color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_1
    }
    active_dot_color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_2
    }
    active_dot_color {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
}
navigation_component {
  property_set {
    side_selection_style: SIDE_BOLD
    top_nav_separator: NONE
    top_selection_style: TOP_BOLD
    transparent_at_top: true
  }
}
scheme_color_component {
  property_set {
    key {
      section_style: REGULAR
    }
    accent1 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    accent2 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    accent3 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    link {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_1
    }
    accent1 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    accent2 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    accent3 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    link {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_2
    }
    accent1 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    accent2 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    accent3 {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
    link {
      red: 0.9764706
      green: 0.9764706
      blue: 0.9764706
      alpha {
        value: 1.0
      }
    }
  }
}
section_component {
  property_set {
    key {
      section_style: REGULAR
    }
    background_color {
      red: 0.003921569
      green: 0.047058824
      blue: 0.10980392
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_1
    }
    background_color {
      red: 0.09411765
      green: 0.16470589
      blue: 0.4117647
      alpha {
        value: 1.0
      }
    }
  }
  property_set {
    key {
      section_style: EMPHASIS_2
    }
    background_color {
      red: 0.12941177
      green: 0.15294118
      blue: 0.23529412
      alpha {
        value: 1.0
      }
    }
  }
}
site_component {
  property_set {
    primary_color {
      red: 0.007843138
      green: 0.07450981
      blue: 0.24313726
      alpha {
        value: 1.0
      }
    }
  }
}
text_component {
  heading_style {
    heading: 0
    paragraph_style {
      line_spacing: 1.25
      ltr: true
      spacing_before: 12.0
    }
    text_style {
      bold: false
      font_size: 16.0
      foreground {
        scheme: 0
      }
      weighted_font_family: "Rubik"
    }
  }
  heading_style {
    heading: 1
    paragraph_style {
      ltr: true
      spacing_before: 34.0
    }
    text_style {
      bold: true
      font_size: 56.0
      foreground {
        scheme: 4
      }
      weighted_font_family: "Rubik"
    }
  }
  heading_style {
    heading: 2
    paragraph_style {
      line_spacing: 1.25
      ltr: true
      spacing_before: 18.0
    }
    text_style {
      bold: false
      font_size: 38.0
      foreground {
        scheme: 5
      }
      weighted_font_family: "Rubik;600"
    }
  }
  heading_style {
    heading: 3
    paragraph_style {
      line_spacing: 1.25
      ltr: true
      spacing_before: 14.0
    }
    text_style {
      bold: true
      font_size: 14.0
      foreground {
        scheme: 6
      }
      weighted_font_family: "Lexend Mega"
    }
  }
  heading_style {
    heading: 102
    paragraph_style {
      line_spacing: 1.25
      ltr: true
      spacing_before: 8.0
    }
    text_style {
      font_size: 8.0
      foreground {
        scheme: 2
      }
      weighted_font_family: "Lexend Giga"
    }
  }
}
name: "Muun"
id: "be563d6261a18_4"
spacing_component {
  property_set {
    frame_selected: false
  }
}

----------------------------------------------------
