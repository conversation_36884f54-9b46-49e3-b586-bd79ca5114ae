Wiki-eryx - Impresora
Skip to main content
Skip to navigation
Wiki-eryx [Inicio.html]
Impresora
Para imprimir hay que instalar un driver que se puede encontrar
acá [https://support.brother.com/g/b/downloadlist.aspx?c=mx&lang=es&prod=dcp1617nw_us&os=128&flang=English]
.
Hay que instalar uno que es un script bash que se llama Driver Install Tool.
Bajarse el script y extraer el archivo. Se puede hacer con click derecho en el archivo y click en "Extraer aquí". Si lo queremos hacer desde la terminal una opción es:
gunzip linux-brprinter-installer-2.2.3-1.gz
Ahora hay que ejecutar los drivers. Pare eso, abrir en una terminal (abrir la carpeta donde está el archivo que extrayeron y hacer click derecho en algun lado y luego click en "Abrir en una terminal") y ejecutar en esa terminal (probablemente se pida una password que es la misma que usan para loguearse en la computadora):
sudo chmod +x linux-brprinter-installer-2.2.3-1
sudo ./linux-brprinter-installer-2.2.3-1
Cuando el script pide el modelo escribir: DCP-1617NW
Cuando la terminal pregunta cosas tipo "está bien que instale estos paquetes" responder sí (ingresar "y" y darle Enter).
Cuando te pregunta "Will you specify the Device URI? [Y/n]" responder sí (porque es una impresora en red y no USB).
Después tira varias opciones, elegir el número que diga:
dnssd://Brother%20DCP-1610NW%20series._pdl-datastream._tcp.local/?uuid=e3248000-80ce-11db-8000-3c2af4b1c121
Opcionalmente podés imprimir una página de prueba.
Cuando te pida la IP del device poner: *************
¡Listo! Podés entrar a CUPS (el sistema de gestión de impresoras) desde
acá [http://localhost:631/]
En el sistema para realizar ciertas acciones (por ejemplo borrar un trabajo de impresión encolado) te pide usuario y contraseña. Estos son el usuario y contraseña de tu computadora.