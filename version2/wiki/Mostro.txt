Wiki-eryx - Mostro
Skip to main content
Skip to navigation
Wiki-eryx [Inicio.html]
Especificaciones:
::::.    ':::::     ::::'             mostro
':::::    ':::::.  ::::'            OS: NixOS 24.05.5287.759537f06e69 (Uakari)
:::::     '::::.:::::             Kernel: x86_64 Linux 6.6.52
.......:::::..... ::::::::              Uptime: 5d 30m
::::::::::::::::::. ::::::    ::::.      Packages: 2685
::::::::::::::::::::: :::::.  .::::'      Shell: bash 5.2.32
.....           ::::' :::::'       Disk: 145G / 919G (17%)
:::::            '::' :::::'        CPU: Intel Core i9-14900K @ 32x 5.7GHz [37.0°C]
........:::::               ' :::::::::::.   GPU: NVIDIA GeForce RTX 4090
:::::::::::::                 :::::::::::::   RAM: 3831MiB / 47940MiB
::::::::::: ..              :::::
.::::: .:::            :::::
.:::::  :::::          '''''    .....
:::::   ':::::.  ......:::::::::::::'
:::     ::::::. ':::::::::::::::::'
.:::::::: '::::::::::
.::::''::::.     '::::.
.::::'   ::::.     '::::.
.::::      ::::      '::::.
Grupo de slack:
#mostro
El grupo sirve para:
pedir users
pedir ayuda con algo
tirar ideas
pedir un lock exclusivo de la GPU
etc
Faq:
¿Cómo puedo saber cuáles son los
usuarios conectados
?
S
e pueden usar los siguiente comandos:
who
lista de usuarios actualmente conectados al sistema
w
igual a who, pero con información más detallada, como cuánto tiempo llevan inactivos, el proceso que están ejecutando y el tiempo de carga del sistema
users
lista simple de los nombres de los usuarios actualmente conectados
last
últimos inicios de sesión de usuarios (incluidos los actualmente conectados)
Dame Ubuntu!
Ejecutá
docker run -it --rm --device nvidia.com/gpu=all -v /home/<USER>/root nvidia/cuda:12.4.1-devel-ubuntu22.04 bash
Dentro de ese container vas a ser root y tu home va a estar en `/root`. Y vas a tener acceso a la GPU.
Quiero agregar un usuario nuevo
Necesitas un compañerito con
sudo
que
Te meta en
/etc/nixos/users.nix
como un usuario nuevo
Te agregue a
/etc/nixos/ssh.nix
con tu nombre de usuario
Que rebuildee nixos con
sudo nixos-rebuild switch
Te corra el comandito
sudo passwd NOMBRE_DE_USUARIO
Quiero acceder a mostro desde casa
La forma manual de hacerlo es conectándose a una VM en Google Cloud, y de ahí a mostro. Eso se hace con los siguientes comandos:
gcloud compute ssh --zone "southamerica-east1-b" "network-controller" --project "eryx-varios"
ssh -p 9000 USER@localhost
Para no tener que correr esto cada vez se puede agregar un "alias" a la config ssh. Para hacerlo tenes que:
Correr
gcloud compute config-ssh --project "eryx-varios" --dry-run
Copiar la sección referida al host
network-controller.southamerica-east1-b.eryx-varios
, debería ser algo similar a esto:
Host network-controller.southamerica-east1-b.eryx-varios
HostName **************
IdentityFile /home/<USER>/.ssh/google_compute_engine
UserKnownHostsFile=/home/<USER>/.ssh/google_compute_known_hosts
HostKeyAlias=compute.7150541048246622819
IdentitiesOnly=yes
CheckHostIP=no
Pegar ese host en el archivo
~/.ssh/config
Agregar a ese mismo archivo el host de Mostro de la siguiente manera:
Host mostro-remote
HostName localhost
Port 9000
User [usuario de Mostro]
ProxyJump network-controller.southamerica-east1-b.eryx-varios
Y listo, ahora podes hacer
ssh mostro-remote
como en la oficina.
Se cayó la conexión remota!
generar una key ssh nueva en mi usuario de mostro.
ssh-keygen -t ed25519 -C "<EMAIL>"
dejar la pubkey correspondiente en mi authorized keys del network-controller
Tiene una pinta de
ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIB7oAFQBNtzX90LmXK/kT3tGO6oMv8gvijpd60udB5Jw
<EMAIL>
desde mostro correr
ssh -R 9000:localhost:22 user@ipdelnetworkcontroller
&
donde user@ipdelnetworkcontroller es su usuario en el network-controller y la IP pública del idem