Wiki-eryx - Capacitación de ingresante a dev
Skip to main content
Skip to navigation
Wiki-eryx [Inicio.html]
Capacitación de ingresante a dev
Acerca de este temario [#h.1q3tmx6a3xrq]
Nuestra forma de programar [#h.qposvtq9rqwr]
Anatomía cliente servidor [#h.obyypxz08myn]
Panorama general [#h.ijr1f7rp4uqw]
Pedido al servidor [#h.pzwxqzc11p08]
Carga de una página en el browser [#h.4nd83zrggg44]
Servidores HTTP y handleo de requests [#h.x32mdxh7uezx]
Client-Server [#h.9f620pp0jilw]
Base de datos [#h.y3bfqzk05mih]
Programación de base [#h.t04whv57jm83]
Código [#h.acr3z7wqtd97]
Excepciones vs Errores vs Result [#h.p75wwdute724]
Frontend [#h.r1ezi5bsv7j]
Cómo carga una página HTML [#h.21ti9ewsfh8i]
Frameworks y arquitectura [#h.5w9ugihjvcxr]
Hacer tutorial de React. [#h.oomeiiyu6ka]
Deconstruir, analizar, criticar el tutorial de React [#h.lbewau8tcjkp]
Testing [#h.xq8qrxpanf0h]
¿Por qué queremos tests? ¿Qué valor que nos da? [#h.eqv0dy2uotv0]
Tipos de test [#h.7hcasz8pxdwt]
Test de regresión [#h.5n2r1nkgfmdk]
Test de integración [#h.2r9gj67c4dpb]
Test end to end (e2e) [#h.en4md434yz5f]
Test de caja blanca y caja negra [#h.fwjvsgexlvp3]
Bugs que no vuelven [#h.c2k028emzsvp]
Smoke test [#h.gi0u8zghi6e1]
Tests manuales [#h.stqs8o8paja9]
Tests no funcionales [#h.e1gn5hgz03ja]
Tests de features definidas y tipo de producto [#h.4d9q8ep70e85]
Entidades que nos aparecen [#h.8l1zvvjftvfm]
Organización de tests como enseñanza del sistema [#h.gi0dj3u6rmls]
¿Qué significa testear mi app? [#h.qehmy69kqboc]
Temas que no vemos [#h.nqjfvlq49fi0]
Metodologías ágiles [#h.7u5o8zmaqczj]
¿Qué son? [#h.poeob075fpd5]
¿Por qué/para qué? [#h.by8i79do2k9]
¿Cuándo se aplican? [#h.zaskjqk0qr3w]
Entorno dinamico cambiante (si se aplica) [#h.ou3ozuisjpaj]
Entorno estático predecible (no se aplica) [#h.skdeg2chh4ao]
Entorno incierto, investigación (no se aplica) [#h.rw01pecssqih]
Kanban vs Scrum [#h.dlmwb8sxl55r]
Kanban [#h.3s4t0jd4k9ya]
Scrum [#h.sne76rdscx3t]
Separando desarrollo de entregas y relevamiento: Sprints y Backlog grooming [#h.nj57914v2h0l]
Roles: Product Owner, Scrum master, dev, tester, ux, analista [#h.yrmzifg1qt36]
Rituales [#h.5c626y1rn4zd]
Variantes [#h.xof652d6nowe]
Scrum of Scrums [#h.5x4okwvk5kbv]
Herramientas [#h.7uhs9uamzpwf]
Git [#h.u6vd9a6ghjp]
Pycharm/IntelliJ [#h.8tfvonhezzm2]
Unix [#h.9ay94llrurr]
Stack del proyecto - Django [#h.bps87hsa653o]
Hacer tutorial [#h.ae491nfhf6x2]
Analisis y critica [#h.d10wahu7dxdm]
Refactor [#h.8cq50o4v4upa]
Introducirnos en cómo desarrollamos en Eryx [#h.m9zqpltqzyi3]
Anatomía de un handler de POST [#h.cubobxrvu5vd]
1er párrafo [#h.4wocqcovjw1k]
2do párrafo [#h.k4jvx4pdpv37]
3er párrafo [#h.2gs2bmtexydp]
Bases de datos [#h.u76eirdhe4vn]
Comienzos: archivos [#h.1c3rxrghpy4f]
Relacional [#h.337crb3dmnw4]
De objetos [#h.3bee6ekh2ujf]
De documentos [#h.ho1zmzl4iwjx]
Key/value [#h.f858gxqk22ul]
Rol de absorber concurrencia y distribución [#h.ww8aj3dt14uc]
Transacciones ocultas (y transacciones) [#h.5csygrktl0h3]
TDD [#h.5h2ebratbfhc]
Docker [#h.f7ktxb799uip]
Docker Compose [#h.xaw0h5micg4]
Acerca de este temario
El objetivo de este manual/ temario es dar unas herramientas básicas al ingresante para que pueda estar al nivel que cualquier desarrollador de Eryx debería tener.
El orden es optativo y debe ajustarse a las necesidades de cada ingresante. Algunos temas son muy largos por lo que sugerimos no darlos en bloque sino ir mezclándolos con otros.
Durante la capacitación del dev entrante se espera también que participe de otras actividades, como estar medio día en proyectos, o áreas y en la otra mitad del día hacer este temario.
Nuestra forma de programar
1 DIA
Esta parte del temario es muy larga pero la idea es que sirva para “rellenar” y ver durante/entre otras capacitaciones. Puede servir para “tener algo que hacer” cuando no hay tareas suficientes asignadas por el capacitador o las puede usar el ingresante para mechar con algo distinto, para cambiar de tema.
Ver el ciclo de videos de Valores, Principios, Prácticas y Heurísticas que hicimos desde Academia.
Valores, principios y prácticas de Eryx [https://miro.com/app/board/uXjVOQbIUNk=/?moveToWidget=3458764537128989826&cot=14]
Cartuchera del Ñery [https://drive.google.com/drive/u/1/folders/11LMew5tmtLJQ3mcaG8YUtEwJYqxp5dv-]
Videos de la cartuchera del ñery [https://www.youtube.com/watch?v=dBujYEjCA9Y&list=PLznGnkMcSfEDo_B--LsBqFH_iSpqfd8Vq]
La idea no es solo que el ingresante vea los videos sino que pueda consultar sobre detalles o aclaraciones de los mismos, que el capacitador pregunte sobre los videos y tome una especie de examen a ver si entendió los conceptos. También es un buen momento para evaluar qué temas son más difíciles para el ingresante y de esa forma poder profundizar con otra capacitación o agendarlo para más adelante luego de la capacitación inicial.
Anatomía cliente servidor
2 hs
Panorama general
La idea de esta sección es entender todas las cosas que pasan desde que tipeo una url en el browser hasta que se muestra en pantalla la página, pasando por lo que sucede en el disco del servidor que atiende mi pedido. Seguramente un ingresante ha visto todos estos temas por separado, ha usado internet, pero tal vez no entienda cómo todo esto se articula, cómo todo esto se conecta y convive para brindar la experiencia de navegar la web.
Pedido al servidor
Anatomía de una URL:
Tipeamos una URL en el browser: traducción a IPv4 o IPv6, DNS.
Se routea por distintos servidores: gateway,
Partes de un request: headers, body, para que sirven (seguridad, expiración, parámetros propios, cookies).
Notar que es stateless.
Carga de una página en el browser
La respuesta es una string (el content, y lo que ves en “View source code” en el browser).
El browser la va leyendo e interpretando, va ejecutando JS a medida que lee.
Si en algún momento se importa un archivo la interpretación del HTML se bloquea, el browser lo trae y luego sigue ejecutando/interpretando el JS.
Las páginas y CSS las maneja asincrónicamente.
El código HTML es interpretado y se arma un árbol de componentes (DOM).
El DOM se usa para renderizar en el canvas del browser.
El canvas es algo que el SO provee.
Hay un intérprete JS corriendo todo el tiempo.
Compartimentalización de las pestañas en los browsers modernos.
Servidores HTTP y handleo de requests
El servidor atiende el request en la placa de red, puede haber varias placas.
Luego SO, puede haber más de una app escuchando, explicar el PORT en un servidor y en la URL y cómo sirve para esto.
Luego web server, que puede hostear varios recursos, estáticos y código. Explicar URL y ruteo de eso.
Luego el código sigue analizando la URL y routea a partes de nuestro código (cada View o Handler).
Client-Server
Definición de cliente (el que hace requests)
Clientes que hacemos nosotros: paginas, mobile apps
Clientes de terceros: APIs que ofrecemos
Responsabilidades de uno y de otro:
Cliente: presentación, interacción, visualización, validación temprana.
Servidor: autenticación, persistencia, lógica, validacion (se repite!).
Un servidor puede necesitar de otros servidores y a su vez ser cliente.
Load balancing.
Firewall: ¿Qué es? ¿para qué sirve? ¿es un programa mas? ¿lo puede correr cualquiera? (hablar de que es un proceso privilegiado del sistema operativo).
Base de datos
Sirven para guardar en disco y que la información esté dure, no se pierda.
Es otro programa, puede estar en la misma máquina o en otro servidor.
Suele ser el centro de una app: varios servidores le pegan.
Hay de varios tipos, cada una con sus pros y contras:
relacional
document
key/value
objetos
Vamos a ahondar en los diferentes tipos de bases de datos más adelante.
Programación de base
1/2 dia
Código
2 hs
La idea es mostrar las slides de este tema y luego complementar con otras cuestiones listadas más abajo. Seguramente convenga modificar las slides para incorporar estos otros temas, o armar otra presentación/slides para estos otros temas.
Slides de este tema [https://docs.google.com/presentation/d/17-b8BNqPpD9lmTr33PLdNgFk7EylN-UuJ_2-1PUMpoY/edit#slide=id.gb9520398d4_1_5]
Cómo nombrar variables
Cómo nombrar variables funciones/métodos
Cómo escribir funciones/métodos
Separación de problemas en partes más chicas (ej: al escribir un método):
Como los ejemplos del cajero automático y del supermercado, pero para otros casos:
Dar de alta un socio en Eryx
Producto de matrices: multiplicar filas, multiplicar elementos
Más en detalle el problema del cajero: que datos meto, qué botones aprieto, cómo muevo la mano y el brazo para apretar botones, etc.
Mostrar y pensar que siempre podemos “ir en más detalle” en un proceso, hasta llegar a cosas muy chiquitas, pero que terminamos en operaciones atómicas (en el caso de lenguajes de programación: arreglos, strings, números, etc)
Niveles de abstracción (ej: el método que llama es más abstracto que los que son llamados)
Al mismo tiempo, mostrar cómo un proceso cualquiera es parte de un proceso más “grande”, más abstracto. Y así como podemos “hacer zoom in” en una tarea, podemos hacer “zoom out” identificando tareas más grandes de las cuales nuestro método es parte.
Ejemplo:
Sacar plata del cajero es parte de Juntar plata para el viaje que es parte de Viajar a Mendoza en auto que es parte de Vacaciones en la montaña, etc.
Comparar en el supermercado es parte de Conseguir ingredientes para la cena que es parte de Preparar la cena que es parte de Hacer reunión con las chicas del club.
Métodos de una línea, por qué:
Cambio de nivel de abstracción
first -> self at: 1
Revista.new() -> revista_vacia()
Nombrar caso frecuente/importante. Ejemplo:
sacar(1000$) -> sacar_mil_pesos
Point(0,0) -> Point.origin() / Point.zero()
Generar un alias. Ejemplo:
modulo(n) -> norma_zero(n)
execute() -> __call__()
Agregar requisito: mismo nivel de abstracción
Un método está en un nivel de abstracción.
Las llamadas a otros métodos que hacemos dentro tienen un nivel de abstracción menor, son más específicas, más concretas.
Pero todas esas llamadas deberían estar en un mismo nivel de abstracción.
Excepciones vs Errores vs Result
1 hr
A continuación cada ítem presenta un concepto. Los ítems de más abajo se apoyan en los conceptos introducidos anteriormente para su comprensión.
Uso esperado, cumplir el protocolo
Cuando definimos un objeto, API, función, esperamos que la gente la use de cierta forma.
Puedo utilizar el objeto, API, función, pero manteniéndome dentro de cumplir los mensajes, parámetros, etc (esto es cumplir el protocolo), pero dentro de ese uso esperado pueden surgir situaciones que no se pueden resolver:
una pila en la cual se meten y sacan elementos. Se puede pedir sacar un elemento cuando la pila está vacía.
puedo querer calcular el camino entre dos lugares pero esos dos lugares no están conectados, por lo tanto no existe un camino.
Incumplir protocolo
Los puedo utilizar mal, en un orden incorrecto, respetando el tipo pero con un valor que produce un error (dividir por cero). En estos casos el responsable del problema, quien genera el problema, es quien interactúa. Puede no ser el que hace la llamada, puede estar más arriba, pero está en el contexto.
Situaciones anómalas
Puede haber contexto que excede a esta interacción y cause problemas. En esos casos la anomalía no se puede resolver localmente.
El responsable del problema está más arriba, quien generó que se puede producir esto.
Ejemplo:
Tengo una colección en memoria.
Alguien de arriba reemplaza esa colección por una colección polimórfica capaz de contener millones de elementos, pero utilizando disco.
Ahora pueden dispararse errores como que el disco está lleno, o un error de datos que antes no podían suceder
Error:
El mismo protocolo (del objeto, API, etc) es el que permite que suceda esto (por eso está bueno generar protocolos que impidan errores por cómo está diseñado, en vez de permitir errores. Algo parecido pasa en UX: generar interacciones que impidan el error en primera instancia más que hacer un buen manejo del error)
Si el protocolo está bien diseñado el cliente debería poder siempre evitar el error
A la pila le puedo preguntar si está vacía, evitando pedir un elemento cuando no hay ninguno
Al mapa le puedo preguntar si dos lugares tienen conexión o puedo hacer que cuando no hay un camino me devuelva un camino vacío o puedo pasarle un bloque que se ejecute en el caso de que el camino es vacío
Notar que en el caso de la colección en disco, no tengo forma de manejar el error, porque nunca pude haber contemplado esa situación. Por eso no puede ser un error.
Ejemplos:
Dividir por cero
Pedir el 8vo elemento a un array de 7
Excepción:
Cuando surge un error o situación que requiere que corte el flujo de ejecución pero este problema no es parte del dominio, no tiene sentido dentro del dominio que se está trabajando. Ejemplo:
Pedir el 1er elemento a un array de 7, pero no hay conexión a internet
Agregar una solicitud de garantía nueva, pero el servidor de Celery está caído
Para esto no solo disparo una excepción que “puentea” a todo el stack de llamado, sino que debería ser atrapada por alguien “más arriba” que sí tenga conciencia del dominio en el que se dispara la excepción.
La solución/arquitectura de estas situaciones es que alguien de más arriba decide que cierta lógica va a usar alguna solución tecnológica puntual (que es de otro dominio e introduce la posibilidad de errores nunca antes vistos). Por ejemplo: armo al negocio para que use un servidor remoto y un ejecutor de tareas asincrónicas Celery, o le indico a un simulador o calculadora que la multiplicación de matrices se va a hacer con una biblioteca externa optimizada (una DLL)
Resultado:
Cuando no quiero o no puedo manejar excepciones (además de errores) puedo implementar un mecanismo de Resultado.
En este mecanismo una llamada siempre devuelve un objeto/dato esperado y al mismo tiempo una indicación de si hubo algún error.
Ejemplo:
Hago un pedido HTTP: No me tira la excepción en ningún caso. Incluso si no hay internet.
Le pido a una persona que haga algo, esa persona vuelve y me explica, me da un reporte
Frontend
1 dia y 1/2
Cómo carga una página HTML
1 hr +
Cómo está organizado un documento HTML: XML, sintaxis, estructura de árbo.l
Javascript: motor corriendo, DOM, eventos.
Historia de la carga de páginas:
Browser recibe string de la red
Convierte en árbol
Renderiza en canvas del sistema operativo
Expone árbol a código JS para que pueda leerse
Expone árbol a código JS para poder modificarse
Si puedo modificar HTML con JS puedo hacer cosas interactivas útiles:
Errores de carga
Ocultar o mostrar secciones
Tooltips avanzados
Recargar/redibujar partes de la página sin saltar a otra
One Page Applications
No cambia la URL (puede ser un problema)
Uso fuerte de AJAX (o su equivalente)
Así como el árbol DOM es el modelo de la vista pixeles del monitor, el árbol DOM es la vista del modelo de datos que tiene la pagina.
Se modifica el arbol de acuerdo a este modelo.
Problema recurrente -> redibujar HTML de acuerdo a modelo -> React
Herramientas: tools del browser, console, elements, network y sources
Declarando funciones y disparandolas desde la consola
Encontrando elementos del DOM en la consola
Vincular una función a un evento de un elemento del DOM
Modificar el DOM desde una funcion JS
Jquery:  ¿qué es? ¿qué resuelve?
Función $, pattern que va de parámetro, manejo de uno o varios elementos de la misma forma. Instalación de handlers.
Frameworks y arquitectura
8 hs
Hacer
tutorial de React [https://legacy.reactjs.org/tutorial/tutorial.html]
.
Explicar la arquitectura a nivel archivos, cómo se cargan, dependencias, preprocesadores
Explicar que hay un motor que procesa el JS de React para generar el HTML/Dom y eso se hace cada vez que se cambia el estado. Compararlo con lo visto anteriormente de “cablear” las interacciones y redibujado y dependencias con JS en HTML. Cómo la complejidad es delegada y absorbida por el motor de React
En particular la sintaxis JSX y cómo el código es preprocesado para convertir JSX en JS que renderiza HTML
Despejar dudas del pasaje de parámetros y cómo los componentes de React dependen de éstos
Hacer paralelo con la programación funcional
Explicitar que esto permite “cachear” o “adivinar” que si los parámetros no cambian, el resultado del componente es el mismo, entonces puedo no evaluarlo
Explicar cómo al cambiar parámetros se reevalúa el árbol de componentes evitando recalcular componentes que reciben el mismo
Deconstruir, analizar, criticar el tutorial de React
Mostrar cómo el negocio, el dominio que se está modelando está embebido, acoplado con el código React
Reflexionar sobre el rol de React: es un motor de rendering, calcula algo visual a partir de datos/modelo/negocio
Entender que el rendering de un negocio y el negocio van separados
Programar desacople de lógica del TaTeTi
Mostrar cómo se puede delegar la lógica ahora a un servidor
Testing
2 hs
¿Por qué queremos tests? ¿Qué valor que nos da?
Explorar y entender el negocio (TDD):
A partir de la funcionalidad vamos definiendo el negocio, vamos aprendiendo. Nos da el valor de ir construyendo, aprendiendo y que nos queden tests que explicitan y testean que eso que aprendimos sigue andando.
Especificar el contrato en una API, con el exterior, con un usuario
Tests a nivel “HttpHandler”. Nos permite asegurar que la forma de comunicarse con alguien del exterior no ha cambiado.
Testeamos:
Happy path (si damos parámetros de forma correcta, recibimos respuesta esperada en formato correcto, y el sistema es modificado de forma acorde)
Errores (si provocamos el error X, la respuesta tiene formato Y y el sistema no es modificado)
Enseñar el negocio
Explicar qué hace mi sistema ¿desde los tests? ¿o es algo que lo excede? ¿Se puede explicar o entender el negocio viendo los tests? ¿Y haciéndolos?
Los tests son una enumeración de propiedades o de “verdades” del sistema. No garantizan pedagogía, pero sí completitud. Aunque sean en un orden pedagógico, tampoco son redundantes como en la pedagogía, no explican lo mismo de varias maneras. Tampoco ejercitan. En matemática no solo te explican de varias formas lo mismo, después hacés ejercicios que ponen en juego lo aprendido.
Los tests enseñan qué cosas puede hacer el negocio o sistema y cómo hay que “hablarle” para hacerlo. Pero no enseña cómo estas funcionalidades se entrecruzan, o conceptos que se construyen sobre estas operaciones fundamentales. Es parecido a saber cómo se mueven las piezas en el ajedrez vs saber jugarlo.
Detectar cuando dejo de cumplir un contrato o de realizar una funcionalidad
Si tengo una regla de negocio, imaginemos un límite de caracteres en un texto, quiero duplicar ese número, ese límite: uno en los tests, otro en le negocio. Eso me permite detectar cuándo el límite del negocio es cambiado.
No es duplicación de código, es una redundancia que me permite detectar cuando algo cambió sin que yo quiera.
Implicación: Dado que tengo tests que detectan cuando dejo de cumplir reglas de negocio, dejo de cumplir una API, entonces si cambio la funcionalidad de mi sistema y dejo de cumplir algunas de esas cosas tengo una forma automática de detectarlo. Esto quiere decir que puedo refactorizar o incluso reescribir mi código y que mi sistema siga teniendo las mismas funcionalidades, el mismo comportamiento.
Tipos de test
Test de regresión
Esto es correr tests que andaban antes, de nuevo ahora, para ver que no “hubo una regresión”, es decir, que no retrocedí y lo que andaba antes sigue andando ahora
Test de integración
Definición muy amplia o ambigua, a tal punto que resulta poco práctica. Y está dada en contraposición al test de unidad. Muchas veces un test de integración es uno que no es de unidad. En la práctica esta definición es muy poco útil.
¿Que es de integración? ¿Que participa mas de un objeto? ¿Mas de un sistema? ¿Mas de un paquete? ¿Qué es lo que se está integrando?
En general vamos a querer siempre tests que testeen cuestiones funcionales, y que involucren a todos los objetos que sean necesarios.
Test end to end (e2e)
Tests donde tratamos de replicar (desde el uso, no necesariamente desde el deploy) el uso del sistema como se va a dar en la realidad.
El ejemplo típico son los tests de Selenium, donde levantan el sistema, luego levantan un navegador web, y le hacen click, rellenan campos, etc en el navegador y ven cómo responde éste
Son tests caros y frágiles
Pero muy valiosos para testear que “el sistema anda de verdad”.
Conviene para unos pocos casos críticos.
Test de caja blanca y caja negra
Negra: testeo lo que el sistema hace, sin tener en cuenta cómo lo hace. Es un test funcional.
Blanca: testeo aspectos que sólo tienen sentido por cómo está implementada la funcionalidad. Pongo a prueba la correcta implementación.
Hay casos que son borde no funcionalmente sino algorítmicamente en la implementación.
Bugs que no vuelven
Cada vez que detecto un bug, hago un test que reproduce el bug
Corrijo el bug
Ahora me queda un test que me garantiza que ese bug no vuelve mas
Esto es especialmente valioso porque suele ser muy caro a nivel negocio o político que un error subsanado vuelva a suceder
Smoke test
Viene de la ingeniería electrónica
Sirve para correr todos los tests (no perder tiempo) si hay cosas muy burdas, muy básicas que están rotas
La idea es correr unos pocos tests que me aseguren que todo lo básico anda.
“Lo básico” depende del sistema, pero normalmente es: la persistencia anda, el login anda, los sistemas externos/emulados están correctamente conectados, etc.
Tests manuales
Son ejecutados por personas
Si testean algo que puedo testear de forma automática con tests escritos por desarrolladores, los estoy usando mal
La idea es que testeen cosas no automatizables
Hay muchas estrategias y técnicas de testeo manual que aportan valor y no son automatizables, pero vamos a ir con algo resumido
El tester manual asume un rol de “hacker” donde trata de hacer cosas erróneas, corromper el sistema o eludir reglas de negocio.
El tester manual se fija si el sistema es consistente en sus reglas de negocio y en su implementación
El tester manual utiliza el sistema y se fija que los procesos sean claros, directos, intuitivos (acá se testea la UX además de lo programado solamente)
Tests no funcionales
Baseline
Mide o comprueba cómo el sistema funciona o debería funcionar normalmente. Cuál es un tiempo de respuesta esperado, por ejemplo.
Compatibilidad
Prueba que el sistema opera bien con los diferentes sistemas para los cuales fue diseñado.
Que el sitio web funciona con los diferentes navegadores
Que la app mobile funciona bien en diferentes celulares
Que el servidor o ejecutable corre en diferentes sistemas operativos
Stress
Uso intensivo del sistema, múltiples usuarios a la vez, muchos requests a la vez
No es obvia la diferencia con los tests de carga
Seguridad
Hacerle ataques típicos y ver que no se vulnera el sistema
De carga
Uso del sistema con grandes volúmenes de datos (pero no necesariamente de usuarios o requests): que el sistema tenga muchos datos, que le subo muchos datos, que procese muchos datos o que corra un proceso caro en CPU o que me devuelva muchos datos
No es obvia la diferencia con los tests de stress
Recovery/Reliability
Testea cuán bien el sistema se recupera de caidas. Desde un “segementation fault” en el servidor, hasta fallas de hardware, pasando por fallas de internet o de otros sistemas externos.
Testea que ante estas eventualidades el sistema quede en un estado válido
Testea que el sistema se recupera (si puede) en tiempo y forma
Volume
Testea que el sistema sigue andando bien (como en baseline) si su base de datos es grande, si ya tiene un manejo de un alto volumen de datos
Documentation
Testea que el sistema es fácil de entender a partir de la documentación, que la documentación es completa y correcta.
Internationalization
Testea que el sistema se puede adaptar a diferentes culturas e idiomas.
Accesibilidad
Testea que las interfaces cumplen las especificaciones y criterios de accesibilidad (que se mapeen bien a una interfaz oral, que las imagenes tengan bien su “alt text”, que sea navegable por teclado, etc)
Tests de features definidas y tipo de producto
Normalmente entendemos que un test prueba que una funcionalidad está presente y que funciona de forma correcta
Sin embargo, existen sistemas que tienen “versiones”. Hay sistemas que el conjuntos de funcionalidades que ofrece varían según el cliente, el plan de pago, la subscripción o algún otro factor
En estos casos vamos a querer testear la inversa: que una funcionalidad NO esté presente en el sistema, que NO funcione de forma correcta
Un ejemplo de esto es cuando tengo dos sistemas, la version Demo y la version Enterprise
Esto nos empieza a mostrar que un test y probar que una funcionalidad está definida (o no) son dos cosas diferentes
Unit tests
Esta es otra mala definición, hermana de “tests de integración”
A veces los tests de integración se definen como “cuando no es de unidad”
Los tests de unidad se definen como que testean sólo una unidad
Pero no definen qué es una unidad. ¿un objeto? ¿un método o función? ¿una jerarquía? ¿un paquete/módulo?
Si bien no nos parecen útiles este tipo de tests, o al menos esta clasificación, sí encontramos casos donde uno quiere testear en más detalle un objeto o pequeña familia de objetos bien cohesivos.
Estos casos suelen ser cuando tengo algo intensivo en cálculo. Un ejemplo es testear el cálculo del camino mínimo sobre un grafo. O un objeto que calcula los intereses de un capital según la cantidad de días hábiles que pasaron. Es decir, objetos o conjuntos de objetos que realizan cálculos complejos.
Entidades que nos aparecen
A medida que testeamos vamos a empezar a resolver problemas propios del mundo del testeo, pero que no son particulares de mi negocio
Mocks, o test doubles
Estos objetos me sirven para reemplazar objetos que estarían en producción, pero no los puedo tener durante el testeo. Típicamente, servicios externos.
Factories
Estos objetos me sirven para crear objetos de mi negocio, o a mi negocio como un todo, para utilizarlo durante los tests
Hay más objetos, que van a ir resolviendo diferentes problemas. Podemos pensar que habrá objetos que nos van a sacar código repetido, van a modelar cuestiones que se repiten en cada una de las tres secciones del test: Given, When, Then.
Las Factories, por ejemplo, resuelven la repetición de código que se produce al establecer el escenario inicial del test, el estado inicial de mi negocio, en el Given.
Análogamente, nos pueden surgir objetos que representen todo aquello que se me repite en el When y en el Then.
Organización de tests como enseñanza del sistema
Los tests pueden ser un buen lugar para empezar a entender o ver ejemplos de uso de los objetos de mi sistema.
Están organizados de menor a mayor complejidad
Tenés código que anda y usa al sistema para una accion específica
Viendo todos los tests deberia saber/aprender todas las cosas que sabe hacer el sistema
Viendo todos los tests, potencialmente, podria saber usar a todo el sistema
Son insuficientes para enseñar el negocio y el sistema, pero ayudan o complementan.
No te dan visibilidad de efectos secundarios, o de qué grandes circuitos se pueden realizar en el sistema o grandes invariantes que se mantienen
Se pueden enumerar u ordenar de forma tal que se puedan leer en orden de complejidad.
¿Qué significa testear mi app?
Testear mi app es otro sistema, es otro mundo:
Mi sistema modela un negocio. Su responsabilidad es la de realizar las funciones que pide el cliente, que dan valor, etc.
¿y los tests? ¿qué hacen? ¿Qué responsabilidades tienen? ¿Cómo se relacionan esas responsabilidades con las del negocio que estoy modelando?
Los tests son “algo”, son código que agarra al sistema que estoy creando y lo “cachetea” para ver si anda bien.
El valor que me dan los tests es bien diferente del valor que me da el sistema con el cual implemento/modelo a mi negocio.
Por lo tanto… es otro sistema. Porque tiene otro propósito, provee otro valor.
Eso significa que también el sistema de testing va a modelar otro dominio: el dominio del testeo.
Temas que no vemos
(se ven en talleres o capacitaciones más adelante)
Tests de actores
Escenarios, interlocutores y asserters/propiedades/aspectos
Formas de organización de los tests (ej: épicas cronológicas)
Tests y la enseñanza/aprendizaje del negocio
Metodologías ágiles
2 hs
¿Qué son?
Son formas de organizarnos para llevar adelante un proyecto. Son ágiles porque la idea es que podamos adaptarnos a los cambios de la realidad, del negocio, del cliente.
¿Por qué/para qué?
En nuestra experiencia (y la de otros en la industria) pactar un proyecto, un producto, un sitio, lo que sea, ir a hacerlo y volver cuando está terminado no anda. Siempre existen divergencias entre lo que el cliente quiere y lo que vos le das. Además, a medida que pasa el tiempo el cliente puede ir cambiando lo que quiere y a medida que el cliente tiene cosas concretas que pidió, eso le hace cambiar lo que quiere también. Entonces necesitamos ciclos más cortos de hacer algo, entregarlo, que el cliente lo vea, y que nos pida algo mas.
¿Cuándo se aplican?
Entorno dinamico cambiante (si se aplica)
Típico caso: el cliente puede cambiar lo que quiere, las necesidades cambian, el contexto/entorno donde va a correr el sistema cambia, el mismo negocio que el sistema va a modelar va cambiando.
Entorno estático predecible (no se aplica)
Este es un caso rarisimo. Donde lo que hay que hacer está fijo, no va a cambiar, es decir, lo que queremos que haga la aplicación y el contexto que la rodea es algo constante. Esto puede ser para algoritmos, o rehacer un sistema.
Así y todo, separar en partes más chicas, e ir dando y mostrando avances es enriquecedor, suma.
Por lo tanto, no se me ocurren buenos ejemplos de este tipo de escenario.
Entorno incierto, investigación (no se aplica)
Si se está desarrollando algo nuevo, que no se sabe cuánto va a tardar, o no se saben a ciencia cierta todos los pasos que hay que hacer, básicamente cuando estamos investigando.
Kanban vs Scrum
Tenemos dos modelos de trabajo: hacer cosas que se planifican o ir respondiendo a medida que surgen las cosas
Kanban es para cuando tenemos que ir respondiendo a cosas que surgen. Un equipo que atiende tickets de soporte por ejemplo, trabajaría con modalidad Kanban.
Scrum es para cuando tenemos una lista de tareas para hacer, hitos que alcanzar (entregables, releases, etc) y necesitamos ir viendo el progreso que se está logrando en esa línea
Kanban
Típicamente tenés 3 columnas: para hacer, haciendo, hecho. Podés tener menos, es normal tener más.
También es típico tener prioridades en las tareas que entran en “para hacer”.
Límite de cuántas cosas pueden estar en “haciendo” o cualquier estado intermedio entre “para hacer” y “hecho”. Esto es para limitar tomar tareas, avanzarlas y no terminarlas y para detectar cuellos de botella o trabas. Y básicamente explicita nuestro “ancho de banda”, nuestra capacidad para resolver problemas.
Tomar tareas de derecha a izquierda. Es decir, tratamos primero de terminar las tareas que le faltan poco y solo después de haber avanzado o terminado esas tareas, tomamos las que les falta más. Esto es para minimizar el “throughput”, es decir, el tiempo en que una tarea tarda en hacerse.
Todo esto se va ajustando de acuerdo al equipo, las capacidades que tiene, etc. Pero la clave es limitar las tareas que hay en las etapas intermedias y priorizar terminar tareas.
Scrum
La idea es hacer un desarrollo grande, largo, en pequeñas “carreras” (sprints).
Empezas con un backlog, con una lista de tareas, y el equipo elige cuántas de esas tareas se va a comprometer para hacer en un sprint.
Un sprint dura entre 1 y 3 semanas, normalmente 2, conviene que sea lo más corto posible
Las hace, las muestra al cliente y se repite el ciclo de elegir tareas del backlog para el siguiente sprint
El cliente define el backlog y el orden de importancia, el equipo elige cuántas tareas del backlog tomar para un sprint
Entre un sprint y otro es importante:
demo, donde el equipo muestra lo logrado en el sprint
retro, donde el equipo reflexiona sobre el sprint y plantea mejoras
planning, donde el equipo elige qué y cómo hacer en el próximo sprint
Separando desarrollo de entregas y relevamiento: Sprints y Backlog grooming
Notar que hay tres flujos de trabajo:
Crear y mantener el backlog (backlog grooming)
Desarrollar las tareas del sprint
Utilizar y dar soporte a lo desarrollado (lo que el cliente hace con lo que le damos y el soporte de errores o temas que nos pueda llegar durante el sprint)
El backlog grooming se hace con una parte del equipo (ver más abajo) y con el cliente durante el sprint
El soporte a lo desarrollado se hace con una parte del equipo respondiendo consultas y/o resolviendo tickets de soporte durante el sprint
Para esto el equipo tiene en cuenta la carga que le va a insumir, y de acuerdo a eso calcula cuántas tareas tomar
Roles: Product Owner, Scrum master, dev, tester, ux, analista
En el equipo de scrum se distinguen tres roles importantes
Desarrollador: programador, tester, UXer, analista, cualquiera que contribuya a hacer realizar las tareas
Scrum master: un miembro del equipo que se dedica a organizar el sprint, los rituales y a destrabar problemas que el resto encuentre: faltan unas credenciales, hay que pedir unas imágenes que faltan, etc
Product owner: un miembro del cliente dedicado al proyecto (full time o part time). Su tarea es definir el backlog (hablando con gente del cliente), priorizarlo, emprolijarlo (grooming) con alguien del equipo, responder dudas sobre las tareas y convocar a la gente relevante para la demo
A veces el rol de scrum master es fijo, a veces rota
Rituales
Los típicos rituales de scrum son:
Planning: se eligen cuántas tareas hacer, se repasan para entenderlas mejor y detallarlas más si hace falta, cada miembro del equipo elige en cuál participar
Daily: se elige un momento corto del día en el cual el equipo se sincroniza, cuenta cómo van las cosas, qué tiene planeado hacer, qué hizo y si tiene algún bloqueante (que otros miembros o el scrum master ayudarán a destrabar). La idea es que sea algo corto, de 15 minutos, se suele hacer con todos parados para incentivar a que sea corta.
Demo/Revisión: se le muestra al cliente (product owner y otros stakeholders que considere) lo alcanzado. Se toman críticas, observaciones, modificaciones que se puedan incorporar en nuevas tareas en sucesivos sprints.
Retro: el equipo se junta y reflexiona sobre el sprint que pasó, cómo están trabajando, qué pueden mejorar y se definen accionables para lograrlo
Además está al “backlog grooming”, que se trata de meter tareas en el backlog, priorizarlas y detallarlas lo suficiente para que se pueda hacer una planning con ellas
Variantes
Scrum se puede adaptar a cada equipo, proyecto o cliente.
Los distintos rituales se pueden modificar, algunos usan todos los rituales, otros usan sólo algunos, a veces no se hacen demos porque apenas se desarrolla algo se hace la demo de eso a quienes les interesa, o la daily se hace cada dos días y así, un sinfín de variantes.
Los mismos rituales tienen variantes de cómo se pueden llevar a cabo
Scrum of Scrums
Scrum tiene sentido para células de entre 3 y 8 miembros
Si se tienen proyectos o equipos de más personas pueden empezar a aparecer más de una célula de scrum
Eventualmente éstas células van a necesitar algún tipo de coordinación y para eso surge el scrum of scrums, donde los scrum masters de cada célula scrum se juntan y coordinan
La idea no es detallar esto, sino saber que se puede escalar con esta metodología y que ya existe algo pensado.
Herramientas
1/2 dia
La idea es que el ingresante entienda y tenga un manejo básico de las herramientas y tecnologías que va a usar en el día a día.
Git
Queremos asegurarnos que el ingresante entienda que es una rama, y que entienda las distintas estrategias sobre cómo usarlas
Organización básica en ramas [https://learngitbranching.js.org/?locale=es_AR]
Validar lo que se aprendió y explicar típico manejo de ramas
Trunk based development: muchos commits chicos a main, las ramas creadas, si las hay, viven poco, no más de un dia
Una rama por card/feature. No duran más de un sprint.
Pycharm/IntelliJ
Dar una explicación básica de la IDE
Menu VCS/Git
Abajo a la derecha: rama actual, cómo administrarlas
Uso de runners, mostrar el composite runner
Project structure, cómo ver una clase, sus métodos, sus variables de instancia y cómo filtrar para no ver cosas de más
IDE Ninja Talk 01 [https://youtu.be/ysWIbKs7So4]
IDE Ninja Talk 02 [https://youtu.be/udSujEip63c]
IDE Ninja Talk 03 [https://youtu.be/flGqA2LhM8Y]
IDE Ninja Talk 04 [https://youtu.be/Y9BrvvRFxjA]
Unix
¿Qué es el shell? (nivel básico)
Interpreta línea de comando, hay varias variantes de shell, convierte lo que tipeas en acciones
No todo lo que tipeas se traduce en algo que el shell hace o ejecuta, hay cosas que le dicen al shell cómo interpretar lo que estas tipeando:
las comillas para ignorar espacios
los pipes | para encadenar stdins con struts
los > para mandar a archivos
los $ para ejecutar en background
Procesos y programas
Se levantan de disco y se ponen en memoria
Se linkean y cambian referencias
Se le setean variables de entorno, running directory
Se le define el stdin, stdout y stderr (el shell define esto con los programas que tipeamos/le decimos que levante)
Pero el shell es otro programa mas, que levanta otros programas hablando con el SO
Nuestros programas pueden hacer lo mismo, levantar a otros programas diciéndole al SO cómo enrutar sus entradas y salidas, qué variables de entorno tiene, etc.
Código de retorno, errores
0 es éxito
distinto de 0 es error, y el número es el código de error
Esto es una convención ¿o es parte del estandar POSIX?
stdin, stdout, stderr
¿Qué son?
¿Para qué sirve cada uno?
¿Cuándo usar cada uno?
Pipe de procesos
Qué es un pipe
Qué hace el SO con los procesos y los standard streams
Ejecución sincrónica
Idea de Unix: componer programas chiquititos
Spawn de programas/procesos, árbol de procesos
Explicar relación y dependencia entre procesos padre e hijos, qué pasa cuando un proceso padre muere.
Shell como programa dentro del árbol de procesos
El shell es un programa mas que suele hacer de padre
Comprobar cómo el shell se comporta y cumple esto de ser un proceso padre:
Levantar un gedit desde la terminal, ver cómo se bloquea y luego de cerrar gedit vuelve el control a la terminal
Qué pasa cuando lanzamos gedit con &
Variables de entorno, alcance (scope)
¿Pero si las variables son de un proceso cómo es que el shell que es un proceso le puede definir variables a otro proceso que spawnea, si es otro proceso?
Exportación de variables de entorno
cron job: qué es, cómo se usa, para qué sirve
root y usuarios
Nunca usar root
Permisos: all, group y owner
Permisos: read, write, exec -> files y directories
Stack del proyecto - Django
4 días
La idea de esta parte de la capacitación es enseñar el stack del proyecto y cómo diferimos de cómo desarrolla la comunidad o industria. La estrategia que usamos es mostrar y enseñar como lo hace la industria (normalmente a través de sus tutoriales), analizar y criticar lo hecho y empezar a construir una alternativa, una mejora a lo hecho hasta que se vea mas o menos la dirección (Appyx normalmente) y ahí cortar. No es la idea enseñar Appyx. Sólo las bases y que el resto lo aprenda en proyecto o alguna capacitación puntual.
Hacer tutorial
Hacer
este tutorial [https://docs.djangoproject.com/en/5.0/intro/tutorial01/]
.
Mientras el dev hace el tutorial sin estar acompañado, la idea es todos los días juntarse y ver los avances. También es deseable que el ingresante pregunte cualquier cosa que no entienda.
Como muchos no tienen la iniciativa de preguntar (por mil motivos que no vienen al caso) sugerimos hacer una puesta en común al final de cada día.
A continuación listo algunos ejemplos de temas del tutorial que se pueden querer evaluar, profundizar o que el ingresante puede preguntar. El orden es el mismo en el que están en el tutorial y en el que el ingresante los encontrará.
Nota: evitar que al final de la parte 4 convierta el código a generic views porque después vamos a querer que deshaga eso y hacer eso no aporta mucho
¿Qué está pasando cuando hago startproject?
Explicar que está creando carpetas y archivos python (y de línea de comando) que tienen lo básico de un proyecto (más adelante vamos a ver qué hace cada cosa)
Explicar por encima lo que es creación automática de código.
¿Cómo se entiende la línea python manage.py runserver ?
Reforzar o testear conocimientos de línea de comando
manage.py es un script de python
¿Cómo le llega “runserver” al script si es un parámetro de “python”?
explicar cómo manage.py tiene el rol de “script de mantenimiento” de un proyecto django
Recorrer cada archivo del proyecto e indicar qué hace, para qué sirve
Cómo es la arquitectura de urls.py y crear views y declararlas ahi
Notar que es estático
Pero son objetos, se podría calcular dinámicamente
¿Uso del include()? ¿explicarlo?
Explicar cómo django tiene una parte oculta que procesa la url del request entrante y usa estos objetos/datos para rutear ese request y mandárselo a la view/código nuestro correspondiente. Unirlo a las charlas anteriores de cómo funciona un server
Qué es una app en Django
Son código+persistencia+urls
Django considera que existen unidades funcionales de urls, con su código y su persistencia que son plausibles de ser reutilizadas en diferentes proyectos
En la práctica esto no nos sucede ni sucedió nunca, no las usamos
Explicar migrations
Resuelve el cambio de schema de una DB cuando hay datos en ésta que hay que preservar
Contar los casos típicos: agregar una tabla, agregar una columna con default constante, agregar una columna con default calculado
Separar una tabla en dos tablas
¿contamos que las migraciones se corren cada vez que se ejecutan los tests?
Model
Explicar que nuestros objetos deben heredar de models.Model
NO cuestionar esto, sólo contarlo, sin sesgos, sin poner caritas ni tonos de voz
Cualquier crítica o cuestionamiento del ingresante tomarlo de forma estoica sin tomar postura
Mostrar cómo declarando las variables de instancia del Model de forma un poco más “costosa” consiguen dar suficiente información como para que Django sepa cómo mapear el modelo de objetos a tablas en una DB relacional
Notar cómo Django es minimal
Notar que Django en este proceso te pidió el mínimo de información posible
No te puede pedir menos información para crear el tutorial
Esto es algo muy deseable en un diseño y en cualquier solución que construyamos
No ser minimal es un “smell” de que hay algo que falta modelar, o hay una responsabilidad mal asignada
Django admin
Si tenemos toda esa información declarada para poder entender cómo se mapean nuestros objetos a la base de datos, entonces podríamos tener un CRUD (Create, Retrieve, Update, Delete) generado automáticamente, no? Eso es el Django admin
Históricamente lo hemos usado como backoffice, no lo hacemos más por los problemas que provoca
Se puede customizar bastante, pero siempre hay un límite. No escala.
Hay código que le va indicando a django admin qué exponer, qué permitir editar, etc. Igual no escala.
Views
Asegurarse que entienda el sistema de rendering con templates: pseudo html en un archivo por fuera, código embebido, el código embebido hace referencia a “algo” que le pasan por parámetro y la estrella que brilla por su ausencia: un motor que interpreta este pseudo html e invierte el archivo, de html con código embebido a código que ejecuta y en el medio escupe strings
Contar que esto es server side rendering
Si da, contrastar con client side rendering (enviar solo datos, por ejemplo con una API REST)
Asegurarse que se entienda la función get_object_or_404(), mostrar las bondades y todo lo que te ahorra empaquetando un montón de cuestiones en una sola función. Notar cómo reacciona y qué postura tiene ante esto. Eso nos va a indicar qué hay para trabajar más adelante. De nuevo, ser objetivo y no emitir opiniones sobre esta característica de Django.
Explicar el reverse, cómo funciona, qué hace, cuándo y por qué usarlo, cómo se relaciona con el name del path en urls.py
Explicar cómo se declaran los parámetros en el path y cómo esto se traduce a que en la view ya te venga un parámetro con ese nombre que el router se encargó de extraer de la url y pasártelo como parámetro
(opcional) explicar el mecanismo de python de **kwargs que es usado para esto
(opcional) explicar qué es el csrf token, qué tipo de ataque evita
Generic views: si bien no las va a implementar/usar, explicar qué problema resuelven.
No las queremos usar porque después vamos a refactorizar el código y refactorizar para meterlas para después hacer el refactor inverso es engorroso
(opcional) hacer que el ingresante convierta las vistas en generic y luego las deshaga, para entender en mayor profundidad cómo te ahorra laburo y después te ata de manos cuando querés hacer algo superador. Nota: en vez de borrarlas o modificarlas, crear unas views paralelas que simplemente no referenciamos en urls.py, así podemos volver atrás y usarlas. De paso practicamos cómo refactorizar código que convive todo en master sin que se rompa nada.
A partir de acá queda por explicar Django Admin. ¿les parece que vale la pena explicarlo?
Analisis y critica
En esta parte de la capacitación, que depende mucho del nivel del ingresante, queremos empezar a señalar malas prácticas de la industria. Pero a través de problemas concretos que se ven en el código sugerido. La idea va a ser ir por el camino de menor resistencia siempre: mirar el código, criticarlo, ver qué problemas tiene, refactorizar y volver a esta etapa.
¿Qué opina el ingresante de los métodos creados en el tutorial? ¿Qué opina el ingresante de que en una misma función/método se hable de http, reglas de negocio y base de datos?
¿cómo meto lógica de negocio?
¿cómo hago si se repite código en distintas Views?
¿cómo me abstraigo de la DB y meto cosas en memoria o en un archivo plano de texto?
¿cómo comparto lógica o comportamiento de cómo procesar cualquier request?
Refactor
Separar http de no-http
Separar errores de negocio de errores de tecnología
Separar lógica de negocio
index: la lista de preguntas que te las de la PollingStation (el Business)
Refactorizar cada endpoint en sus componentes. Nos deberia quedar un template method idéntico para cada View. Las partes de ese template method deberían corresponderse más o menos a esto:
extraer parámetros
crear ¿y parametrizar? la interaction
procesar el resultado de la interaction, generando el content, aplanandolo
calcular el status code
Introducirnos en cómo desarrollamos en Eryx
Como parte de esta parte de la capacitación queremos mostrarle al ingresante que analizando y reflexionando sobre el código y sobre el problema se pueden crear soluciones superadoras.
En este caso en particular, hay cosas que suceden todo el tiempo y las modelamos.
Queremos mostrar que en Eryx vamos mas allá de lo que recibimos. No nos quedamos con lo que nos dice o da la industria. Lo miramos, lo analizamos, lo criticamos y hacemos algo superador.
Ese es el espíritu de esta parte.
De paso, vamos mostrando que estas abstracciones que se empezaron a ver, que empezamos a modelar ya fueron iteradas en nuestra biblioteca Appyx.
Result
Se deduce que hay partes del código (la interaction) que van a devolver a veces un objeto útil y a veces van a tirar una excepción. Y siempre vamos a tener que manejar todo eso
No es fácil de manejar genéricamente excepciones, o delegarlas
Conviene tener un manejo homogéneo de estas cuestiones
Así es como debería surgir la idea de Result
Interaction
La aplicación es muy chica todavía para justificar la interaction por el lado de que desde varios lados se usa la misma. Aunque se le puede agregar al tutorial alguna funcionalidad por línea de comando
Sí debería quedar claro que no tiene sentido mezclar extracción de parámetros del request con la acción que quiero hacer en el negocio
Facilita el testeo si tengo algo que ejecuta lo que quiero, pero sin necesidad de un request, tan sólo pasándole los parámetros que necesito
Esconder o parametrizar la base de datos está lejos todavía, pero algunos ingresantes pueden llegar a verlo o parecerles interesante
Estas son las razones que se pueden esgrimir y trabajar para que surja la idea de interaction en este tutorial
Pueden surgir más abstracciones que tenemos en Appyx (sería raro que surja alguna que no tenemos ya en Appyx). Acá el capacitador tiene que estar atento y aprovechar (o dejar pasar) la oportunidad
Anatomía de un handler de POST
A continuación se describe la estructura lógica (separada en 3 párrafos) a la que se debería converger en la View/http handler que estamos trabajando en el tutorial y en su posterior crítica.
Esta descripción es para poder señalar responsabilidades que a futuro van a surgir y que se pueden encontrar en Appyx.
1er párrafo
Este párrafo se encarga de extraer y validar parámetros, hacer una primera validacion del request.
Validación básica de parámetros
Chequeo de parámetros obligatorios
Extracción de parámetros del request
URL
Request Body
Request Headers
FILE (en headers)
Cookie + Session + User
Cookies
Si en el request no viene una cookie (no visitó nunca la página de zapas) entonces:
el server crea una cookie (unívoco)
En una tabla agrega una entrada para la cookie mete una nueva entrada: cookie_nueva: []
Si estoy viendo una pagina de un tiem, lo agrego al historial de compra y actualizo la entrada del diccionario
Cuando genero la página de respuesta, le agrego el pie de página con los últimos 10 items del historial
la agrega a la response del request.
Si en el request viene una cookie
Va al diccionario “principal” y obtiene el historial de compras asociado a esa cookie (diccionario[cookie]).
Si estoy viendo una pagina de un tiem, lo agrego al historial de compra y actualizo la entrada del diccionario
Cuando genero la página de respuesta, le agrego el pie de página con los últimos 10 items del historial
Cuando esta request saliente del mismo cliente vuelva a conectarse con la página de zapas (server) entonces la request se comunica con el server mediante el numero de cookie que ya tiene creado.
It was all about SESSIONS!
Se guarda en disco esta información
En los browsers (clientes modernos), se tiene un historial por cada cuenta / usuario.
2do párrafo
Este párrafo lidia con mandar el pedido para adentro, hacia el negocio.
Transformar parámetros del request en objetos de dominio. O guarda un error si no puede transformarlos.
Yields to the business the computation of the request
Algo más…
3er párrafo
Esta párrafo lidia con el protocolo. Crea la respuesta,cumpliendo el protocolo, en base al resultado recibido.
Setea el status code de acuerdo a cómo salió la respuesta
Setea el contenido a devolver, si es que hay contenido a devolver
Redirige el request, de ser necesario
Bases de datos
1/2 dia
Comienzos: archivos
¿Como haría para persistir algo si no existieran las bases de datos?
Como hacían antes: archivos. Y archivos de texto.
Voy agregando líneas. Cada línea representa un elemento que meto.
A priori en un archivo meto todos elementos del mismo tipo (decisión arbitraria).
La línea va a tener un formato, el más sencillo es que cada campo tenga una longitud fija.
Para levantar los objetos persistidos, leo todo el archivo en memoria.
¿Cómo resuelvo la búsqueda, referencia, agregado y borrado con este esquema?
¿Y si la longitud de los campos es variable?
¿Y si agrego ID?
Recordar que los sistemas de archivos permiten acceso secuencial y aleatorio arbitrario.
¿Cómo puedo acelerar la lectura cuando busco por un campo en particular? (indices)
¿Qué costo tiene esto? (ver impacto en agregado y borrado)
¿Cómo hago cuando un registro quiere referenciar a otro?
Mostramos cómo se va generando código repetido, o cómo se van repitiendo problemáticas que trascienden al modelo de datos en sí. No son de mis datos, sino de esta forma de persistir.
Relacional
A lo anterior le agregamos varias funcionalidades y capas
Aislamos los archivos e índices, ahora las DBs pueden tener índices o tablas en memoria, pueden reordenar, compactar, saltear el file system, etc sin nosotros enterarnos
Tienen una API: DLL/SO local, remoto vía red
Lenguaje de query: SQL
Meta data: database schema
Usuarios y permisos
Concurrencia
Transacciones
Aparece el término “foreign key”, pero que ya existía
De objetos
No usa tablas
Define un conjunto de objetos raíz
Se levantan con proxies
No hay indices. El llegar eficientemente a un objeto tiene que estar dado por el modelo.
Pero podés hacer índices si querés o si tenés colecciones muy grandes.
Cuando cambiás un objeto tenés que decirle que lo persista.
Las ODBs mas avanzadas detectan el cambio via la VM.
Los objetos se levantan y viven en el contexto de una transacción.
Todas las técnicas que usan las ODBs son exactamente las mismas que nosotros usamos cuando nos empezamos a aislar de una RDB.
De documentos
Sirven para guardar y gestionar información que normalmente no está generada por nosotros, pero de la cual somos dueños
Ejemplos: jsons que vienen de otro sistema, PDFs de una publicación hecha hace tiempo o por otro, escaneos de documentos físicos, mails, etc.
Por defecto a todos los documentos se les generan campos “meta”, que son independientes del formato interno: fecha de creacion, modificacion, tamaño, tipo de doc (terminación) y algunas extra dependiendo de si tienen formatos estandarizados como PDF
Se definen propiedades, campos sobre estos documentos, que van a ser el equivalente a campos de un registro. Pero puede que necesitemos definir funciones específicas para obtener ese campo a partir de un documento (porque el documento es algo arbitrario externo)
No tiene esquema
Algunos las usan justamente por no tener esquema, pero eso es un error, para eso podes usar otras como objetos o key/value
Key/value
La podes pensar como una base de objetos con mucha menos funcionalidad
La idea es que la key y el value sean arbitrarios tuyos
Son muy eficientes porque hacen algo muy limitado
La relación entre un registro y otro lo tenes que inventar vos. Es decir, si un value adentro referencia o apunta a otro key o varios key, eso lo tenes que implementar/inventar vos
Sirve cuando querés mucha eficiencia y estás dispuesto a pagar el costo de implementar todo lo extra que necesitas a una DB
Rol de absorber concurrencia y distribución
Dado que las bases de datos relacionales resuelven la concurrencia y distribución de datos nosotros no tenemos que programar esas cosas.
Pero eso nos limita a lo que las bases de datos hacen o pueden hacer.
[ejemplo de cuándo no podemos ignorar la concurrencia y tenemos que intervenir o puentear el manejo de concurrencia de la DB]
Transacciones ocultas (y transacciones)
Toda operación sobre una DB es transaccional. Es decir, ocurre en el contexto de una transacción. Incluso si estamos hablando de archivos. Los archivos, a nivel filesystem usan transacciones.
En los sistemas que solemos programar en ningún momento aparecen las transacciones ni sus productos secundarios: commit, abort, etc.
Lo que sucede en nuestros sistemas es que cuando atendemos un request, se abre una transacción y cuando se da la respuesta se hace commit. Incluso si hay un error hay que ver si no se hace commit también. No lo sé y depende del framework web.
Ejemplo de cuando no podemos ignorar las transacciones:
Como el commit se hace luego de responder el request
Y como necesito que alguien externo lea mi estado durante mi ejecución
Entonces necesito hacer commit ANTES de terminar de procesar el request
Un caso concreto surgió en un proyecto de Eryx donde disparábamos un evento que alguien desde afuera iba a propagar, entonces necesitaba leer los datos nuestros, pero para leerlos de forma consistente necesitaban estar en la base de datos (al ser un sistema externo los iba a leer con una nueva transacción, y como la actual no había terminado, no los iba a ver)
TDD
1 dia
Acá estoy suponiendo que el ingresante ya hizo TDD y la idea es refrescar la técnica y “afilarlo” en la misma. En caso de que NO haya hecho TDD la idea es introducirlo a ésta para que pueda acompañar al equipo e ir profundizando durante su trabajo en un proyecto. O de última con una capacitación aparte luego de ingresar al proyecto.
Empezamos modelando un solo objeto con TDD: un contador (que la haces click y suma uno)
Deberíamos tener tres tests: inicial en cero, si hago click me da 1 (hardcodeado) si hago dos clicks, da 2 (rompo el hardcodeo)
Eliminamos los dos últimos dos tests y recordamos la diferencia entre caso de test y dato de test
Hacemos el ejercicio del calendar de la materia.
Docker
3 horas
El objetivo es dar una introducción básica a los conceptos de Docker, Docker Compose y los entornos de desarrollo usando contenedores.
Empezamos
con una intro:
¿Qué es Docker?
VMs vs Containers
Dockerfile, Imagen y Container
Build por capas
Levantar container con la imagen de
hello-world [https://hub.docker.com/_/hello-world]
. Explicación pasos.
Mapear puertos y montar volúmenes
Comandos (build, run, exec, ps, etc)
Push/pull de imágenes con Dockerhub
Luego, hacemos un ejercicio práctico para mostrar los conceptos anteriores. Creamos una imagen para levantar un servidor FastApi:
Arrancamos con una imagen base (ej: python:3.10). Levantar y correr algo en el container
Agregamos requirements.txt con fastapi[standard] como dependencia. Build y run del container para probar
Agregamos un main.py como muestra el
tutorial [https://fastapi.tiangolo.com/#create-it]
de FastAPI a la imagen
Ver como funciona el mapeo de puertos y montar volúmenes
Etiquetar la imagen y subirla a Dockerhub
Correr un container con la imagen desde Dockerhub desde otra máquina. Introducir un poco la idea de deployear a partir de esto
Docker Compose
Comentar que existe para levantar múltiples containers que hablen entre sí. Mostrar cómo se puede desarrollar sin instalar las dependencias locales (o en un virtualenv) usando el container como intérprete remoto.
Levantar un proyecto en IntelliJ + Docker Compose
Nota:
el documento original de esta capacitación, junto con temas que se excluyeron de este temario se puede encontrar
en este link [https://docs.google.com/document/d/1ezFeSW619NJQkt8Uc1jZCZVw6g2jDbl1XO4Ga8-A7Zk/edit#heading=h.rc3zt91vu2y0]
.