Wiki-eryx - <PERSON>K
Skip to main content
Skip to navigation
Wiki-eryx [Inicio.html]
Grant Aiken ZK
¿De qué va?
Proveer una API a los programadores de smart contracts en Aiken que les permita de forma sencilla:
crear circuitos
crear/verificar pruebas ZK
La idea es facilitar la programación de zk-dApps.
Recursos
Canal Slack Eryx: #p-
grant-aiken-zk
Tablero:
Notion [https://www.notion.so/17b0e408b75180cfb97fcb992274dfcb?v=17b0e408b75180f99237000c687f8363&p=17b0e408b751809b89a5e8da5e0c8761&pm=s&pvs=31]
Documentación:
Drive [https://drive.google.com/drive/folders/1FTDgcPCZoLYMOcEAGRzpLtCE_nJrNhKM]
Código:
Repo [https://github.com/eryxcoop/cardano-zk-aiken]
Diagramas
:
Miro [https://miro.com/app/board/uXjVIB_Ji2Y=/?moveToWidget=3458764625074408005&cot=14]
Material
Página oficial de la grant [https://projectcatalyst.io/funds/13/cardano-open-developers/designing-an-api-for-zk-snark-proof-verification-in-aiken-eryx]
Notas clases de Chou [https://hackmd.io/VDz0WUywTqiHXCMFYk3LOA?both]
Notas investigación Aiken y proving system - milestone 1 [https://hackmd.io/ayV1_ghSQwuS4tuew5J8xA]
Milestones
Milestones de la grant [https://milestones.projectcatalyst.io/projects/1300084/milestones]
1: 1 Febrero - decidir y documentar la elección del proving system
2: 3 Abril - armar una biblioteca básica y un programa de ejemplo sencillo
3: 4 Mayo - integración de la biblioteca y el programa en cardano testnet
4: 5 Junio - desarrollar primitivas más complejas y un ejemplo complejo
5: 6 Julio - Hacer optimizaciones y bugfixes. Armar documentación y tutoriales