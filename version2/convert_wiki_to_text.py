from bs4 import BeautifulSoup
from pathlib import Path

def html_to_text_with_links(html_content: str) -> str:
    soup = BeautifulSoup(html_content, "lxml")

    # Eliminar las etiquetas <nav> y su contenido
    for nav in soup.find_all("nav"):
        nav.decompose()

    # Reemplazar cada <a> con texto + [href]
    for a_tag in soup.find_all("a"):
        text = a_tag.get_text(strip=True)
        href = a_tag.get("href", "").strip()
        a_tag.replace_with(f"{text} [{href}]")

    # Obtener solo el texto plano
    return soup.get_text(separator="\n", strip=True)

def convert_all_htmls_to_txt(base_dir: str):
    for html_path in Path(base_dir).rglob("*.html"):
        # Evitar procesar archivos que ya fueron convertidos a .txt
        if html_path.suffix.lower() != ".html":
            continue

        with open(html_path, "r", encoding="utf-8") as f:
            html_content = f.read()

        text_content = html_to_text_with_links(html_content)

        txt_path = html_path.with_suffix(".txt")
        with open(txt_path, "w", encoding="utf-8") as out_file:
            out_file.write(text_content)

        print(f"✅ Procesado: {html_path} → {txt_path}")

# Usá esta línea con la ruta a tu carpeta raíz que contiene HTMLs
convert_all_htmls_to_txt("wiki")
