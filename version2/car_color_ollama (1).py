import asyncio
import base64
import json
from typing import Any, Dict, List, Optional

import aiohttp


OLLAMA_API_URL = "http://192.168.1.5:11434/api/generate"
OLLAMA_MODEL = "gemma3:27b"
AVAILABLE_COLORS = [
    "blanco",
    "negro",
    "gris",
    "azul",
    "celeste",
    "champagne",
    "bordeaux",
    "rojo",
    "taxi",
    "otro",
    "too_dark",
]


async def _make_ollama_request(
    session: aiohttp.ClientSession, data: Dict[str, Any], prediction_type: str
) -> str:
    """Make a request to the Ollama API and parse the response."""
    try:
        async with session.post(OLLAMA_API_URL, json=data) as response:
            response.raise_for_status()
            json_str = ""
            async for line in response.content:
                try:
                    decoded_line = json.loads(line)
                    if "response" in decoded_line:
                        json_str += decoded_line["response"]
                except json.JSONDecodeError:
                    print(f"Error decoding JSON line: {line}")
                    continue
            try:
                json_response = json.loads(json_str)
                return json_response[prediction_type]
            except json.JSONDecodeError:
                print(f"Error decoding JSON response: {json_str}")
                return f"Error: Invalid JSON response for {prediction_type}"
    except Exception as e:
        print(f"Error during Ollama request: {e}")
        return f"Error: API request failed for {prediction_type}"


async def detect_car_color(
    image_path: str,
    available_colors: Optional[List[str]] = None,
    session: Optional[aiohttp.ClientSession] = None,
) -> str:
    try:
        with open(image_path, "rb") as image_file:
            img_base64 = base64.b64encode(image_file.read()).decode("utf-8")

        # Prepare color prompt and data
        color_prompt = "What is the color of the car in this image? Respond with a JSON object containing only the 'color' tag."
        if available_colors:
            colors_str = ", ".join(available_colors)
            color_prompt += (
                f" Choose from these colors only: {colors_str}. "
                "If the car is yellow and black, use 'taxi'. "
                "If the color is not in the list or the car has more than one color, use 'otro'."
                "If the image was taken at night and you cannot determine the color, use 'too_dark'."
            )

        color_data: Dict[str, Any] = {
            "prompt": color_prompt,
            "images": [img_base64],
            "format": {
                "type": "object",
                "properties": {
                    "color": {
                        "type": "string",
                        "description": "The color of the car in spanish, lowercase",
                    },
                },
                "required": ["color"],
            },
            "model": OLLAMA_MODEL,
        }

        # Use provided session or create a new one
        if session is None:
            async with aiohttp.ClientSession() as session:
                color = await _make_ollama_request(session, color_data, "color")
                return color
        else:
            color = await _make_ollama_request(session, color_data, "color")
            return color
    except FileNotFoundError:
        print(f"Error: Image file not found at {image_path}")
        return "Error: File not found"
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return "Error"


async def main():
    path = "car.jpg"
    car_color = await detect_car_color(path, AVAILABLE_COLORS)
    print(f"Detected color: {car_color}")


if __name__ == "__main__":
    asyncio.run(main())
