wiki/Feedbacks.html
Wiki-eryx - Feedbacks
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Feedbacks
🌀 Feedback entre ñerys
Cada semestre, cada ñery debe solicitar feedback a un grupo de al menos 5 personas (¡sin tope máximo!).
Es recomendable que ese grupo incluya diversidad de vínculos, por ejemplo:
Alguien con quien trabajás cotidianamente.
Alguien con quien no trabajás directamente.
Alguien con quien compartís poco espacio o intercambio.
Esto permite tener una mirada más amplia y enriquecedora sobre tu desempeño y tu forma de estar en Eryx.
🙋‍♀️ ¿Querés dar feedback aunque no te lo hayan pedido?
Si sentís la necesidad o tenés ganas de compartir tu mirada con un compañero o compañera que no te lo solicitó, podés hacerlo de estas formas:
Conversarlo directamente y ofrecer tu devolución en persona.
Pedirle que te comparta su formulario de feedback.
Contactar al área de Talento para canalizarlo.
También podés ingresar directamente al formulario correspondiente y dejar tus aportes para su proceso de desempeño.
Siempre es importante hacerlo con respeto, en línea con las normativas y valores de la cooperativa.
☝️ Si el feedback es constructivo o complejo, recomendamos fuertemente darlo en persona, desde el cuidado y el deseo de construir juntos.
Forms 🗣️
Agus C
Agus G
Agus M
Ale
Bruno
Carlo
Caro L
Caro W
Chino
Dal
Delfi
Doc
Facu
Feli
Flor
Gonza
Juli A
Juli D
Juli S
Laski
Leon
Lucas
Lucho
Manu
Mati
D
Mati L
Nacho
Naza
Roni
Serg
Sherman
Tobi
Tom
¿Cómo debo recibir y dar feedback?
Feedback wiki

wiki/Team eryx.html
Wiki-eryx - Team eryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
El team eryx
Agus C
Comunicación
<EMAIL>
Agus G
Criptografía
a
garassino
@eryx.co
Agus M
Comercial
a
mosteiro
@eryx.co
Ale
Oficina/Admin
a
denardi
<EMAIL>
Bruno
Cripto/Oficina
bweisz
@eryx.co
Carlo
Criptografía
cgferrari
@eryx.co
Caro L
Criptografía
clang
@eryx.co
Caro W
Desarrollo
/
Ente
cwright
@eryx.co
Chino
Criptografía
chino
@eryx.co
Chou
Criptografía
schouhy
@eryx.co
Dal
Talento
dborda
@eryx.co
Delfi
Comunicación
dvaldescastro
@eryx.co
Doc
Desarrollo
jburella
@eryx.co
Erika
Mascota
Eryk
Mascota
Facu
Cripto/Talento
fdecroix
@eryx.co
Feli
Computer Vision
fmarelli
@eryx.co
Flor
Desarrollo
frodriguez
@eryx.co
Gonza
Cripto
glenzi@eryx.
co
Juli A
Criptografía
Juli D
Desarrollo/Talento
Juli S
Admin&Finanzas/CFO
Laski
Comercial Cripto - Finanzas
León
Desarrollo/Ente
Lucas
Labs
Lucho
Desarrollo
Manu
Comercial Cripto
Mati D
Desarrollo
Ma
ti L
Computer Vision
Nacho
Computer Vision
/Talento
Naza
Desarrollo
Roni
Computer Vision/Ente
Sherman
Desarrollo/Ente
Serg
Desarrollo
Tobi
Comercial/Ente
Tom
Cripto

wiki/Pasos a seguir ante una licencia.html
Wiki-eryx - Pasos a seguir ante una licencia
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Licencias
¿Cuáles existen?
Podes conocer todas nuestras licencias haciendo
click aquí
¿Qué debo hacer si me tomo una licencia?
Lo primero que tenes que hacer es informarlo a tus compañeros de equipo.
Es importante que los demás socios sepan que te encontras de licencia, po
r
eso lo mejor es que lo registres en el calendar de google en:
Añadir un evento - más opciones ﻿- días no trabajados y en el título el motivo de tu
Licencia
. Por ejemplo: Día de estudio Agus M
Como anteúltimo paso,
tenes que completar la planilla de
Portal Socios, Hoja Carga Dedicación/Licencias
indicando la cantidad de días de licencia
.
Y para finalizar, te recomendamos informarlo en el canal de #a-talento de slack.
¡Y listo!
Muchas gracias por leer.
Cualquier duda podes acercarte al grupo de Talento.

wiki/Proyectos.html
Wiki-eryx - Proyectos
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Criptoproyectos

wiki/Programa de Buddies.html
Wiki-eryx - Programa de Buddies
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Programa de Buddies
Buddies Final (1).mp4

wiki/Pedidos a Oficina.html
Wiki-eryx - Pedidos a Oficina
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Pedidos a Oficina
¿Cómo puedo hacer un pedido a Oficina?
Oficina
tiene un canal en Slack llamado
tickets-oficina
con la finalidad de juntar todos los pedidos de los ñerys hacia el área en un solo lugar permitiendo además tener una alta visibilidad del progreso de los mismos.
¿Qué puedo cargar como ticket?
Los pedidos pueden ir desde cosas que faltan, cosas que se rompieron o bien cosas que les gustaría con respecto a la oficina (en este último caso, tener en cuenta que el pedido debe tener una
definition of done
).

wiki/Equipos BET.html
Wiki-eryx - Equipos BET
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Equipos BET
Video BET final (1).mp4

wiki/Prepaga Plan OSDE 210.html
Wiki-eryx - Prepaga: Plan OSDE 210
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Obra social: OSDE 210
Cada socio de Eryx cuenta con la cobertura de salud de Osde: Plan 210.
Descarganda la app “Osde Movil” podrás visualizar:
Cartilla Médica
Gestiones On-Line
Telefonos
Centros de Atencion Personalizada
Atención Médica:
Para tu atención médica deberás contar con tu credencial virtual, que podrás obtenerla descargando la app “Credencial Digital”.
Si necesitas saber tu número de socio, o cualquier otra info adicional de OSDE,
ingresa acá.

wiki/Comida del medioda.html
Wiki-eryx - Comida del mediodía
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Comida del mediodía
Almuerzo a cargo de Eryx de lun a vier en:
Pranzo - Responsable: Dal
Local de comida al peso.
Dirección:
Fitz Roy 1951.
Informar en el local que vas de parte de Eryx y completar la planilla.
Vino Tinto - Responsable: Naza
Local de comida
Dirección:
Humboldt 2157.
Eryx cubre plato del dia, plato del día veggie y ensaladas (no cubre sopa ni bebida). Para ver cuál es el plato del día podés fijarte en el
instagram
.
Se recomienda llamar por teléfono e ir a buscarlo para no tener que esperar.
Teléfono: (+5411) 2135-3643
Informar en el local que vas de parte de Eryx y completar la planilla.
Bonatural - Responsable Dal (COMPLETAR)
Local de comida al peso.
Dirección:
Bonpland 1577
.
Informar en el local que vas de parte de Eryx y completar la planilla.
¿Qué significa ser responsable?
Significa ser quién coordine con el proveedor el pago y la solicitud de la factura.
☝️Tener en cuenta que el pago se puede realizar por transferencia cada una semana o 15 días mediante el
form de transferencias
, y el proveedor debe poder emitirnos
factura o ticket A o C
a
nuestro nombre
que luego deberá ser
subida al form de compras
.
¿Y si quiero sumar un nuevo lugar para comer?
En el caso que quieras sumar un nuevo lugar para acceder en los almuerzos, te pedimos por favor que te acerques a Talento.

wiki/Desarrollo.html
Wiki-eryx - Desarrollo
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Rol DEV

wiki/Complemento de ADE por hijo.html
Wiki-eryx - Complemento de ADE por hijo
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Complemento de ADE por hijo
Propuesta Loomio: compensación por hijo

wiki/Internet.html
Wiki-eryx - Internet
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Internet
Wifi (red | pass):
Eryx | eryxcoop1234
EryxWifi | bonpland1953

wiki/Slack.html
Wiki-eryx - Slack
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Slack
Slack es la herramienta que utilizamos para nuestra comunicación en el día a día.
La herramienta permite crear distintos canales, cada uno para hablar de distintos temas. También permite tener chats individuales.
Los canales suelen cambiar frecuentemente, pero tenemos distintas categorías:
a-
nombre-de-area
:
Se usa para hablar sobre temas que tengan que ver con el área, su principal uso es como canal de consultas por parte de miembros que no pertenecen al área.
com-
nombre_de_comision:
cada comisión genera un canal para comunicarse entre los participantes y los interesados en el tema.
p-
nombre-de-proyecto:
se generan para poder hablar sobre temas referidos al proyecto.
ot-
algun-tema:
se generan para poder hablar sobre temas que no están referidos directamente con la coope o el trabajo, pero son de interés común entre la mayoría de los socios.
Intentamos limitar el uso del
@channel
para no molestar al resto con notificaciones. Se usa únicamente cuando necesitamos de la respuesta de todos rápidamente.
Igualmente, disponemos de dos canales para informar
(anuncios-info)
o solicitar al socio realizar ciertas tareas
(anuncios-tareas)
.

wiki/Inicio.html
Wiki-eryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
[¡Hola!]
Este es tu punto de entrada a Eryx.
¡Acá vas a encontrar respuestas rápidas a tus dudas!
INFO IMPORTANTE
Dirección:
Bonpland 1953, entre Costa Rica y Nicaragua, en Palermo.
Primeros auxilios:
107
Policía:
911
OSDE Urgencias:
0810-888-7788 / 0810-999-6300

wiki/eryx(1).html
Wiki-eryx - Ñeryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Ñeryx

wiki/Admin.html
Wiki-eryx - Admin
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Admin

wiki/Actualizacin de info.html
Wiki-eryx - Actualización de info
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Actualización de información personal
La importancia de mantener actualizada la información de los socios en la cooperativa
Mantener la información de los socios actualizada es fundamental para el buen funcionamiento de la cooperativa. Esta información no solo es útil para procesos legales y contables, sino que también es crucial para poder actuar con rapidez y eficacia en caso de una
emergencia
.
☝️¿Qué datos de los socios es importante actualizar?
Estado civil
Domicilio actual
Números de teléfono
Teléfono de contacto de emergencia (familiar o amigo cercano)
¡Y cualquier otra info que creas relevante!
👉¿Cómo actualizar tu información?
Ingresá al siguiente form.

wiki/Eryx.html
Wiki-eryx - Eryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Eryx

wiki/Qu son las reas Qu hacen.html
Wiki-eryx - ¿Qué son las áreas? ¿Qué hacen?
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Áreas: qué son y qué hacen
A medida que fuimos creciendo las tareas fueron aumentando y
tomar todas las decisiones de forma centralizada entre todos comenzó a dificultarse
.
Las áreas surgieron para poder designar mejor las responsabilidades de las distintas tareas en de gestión de Eryx y descentralizar la toma de decisiones
Cada área tiene una función y es responsable de atacar distintos problemas.
Academia
Es responsable de capacitar a los socios y elevar la calidad de nuestras soluciones.
Se enfocan en fomentar que la capacitación profesional, en algunos casos ayudando a gente con iniciativas propias, consiguiendo capacitaciones externas o brindándolas ellos mismos.
A
dmin
Responsabilidades
Contabilidad y Legal:
Seguimiento contable y legal de la cooperativa y Aurora.
Gestión de poderes, trámites impositivos y societarios.
Elaboración de actas, memorias y estados contables.
Conciliación de saldos bancarios y control de movimientos.
Vinculación con profesionales externos de asesoría contable y legal.
Vinculación con profesionales externos para la gestión de contratos.
Administración y Finanzas:
Gestión del portal de socios (PAD, descuentos, retiros en USD, etc.).
Control de gastos personales en el exterior y seguimiento de monotributos.
Apertura de cuentas, solicitud de tarjetas y límites de crédito.
Elaboración de declaraciones juradas (ingresos brutos, IVA).
Control de compras, pagos a proveedores y retenciones.
Resguardo de liquidez (FCI, plazo fijo, etc.).
Análisis económico-financiero, generación de reportes y estimaciones.
Capacitación a socios y al equipo del área.
Gestión de cuentas bancarias (reclamos, documentación, límites de tarjetas) de los socios y de la cooperativa.
Facturación Eryx-Aurora, seguimiento y reclamo de cobros.
Liquidación de exportaciones.
Asistencia a comercial en la cobranza a clientes.
Participación en reuniones y espacios que requieran atención.
Mantenimiento de vínculos con otras cooperativas.
Comercial
Responsabilidades
Gestión de Leads:
Atención al cliente: Responder correos electrónicos, coordinar reuniones, realizar presentaciones.
Análisis e investigación: Recopilar y curar información para las reuniones de desarrollo.
Gestión de proyectos: Proponer equipos de trabajo, elaborar presupuestos y cerrar acuerdos.
Desarrollo de Relaciones:
Construcción de vínculos: Fomentar relaciones duraderas con clientes y partners.
Negociación: Gestionar acuerdos sobre rates, fechas, metodología y condiciones de trabajo.
Participación en Eventos:
Búsqueda y selección: Identificar y asistir a eventos relevantes para el sector.
Representación de la empresa: Promover la marca y generar oportunidades de negocio.
Análisis y Estrategia:
Salud económica: Monitorear indicadores clave y brindar información a los espacios estratégicos.
Desarrollo de estrategias comerciales: Proponer nuevas estrategias para el crecimiento del negocio.
Capacitación y Mejora Continua:
Actualización de la propuesta de valor: Refinar la propuesta de valor del área para adaptarla a las necesidades del mercado.
Mejora del discurso: Desarrollar un discurso comercial efectivo para la captación de clientes.
Optimización del material gráfico: Diseñar y actualizar materiales de marketing para la promoción del área.
Estudio de verticales: Profundizar en el conocimiento de las tecnologías y rubros relevantes para el negocio.
Comunicación
Responsabilidades
Pedidos de áreas:
Atender, gestionar y priorizar pedidos.
Brindar soluciones eficientes y oportunas.
Realizar seguimiento hasta su cierre.
Asistencia en charlas y presentaciones externas:
Colaborar en la preparación y brindar apoyo técnico y logístico.
Representar a la empresa en eventos y conferencias.
Comunicación interna:
Elaborar y enviar comunicaciones internas.
Organizar y participar en reuniones interdepartamentales.
Mantener informados a los
asociados.
Promover la comunicación interna fluida y efectiva.
Visibilidad:
Redes sociales: Crear y gestionar contenido, desarrollar estrategias, analizar métricas y optimizarlas.
Eventos: Organizar eventos propios, asistir a eventos relevantes, gestionar sponsors, dar charlas, participar en meetups, desayunos y stands.
Medios de comunicación: Redactar notas de prensa y gestionar la relación con los medios.
Merchandising: Diseñar y gestionar la producción de merchandising.
Web: Mantener actualizada la web y desarrollar contenido atractivo.
Portfolio: Mantener actualizado el portfolio.
Tarjetas y banners: Diseñar y gestionar la producción de tarjetas y banners.
Otras responsabilidades:
Mantenerse actualizado sobre las últimas tendencias en marketing y comunicación.
Investigar y analizar nuevos mercados y oportunidades de negocio.
Desarrollar estrategias para aumentar la visibilidad de la empresa.
Colaborar con otras áreas para lograr los objetivos comunes.
Oficina
Es el área
responsable de garantizar el correcto funcionamiento de la oficina con el objetivo de que las diferentes áreas de cooperativa funcionen de manera  eficiente.
Responsabilidades
Compras:
Gestionar la compra de insumos (alimentos y art de librería).
Realizar pedidos y seguimiento de entregas.
Controlar stock y presupuestos.
Contactos y gestión:
Mantener comunicación con el dueño oficina para coordinar tareas y necesidades.
Administrar proveedores que correponsan al funcionamiento habitual de la oficina (Alarma de seguridad, Internet y telefonía, Limpieza, etc).
Mantenimiento de la oficina:
Atender las necesidades de mantenimiento físico de la oficina y sus espacios (jardín, salas, etc) incluyendo reparaciones de picaportes, cambio de filtros, limpieza del techo y cambio de artefactos.
Garantizar el correcto funcionamiento de internet y elementos de los puestos de trabajo.
Gestionar los servicios de fumigación.
Infraestructura:
Gestionar la compra de computadoras e insumos informáticos.
Coordinar reparaciones y arreglos en la infraestructura.
Capacitaciones:
Organizar y gestionar la capacitación en RCP.
Coordinar el curso de Seguridad e Higiene, incluyendo simulacros y uso de extintores.
Responsabilidades adicionales:
Cumplir con las normas de seguridad e higiene en el trabajo.
Talento
Es
el responsable del recorrido del ñery desde antes de su ingreso y en toda su trayectoria dentro de Eryx. Nuestro objetivo es el acompañamiento de los socies en su desarrollo profesional y promover la cultura Eryxiana.
Responsabilidades
Administración de Asociados:
Liquidación de retiros: preparación de retiros para su pago. Emisión de recibos de retiros de INAES.
Seguimiento de licencias.
Cumplimiento normativo de IN
AES
: asegurar el cumplimiento de las normativas vigentes. Alta y seguimiento de prepaga, seguro de vida y escritura de acta de ingreso.
Salud y seguridad: fomentar la seguridad (talleres de RCP, seguridad e higiene). Implementar medidas para el cuidado de la salud de los asociados (chequeo anual).
Gestión del Talento:
Reclutamiento y selección: buscar y seleccionar a los mejores candidatos para las vacantes disponibles, priorizando los principios cooperativos.
Onboarding: integrar a los nuevos asociados a la Cooperativa, familiarizándolos con la cultura, la misión, los valores y los principios del cooperativismo.
Formación y desarrollo: velar por la capacitación de los asociados para que puedan desarrollar sus habilidades y conocimientos, promoviendo el aprendizaje continuo y la autogestión.
Evaluación del desempeño: implementar un proceso de evaluación del desempeño participativo y transparente, que fomente la retroalimentación constructiva y el desarrollo individual.
Planificación de la carrera profesional: ayudar a los asociados a planificar su desarrollo profesional dentro de la Cooperativa, considerando sus intereses y aptitudes, y las necesidades de la organización.
Bienestar Grupal:
Comunicación: fomentar una comunicación abierta, transparente y fluida entre todos los asociados.
Resolución de conflictos: implementar mecanismos de resolución de conflictos pacíficos, basados en el diálogo y la búsqueda de soluciones consensuadas.
Convivencia: definir, comunicar y velar por el cumplimiento de las reglas de convivencia y protocolo, promoviendo un ambiente de respeto mutuo y colaboración.
Eventos: organizar eventos que fomenten la integración, el compañerismo y el sentido de pertenencia a la cooperativa.
Beneficios: promover actividades beneficiosas para los asociados, como programas de salud, bienestar y desarrollo personal.

wiki/Eryx Recomienda.html
Wiki-eryx - Eryx Recomienda
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Eryx recomienda
Eryx recomienda
En esta página podés encontrar recomendaciones de libros, series, discos y peliculas de tus ñerys. También podes agregar las tuyas commiteando en
este repo

wiki/FAQ cripto.html
Wiki-eryx - FAQ cripto
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
FAQ criptoraccoons
Qué es una grant?
Qué es Cardano?
Qué es una prueba ZK?
Qué es Aiken?
Qué es un smart contract?

wiki/Comunicacin.html
Wiki-eryx - Comunicación
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Comunicación

wiki/Loomio.html
Wiki-eryx - Loomio
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Loomio
Loomio es una aplicación web de software libre para la toma de decisiones, que nos ayuda como grupo en los procesos(ver apartado "toma de decisiones").
Utilizamos Loomio para tomar las decisiones más importantes de la empresa y que no pueden resolverse usando un mecanismo más simple como un termómetro.
Cuando un socio de Eryx necesita resolver algo que puede afectar a los demás socios en su día a día o que requieran un gasto de dinero considerable (en general usamos el retiro promedio de un socio de Eryx como regla de pulgar).
En estos casos, se crea una propuesta en la plataforma y se invita al resto a votar.
Como sistema de votación, Loomio implementa los gestos de manos de Occupy mediante el cual un socio puede expresar si está de acuerdo, en desacuerdo, abstenerse y si es necesario bloquear la propuesta.
Además cada persona puede dejar sus dudas o comentarios para que los demás tomen conocimiento y, en caso de ser necesario, dar respuesta.
Cada propuesta tiene un tiempo de tratamiento, definido por el socio que implementó la misma. Pasado ese plazo puede extenderse o directamente darla por cerrada.

wiki/Bonos mensuales.html
Wiki-eryx - Bonos mensuales
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Bonos mensuales
🏅
En Eryx contamos con
dos bonos mensuales
que buscan acompañar el bienestar y la vida cotidiana de cada socio y socia. Ambos se actualizan
trimestralmente según inflación
y se brindan de forma
mensual a través de la tarjeta precargada
.
🧘‍♀️ Eryx Ejercita
👉
Porque cuidarnos también es parte del trabajo
Este bono promueve el bienestar físico y emocional, entendiendo que la salud es clave para sostener nuestro compromiso colectivo. Podés usarlo para:
Realizar actividad física (gimnasios, deportes, entrenamientos, etc.).
Pagar clases o talleres vinculados al movimiento y la salud.
Comprar elementos para ejercitarte en casa.
Cubrir sesiones con psicólogo/a como parte del cuidado mental.
🚋 Eryx Transporte
👉
Porque el acceso al espacio común también es una inversión compartida
Este bono busca facilitar la presencia en la oficina, entendiendo que compartir espacios presenciales fortalece el vínculo, el trabajo en equipo y la construcción colectiva. Cubre todo gasto relacionado con ir o volver de nuestra bella oficina, como:
Transporte público.
Combustible.
Servicios de movilidad (como taxis o apps).

wiki/Alarma.html
Wiki-eryx - Alarma
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team
                                                            eryx
Historia
                                                                Eryx
Cumpleaños
                                                                            de Eryx
¿Qué
                                                            servicios ofrecemos?
Visión,
                                                            Metas y Valores
Documentación
                                                            Coope
Cooperativismo
¿Qué
                                                                            son las comisiones? ¿Qué hacen?
Toma
                                                                            de decisiones
Nuestros
                                                                            rituales y asambleas
¿Qué son
                                                            las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento
                                                            interno
Derechos y
                                                            obligaciones
¿Qué
                                                            licencias tenemos?
Retiro
Complemento
                                                                            de ADE por hijo
Tarjetas
                                                                            precargadas
Beneficios
Bonos
                                                                            mensuales
Comida
                                                                            del mediodía
Cabify
Prepaga:
                                                                            Plan OSDE 210
Beneficios
                                                                            autogestivos
Programa de
                                                                Desarrollo Profesional
eMe
Pool
                                                                            de Mentores
Asignaciones
                                                                            eMe
Feedbacks
Guía
                                                                            Mentoring
Tablero
                                                                            de Seguimiento
Política
                                                            de compra/recambio de laptops
Cartilla
                                                            Eryx
Eryx
                                                            Recomienda
Herramientas
Servicios
                                                            Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso
                                                            Oficina
Cerrar
                                                            Oficina
Impresora
Inventario
Protocolo
                                                            de incendio
Biblioteca
                                                            de libros
Procesos
Bajo
                                                            desempeño
Compras
Compras
                                                            personales en el exterior - TC Eryx
Pasos a
                                                            seguir ante una licencia
Pedidos a
                                                            Oficina
Chequeo
                                                            médico anual
Facturación
Actualización
                                                            de info
Capacitación
                                                            de ingresante a dev
Contactos
Cartuchera
La
                                                            Cartuchera del Ñery
Criptografía
CryptoHacks
Template
                                                            de proyecto
Diseño a
                                                            la gorra
Proyectos
Grant
                                                            Aiken ZK
muun
FAQ
                                                            cripto
Wiki-eryx
Alarma
Para operar la alarma se puede utilizar la app ADT Interactive. Está disponible para
Android
y
iOS
.
Usuario: 89_747B
Password: 31Mayo2016!
Armado / Desarmado desde la app
Una vez logueado en la app, se debe hacer click en "SISTEMA DE SEGURIDAD" y elegir la opción "Desarmar" o "Salir" (para armar).
Botón de pánico
La app cuenta con un botón de pánico.Para activarlo se debe deplegar la pestaña "Pánico" en la pantalla principal.En ella se podrá identificar cuatro iconos.
Tres de emergencia audibles
: Asistencia Policial,Asistencia en caso de incendios y asistencia médica. Y uno de
emergencia silenciosa
:
Asistencia Policial
. Al pulsarla se activara una alerta a la central de monitoreo y se tendrá 2 minutos para confirmar o cancelar la alerta.

wiki/Cumpleaos de Eryx.html
Wiki-eryx - Cumpleaños de Eryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Cumpleaños de Eryx
18 de Abril de 2011

wiki/Nuestros rituales y asambleas.html
Wiki-eryx - Nuestros rituales y asambleas
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Nuestros rituales y asambleas
En Eryx tenemos distintas instancias para compartir entre todos el estado de la cooperativa. A continuación las detallamos.
La semana en un minuto
Es una instancia relajada en la que
cada equipo de trabajo (proyectos, áreas y comisiones) cuenta en un minuto las novedades que de la semana.
Esta reunión nos brinda la posibilidad de estar al tanto del día a día de la empresa de forma rápida.
Para saber más de esta asamblea podés leer
el siguiente artículo
.
Tienen lugar los viernes a las 14hs (después de almorzar) y duran aproximadamente media hora. Tenemos
una
presentación
que cada proyecto/área debe completar antes de la reunión. La reunión cuenta con un moderador designado, cuando el moderador quiere dejar de hacerlo puede delegar su responsabilidad a otro socio.
Asambleas mensuales
Las asambleas mensuales tienen como objetivo
dar un resumen del estado general de la empresa.
También hay un espacio para poder discutir ciertos temas que involucran a toda la cooperativa.
Se realiza los segundos mi
é
rcoles de cada mes de 9:30hs a 13hs
y e
s moderad
a
por la gente del área de Direcci
ón
& Autoges
tión
.
La reunión está dividida en etapas:
1 -
Check in (30 min):
Los socios se dividen en grupos de 3 o 4 personas y charlan sobre el tópico propuesto. Se busca que sean temas descontracturados para que la charla sea relajada y amena.
2 -
Reporte económico (15 min):
En esta etapa se muestra el estado económico de la empresa. Se explican los ingresos y los egresos que tuvo la cooperativa correspondiente al mes y una estimación de los siguientes meses. También se muestra la liquidez de la empresa y los gastos de cada área y su presupuesto.
3 -
BUs (15 min):
Continuamos con un panorama general de las distintas unidades de negocio de Eryx. Se busca dar una visión de alto nivel de la unidad, sin entrar en detalles. Las unidades que presentan son Desarrollo, DSOR y Labs.
4 -
Repaso:
Hacemos un repaso rápido de nuestra misión y valores. También de los distintos espacios donde tomamos decisiones. Esto se hace para que todos tengan presente esta información.
5 -
Metas (60 min):
Repasamos las tres metas: Mejora iterativa de proyectos (MIP), Aspiraciones económicas (AE) y Vinculación
. Cada una muestra sus avances y su estado actual. Este espacio funciona también para que los socios puedan hacer preguntas a los representantes de cada meta. Al final se hace una votación para reflejar la conformidad de los socios y socias con las metas.
7 -
Charlemos de (aprox 1 hora):
Es el momento para charlar sobre cualquier tema de la cooperativa que el socio requiera. Antes del mes se pueden
cargar charlas
y las más votadas son las que se hablan. Pueden ser en modalidad debate o exposición. Los "charlemos de..." también tienen otra instancia por fuera de la mensual, se realiza el tercer viernes de cada mes a las 14:30hs.

wiki/Cartilla Eryx.html
Wiki-eryx - Cartilla Eryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Cartilla Eryx
WIP: Cartilla Eryx

wiki/Gua Mentoring.html
Wiki-eryx - Guía Mentoring
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Guía Mentoring
Guia-Mentoring-Eryx.pdf

wiki/Grant Aiken ZK.html
Wiki-eryx - Grant Aiken ZK
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Grant Aiken ZK
¿De qué va?
Proveer una API a los programadores de smart contracts en Aiken que les permita de forma sencilla:
crear circuitos
crear/verificar pruebas ZK
La idea es facilitar la programación de zk-dApps.
Recursos
Canal Slack Eryx: #p-
grant-aiken-zk
Tablero:
Notion
Documentación:
Drive
Código:
Repo
Diagramas
:
Miro
Material
Página oficial de la grant
Notas clases de Chou
Notas investigación Aiken y proving system - milestone 1
Milestones
Milestones de la grant
1: 1 Febrero - decidir y documentar la elección del proving system
2: 3 Abril - armar una biblioteca básica y un programa de ejemplo sencillo
3: 4 Mayo - integración de la biblioteca y el programa en cardano testnet
4: 5 Junio - desarrollar primitivas más complejas y un ejemplo complejo
5: 6 Julio - Hacer optimizaciones y bugfixes. Armar documentación y tutoriales

wiki/Toma de decisiones.html
Wiki-eryx - Toma de decisiones
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Toma de decisiones
En los inicios de Eryx, al ser pocas personas, la toma de decisiones no representaba un problema. Simplemente se charlaba el tema y decidíamos en el momento entre todos.
A medida que la cooperativa se fue agrandado, también crecieron las decisiones y la cantidad de personas que debían participar, por lo que se volvió más difícil acordar rápidamente.
Ante este problema optamos por un mecanismo llamado
advice process
,
que se basa en delegar la responsabilidad a los socios. Las decisiones se dividen en tres categorías, cada una conlleva un mecanismo distinto. Cuanto más alto es el impacto de la decisión, más gente debe ser incluida en el proceso.
Decisiones de bajo impacto:
Tienen un costo económico bajo, que no afectan a los socios en su día a día y son fácilmente reversibles. Pueden ser tomadas directamente por el socio, aunque se sugiere consultar con algunas personas antes para tener otras miradas sobre el tema. En caso de que se considere necesario puede hacer un sondeo en el canal #termometro de Slack.
Decisiones de impacto grupal / alto impacto:
Impactan en la vida diaria del grupo, en la estructura, las costumbres y/o conllevan un consumo de recursos no menor (tiempo y/o dinero). También puede ser difícil de revertir. Para este tipo de decisiones se debe presentar una propuesta en
Loomio
. Se sugiere dar un plazo mínimo de 7 días de votación a la propuesta. Es importante destacar que en este caso el socio también es responsable de que la decisión se lleve a cabo. El criterio de aprobación de la propuesta es
estándar
, significa que no tiene que haber bloqueos. Si hay más NO que SI, se sugiere iterar. No importa el grado de participación (aunque si es bajo, debe ser considerado).
Decisiones fundacionales/cambios en el manifiesto (máximo impacto):
Estas decisiones deben pasar obligatoriamente por Loomio. El criterio de consenso es
quorum,
lo que significa que
no tiene que haber bloqueos. Se requiere el 100% de participación, y al menos el 75% de los votos (excluyendo las abstenciones) sean positivos. La decisión fundacional más frecuente en la coope es la de incoporar un nuevo socie.
Para tener más información de nuestra toma de decisiones, se recomienda leer
nuestro manifiesto

wiki/Programa de Desarrollo Profesional.html
Wiki-eryx - Programa de Desarrollo Profesional
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Programa de Desarrollo Profesional

wiki/Poltica de comprarecambio de laptops.html
Wiki-eryx - Política de compra/recambio de laptops
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Política de compra/recambio de laptops
Cambio de PCs

wiki/Visin Metas y Valores.html
Wiki-eryx - Visión, Metas y Valores
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Visión, metas y valores
Visión
Es una frase que resume nuestros objetivos a largo plazo
“Impactar positivamente en la sociedad creando soluciones tecnológicas de alta calidad.”
Valores de Eryx
¿Cuáles son?
Ser inquietos:
Proactividad, Autosuperación, Mejora continua, Curiosidad, Valentía, Vanguardia, Pasión Innovación, Investigación, Academia.
Ser ágiles:
Simplicidad, Agilidad, Pragmatismo, Tomar decisiones.
Ser profesionales:
Racionalidad, Responsabilidad, Argumentos sólidos, Comunicación.
Ser humanos:
Honestidad, Transparencia, Confianza, Respeto, Justicia, Amistad, Compromiso social, Autogestión, Felicidad, Horizontalidad, Unión, Cooperativismo.

wiki/Servicios Digitales.html
Wiki-eryx - Servicios Digitales
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Servicios digitales
Todas las credenciales están en Bitwarden
Email:
bitwarden
@eryxsoluciones.com.ar
P
assword:
c
onsultar
a u
n socie en la ofi
D
isponibles:
Bitwarden
(
https://bitwarden.com/
)
Diseño a la gorra
(
https://academia.10pines.com/disenio_a_la_gorra
)
GPT 4
(
https://chatgpt.com/
)
Masterclass
(
https://www.masterclass.com/
)
Medium
(
https://medium.com/
)
MIT Sloan Management Review
(
https://sloanreview.mit.edu/
)
MIT
Technology review
(
https://www.technologyreview.com/
)

wiki/eMe.html
Wiki-eryx - eMe
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
eMe(eryxMentoring)
eMe
(eryxMentoring) es nuestro programa de desarrollo profesional. Este busca ofrecer una revisión del desempeño que sea cercana y objetiva, con el desarrollo profesional guiado por mentorías como eje central del aprendizaje.
Cada
ñery
tendrá un equipo conformado por un mentor, un representante de equipos y un extra. Estos son los roles y responsabilidades de cada uno:
Responsabilidades de los integrantes de un eM
e
Socio:
Asegurarse de que las reuniones eMe sucedan y se complete la documentación correspondiente al proceso.
Enfocarse en sus objetivos profesionales, recordarlos y tener seguimiento de los accionables.
Buscar formas de destrabar, desde su experiencia o acercándose a alguien que pueda ayudarlo.
Tiene como objetivo buscar un mentor y cumplir con las reuniones mensuales a lo largo de todo el proceso de desarrollo semestral.
Asistir a las capacitaciones sobre el proceso
Mentor:
Cumplir con las reuniones con su mentoreado a lo largo de todo el proceso de desarrollo semestral.
Hacer mentoring sobre lo que cada socio prefiera, ya sea desde
su
rol de socio como su
rol
técnico. Se recomienda aprovechar los objetivos planteados por eMe.
Asegurarse de la periodicidad y calidad de las revisiones de desempeño.
Mentor + Equipos + Extra:
Ayudar a definir los objetivos teniendo en cuenta el deseo del socio, sus roles
,
sus fortalezas y debilidades.
Aconsejar al socio en todo lo que puedan, desde su experiencia o acercándose a alguien que pueda ayudar a destrabar su situación. Participar de la revisión de desempeño del semestre.
Representantes de equipos:
Traer el feedback de su equipo sobre la mesa y llevar al equipo necesidades que se planteen desde el eMe.
¿Cuál es el Rol de Talento?
Talento tendrá un rol de apoyo y supervisión en los eMe. Sus funciones serán:
Proveer herramientas para la continuidad del proceso de desarrollo.
Ser la persona de consulta sobre una mirada más global en lo que respecta a las herramientas del esquema.
Definir los responsables de equipos y extras de los eMe para cada socio.
Asegurarse de la periodicidad y calidad de las revisiones de desempeño supliendo las potenciales fallas del socio y el mentor.
🌀
Rituales - Línea de tiempo recomendada
El programa eMe se estructura en base a rituales periódicos que permiten acompañar el desarrollo de cada persona en la cooperativa. A continuación, se detalla el circuito sugerido:
1. 🗣️
Solicitar feedback
(inicio del semestre)
Cada ñery debe pedir feedback, com mínimo, a 5 personas.
Es importante incluir tanto socios con las que trabajó directamente como otras con las que no, para tener una mirada amplia y variada.
2. 👥 Elección del mentor o mentora
Cada ñery debe elegir una persona de la lista de
mentores/as disponibles
para acompañar su proceso durante el semestre.
Esta elección da inicio formal al trabajo dentro del programa eMe.
3. 🤝 Reunión con el equipo eMe
Una vez recopilado el feedback y definido el mentor, se coordina una reunión con el eMe.
En ese espacio se analiza en conjunto el feedback y se definen los objetivos del semestre.
Para registrar lo trabajado, se debe completar el formulario de objetivos.
4. 🔄 Reuniones de mentoría (durante el semestre)
Obligatorias: mínimo 1 hora mensual.
Recomendación: hacerlo cada dos semanas en encuentros de media hora.
Sirven para revisar el avance, ajustar objetivos y sostener un espacio de acompañamiento continuo.
5. 📌 Checkpoints eMe (a demanda)
Se pueden pedir en cualquier momento si surgen dudas, necesidades o situaciones particulares que requieran apoyo.
6. 📋 Revisión de desempeño (fin del semestre)
Se completa el formulario de desempeño con el objetivo de analizar el cumplimiento de metas, entender qué ayudó o dificultó el proceso, y rescatar aprendizajes para lo que viene.
⏳ Frecuencia sugerida para los ciclos: cada 6 meses (no debe superar los 8 meses).
💡
Tips útiles
Agendate un recordatorio mensual para revisar en qué parte del proceso estás y qué te falta.
Chequear que tu eMe y los forms estén actualizados cada semestre.
Aprovechá el espacio con tu mentor/a para conversar no solo de tareas, sino también de tu desarrollo, motivaciones, límites y deseos.
Pedí ayuda si te trabás en alguna parte del proceso: el eMe está para acompañarte, no para evaluarte.
Tené presente tu línea de tiempo personal, no hace falta que todas las personas avancen igual.
Si te sentís trabado/a, recomendamos leer el proceso que se utilizó para los planes de carrera, puede aportar claridad y ayudarte a encontrar tu próximo paso.

wiki/Facturacin.html
Wiki-eryx - Facturación
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Facturación
En este apartado te vamos a contar algunas cosas a tener en cuenta para que ADMIN pueda emitir una factura.
Antes de emitir una factura debemos identificar si la misma se va a emitir a un
cliente local
o a un
cliente del exterior
.
IMPORTANTE: muchas veces el proyecto es de un pais, pero la factura hay que emitirla a una empresa de otro país.
En caso de que el
cliente sea local
(es decir de Argentina) debemos pedir los siguientes datos:
- CUIT
- RAZON SOCIAL (es el nombre de la empresa)
- DOMICILIO COMPLETO
Si el
cliente es del exterior
debemos pedir los siguientes datos:
- NOMBRE DE LA EMPRESA
- DOMICILIO COMPLETO
- PAIS
En este apartado te vamos a contar el proceso de cobro de acuerdo a la factura emitida:
Si el
cliente es local
, ADMIN emite una factura tipo "A" (recuerden que el monto facturado siempre contiene el 21% de iva), y se solicita transferencia a nuestra cuenta bancaria del Credicoop:
Cuenta Corriente $ 191-301-001098/2
CBU 19103017 - 55030100109828
CUIT 30715937251 - COOPERATIVA DE TRABAJO ERYX LIMITADA
Si el cliente es del exterior tenemos 2 formas de cobros habilitadas:
1) Por medio del MULC (Mercado Unico y Libre de Cambios): Para este caso ADMIN emite una factura tipo "E" (exportación) en USD, el cliente gira dichos USD a nuestra cuenta del Credicoop, y el banco convierte automaticamente esos USD a pesos al tipo de cambio divisa oficial. En caso de optar por esta vía, solicitar a admin los datos bancarios para que el cliente haga la transferencia.
2) Por medio de plataforma alternativa: Esto implica utilizar LetsBit y obtener un tipo de cambio diferencial. En este caso primero ADMIN emite una invoice (es un "factura proforma" no valida para afip) con el valor del servicio en USD. Al cliente se le envía dicha invoice mas los datos de cuenta del exterior del operador que deseemos utilizar (Letsbit). Una vez que el cliente gira los USD, se liquidan a pesos a un tipo de cambio diferencial. Y por ultimo, ya teniendo el valor final en pesos acreditado ADMIN emite factura tipo "E" (exportación) en PESOS ARS coincidentes con la acreditación recibida.

wiki/Herramientas.html
Wiki-eryx - Herramientas
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Herramientas

wiki/Mostro.html
Wiki-eryx - Mostro
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Especificaciones:
::::.    ':::::     ::::'             mostro
':::::    ':::::.  ::::'            OS: NixOS 24.05.5287.759537f06e69 (Uakari)
:::::     '::::.:::::             Kernel: x86_64 Linux 6.6.52
.......:::::..... ::::::::              Uptime: 5d 30m
::::::::::::::::::. ::::::    ::::.      Packages: 2685
::::::::::::::::::::: :::::.  .::::'      Shell: bash 5.2.32
.....           ::::' :::::'       Disk: 145G / 919G (17%)
:::::            '::' :::::'        CPU: Intel Core i9-14900K @ 32x 5.7GHz [37.0°C]
........:::::               ' :::::::::::.   GPU: NVIDIA GeForce RTX 4090
:::::::::::::                 :::::::::::::   RAM: 3831MiB / 47940MiB
::::::::::: ..              :::::
.::::: .:::            :::::
.:::::  :::::          '''''    .....
:::::   ':::::.  ......:::::::::::::'
:::     ::::::. ':::::::::::::::::'
.:::::::: '::::::::::
.::::''::::.     '::::.
.::::'   ::::.     '::::.
.::::      ::::      '::::.
Grupo de slack:
#mostro
El grupo sirve para:
pedir users
pedir ayuda con algo
tirar ideas
pedir un lock exclusivo de la GPU
etc
Faq:
¿Cómo puedo saber cuáles son los
usuarios conectados
?
S
e pueden usar los siguiente comandos:
who
lista de usuarios actualmente conectados al sistema
w
igual a who, pero con información más detallada, como cuánto tiempo llevan inactivos, el proceso que están ejecutando y el tiempo de carga del sistema
users
lista simple de los nombres de los usuarios actualmente conectados
last
últimos inicios de sesión de usuarios (incluidos los actualmente conectados)
Dame Ubuntu!
Ejecutá
docker run -it --rm --device nvidia.com/gpu=all -v /home/<USER>/root nvidia/cuda:12.4.1-devel-ubuntu22.04 bash
Dentro de ese container vas a ser root y tu home va a estar en `/root`. Y vas a tener acceso a la GPU.
Quiero agregar un usuario nuevo
Necesitas un compañerito con
sudo
que
Te meta en
/etc/nixos/users.nix
como un usuario nuevo
Te agregue a
/etc/nixos/ssh.nix
con tu nombre de usuario
Que rebuildee nixos con
sudo nixos-rebuild switch
Te corra el comandito
sudo passwd NOMBRE_DE_USUARIO
Quiero acceder a mostro desde casa
La forma manual de hacerlo es conectándose a una VM en Google Cloud, y de ahí a mostro. Eso se hace con los siguientes comandos:
gcloud compute ssh --zone "southamerica-east1-b" "network-controller" --project "eryx-varios"
ssh -p 9000 USER@localhost
Para no tener que correr esto cada vez se puede agregar un "alias" a la config ssh. Para hacerlo tenes que:
Correr
gcloud compute config-ssh --project "eryx-varios" --dry-run
Copiar la sección referida al host
network-controller.southamerica-east1-b.eryx-varios
, debería ser algo similar a esto:
Host network-controller.southamerica-east1-b.eryx-varios
HostName **************
IdentityFile /home/<USER>/.ssh/google_compute_engine
UserKnownHostsFile=/home/<USER>/.ssh/google_compute_known_hosts
HostKeyAlias=compute.7150541048246622819
IdentitiesOnly=yes
CheckHostIP=no
Pegar ese host en el archivo
~/.ssh/config
Agregar a ese mismo archivo el host de Mostro de la siguiente manera:
Host mostro-remote
HostName localhost
Port 9000
User [usuario de Mostro]
ProxyJump network-controller.southamerica-east1-b.eryx-varios
Y listo, ahora podes hacer
ssh mostro-remote
como en la oficina.
Se cayó la conexión remota!
generar una key ssh nueva en mi usuario de mostro.
ssh-keygen -t ed25519 -C "<EMAIL>"
dejar la pubkey correspondiente en mi authorized keys del network-controller
Tiene una pinta de
ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIB7oAFQBNtzX90LmXK/kT3tGO6oMv8gvijpd60udB5Jw
<EMAIL>
desde mostro correr
ssh -R 9000:localhost:22 user@ipdelnetworkcontroller
&
donde user@ipdelnetworkcontroller es su usuario en el network-controller y la IP pública del idem

wiki/Retiro.html
Wiki-eryx - Retiro
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Retiro
Portal Ñeryxes
Esquema de retiros
El esquema es la herramienta que utilizamos para definir el monto final del retiro de cada socio en Eryx.
Hay tres componentes que definen el monto: base, antigüedad y expertise:
El base es un monto igual para todos los socios.
La antigüedad es de un 1.5% por cada año de antiguedad que tenga el socio en eryx.
El expertise es el componente más complejo. Se ubica a cada socio en un esquema de escalones que dependen de su experiencia y su aporte a la cooperativa.
Según el rol que tiene cada socio en la empresa, se definen cuáles son las habilidades a tener en cuenta para defiinr el expertise. Estas habilidades están definidas en
este documento
.

wiki/Taller de arquitectura.html
Wiki-eryx - Taller de arquitectura
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Taller de arquitectura

wiki/Qu son las comisiones Qu hacen.html
Wiki-eryx - ¿Qué son las comisiones? ¿Qué hacen?
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Comisiones: qué son y qué hacen
En Eryx, las comisiones se utilizan como mecanismo para resolver problemas de forma ágil. Se crean
para atacar temas puntuales, que no son recurrentes, y que no son potestad de un área en particular.
Las comisiones son conform
adas de forma orgánica po
r socios y socias que estén interesados en resolver un determinado tema. Buscamos que entre sus integrantes haya personas con experiencia que puedan dar su punto de vista pero, mas allá de eso,
cualquier socio que lo desee puede integrarlas.
Al crear una comisión no se le asigna un límite de tiempo. Algunas se hacen cargo de temas complejos que duran varios meses y otras atacan problemas pequeños de menor duración.
Una comisión finaliza cuando se resuelve el problema o cuando se decide no invertir más tiempo en resolverlo.

wiki/Criptografa.html
Wiki-eryx - Criptografía
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Criptografía
Capacitaciones
MIT lecture on FFT
Biblioteca de libros y cursos
Node Guardians
Capacitaciones
MIT lecture on FFT
Tremendísimo video!
Biblioteca de libros y cursos
Libros cripto
Node Guardians
Usado por Roni y Carlo para aprender
Ethereum
y
Blockchain
Usado por Carlo para aprender
Noir

wiki/Compras personales en el exterior - TC Eryx.html
Wiki-eryx - Compras personales en el exterior - TC Eryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Compras personales en el exterior - TC Eryx
Si querés hacer una compra en el exterior con la tarjeta de Eryx, tenés que saber lo siguiente:
¿Cuál es el beneficio?:
Para el asociade: no abona la percepción de ganancias / bienes
Para Eryx: No tendrá ningún impacto
¿Puedo comprar cualquier cosa?:
No, por ahora solo se puede comprar por esta vía: equipos tecnológicos (relacionados con la actividad principal de Erxy) y viajes al exterior.
¿Hay tope de compra mensual?:
Sí, el tope mensual es de USD 3.000 aprox por mes para todas las compras realizadas por todes. (El monto mensual puede ir variando de acuerdo al tipo de cambio, pero entendemos va a rondar ese monto)
¿Qué tarjeta tengo que usar?:
Usá la extensión VISA a nombre de Juliana Salvemini. Los datos los vas a poder encontrar en Bitwarden
¿Cual es el procedimiento?
1)
Verificar el presupuesto en la planilla
"Compras . Presupuestos areas"
, desde la hoja "Visualización", en mes elegir el mes en que vas a hacer la compra y en área "Compras personales TC Eryx". Desde allí vas a ver el tope mensual; lo gastado y la diferencia que se puede seguir usando.
2)
Dar aviso en el canal
#a-administracion
que se va a hacer uso de este presupuesto.
3)
Cargar en el
form de compras
el comprobante con el concepto de "Compras personales TC ERYX" y el nombre del asociade que hizo la operación.
4)
El monto traducido en pesos lo verás reflejado como descuento del retiro a cobrar.

wiki/Tarjetas precargadas.html
Wiki-eryx - Tarjetas precargadas
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Tarjetas precargadas
Tarjetas precargadas
¿Para qué se pueden utilizar?
Para realizar compras en locales, por Internet en una sola cuota o bien extraer efectivo en cajeros.
¿Tengo que habilitar la tarjeta?
Para realizar compras, las tarjetas ya están 100% operativas.
Para extracciones hay que llamar al 0810-888-4500 y obtener el número de identificacion personal (NIP), el cual deberá ingresarse en un cajero automático de las redes cabal o link y modificarlo para comenzar a operar en cajeros.
¿Todos los meses voy a recibir plata en esta tarjeta?
Si, todos los meses.
¿Qué conceptos van en la tarjeta recargable?
Eryx Salud
Eryx Transporte
Complemento por hijo
Programa de ampliación de derechos (PAD): Gastos teletrabajo, gastos de estudios (personales o grupo fliar), gastos de salud (personales o grupo fliar), otros.
¿Dónde registro mis conceptos?
https://docs.google.com/spreadsheets/d/1I9kM2azR773AgSYes7kVGvdz4-FAzJUewenVDPqb9Gk/edit#gid=1323360540
Hoja: PAD

wiki/Diseo a la gorra.html
Wiki-eryx - Diseño a la gorra
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Diseño a la gorra
Objetivo: V
er los videos de Bs. As. vs London vs Chicago de Hernán Wilkinson. ...
¿Quiénes? Facu, Mati, Flor, Naza y CaroW
Modalidad: Viernes de por medio a las 10hs una hora. Hay que tener el video visto para ese dia y se charla sobre qué les llamo la atención.
Slack: #
diseño-a-la-gorra-10p
Link:
Diseño a la gorra

wiki/Derechos y obligaciones.html
Wiki-eryx - Derechos y obligaciones
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Derechos y obligaciones
Derechos:
Utilizar los servicios de la cooperativa.
Proponer iniciativas de interés social, mejoras en las condiciones dentro de la cooperativa o proyectos para desarrollar colectivamente.
Participar en las asambleas con voz y voto.
Aspirar a ocupar cargos en el Consejo de Administración y en el órgano de fiscalización.
Acceder al libro de registro de asociadas/os.
Solicitar al síndico información sobre los libros contables y sociales.
Retirarse voluntariamente de la cooperativa, notificando con un preaviso de 30 días.
Trabajar desde casa: Contar con la posibilidad de trabajar desde el hogar o desde otro espacio fuera de la oficina oficial, siempre que no afecte al equipo ni al funcionamiento de Eryx.
Días de estudio: disponer del tiempo necesario para poder cumplir con sus obligaciones para su desarrollo profesional.
Licencias: Acceder, como mínimo, a las licencias votadas en la asamblea, las cuales no podrán ser inferiores a las establecidas por la
Ley N.º 20.744.
Material de trabajo: Contar con los elementos necesarios para desempeñar las tareas asignadas por el colectivo.
Solicitar la realización de una asamblea extraordinaria, en conjunto con al menos el 10% de los/as asociados/as, conforme a las normas estatutarias.
Participar en los excedentes y decidir en asamblea su forma de distribución.
Recuperar las cuotas sociales aportadas.
Apelar ante la asamblea en caso de recibir una notificación de exclusión.
Acceder a la previsión social mediante las contribuciones correspondientes al régimen de monotributo, según la Resolución 4664/13.
Acceder a la indemnización por infortunios: Contar con cobertura ante accidentes o enfermedades derivadas de la actividad en la cooperativa, ya sea en casos de incapacidad parcial, total o fallecimiento. Estos riesgos deben estar cubiertos mediante la contratación, por parte de la cooperativa, de un seguro adecuado (Resolución 4664/13).
Acceder a condiciones laborales equitativas, con una protección equivalente a la de los trabajadores/as en relación de dependencia dentro de la actividad (Resolución 4664/13).
Obligaciones:
Prestar su trabajo personal en el área y/o especialidad que se le asigne cumpliendo con las directivas e instrucciones que fueren impartidas por la Cooperativa.
No realizar acciones que puedan perjudicar el trabajo de otro asociado/a.
No llevar a cabo acciones que puedan generar perjuicios a la cooperativa o a alguno de sus socios/as.
Acatar las disposiciones del estatuto, el reglamento interno y la legislación vigente.
Participar activamente en la cooperativa, involucrándose en la organización y la toma de decisiones.
Fomentar buenas relaciones con los demás asociados/as y priorizar la cordialidad ante cualquier conflicto.
Cumplir con los compromisos asumidos con la cooperativa.
Aceptar las resoluciones de los órganos sociales, sin perjuicio del derecho de recurrir contra ellos.
Mantener actualizado el domicilio, informando fehacientemente cualquier cambio al respecto.
No realizar acciones que puedan generar daños físicos a los elementos de trabajo como a uno mismo o un asociado.
Cumplir con las medidas de protección implementadas en el trabajo.
Preocuparse por el bienestar de todos los asociados.

wiki/CryptoHacks.html
Wiki-eryx - CryptoHacks
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
CryptoHacks
Incompatibilidad Python 3.12 y gmpy2:
Acá
se detalla la instalación de gmpy2 para resolver los ejercicios, pero es incompatible con la versión de python 3.12 incluída en ubuntu 24.04 y no piensan darle soporte hasta que salga python 3.13.
Instalar otra versión mientras tanto, ver
Readme del template de proyectos
.
Info GitHub issue

wiki/Template de proyecto.html
Wiki-eryx - Template de proyecto
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Template de proyecto
Objetivo: Tener un cascarón vacío (o template) con la estructura típica de un proyecto de Eryx para usarlo cuando empieza un nuevo proyecto. El template debe concentrar el conocimiento aprendido y generado en Eryx, es decir, tener appyx listo para usar junto con los frameworks comunmente utilizados ya integrados (Django, React).
Slack: #template-de-proyecto
Repo:
eryx-template
Trello:
desarrollo

wiki/Impresora.html
Wiki-eryx - Impresora
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Impresora
Para imprimir hay que instalar un driver que se puede encontrar
acá
.
Hay que instalar uno que es un script bash que se llama Driver Install Tool.
Bajarse el script y extraer el archivo. Se puede hacer con click derecho en el archivo y click en "Extraer aquí". Si lo queremos hacer desde la terminal una opción es:
gunzip linux-brprinter-installer-2.2.3-1.gz
Ahora hay que ejecutar los drivers. Pare eso, abrir en una terminal (abrir la carpeta donde está el archivo que extrayeron y hacer click derecho en algun lado y luego click en "Abrir en una terminal") y ejecutar en esa terminal (probablemente se pida una password que es la misma que usan para loguearse en la computadora):
sudo chmod +x linux-brprinter-installer-2.2.3-1
sudo ./linux-brprinter-installer-2.2.3-1
Cuando el script pide el modelo escribir: DCP-1617NW
Cuando la terminal pregunta cosas tipo "está bien que instale estos paquetes" responder sí (ingresar "y" y darle Enter).
Cuando te pregunta "Will you specify the Device URI? [Y/n]" responder sí (porque es una impresora en red y no USB).
Después tira varias opciones, elegir el número que diga:
dnssd://Brother%20DCP-1610NW%20series._pdl-datastream._tcp.local/?uuid=e3248000-80ce-11db-8000-3c2af4b1c121
Opcionalmente podés imprimir una página de prueba.
Cuando te pida la IP del device poner: *************
¡Listo! Podés entrar a CUPS (el sistema de gestión de impresoras) desde
acá
En el sistema para realizar ciertas acciones (por ejemplo borrar un trabajo de impresión encolado) te pide usuario y contraseña. Estos son el usuario y contraseña de tu computadora.

wiki/Qu licencias tenemos.html
Wiki-eryx - ¿Qué licencias tenemos?
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
¿Qué licencias tenemos?
Al tomarse una licencia, tener en cuenta el
actual procedimiento
Actualmente existen licencias por:
En el
manifiesto
se encuentra la información detallada de las licencias. A continuación, un resumen:
Vacaciones
(podés ver tus vacaciones disponibles
acá
):
Si tu antigüedad es menor a 5 años: 15 días hábiles acumulativos con vencimento a los dos años.
Si tu antigüedad está entre 5 y 10 años: 20
días hábiles acumulativos con vencimento a los dos años.
Si tu antigüedad es mayor a 10 años: 25 días hábiles acumulativos con vencimiento a los dos años.
Duelo:
Depende de la necesidad del socio.
Salud:
Depende de la necesidad del socio (incluye día por licencia menstrual).
Nacimiento o adopción (no gestantes):
20 días hábiles, no necesariamente consecutivos.
Licencia por maternidad (gestantes) :
90 días de licencia, que podrán iniciarse con anterioridad al parto.
Huelga general (convocadas por una o más centrales de trabajadores)
:
A consideración del socio. Se recomienda que los socios que ejerzan este derecho lo aprovechen para participar de las movilizaciones y actividades a realizarse ese día.
Día de parcial/día de estudio:
aproximadamente 16 días al año.
Docencia/Ayudantía:
4 días al año.
Matrimonio:
10 días hábiles, no necesariamente consecutivos, para usar con motivos relacionados a los festejos correspondientes.
Mudanza:
2 días al año.
Extraordinario (para cuestiones no contempladas en las anteriores):
2 días al año.
Todas las lic. deben ser informadas al área de Talento con la mayor anticipación posible.

wiki/Biblioteca de libros.html
Wiki-eryx - Biblioteca de libros
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Biblioteca de libros

wiki/Procesos.html
Wiki-eryx - Procesos
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Procesos

wiki/Bajo desempeo.html
Wiki-eryx - Bajo desempeño
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Bajo desempeño
Un proceso de bajo desempeño tiene lugar cuando identificamos que un ñery no está cumpliendo con las expectativas.
En el caso de que creas que un ñery no se está desempeñando de manera acorde, tenes dos alternativas para iniciar el proceso:
Podes acercarte a un
representante de su equipo eMe.
Podes completar el siguiente
form
.
En ambos casos,
te recomendamos siempre como primera iniciativa acercarte al ñery.
Esto puede darte un panorama más amplio y le sirva como una oportunidad de explicar su situación.
💡 Talent tip:
cuando informes sobre el bajo desempeño del compañero, hacelo de manera profesional y respetuosa, concentrándote en los hechos y evitando hacer acusaciones personales.
☝️Te pedimos que:
Seas específico
. No decir simplemente que el ñery tiene un bajo desempeño. Describir los comportamientos o las acciones específicas que están causando el problema.
Seas paciente.
El bajo desempeño no se corrige de un día al otro, lleva tiempo identificarlo y mejorarlo.
¿
Cómo es el proceso
?
El proceso cuenta de 5 etapas:
Etapa 1. Identificación de expectativas no cumplidas (1 mes)
Comienza cuando un ñery, el equipo eMe u otra persona identifica expectativas que no se están cumpliendo.
Las expectativas pueden estar relacionadas con cualquier aspecto del trabajo del ñery: sus responsabilidades, los plazos, la calidad del trabajo o su comportamiento.
Durante esta etapa, Talento y el equipo eMe evaluan si efectivamente se trata de un caso de bajo desempeño. Lo analizan, juntan feedback y lo escalan de ser necesario. De no ser un caso de bajo desempeño, el proceso termina acá y se le informa al ñery que subió la preocupación.
Etapa 2. Análisis del caso y búsqueda de soluciones (6 meses)
Responsable
: equipo eMe.
Objetivo
: proponer una primera instancia de soluciones.
Tareas
:
Estudiar la situación (recolectar feedback y entrevistar al ñery en cuestión)
Definir métricas de mejora para medir el progreso.
Hacer un seguimiento del proceso durante un semestre.
💡 Talent tip:
estas métricas deben ser claras, medibles, alcanzables, relevantes y limitadas en el tiempo.
Si el plan funciona y las métricas mejoran en un plazo recomendable de 6 meses, el proceso de bajo desempeño termina en esta instancia.
Etapa 3. Intervención del área de Talento (6 meses)
Responsable
: Talento
Objetivo
: proponer una segunda instancia de soluciones.
Tareas
:
Analiza el caso desde cero junto con el trabajo realizado por el equipo eMe.
Refinar métricas de mejora o proponer nuevas.
Visibilizar la situación al resto de la coope.
Al igual que en la etapa anterior, si las métricas mejoran en un plazo recomendable de 6 meses, el proceso de bajo desempeño termina en esta instancia.
Etapa 4. Intervención del espacio estratégico
Responsable
: Espacio estratégico
Objetivo
: encontrar/definir el rol para el ñery en cuestión. Un rol más definido y acorde le permite al ñery enfocarse en las áreas en las que es más productivo y cumple mejor con las expectativas.
Tareas
:
Verificar una reasignación de responsabilidades, áreas o proyectos.
Esta etapa dura un semestre más, siempre y cuando se encuentre un rol. De lo contrario, se instancia directamente a la etapa siguiente.
Etapa 5. Asamblea extraordinaria
En el caso de que el problema no se haya solucionado en las etapas anteriores, se procede a realizar una asamblea, en la cual se determinará la permanencia o no del ñery en cuestión. Esta es la última etapa del proceso y se utiliza como último recurso.
Proceso: Bajo desempeño

wiki/Reglamento interno.html
Wiki-eryx - Reglamento interno
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Reglamento interno
Reglamento interno
Anexo 1: comunicación interna

wiki/Cerrar Oficina.html
Wiki-eryx - Cerrar Oficina
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Cerrar oficina
Al salir: paso a paso
Agarrar llaves internas de la caja fuerte. La contraseña es
2011.
Al fondo, cerrar reja
con llave y la blindada
sin llave.
Cerrar la blindada que separa B y C con llave.
Cerrar con llave la sala silenciosa.
La puerta de la sala Turing no se cierra.
Cerrar balcones del frente (postigones cerrados).
Parte A: apagar luces y aires.
Al salir de A, cerrar la blindada con llave.
Cocina: apagar luces y
cafetera
(evita un incendio)
.
Dejar llaves internas en la caja fuerte
(¡si te las llevás mañana nadie labura!).
Al salir, dejar prendida la luz del hall (simula que la casa está habitada).
En caso de no tener las llaves de la oficina, tomar la copia extra que hay en la caja fuerte (devolverlas lo antes posible).
Cerrar con llave la puerta de madera (al bajar escalera).
Dejar abierta la de rejas.
Verificar una vez más que no te estás llevando las llaves
de las blindadas
.
Cerrar con llave la puerta de calle.
Asegurarse de que la
alarma
quede activada luego de salir

wiki/Beneficios autogestivos.html
Wiki-eryx - Beneficios autogestivos
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Beneficios autogestivos
[FALTA COMPLETAR]
En Eryx todes podemos proponer beneficios
Hoy en día tenemos clases de:
Ping-pong: Martes y jueves a las 17 y 18hs -> #ot-ping-pong
Flexibilidad y movilidad: Martes de 9 a 10 -> #ot-flexibilidad-movilidad
Podés sumarte al que quieras.
Si se te ocurre uno nuevo podés proponerlo y se analizará! Si hay varias propuestas y el presupuesto no alcanza, nos pondremos de acuerdo para priorizarlos.
¿Cómo proponer un beneficio nuevo?
Lo primero que debemos hacer es averiguar cuántas personas harían uso del beneficio y presentar un presupuesto.
En caso de que el presupuesto actual alcance para cubrir el nuevo beneficio sin pasarse y haya una base de personas que quieran utilizar el beneficio, el mismo se agrega sin problemas.
El presupuesto actual de los beneficios puede encontrarse en el canal #a-beneficios de slack, al igual que el presupuesto que este siendo utilizado actualmente. Este presupuesto se decide semestralmente
En caso de que el margen en el presupuesto no alcance para cubrir el nuevo beneficio se debe utilizar el
proceso de selección de beneficios.
Este proceso ocurre semestralmente (siempre que sea necesario, si no hay nuevos beneficios no es necesario que el proceso ocurra).
Mecanismo parte 1
Mecanismo

wiki/Historia Eryx.html
Wiki-eryx - Historia Eryx
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Historia Eryx
Eryx nace en el año 2011 cuando dos compañeros de la facultad de Exactas, Agustín y Luciano, decidieron comenzar su carrera profesional poniéndose a programar en algunos proyectos pequeños que habían conseguido. En un principio, como en muchos otros casos, lo que llamaban oficina era el departamento de uno de ellos. Siempre tuvieron algo en claro: ninguno quería ser “el jefe” y decidieron apostar por una relación de equidad entre ellos.
Pasaba el tiempo, tanto los proyectos como los clientes empezaron a crecer y Eryx no se quedó atrás. A través de amigos de la facultad, conocidos y recomendaciones, comenzamos a agrandarnos como grupo. En un punto el departamento quedó chico y fue por eso que en 2013 empezamos a trabajar en un coworking ("La maquinita"). Esto tuvo especial importancia ya que no solo nos permitió relacionarnos con otras empresas que estaban ahí, sino que también conocimos a una persona que nos comentó la posibilidad de convertirnos en una cooperativa. Si bien para algunos era un concepto nuevo, el cooperativismo se adaptaba perfectamente a la manera horizontal en la que nos manejábamos. Después de varias idas y vueltas, muchos pero muchos papeles y un trámite que duró al menos dos años, Eryx logró registrarse como cooperativa de trabajo.
A partir de eso seguimos sumando asociados y, como el departamento en su momento, el coworking empezaba a cumplir su ciclo. Nos pusimos en campaña para buscar un lugar propio donde poder, no solo trabajar, sino también expresar nuestra cultura. Finalmente en 2019 dimos el paso y nos mudamos a la oficina en la que estamos actualmente.
Hoy en día somos 36 asociados y, además de trabajar de lo que nos gusta, también intentamos llevar nuestra experiencia a otras personas para contarles que formar una cooperativa no solo es una opción viable, sino también un camino muy gratificante.

wiki/Qu hacer ante un conflicto.html
Wiki-eryx - ¿Qué hacer ante un conflicto?
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
¿Qué hacer ante un conflicto?

wiki/Protocolo de incendio.html
Wiki-eryx - Protocolo de incendio
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Protocolo de incendio
Manual contra incendios

wiki/Inventario.html
Wiki-eryx - Inventario
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Inventario
El inventario de la oficina se puede ver en:
https://docs.google.com/spreadsheets/d/1Msk0PYwUFxKwPWz6YTJjdCzI2K2jqbYmygR1DPdgQvA/edit#gid=1856588650

wiki/Pool de Mentores.html
Wiki-eryx - Pool de Mentores
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Pool de Mentores
A continuación se encuentran todas las personas que se proponen como posibles mentores dentro del programa eMe.
Estar en el pool de mentores significa que los ñerys puede buscar a una de estas personas y pedirle hacer una mentoría con ella. La palabra final la tiene el/la mentor/a!
Ser mentor/a implica una responsabilidad con tu ñery y su desarrollo profesional. Implica participar de su eMe y de cumplir ciertas responsabilidades.
Al haber match entre ñery y mentor, se debe avisar al equipo de Talento para la actualización y seguimiento correspondiente
Agus M
Agus C
Caro L
Caro W
Dal
Delfi
Doc
Facu
Juli D
Laski
Lucho
Mati
Nacho
Naza
Roni
Serge
Sherman
Tobi

wiki/Qu servicios ofrecemos.html
Wiki-eryx - ¿Qué servicios ofrecemos?
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
¿Qué servicios ofrecemos?
Somos una cooperativa argentina dedicada al desarrollo de software de alta complejidad. Ofrecemos soluciones en tecnología que desafían lo convencional, abriendo las puertas a posibilidades innovadoras.
Los servicios que ofrecemos son: DevOps, I+D, Computer Vision, UX/UI y Data Science & Operations Research. ¡Contamos con especialistas que trabajan para optimizar los proyectos con decisiones inteligentes!
Brindamos soluciones integrales
, nuestro diferencial es que tenemos una bajísima rotación
y la posibilidad de ofrecer todos los servicios dentro del rubro del software. Trabajando con nosotres no tenes que ir a buscar a nadie más. Nos definimos por nuestra visión humanizada, nuestros pilares son el cooperativismo y los lazos entre nuestro equipo y clientes, a quienes consideramos nuestros “partners”. Buscamos crear vínculos sólidos que nos permitan entender las necesidades de nuestros clientes de manera profunda y auténtica.
Contamos con un equipo experimentado y altamente capacitado integrado por investigadores y docentes. Llevamos la teorías a la práctica ofreciendo las mejores soluciones. Nuestro compromiso es el de ofrecer servicios de calidad y vanguardia. Pero más allá de nuestro enfoque técnico, tenemos una misión más amplia: mejorar la sociedad a través de la tecnología. Nos moviliza hacer del mundo un lugar mejor, y lo logramos brindando soluciones o impulsando proyectos que impacten positivamente en la vida de las personas.

wiki/muun.html
Wiki-eryx - muun
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
muun
¿De qué va?
Muun
es una aplicación mobile disponible para Android y iOS. Se trata de un monedero virtual para enviar y recibir Bitcoins.
Permite asignar cuánto tiempo está uno dispuesto a esperar para que se efectivice una transacción. A mayor tiempo, menor coste de las comisiones. También se puede usar la L2 de
Lightning Network
(
paper
)
para realizar transacciones.
Recursos
Canal Slack Eryx: #p-crypto-muun
Canal Slack compartido: #muunyx-project1
Tablero
:
P
edir Trello a Tom
Documentación
:
Crypto -> Muun
,
Compartido
Código: Aún no hay repo
Diagramas
:
Miro
Material
Enunciado
Ideas y objetivos del proyecto en el siguiente
documento
HackMD
de aprendizaje e ida y vuelta con Muun sobre el problema
HackMD
con digest del Chino sobre Lightning

wiki/Beneficios.html
Wiki-eryx - Beneficios
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Beneficios

wiki/Ingreso Oficina.html
Wiki-eryx - Ingreso Oficina
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Ingreso oficina
Si sos el primero que llega, no te olvides de
desactivar la alarma
. Para abrir las salas vas a necesitar las llaves que están en la caja fuerte.
Las puertas de entrada estan cerradas con llave. Para entrar vas a necesitar tener una copia. Las mismas se solicitan a Coordinación.
Al entrar se deben buscar las llaves de las puertas blindadas, que están en la caja fuerte de la sala Von Newman dentro del armario (apertura
push open)
. El código de la caja fuerte es 2011.
Con esas llaves, abrir la puerta del sector C y el sector A. Luego de abrirlas, se debe dejar las llaves de nuevo en la caja fuerte. Es importante que las llaves queden ahí para que nadie se las olvide en algún lado y cierre la puerta blindada y no poder abrirlas luego.
Para saber cómo cerrar la oficina, ir a la sección
Cerrar oficina
.

wiki/Chequeo mdico anual.html
Wiki-eryx - Chequeo médico anual
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Chequeo médico anual
Creemos que es de gran importancia el bienestar integral de cada Ñery, incluyendo su salud. Es por eso que enfatizamos en la importancia de realizar los chequeos médicos.
Más que una simple revisión
Los chequeos médicos anuales no son sólo una simple revisión, son también una oportunidad para:
Prevenir enfermedades: la detección temprana de enfermedades como la presión arterial alta, el colesterol alto o la diabetes permite un tratamiento oportuno y eficaz, mejorando las posibilidades de éxito.
Promover hábitos saludables: tu médico puede ofrecerte recomendaciones personalizadas para mejorar tu estilo de vida.
Mantener una buena salud: los chequeos periódicos te ayudan a mantenerte al día sobre tu estado de salud general.
Recordá: sos una pieza fundamental para nosotros y tu salud es nuestra mayor preocupación.    ¡Cuídate por vos y por nosotros! ❤️
¿Cómo te acompañamos desde Eryx?
Brindamos un día de licencia para  los chequeos médicos y en el caso de las mujeres 1/2 día más para los chequeos ginecológicos. Además, podes realizarlo de manera gratuita con nuestra prepaga OSDE.
¿Dónde puedo hacerme el chequeo?
Diagnóstico Maipú
Pilar | Panamericana Colectora Este Pilar Km 48,5
Cabildo | Av. Cabildo 457 - CABA
Turnos: por teléfono 4837-7777, Whatsapp +54 9 11 2700-7777 o de forma online (
click acá
)
Centro Rossi
Esmeralda
|
Esmeralda 141 - CABA
Beruti
|
Beruti 2853 - CABA
San Isidro
|
Dardo Rocha 3034 - San Isidro.
Turnos: pueden sacar web o llamando a CENTRAL DE TURNOS (011) 4011-8080
Pasos a seguir después de realizarme el chequeo:
Queremos estar al tanto de tu estado de salud y cómo te fue en el proceso, por eso te pedimos que completes el siguiente form:
https://docs.google.com/forms/d/e/1FAIpQLSfMJiB0u7Esg2B_Pwc3uFbgcmWmqTBRIGWLbKlV_MG0dqT09g/viewform

wiki/Contactos.html
Wiki-eryx - Contactos
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Contactos de Ñerys para agregar a Google Contacts
Acá podés encontrar la descripción de cómo hacer para tener los contactos de tus Ñerys en tu cuenta de Google. Esto tiene muchas ventajas:
tener los mails de toda la coope
(para coordinar reuniones
en gcalendar o escribirles
)
.
si tenés la cuenta de Google en tu celular
,
te agrega los contactos a WhatsApp autom
áticamente
(y dejás de tener un montón que son ~Q🕵🏼‍♂️🦄🧶🐻🕸️).
se te agregan todos los cumpleaños al calendario de cumpleaños de tu cuenta de Google
.
vas a
pod
er
buscar por nombre, apellido o
apodo
.
¿Cómo lo logro? En 2 simples pasos:
Descargá
esta planilla
como
CSV
(
Archivo -> Descargar como... -> Archivo Separado por Comas (CSV)
Luego, abrís
https://contacts.google.com/
, vas al menú de la izquierda a la opción "
importar
" y cargás el archivo CSV.
La
planilla
tiene los siguientes datos
:
n
ombre
a
pellido
apodo
m
ails (tanto @eryx.co como @eryxsoluciones.com.ar)
n
úmero de
t
eléfono
d
ía de cumpleaños (sin año)
Si querés podés cambiar alguno
(ej. el apodo)
antes de importar los contactos.

wiki/Documentacin Coope.html
Wiki-eryx - Documentación Coope
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Documentación Coope
Datos facturación
Razón social: Cooperativa de trabajo Eryx Ltda
CUIT: 30715937251
Dirección: Bonpland 1953 CP: 1414, Palermo CABA.
Condición impositiva: Responsable Inscripta
Exenta de Ingresos Brutos (IIBB)
Tel de contacto: 4774-5604
Matricula: 56455
Constancia AFIP
Constancia Exención de IIBB
Constancia Exención de ganancias.
Certificado Pyme
Presentaciones mensuales de IVA 2023
Balances
Balance 2017
Balance 2018
Balance 2019
Balance 2020
Balance 2021
Balance 2022
Balance 2023

wiki/Asignaciones eMe.html
Wiki-eryx - Asignaciones eMe
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Asignaciones eMe

wiki/Legajos.html
Wiki-eryx - Legajos
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Legajos
Legajos
Luciano Leveroni
Agustín Mosteiro
Matias Gabriel Dinota
Agustín Garassino
Tobías Valdes Castro
Germán De Sousa Bispo
Juan Matias Burella
Facundo Nicolás Decroix
Maximiliano Suppes
Sergio Fedi De Oro
Nahuel Diego Lascano
(Laski)
Lucas Ezequiel Rodriguez
Dalma Nahir Borda
Federico Javier Pousa
Maia Numerosky
Ignacio Espino
Carolina Lang
Delfina Valdes Castro
Sergio Nicolas Chouhy
Roni Ariel Barylko
Julián Ezequiel Arnesino
Florencia Rodríguez
Juliana Salvemini
Alejandro Tomás Grosso
Julian Lautaro Diaz
Bruno Weisz
Felipe Marelli
Agustina Claramut
Carlo Ferrari
Alejandra De Nardi
Caro Wright
Nazarena Rueda
Gonzalo Lenzi
Ezequiel Cribioli (Chino)
Matias Lopez y Rosenfeld

wiki/Cooperativismo.html
Wiki-eryx - Cooperativismo
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Cooperativismo
¿Qué es una cooperativa?
Cuando un grupo de personas toma conciencia de sus necesidades, problemas e intereses comunes comienza a organizarse buscando la forma más adecuada de resolverlos.
La Alianza Cooperativa Internacional (ACI) estableció la siguiente definición:
“Cooperativa es una asociación autónoma de personas que se han unido voluntariamente para satisfacer sus necesidades y aspiraciones económicas, sociales y culturales comunes, por medio de una empresa de propiedad conjunta y democráticamente gestionada”. Es decir, son empresas centradas en las personas, que pertenecen a sus miembros, quienes las controlan y dirigen. Las cooperativas asocian a las personas de manera voluntaria,democrática e igualitaria, y se gestionan con la regla de “un miembro, un voto”.
Las cooperativas comparten una serie de principios acordados internacionalmente que se basan en la equidad, la igualdad y la justicia social. Son sociedades cuyo objetivo no es solamente crear riqueza sino trabajo de calidad en el marco de empresas sostenibles, que intentan aportar a una matriz económica sustentable y más justa para toda la sociedad. Permiten que las personas tomen el control de sus economías futuras y, al no ser propiedad de accionistas, los beneficios económicos y sociales de su actividad permanecen en las comunidades en las que se establecen. Las ganancias generadas, llamadas excedentes, se reinvierten en la empresa o se devuelven a los miembros.
¿Qué son los valores y principios cooperativos?
Son las normas éticas que enmarcan a las y los asociados para organizar la concreción de sus objetivos comunes. Es decir, esos objetivos se lograrán a través de la puesta en práctica de los valores y principios del cooperativismo. Desde sus orígenes el movimiento cooperativo estableció estas pautas que contribuyen a la creación de las dinámicas necesarias para consolidar a los grupos cooperativos y constituir una genuina organización solidaria.
¿Cómo está compuesta una cooperativa?
Las cooperativas son asociaciones voluntarias y abiertas a todas las personas capaces de utilizar sus servicios y dispuestas a aceptar las responsabilidades de asociarse. Pueden ser asociadas las personas humanas mayores de 18 años, los menores de edad por medio de sus representantes legales y los demás sujetos de derecho, inclusive las sociedades por acciones, siempre que reúnan los requisitos establecidos por el estatuto. Toda persona que quiera asociarse a la cooperativa deberá presentar una solicitud por escrito al Consejo de Administración, comprometiéndose a cumplir las disposiciones del estatuto y del reglamento interno. Dicha solicitud deberá ser aprobada por el Consejo de Administración. A su vez, cuando un asociado renuncia debe enviar una nota dirigida al presidente del Consejo de Administración indicando, además del motivo de su renuncia, datos formales, tales como: lugar, fecha, número de documento, firma y aclaración.
¿Qué son las Asambleas?
Las cooperativas son organizaciones democráticamente gestionadas por sus asociados, quienes participan activamente en la fijación de políticas y en la toma de decisiones a través de sus órganos sociales. Los hombres y mujeres elegidos como representantes son responsables de sus acciones ante el conjunto de los asociado/as. La Asamblea es el órgano superior y soberano, a través del cual los asociados expresan su voluntad. En ella participan todos los asociados en un plano de igualdad: un voto por asociado. Las resoluciones se toman por simple mayoría de los presentes en el momento de la votación, con excepción de las relativas a las reformas de estatuto, cambio de objeto social, fusión, incorporación o disolución de la cooperativa, para las cuales se necesita una mayoría de dos tercios de los asociados presentes en el momento de la votación. Si bien la Asamblea es soberana, su funcionamiento y sus decisiones deben ser concordantes con lo que determina la Ley, el Estatuto y las normativas que puedan regular su funcionamiento.
Tipos de asambleas
Ordinarias:
Se realizan una vez al año, dentro de los cuatro meses siguientes a la fecha de cierre del ejercicio económico.
Extraordinarias:
Tienen lugar cada vez que lo disponga el Consejo de Administración, el Síndico o un 10% de los asociados.
¿Qué es el Consejo de Administración?
Es el órgano directivo de la cooperativa, elegido por la asamblea y tiene a su cargo la dirección de las operaciones sociales y administrativas de la cooperativa, siempre dentro de los límites que fija el estatuto. Los miembros del Consejo de Administración deben ser asociado/as de la cooperativa.
En  Eryx el consejo administrativo nos representa como tal de manera contable y legal, pero internamente la dirección de operaciones sociales y administrativas está dada por todos los socios de la cooperativa de manera igualitaria y las decisiones se realizan mediante votación.
Hoy en día nuestro consejo está compuesto por:
Presidenta:
Juliana Salvemini
Secretario:
Maximiliano Suppes
Tesorero:
Sergio Fedi De Oro

wiki/Capacitacin de ingresante a dev.html
Wiki-eryx - Capacitación de ingresante a dev
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Capacitación de ingresante a dev
Acerca de este temario
Nuestra forma de programar
Anatomía cliente servidor
Panorama general
Pedido al servidor
Carga de una página en el browser
Servidores HTTP y handleo de requests
Client-Server
Base de datos
Programación de base
Código
Excepciones vs Errores vs Result
Frontend
Cómo carga una página HTML
Frameworks y arquitectura
Hacer tutorial de React.
Deconstruir, analizar, criticar el tutorial de React
Testing
¿Por qué queremos tests? ¿Qué valor que nos da?
Tipos de test
Test de regresión
Test de integración
Test end to end (e2e)
Test de caja blanca y caja negra
Bugs que no vuelven
Smoke test
Tests manuales
Tests no funcionales
Tests de features definidas y tipo de producto
Entidades que nos aparecen
Organización de tests como enseñanza del sistema
¿Qué significa testear mi app?
Temas que no vemos
Metodologías ágiles
¿Qué son?
¿Por qué/para qué?
¿Cuándo se aplican?
Entorno dinamico cambiante (si se aplica)
Entorno estático predecible (no se aplica)
Entorno incierto, investigación (no se aplica)
Kanban vs Scrum
Kanban
Scrum
Separando desarrollo de entregas y relevamiento: Sprints y Backlog grooming
Roles: Product Owner, Scrum master, dev, tester, ux, analista
Rituales
Variantes
Scrum of Scrums
Herramientas
Git
Pycharm/IntelliJ
Unix
Stack del proyecto - Django
Hacer tutorial
Analisis y critica
Refactor
Introducirnos en cómo desarrollamos en Eryx
Anatomía de un handler de POST
1er párrafo
2do párrafo
3er párrafo
Bases de datos
Comienzos: archivos
Relacional
De objetos
De documentos
Key/value
Rol de absorber concurrencia y distribución
Transacciones ocultas (y transacciones)
TDD
Docker
Docker Compose
Acerca de este temario
El objetivo de este manual/ temario es dar unas herramientas básicas al ingresante para que pueda estar al nivel que cualquier desarrollador de Eryx debería tener.
El orden es optativo y debe ajustarse a las necesidades de cada ingresante. Algunos temas son muy largos por lo que sugerimos no darlos en bloque sino ir mezclándolos con otros.
Durante la capacitación del dev entrante se espera también que participe de otras actividades, como estar medio día en proyectos, o áreas y en la otra mitad del día hacer este temario.
Nuestra forma de programar
1 DIA
Esta parte del temario es muy larga pero la idea es que sirva para “rellenar” y ver durante/entre otras capacitaciones. Puede servir para “tener algo que hacer” cuando no hay tareas suficientes asignadas por el capacitador o las puede usar el ingresante para mechar con algo distinto, para cambiar de tema.
Ver el ciclo de videos de Valores, Principios, Prácticas y Heurísticas que hicimos desde Academia.
Valores, principios y prácticas de Eryx
Cartuchera del Ñery
Videos de la cartuchera del ñery
La idea no es solo que el ingresante vea los videos sino que pueda consultar sobre detalles o aclaraciones de los mismos, que el capacitador pregunte sobre los videos y tome una especie de examen a ver si entendió los conceptos. También es un buen momento para evaluar qué temas son más difíciles para el ingresante y de esa forma poder profundizar con otra capacitación o agendarlo para más adelante luego de la capacitación inicial.
Anatomía cliente servidor
2 hs
Panorama general
La idea de esta sección es entender todas las cosas que pasan desde que tipeo una url en el browser hasta que se muestra en pantalla la página, pasando por lo que sucede en el disco del servidor que atiende mi pedido. Seguramente un ingresante ha visto todos estos temas por separado, ha usado internet, pero tal vez no entienda cómo todo esto se articula, cómo todo esto se conecta y convive para brindar la experiencia de navegar la web.
Pedido al servidor
Anatomía de una URL:
Tipeamos una URL en el browser: traducción a IPv4 o IPv6, DNS.
Se routea por distintos servidores: gateway,
Partes de un request: headers, body, para que sirven (seguridad, expiración, parámetros propios, cookies).
Notar que es stateless.
Carga de una página en el browser
La respuesta es una string (el content, y lo que ves en “View source code” en el browser).
El browser la va leyendo e interpretando, va ejecutando JS a medida que lee.
Si en algún momento se importa un archivo la interpretación del HTML se bloquea, el browser lo trae y luego sigue ejecutando/interpretando el JS.
Las páginas y CSS las maneja asincrónicamente.
El código HTML es interpretado y se arma un árbol de componentes (DOM).
El DOM se usa para renderizar en el canvas del browser.
El canvas es algo que el SO provee.
Hay un intérprete JS corriendo todo el tiempo.
Compartimentalización de las pestañas en los browsers modernos.
Servidores HTTP y handleo de requests
El servidor atiende el request en la placa de red, puede haber varias placas.
Luego SO, puede haber más de una app escuchando, explicar el PORT en un servidor y en la URL y cómo sirve para esto.
Luego web server, que puede hostear varios recursos, estáticos y código. Explicar URL y ruteo de eso.
Luego el código sigue analizando la URL y routea a partes de nuestro código (cada View o Handler).
Client-Server
Definición de cliente (el que hace requests)
Clientes que hacemos nosotros: paginas, mobile apps
Clientes de terceros: APIs que ofrecemos
Responsabilidades de uno y de otro:
Cliente: presentación, interacción, visualización, validación temprana.
Servidor: autenticación, persistencia, lógica, validacion (se repite!).
Un servidor puede necesitar de otros servidores y a su vez ser cliente.
Load balancing.
Firewall: ¿Qué es? ¿para qué sirve? ¿es un programa mas? ¿lo puede correr cualquiera? (hablar de que es un proceso privilegiado del sistema operativo).
Base de datos
Sirven para guardar en disco y que la información esté dure, no se pierda.
Es otro programa, puede estar en la misma máquina o en otro servidor.
Suele ser el centro de una app: varios servidores le pegan.
Hay de varios tipos, cada una con sus pros y contras:
relacional
document
key/value
objetos
Vamos a ahondar en los diferentes tipos de bases de datos más adelante.
Programación de base
1/2 dia
Código
2 hs
La idea es mostrar las slides de este tema y luego complementar con otras cuestiones listadas más abajo. Seguramente convenga modificar las slides para incorporar estos otros temas, o armar otra presentación/slides para estos otros temas.
Slides de este tema
Cómo nombrar variables
Cómo nombrar variables funciones/métodos
Cómo escribir funciones/métodos
Separación de problemas en partes más chicas (ej: al escribir un método):
Como los ejemplos del cajero automático y del supermercado, pero para otros casos:
Dar de alta un socio en Eryx
Producto de matrices: multiplicar filas, multiplicar elementos
Más en detalle el problema del cajero: que datos meto, qué botones aprieto, cómo muevo la mano y el brazo para apretar botones, etc.
Mostrar y pensar que siempre podemos “ir en más detalle” en un proceso, hasta llegar a cosas muy chiquitas, pero que terminamos en operaciones atómicas (en el caso de lenguajes de programación: arreglos, strings, números, etc)
Niveles de abstracción (ej: el método que llama es más abstracto que los que son llamados)
Al mismo tiempo, mostrar cómo un proceso cualquiera es parte de un proceso más “grande”, más abstracto. Y así como podemos “hacer zoom in” en una tarea, podemos hacer “zoom out” identificando tareas más grandes de las cuales nuestro método es parte.
Ejemplo:
Sacar plata del cajero es parte de Juntar plata para el viaje que es parte de Viajar a Mendoza en auto que es parte de Vacaciones en la montaña, etc.
Comparar en el supermercado es parte de Conseguir ingredientes para la cena que es parte de Preparar la cena que es parte de Hacer reunión con las chicas del club.
Métodos de una línea, por qué:
Cambio de nivel de abstracción
first -> self at: 1
Revista.new() -> revista_vacia()
Nombrar caso frecuente/importante. Ejemplo:
sacar(1000$) -> sacar_mil_pesos
Point(0,0) -> Point.origin() / Point.zero()
Generar un alias. Ejemplo:
modulo(n) -> norma_zero(n)
execute() -> __call__()
Agregar requisito: mismo nivel de abstracción
Un método está en un nivel de abstracción.
Las llamadas a otros métodos que hacemos dentro tienen un nivel de abstracción menor, son más específicas, más concretas.
Pero todas esas llamadas deberían estar en un mismo nivel de abstracción.
Excepciones vs Errores vs Result
1 hr
A continuación cada ítem presenta un concepto. Los ítems de más abajo se apoyan en los conceptos introducidos anteriormente para su comprensión.
Uso esperado, cumplir el protocolo
Cuando definimos un objeto, API, función, esperamos que la gente la use de cierta forma.
Puedo utilizar el objeto, API, función, pero manteniéndome dentro de cumplir los mensajes, parámetros, etc (esto es cumplir el protocolo), pero dentro de ese uso esperado pueden surgir situaciones que no se pueden resolver:
una pila en la cual se meten y sacan elementos. Se puede pedir sacar un elemento cuando la pila está vacía.
puedo querer calcular el camino entre dos lugares pero esos dos lugares no están conectados, por lo tanto no existe un camino.
Incumplir protocolo
Los puedo utilizar mal, en un orden incorrecto, respetando el tipo pero con un valor que produce un error (dividir por cero). En estos casos el responsable del problema, quien genera el problema, es quien interactúa. Puede no ser el que hace la llamada, puede estar más arriba, pero está en el contexto.
Situaciones anómalas
Puede haber contexto que excede a esta interacción y cause problemas. En esos casos la anomalía no se puede resolver localmente.
El responsable del problema está más arriba, quien generó que se puede producir esto.
Ejemplo:
Tengo una colección en memoria.
Alguien de arriba reemplaza esa colección por una colección polimórfica capaz de contener millones de elementos, pero utilizando disco.
Ahora pueden dispararse errores como que el disco está lleno, o un error de datos que antes no podían suceder
Error:
El mismo protocolo (del objeto, API, etc) es el que permite que suceda esto (por eso está bueno generar protocolos que impidan errores por cómo está diseñado, en vez de permitir errores. Algo parecido pasa en UX: generar interacciones que impidan el error en primera instancia más que hacer un buen manejo del error)
Si el protocolo está bien diseñado el cliente debería poder siempre evitar el error
A la pila le puedo preguntar si está vacía, evitando pedir un elemento cuando no hay ninguno
Al mapa le puedo preguntar si dos lugares tienen conexión o puedo hacer que cuando no hay un camino me devuelva un camino vacío o puedo pasarle un bloque que se ejecute en el caso de que el camino es vacío
Notar que en el caso de la colección en disco, no tengo forma de manejar el error, porque nunca pude haber contemplado esa situación. Por eso no puede ser un error.
Ejemplos:
Dividir por cero
Pedir el 8vo elemento a un array de 7
Excepción:
Cuando surge un error o situación que requiere que corte el flujo de ejecución pero este problema no es parte del dominio, no tiene sentido dentro del dominio que se está trabajando. Ejemplo:
Pedir el 1er elemento a un array de 7, pero no hay conexión a internet
Agregar una solicitud de garantía nueva, pero el servidor de Celery está caído
Para esto no solo disparo una excepción que “puentea” a todo el stack de llamado, sino que debería ser atrapada por alguien “más arriba” que sí tenga conciencia del dominio en el que se dispara la excepción.
La solución/arquitectura de estas situaciones es que alguien de más arriba decide que cierta lógica va a usar alguna solución tecnológica puntual (que es de otro dominio e introduce la posibilidad de errores nunca antes vistos). Por ejemplo: armo al negocio para que use un servidor remoto y un ejecutor de tareas asincrónicas Celery, o le indico a un simulador o calculadora que la multiplicación de matrices se va a hacer con una biblioteca externa optimizada (una DLL)
Resultado:
Cuando no quiero o no puedo manejar excepciones (además de errores) puedo implementar un mecanismo de Resultado.
En este mecanismo una llamada siempre devuelve un objeto/dato esperado y al mismo tiempo una indicación de si hubo algún error.
Ejemplo:
Hago un pedido HTTP: No me tira la excepción en ningún caso. Incluso si no hay internet.
Le pido a una persona que haga algo, esa persona vuelve y me explica, me da un reporte
Frontend
1 dia y 1/2
Cómo carga una página HTML
1 hr +
Cómo está organizado un documento HTML: XML, sintaxis, estructura de árbo.l
Javascript: motor corriendo, DOM, eventos.
Historia de la carga de páginas:
Browser recibe string de la red
Convierte en árbol
Renderiza en canvas del sistema operativo
Expone árbol a código JS para que pueda leerse
Expone árbol a código JS para poder modificarse
Si puedo modificar HTML con JS puedo hacer cosas interactivas útiles:
Errores de carga
Ocultar o mostrar secciones
Tooltips avanzados
Recargar/redibujar partes de la página sin saltar a otra
One Page Applications
No cambia la URL (puede ser un problema)
Uso fuerte de AJAX (o su equivalente)
Así como el árbol DOM es el modelo de la vista pixeles del monitor, el árbol DOM es la vista del modelo de datos que tiene la pagina.
Se modifica el arbol de acuerdo a este modelo.
Problema recurrente -> redibujar HTML de acuerdo a modelo -> React
Herramientas: tools del browser, console, elements, network y sources
Declarando funciones y disparandolas desde la consola
Encontrando elementos del DOM en la consola
Vincular una función a un evento de un elemento del DOM
Modificar el DOM desde una funcion JS
Jquery:  ¿qué es? ¿qué resuelve?
Función $, pattern que va de parámetro, manejo de uno o varios elementos de la misma forma. Instalación de handlers.
Frameworks y arquitectura
8 hs
Hacer
tutorial de React
.
Explicar la arquitectura a nivel archivos, cómo se cargan, dependencias, preprocesadores
Explicar que hay un motor que procesa el JS de React para generar el HTML/Dom y eso se hace cada vez que se cambia el estado. Compararlo con lo visto anteriormente de “cablear” las interacciones y redibujado y dependencias con JS en HTML. Cómo la complejidad es delegada y absorbida por el motor de React
En particular la sintaxis JSX y cómo el código es preprocesado para convertir JSX en JS que renderiza HTML
Despejar dudas del pasaje de parámetros y cómo los componentes de React dependen de éstos
Hacer paralelo con la programación funcional
Explicitar que esto permite “cachear” o “adivinar” que si los parámetros no cambian, el resultado del componente es el mismo, entonces puedo no evaluarlo
Explicar cómo al cambiar parámetros se reevalúa el árbol de componentes evitando recalcular componentes que reciben el mismo
Deconstruir, analizar, criticar el tutorial de React
Mostrar cómo el negocio, el dominio que se está modelando está embebido, acoplado con el código React
Reflexionar sobre el rol de React: es un motor de rendering, calcula algo visual a partir de datos/modelo/negocio
Entender que el rendering de un negocio y el negocio van separados
Programar desacople de lógica del TaTeTi
Mostrar cómo se puede delegar la lógica ahora a un servidor
Testing
2 hs
¿Por qué queremos tests? ¿Qué valor que nos da?
Explorar y entender el negocio (TDD):
A partir de la funcionalidad vamos definiendo el negocio, vamos aprendiendo. Nos da el valor de ir construyendo, aprendiendo y que nos queden tests que explicitan y testean que eso que aprendimos sigue andando.
Especificar el contrato en una API, con el exterior, con un usuario
Tests a nivel “HttpHandler”. Nos permite asegurar que la forma de comunicarse con alguien del exterior no ha cambiado.
Testeamos:
Happy path (si damos parámetros de forma correcta, recibimos respuesta esperada en formato correcto, y el sistema es modificado de forma acorde)
Errores (si provocamos el error X, la respuesta tiene formato Y y el sistema no es modificado)
Enseñar el negocio
Explicar qué hace mi sistema ¿desde los tests? ¿o es algo que lo excede? ¿Se puede explicar o entender el negocio viendo los tests? ¿Y haciéndolos?
Los tests son una enumeración de propiedades o de “verdades” del sistema. No garantizan pedagogía, pero sí completitud. Aunque sean en un orden pedagógico, tampoco son redundantes como en la pedagogía, no explican lo mismo de varias maneras. Tampoco ejercitan. En matemática no solo te explican de varias formas lo mismo, después hacés ejercicios que ponen en juego lo aprendido.
Los tests enseñan qué cosas puede hacer el negocio o sistema y cómo hay que “hablarle” para hacerlo. Pero no enseña cómo estas funcionalidades se entrecruzan, o conceptos que se construyen sobre estas operaciones fundamentales. Es parecido a saber cómo se mueven las piezas en el ajedrez vs saber jugarlo.
Detectar cuando dejo de cumplir un contrato o de realizar una funcionalidad
Si tengo una regla de negocio, imaginemos un límite de caracteres en un texto, quiero duplicar ese número, ese límite: uno en los tests, otro en le negocio. Eso me permite detectar cuándo el límite del negocio es cambiado.
No es duplicación de código, es una redundancia que me permite detectar cuando algo cambió sin que yo quiera.
Implicación: Dado que tengo tests que detectan cuando dejo de cumplir reglas de negocio, dejo de cumplir una API, entonces si cambio la funcionalidad de mi sistema y dejo de cumplir algunas de esas cosas tengo una forma automática de detectarlo. Esto quiere decir que puedo refactorizar o incluso reescribir mi código y que mi sistema siga teniendo las mismas funcionalidades, el mismo comportamiento.
Tipos de test
Test de regresión
Esto es correr tests que andaban antes, de nuevo ahora, para ver que no “hubo una regresión”, es decir, que no retrocedí y lo que andaba antes sigue andando ahora
Test de integración
Definición muy amplia o ambigua, a tal punto que resulta poco práctica. Y está dada en contraposición al test de unidad. Muchas veces un test de integración es uno que no es de unidad. En la práctica esta definición es muy poco útil.
¿Que es de integración? ¿Que participa mas de un objeto? ¿Mas de un sistema? ¿Mas de un paquete? ¿Qué es lo que se está integrando?
En general vamos a querer siempre tests que testeen cuestiones funcionales, y que involucren a todos los objetos que sean necesarios.
Test end to end (e2e)
Tests donde tratamos de replicar (desde el uso, no necesariamente desde el deploy) el uso del sistema como se va a dar en la realidad.
El ejemplo típico son los tests de Selenium, donde levantan el sistema, luego levantan un navegador web, y le hacen click, rellenan campos, etc en el navegador y ven cómo responde éste
Son tests caros y frágiles
Pero muy valiosos para testear que “el sistema anda de verdad”.
Conviene para unos pocos casos críticos.
Test de caja blanca y caja negra
Negra: testeo lo que el sistema hace, sin tener en cuenta cómo lo hace. Es un test funcional.
Blanca: testeo aspectos que sólo tienen sentido por cómo está implementada la funcionalidad. Pongo a prueba la correcta implementación.
Hay casos que son borde no funcionalmente sino algorítmicamente en la implementación.
Bugs que no vuelven
Cada vez que detecto un bug, hago un test que reproduce el bug
Corrijo el bug
Ahora me queda un test que me garantiza que ese bug no vuelve mas
Esto es especialmente valioso porque suele ser muy caro a nivel negocio o político que un error subsanado vuelva a suceder
Smoke test
Viene de la ingeniería electrónica
Sirve para correr todos los tests (no perder tiempo) si hay cosas muy burdas, muy básicas que están rotas
La idea es correr unos pocos tests que me aseguren que todo lo básico anda.
“Lo básico” depende del sistema, pero normalmente es: la persistencia anda, el login anda, los sistemas externos/emulados están correctamente conectados, etc.
Tests manuales
Son ejecutados por personas
Si testean algo que puedo testear de forma automática con tests escritos por desarrolladores, los estoy usando mal
La idea es que testeen cosas no automatizables
Hay muchas estrategias y técnicas de testeo manual que aportan valor y no son automatizables, pero vamos a ir con algo resumido
El tester manual asume un rol de “hacker” donde trata de hacer cosas erróneas, corromper el sistema o eludir reglas de negocio.
El tester manual se fija si el sistema es consistente en sus reglas de negocio y en su implementación
El tester manual utiliza el sistema y se fija que los procesos sean claros, directos, intuitivos (acá se testea la UX además de lo programado solamente)
Tests no funcionales
Baseline
Mide o comprueba cómo el sistema funciona o debería funcionar normalmente. Cuál es un tiempo de respuesta esperado, por ejemplo.
Compatibilidad
Prueba que el sistema opera bien con los diferentes sistemas para los cuales fue diseñado.
Que el sitio web funciona con los diferentes navegadores
Que la app mobile funciona bien en diferentes celulares
Que el servidor o ejecutable corre en diferentes sistemas operativos
Stress
Uso intensivo del sistema, múltiples usuarios a la vez, muchos requests a la vez
No es obvia la diferencia con los tests de carga
Seguridad
Hacerle ataques típicos y ver que no se vulnera el sistema
De carga
Uso del sistema con grandes volúmenes de datos (pero no necesariamente de usuarios o requests): que el sistema tenga muchos datos, que le subo muchos datos, que procese muchos datos o que corra un proceso caro en CPU o que me devuelva muchos datos
No es obvia la diferencia con los tests de stress
Recovery/Reliability
Testea cuán bien el sistema se recupera de caidas. Desde un “segementation fault” en el servidor, hasta fallas de hardware, pasando por fallas de internet o de otros sistemas externos.
Testea que ante estas eventualidades el sistema quede en un estado válido
Testea que el sistema se recupera (si puede) en tiempo y forma
Volume
Testea que el sistema sigue andando bien (como en baseline) si su base de datos es grande, si ya tiene un manejo de un alto volumen de datos
Documentation
Testea que el sistema es fácil de entender a partir de la documentación, que la documentación es completa y correcta.
Internationalization
Testea que el sistema se puede adaptar a diferentes culturas e idiomas.
Accesibilidad
Testea que las interfaces cumplen las especificaciones y criterios de accesibilidad (que se mapeen bien a una interfaz oral, que las imagenes tengan bien su “alt text”, que sea navegable por teclado, etc)
Tests de features definidas y tipo de producto
Normalmente entendemos que un test prueba que una funcionalidad está presente y que funciona de forma correcta
Sin embargo, existen sistemas que tienen “versiones”. Hay sistemas que el conjuntos de funcionalidades que ofrece varían según el cliente, el plan de pago, la subscripción o algún otro factor
En estos casos vamos a querer testear la inversa: que una funcionalidad NO esté presente en el sistema, que NO funcione de forma correcta
Un ejemplo de esto es cuando tengo dos sistemas, la version Demo y la version Enterprise
Esto nos empieza a mostrar que un test y probar que una funcionalidad está definida (o no) son dos cosas diferentes
Unit tests
Esta es otra mala definición, hermana de “tests de integración”
A veces los tests de integración se definen como “cuando no es de unidad”
Los tests de unidad se definen como que testean sólo una unidad
Pero no definen qué es una unidad. ¿un objeto? ¿un método o función? ¿una jerarquía? ¿un paquete/módulo?
Si bien no nos parecen útiles este tipo de tests, o al menos esta clasificación, sí encontramos casos donde uno quiere testear en más detalle un objeto o pequeña familia de objetos bien cohesivos.
Estos casos suelen ser cuando tengo algo intensivo en cálculo. Un ejemplo es testear el cálculo del camino mínimo sobre un grafo. O un objeto que calcula los intereses de un capital según la cantidad de días hábiles que pasaron. Es decir, objetos o conjuntos de objetos que realizan cálculos complejos.
Entidades que nos aparecen
A medida que testeamos vamos a empezar a resolver problemas propios del mundo del testeo, pero que no son particulares de mi negocio
Mocks, o test doubles
Estos objetos me sirven para reemplazar objetos que estarían en producción, pero no los puedo tener durante el testeo. Típicamente, servicios externos.
Factories
Estos objetos me sirven para crear objetos de mi negocio, o a mi negocio como un todo, para utilizarlo durante los tests
Hay más objetos, que van a ir resolviendo diferentes problemas. Podemos pensar que habrá objetos que nos van a sacar código repetido, van a modelar cuestiones que se repiten en cada una de las tres secciones del test: Given, When, Then.
Las Factories, por ejemplo, resuelven la repetición de código que se produce al establecer el escenario inicial del test, el estado inicial de mi negocio, en el Given.
Análogamente, nos pueden surgir objetos que representen todo aquello que se me repite en el When y en el Then.
Organización de tests como enseñanza del sistema
Los tests pueden ser un buen lugar para empezar a entender o ver ejemplos de uso de los objetos de mi sistema.
Están organizados de menor a mayor complejidad
Tenés código que anda y usa al sistema para una accion específica
Viendo todos los tests deberia saber/aprender todas las cosas que sabe hacer el sistema
Viendo todos los tests, potencialmente, podria saber usar a todo el sistema
Son insuficientes para enseñar el negocio y el sistema, pero ayudan o complementan.
No te dan visibilidad de efectos secundarios, o de qué grandes circuitos se pueden realizar en el sistema o grandes invariantes que se mantienen
Se pueden enumerar u ordenar de forma tal que se puedan leer en orden de complejidad.
¿Qué significa testear mi app?
Testear mi app es otro sistema, es otro mundo:
Mi sistema modela un negocio. Su responsabilidad es la de realizar las funciones que pide el cliente, que dan valor, etc.
¿y los tests? ¿qué hacen? ¿Qué responsabilidades tienen? ¿Cómo se relacionan esas responsabilidades con las del negocio que estoy modelando?
Los tests son “algo”, son código que agarra al sistema que estoy creando y lo “cachetea” para ver si anda bien.
El valor que me dan los tests es bien diferente del valor que me da el sistema con el cual implemento/modelo a mi negocio.
Por lo tanto… es otro sistema. Porque tiene otro propósito, provee otro valor.
Eso significa que también el sistema de testing va a modelar otro dominio: el dominio del testeo.
Temas que no vemos
(se ven en talleres o capacitaciones más adelante)
Tests de actores
Escenarios, interlocutores y asserters/propiedades/aspectos
Formas de organización de los tests (ej: épicas cronológicas)
Tests y la enseñanza/aprendizaje del negocio
Metodologías ágiles
2 hs
¿Qué son?
Son formas de organizarnos para llevar adelante un proyecto. Son ágiles porque la idea es que podamos adaptarnos a los cambios de la realidad, del negocio, del cliente.
¿Por qué/para qué?
En nuestra experiencia (y la de otros en la industria) pactar un proyecto, un producto, un sitio, lo que sea, ir a hacerlo y volver cuando está terminado no anda. Siempre existen divergencias entre lo que el cliente quiere y lo que vos le das. Además, a medida que pasa el tiempo el cliente puede ir cambiando lo que quiere y a medida que el cliente tiene cosas concretas que pidió, eso le hace cambiar lo que quiere también. Entonces necesitamos ciclos más cortos de hacer algo, entregarlo, que el cliente lo vea, y que nos pida algo mas.
¿Cuándo se aplican?
Entorno dinamico cambiante (si se aplica)
Típico caso: el cliente puede cambiar lo que quiere, las necesidades cambian, el contexto/entorno donde va a correr el sistema cambia, el mismo negocio que el sistema va a modelar va cambiando.
Entorno estático predecible (no se aplica)
Este es un caso rarisimo. Donde lo que hay que hacer está fijo, no va a cambiar, es decir, lo que queremos que haga la aplicación y el contexto que la rodea es algo constante. Esto puede ser para algoritmos, o rehacer un sistema.
Así y todo, separar en partes más chicas, e ir dando y mostrando avances es enriquecedor, suma.
Por lo tanto, no se me ocurren buenos ejemplos de este tipo de escenario.
Entorno incierto, investigación (no se aplica)
Si se está desarrollando algo nuevo, que no se sabe cuánto va a tardar, o no se saben a ciencia cierta todos los pasos que hay que hacer, básicamente cuando estamos investigando.
Kanban vs Scrum
Tenemos dos modelos de trabajo: hacer cosas que se planifican o ir respondiendo a medida que surgen las cosas
Kanban es para cuando tenemos que ir respondiendo a cosas que surgen. Un equipo que atiende tickets de soporte por ejemplo, trabajaría con modalidad Kanban.
Scrum es para cuando tenemos una lista de tareas para hacer, hitos que alcanzar (entregables, releases, etc) y necesitamos ir viendo el progreso que se está logrando en esa línea
Kanban
Típicamente tenés 3 columnas: para hacer, haciendo, hecho. Podés tener menos, es normal tener más.
También es típico tener prioridades en las tareas que entran en “para hacer”.
Límite de cuántas cosas pueden estar en “haciendo” o cualquier estado intermedio entre “para hacer” y “hecho”. Esto es para limitar tomar tareas, avanzarlas y no terminarlas y para detectar cuellos de botella o trabas. Y básicamente explicita nuestro “ancho de banda”, nuestra capacidad para resolver problemas.
Tomar tareas de derecha a izquierda. Es decir, tratamos primero de terminar las tareas que le faltan poco y solo después de haber avanzado o terminado esas tareas, tomamos las que les falta más. Esto es para minimizar el “throughput”, es decir, el tiempo en que una tarea tarda en hacerse.
Todo esto se va ajustando de acuerdo al equipo, las capacidades que tiene, etc. Pero la clave es limitar las tareas que hay en las etapas intermedias y priorizar terminar tareas.
Scrum
La idea es hacer un desarrollo grande, largo, en pequeñas “carreras” (sprints).
Empezas con un backlog, con una lista de tareas, y el equipo elige cuántas de esas tareas se va a comprometer para hacer en un sprint.
Un sprint dura entre 1 y 3 semanas, normalmente 2, conviene que sea lo más corto posible
Las hace, las muestra al cliente y se repite el ciclo de elegir tareas del backlog para el siguiente sprint
El cliente define el backlog y el orden de importancia, el equipo elige cuántas tareas del backlog tomar para un sprint
Entre un sprint y otro es importante:
demo, donde el equipo muestra lo logrado en el sprint
retro, donde el equipo reflexiona sobre el sprint y plantea mejoras
planning, donde el equipo elige qué y cómo hacer en el próximo sprint
Separando desarrollo de entregas y relevamiento: Sprints y Backlog grooming
Notar que hay tres flujos de trabajo:
Crear y mantener el backlog (backlog grooming)
Desarrollar las tareas del sprint
Utilizar y dar soporte a lo desarrollado (lo que el cliente hace con lo que le damos y el soporte de errores o temas que nos pueda llegar durante el sprint)
El backlog grooming se hace con una parte del equipo (ver más abajo) y con el cliente durante el sprint
El soporte a lo desarrollado se hace con una parte del equipo respondiendo consultas y/o resolviendo tickets de soporte durante el sprint
Para esto el equipo tiene en cuenta la carga que le va a insumir, y de acuerdo a eso calcula cuántas tareas tomar
Roles: Product Owner, Scrum master, dev, tester, ux, analista
En el equipo de scrum se distinguen tres roles importantes
Desarrollador: programador, tester, UXer, analista, cualquiera que contribuya a hacer realizar las tareas
Scrum master: un miembro del equipo que se dedica a organizar el sprint, los rituales y a destrabar problemas que el resto encuentre: faltan unas credenciales, hay que pedir unas imágenes que faltan, etc
Product owner: un miembro del cliente dedicado al proyecto (full time o part time). Su tarea es definir el backlog (hablando con gente del cliente), priorizarlo, emprolijarlo (grooming) con alguien del equipo, responder dudas sobre las tareas y convocar a la gente relevante para la demo
A veces el rol de scrum master es fijo, a veces rota
Rituales
Los típicos rituales de scrum son:
Planning: se eligen cuántas tareas hacer, se repasan para entenderlas mejor y detallarlas más si hace falta, cada miembro del equipo elige en cuál participar
Daily: se elige un momento corto del día en el cual el equipo se sincroniza, cuenta cómo van las cosas, qué tiene planeado hacer, qué hizo y si tiene algún bloqueante (que otros miembros o el scrum master ayudarán a destrabar). La idea es que sea algo corto, de 15 minutos, se suele hacer con todos parados para incentivar a que sea corta.
Demo/Revisión: se le muestra al cliente (product owner y otros stakeholders que considere) lo alcanzado. Se toman críticas, observaciones, modificaciones que se puedan incorporar en nuevas tareas en sucesivos sprints.
Retro: el equipo se junta y reflexiona sobre el sprint que pasó, cómo están trabajando, qué pueden mejorar y se definen accionables para lograrlo
Además está al “backlog grooming”, que se trata de meter tareas en el backlog, priorizarlas y detallarlas lo suficiente para que se pueda hacer una planning con ellas
Variantes
Scrum se puede adaptar a cada equipo, proyecto o cliente.
Los distintos rituales se pueden modificar, algunos usan todos los rituales, otros usan sólo algunos, a veces no se hacen demos porque apenas se desarrolla algo se hace la demo de eso a quienes les interesa, o la daily se hace cada dos días y así, un sinfín de variantes.
Los mismos rituales tienen variantes de cómo se pueden llevar a cabo
Scrum of Scrums
Scrum tiene sentido para células de entre 3 y 8 miembros
Si se tienen proyectos o equipos de más personas pueden empezar a aparecer más de una célula de scrum
Eventualmente éstas células van a necesitar algún tipo de coordinación y para eso surge el scrum of scrums, donde los scrum masters de cada célula scrum se juntan y coordinan
La idea no es detallar esto, sino saber que se puede escalar con esta metodología y que ya existe algo pensado.
Herramientas
1/2 dia
La idea es que el ingresante entienda y tenga un manejo básico de las herramientas y tecnologías que va a usar en el día a día.
Git
Queremos asegurarnos que el ingresante entienda que es una rama, y que entienda las distintas estrategias sobre cómo usarlas
Organización básica en ramas
Validar lo que se aprendió y explicar típico manejo de ramas
Trunk based development: muchos commits chicos a main, las ramas creadas, si las hay, viven poco, no más de un dia
Una rama por card/feature. No duran más de un sprint.
Pycharm/IntelliJ
Dar una explicación básica de la IDE
Menu VCS/Git
Abajo a la derecha: rama actual, cómo administrarlas
Uso de runners, mostrar el composite runner
Project structure, cómo ver una clase, sus métodos, sus variables de instancia y cómo filtrar para no ver cosas de más
IDE Ninja Talk 01
IDE Ninja Talk 02
IDE Ninja Talk 03
IDE Ninja Talk 04
Unix
¿Qué es el shell? (nivel básico)
Interpreta línea de comando, hay varias variantes de shell, convierte lo que tipeas en acciones
No todo lo que tipeas se traduce en algo que el shell hace o ejecuta, hay cosas que le dicen al shell cómo interpretar lo que estas tipeando:
las comillas para ignorar espacios
los pipes | para encadenar stdins con struts
los > para mandar a archivos
los $ para ejecutar en background
Procesos y programas
Se levantan de disco y se ponen en memoria
Se linkean y cambian referencias
Se le setean variables de entorno, running directory
Se le define el stdin, stdout y stderr (el shell define esto con los programas que tipeamos/le decimos que levante)
Pero el shell es otro programa mas, que levanta otros programas hablando con el SO
Nuestros programas pueden hacer lo mismo, levantar a otros programas diciéndole al SO cómo enrutar sus entradas y salidas, qué variables de entorno tiene, etc.
Código de retorno, errores
0 es éxito
distinto de 0 es error, y el número es el código de error
Esto es una convención ¿o es parte del estandar POSIX?
stdin, stdout, stderr
¿Qué son?
¿Para qué sirve cada uno?
¿Cuándo usar cada uno?
Pipe de procesos
Qué es un pipe
Qué hace el SO con los procesos y los standard streams
Ejecución sincrónica
Idea de Unix: componer programas chiquititos
Spawn de programas/procesos, árbol de procesos
Explicar relación y dependencia entre procesos padre e hijos, qué pasa cuando un proceso padre muere.
Shell como programa dentro del árbol de procesos
El shell es un programa mas que suele hacer de padre
Comprobar cómo el shell se comporta y cumple esto de ser un proceso padre:
Levantar un gedit desde la terminal, ver cómo se bloquea y luego de cerrar gedit vuelve el control a la terminal
Qué pasa cuando lanzamos gedit con &
Variables de entorno, alcance (scope)
¿Pero si las variables son de un proceso cómo es que el shell que es un proceso le puede definir variables a otro proceso que spawnea, si es otro proceso?
Exportación de variables de entorno
cron job: qué es, cómo se usa, para qué sirve
root y usuarios
Nunca usar root
Permisos: all, group y owner
Permisos: read, write, exec -> files y directories
Stack del proyecto - Django
4 días
La idea de esta parte de la capacitación es enseñar el stack del proyecto y cómo diferimos de cómo desarrolla la comunidad o industria. La estrategia que usamos es mostrar y enseñar como lo hace la industria (normalmente a través de sus tutoriales), analizar y criticar lo hecho y empezar a construir una alternativa, una mejora a lo hecho hasta que se vea mas o menos la dirección (Appyx normalmente) y ahí cortar. No es la idea enseñar Appyx. Sólo las bases y que el resto lo aprenda en proyecto o alguna capacitación puntual.
Hacer tutorial
Hacer
este tutorial
.
Mientras el dev hace el tutorial sin estar acompañado, la idea es todos los días juntarse y ver los avances. También es deseable que el ingresante pregunte cualquier cosa que no entienda.
Como muchos no tienen la iniciativa de preguntar (por mil motivos que no vienen al caso) sugerimos hacer una puesta en común al final de cada día.
A continuación listo algunos ejemplos de temas del tutorial que se pueden querer evaluar, profundizar o que el ingresante puede preguntar. El orden es el mismo en el que están en el tutorial y en el que el ingresante los encontrará.
Nota: evitar que al final de la parte 4 convierta el código a generic views porque después vamos a querer que deshaga eso y hacer eso no aporta mucho
¿Qué está pasando cuando hago startproject?
Explicar que está creando carpetas y archivos python (y de línea de comando) que tienen lo básico de un proyecto (más adelante vamos a ver qué hace cada cosa)
Explicar por encima lo que es creación automática de código.
¿Cómo se entiende la línea python manage.py runserver ?
Reforzar o testear conocimientos de línea de comando
manage.py es un script de python
¿Cómo le llega “runserver” al script si es un parámetro de “python”?
explicar cómo manage.py tiene el rol de “script de mantenimiento” de un proyecto django
Recorrer cada archivo del proyecto e indicar qué hace, para qué sirve
Cómo es la arquitectura de urls.py y crear views y declararlas ahi
Notar que es estático
Pero son objetos, se podría calcular dinámicamente
¿Uso del include()? ¿explicarlo?
Explicar cómo django tiene una parte oculta que procesa la url del request entrante y usa estos objetos/datos para rutear ese request y mandárselo a la view/código nuestro correspondiente. Unirlo a las charlas anteriores de cómo funciona un server
Qué es una app en Django
Son código+persistencia+urls
Django considera que existen unidades funcionales de urls, con su código y su persistencia que son plausibles de ser reutilizadas en diferentes proyectos
En la práctica esto no nos sucede ni sucedió nunca, no las usamos
Explicar migrations
Resuelve el cambio de schema de una DB cuando hay datos en ésta que hay que preservar
Contar los casos típicos: agregar una tabla, agregar una columna con default constante, agregar una columna con default calculado
Separar una tabla en dos tablas
¿contamos que las migraciones se corren cada vez que se ejecutan los tests?
Model
Explicar que nuestros objetos deben heredar de models.Model
NO cuestionar esto, sólo contarlo, sin sesgos, sin poner caritas ni tonos de voz
Cualquier crítica o cuestionamiento del ingresante tomarlo de forma estoica sin tomar postura
Mostrar cómo declarando las variables de instancia del Model de forma un poco más “costosa” consiguen dar suficiente información como para que Django sepa cómo mapear el modelo de objetos a tablas en una DB relacional
Notar cómo Django es minimal
Notar que Django en este proceso te pidió el mínimo de información posible
No te puede pedir menos información para crear el tutorial
Esto es algo muy deseable en un diseño y en cualquier solución que construyamos
No ser minimal es un “smell” de que hay algo que falta modelar, o hay una responsabilidad mal asignada
Django admin
Si tenemos toda esa información declarada para poder entender cómo se mapean nuestros objetos a la base de datos, entonces podríamos tener un CRUD (Create, Retrieve, Update, Delete) generado automáticamente, no? Eso es el Django admin
Históricamente lo hemos usado como backoffice, no lo hacemos más por los problemas que provoca
Se puede customizar bastante, pero siempre hay un límite. No escala.
Hay código que le va indicando a django admin qué exponer, qué permitir editar, etc. Igual no escala.
Views
Asegurarse que entienda el sistema de rendering con templates: pseudo html en un archivo por fuera, código embebido, el código embebido hace referencia a “algo” que le pasan por parámetro y la estrella que brilla por su ausencia: un motor que interpreta este pseudo html e invierte el archivo, de html con código embebido a código que ejecuta y en el medio escupe strings
Contar que esto es server side rendering
Si da, contrastar con client side rendering (enviar solo datos, por ejemplo con una API REST)
Asegurarse que se entienda la función get_object_or_404(), mostrar las bondades y todo lo que te ahorra empaquetando un montón de cuestiones en una sola función. Notar cómo reacciona y qué postura tiene ante esto. Eso nos va a indicar qué hay para trabajar más adelante. De nuevo, ser objetivo y no emitir opiniones sobre esta característica de Django.
Explicar el reverse, cómo funciona, qué hace, cuándo y por qué usarlo, cómo se relaciona con el name del path en urls.py
Explicar cómo se declaran los parámetros en el path y cómo esto se traduce a que en la view ya te venga un parámetro con ese nombre que el router se encargó de extraer de la url y pasártelo como parámetro
(opcional) explicar el mecanismo de python de **kwargs que es usado para esto
(opcional) explicar qué es el csrf token, qué tipo de ataque evita
Generic views: si bien no las va a implementar/usar, explicar qué problema resuelven.
No las queremos usar porque después vamos a refactorizar el código y refactorizar para meterlas para después hacer el refactor inverso es engorroso
(opcional) hacer que el ingresante convierta las vistas en generic y luego las deshaga, para entender en mayor profundidad cómo te ahorra laburo y después te ata de manos cuando querés hacer algo superador. Nota: en vez de borrarlas o modificarlas, crear unas views paralelas que simplemente no referenciamos en urls.py, así podemos volver atrás y usarlas. De paso practicamos cómo refactorizar código que convive todo en master sin que se rompa nada.
A partir de acá queda por explicar Django Admin. ¿les parece que vale la pena explicarlo?
Analisis y critica
En esta parte de la capacitación, que depende mucho del nivel del ingresante, queremos empezar a señalar malas prácticas de la industria. Pero a través de problemas concretos que se ven en el código sugerido. La idea va a ser ir por el camino de menor resistencia siempre: mirar el código, criticarlo, ver qué problemas tiene, refactorizar y volver a esta etapa.
¿Qué opina el ingresante de los métodos creados en el tutorial? ¿Qué opina el ingresante de que en una misma función/método se hable de http, reglas de negocio y base de datos?
¿cómo meto lógica de negocio?
¿cómo hago si se repite código en distintas Views?
¿cómo me abstraigo de la DB y meto cosas en memoria o en un archivo plano de texto?
¿cómo comparto lógica o comportamiento de cómo procesar cualquier request?
Refactor
Separar http de no-http
Separar errores de negocio de errores de tecnología
Separar lógica de negocio
index: la lista de preguntas que te las de la PollingStation (el Business)
Refactorizar cada endpoint en sus componentes. Nos deberia quedar un template method idéntico para cada View. Las partes de ese template method deberían corresponderse más o menos a esto:
extraer parámetros
crear ¿y parametrizar? la interaction
procesar el resultado de la interaction, generando el content, aplanandolo
calcular el status code
Introducirnos en cómo desarrollamos en Eryx
Como parte de esta parte de la capacitación queremos mostrarle al ingresante que analizando y reflexionando sobre el código y sobre el problema se pueden crear soluciones superadoras.
En este caso en particular, hay cosas que suceden todo el tiempo y las modelamos.
Queremos mostrar que en Eryx vamos mas allá de lo que recibimos. No nos quedamos con lo que nos dice o da la industria. Lo miramos, lo analizamos, lo criticamos y hacemos algo superador.
Ese es el espíritu de esta parte.
De paso, vamos mostrando que estas abstracciones que se empezaron a ver, que empezamos a modelar ya fueron iteradas en nuestra biblioteca Appyx.
Result
Se deduce que hay partes del código (la interaction) que van a devolver a veces un objeto útil y a veces van a tirar una excepción. Y siempre vamos a tener que manejar todo eso
No es fácil de manejar genéricamente excepciones, o delegarlas
Conviene tener un manejo homogéneo de estas cuestiones
Así es como debería surgir la idea de Result
Interaction
La aplicación es muy chica todavía para justificar la interaction por el lado de que desde varios lados se usa la misma. Aunque se le puede agregar al tutorial alguna funcionalidad por línea de comando
Sí debería quedar claro que no tiene sentido mezclar extracción de parámetros del request con la acción que quiero hacer en el negocio
Facilita el testeo si tengo algo que ejecuta lo que quiero, pero sin necesidad de un request, tan sólo pasándole los parámetros que necesito
Esconder o parametrizar la base de datos está lejos todavía, pero algunos ingresantes pueden llegar a verlo o parecerles interesante
Estas son las razones que se pueden esgrimir y trabajar para que surja la idea de interaction en este tutorial
Pueden surgir más abstracciones que tenemos en Appyx (sería raro que surja alguna que no tenemos ya en Appyx). Acá el capacitador tiene que estar atento y aprovechar (o dejar pasar) la oportunidad
Anatomía de un handler de POST
A continuación se describe la estructura lógica (separada en 3 párrafos) a la que se debería converger en la View/http handler que estamos trabajando en el tutorial y en su posterior crítica.
Esta descripción es para poder señalar responsabilidades que a futuro van a surgir y que se pueden encontrar en Appyx.
1er párrafo
Este párrafo se encarga de extraer y validar parámetros, hacer una primera validacion del request.
Validación básica de parámetros
Chequeo de parámetros obligatorios
Extracción de parámetros del request
URL
Request Body
Request Headers
FILE (en headers)
Cookie + Session + User
Cookies
Si en el request no viene una cookie (no visitó nunca la página de zapas) entonces:
el server crea una cookie (unívoco)
En una tabla agrega una entrada para la cookie mete una nueva entrada: cookie_nueva: []
Si estoy viendo una pagina de un tiem, lo agrego al historial de compra y actualizo la entrada del diccionario
Cuando genero la página de respuesta, le agrego el pie de página con los últimos 10 items del historial
la agrega a la response del request.
Si en el request viene una cookie
Va al diccionario “principal” y obtiene el historial de compras asociado a esa cookie (diccionario[cookie]).
Si estoy viendo una pagina de un tiem, lo agrego al historial de compra y actualizo la entrada del diccionario
Cuando genero la página de respuesta, le agrego el pie de página con los últimos 10 items del historial
Cuando esta request saliente del mismo cliente vuelva a conectarse con la página de zapas (server) entonces la request se comunica con el server mediante el numero de cookie que ya tiene creado.
It was all about SESSIONS!
Se guarda en disco esta información
En los browsers (clientes modernos), se tiene un historial por cada cuenta / usuario.
2do párrafo
Este párrafo lidia con mandar el pedido para adentro, hacia el negocio.
Transformar parámetros del request en objetos de dominio. O guarda un error si no puede transformarlos.
Yields to the business the computation of the request
Algo más…
3er párrafo
Esta párrafo lidia con el protocolo. Crea la respuesta,cumpliendo el protocolo, en base al resultado recibido.
Setea el status code de acuerdo a cómo salió la respuesta
Setea el contenido a devolver, si es que hay contenido a devolver
Redirige el request, de ser necesario
Bases de datos
1/2 dia
Comienzos: archivos
¿Como haría para persistir algo si no existieran las bases de datos?
Como hacían antes: archivos. Y archivos de texto.
Voy agregando líneas. Cada línea representa un elemento que meto.
A priori en un archivo meto todos elementos del mismo tipo (decisión arbitraria).
La línea va a tener un formato, el más sencillo es que cada campo tenga una longitud fija.
Para levantar los objetos persistidos, leo todo el archivo en memoria.
¿Cómo resuelvo la búsqueda, referencia, agregado y borrado con este esquema?
¿Y si la longitud de los campos es variable?
¿Y si agrego ID?
Recordar que los sistemas de archivos permiten acceso secuencial y aleatorio arbitrario.
¿Cómo puedo acelerar la lectura cuando busco por un campo en particular? (indices)
¿Qué costo tiene esto? (ver impacto en agregado y borrado)
¿Cómo hago cuando un registro quiere referenciar a otro?
Mostramos cómo se va generando código repetido, o cómo se van repitiendo problemáticas que trascienden al modelo de datos en sí. No son de mis datos, sino de esta forma de persistir.
Relacional
A lo anterior le agregamos varias funcionalidades y capas
Aislamos los archivos e índices, ahora las DBs pueden tener índices o tablas en memoria, pueden reordenar, compactar, saltear el file system, etc sin nosotros enterarnos
Tienen una API: DLL/SO local, remoto vía red
Lenguaje de query: SQL
Meta data: database schema
Usuarios y permisos
Concurrencia
Transacciones
Aparece el término “foreign key”, pero que ya existía
De objetos
No usa tablas
Define un conjunto de objetos raíz
Se levantan con proxies
No hay indices. El llegar eficientemente a un objeto tiene que estar dado por el modelo.
Pero podés hacer índices si querés o si tenés colecciones muy grandes.
Cuando cambiás un objeto tenés que decirle que lo persista.
Las ODBs mas avanzadas detectan el cambio via la VM.
Los objetos se levantan y viven en el contexto de una transacción.
Todas las técnicas que usan las ODBs son exactamente las mismas que nosotros usamos cuando nos empezamos a aislar de una RDB.
De documentos
Sirven para guardar y gestionar información que normalmente no está generada por nosotros, pero de la cual somos dueños
Ejemplos: jsons que vienen de otro sistema, PDFs de una publicación hecha hace tiempo o por otro, escaneos de documentos físicos, mails, etc.
Por defecto a todos los documentos se les generan campos “meta”, que son independientes del formato interno: fecha de creacion, modificacion, tamaño, tipo de doc (terminación) y algunas extra dependiendo de si tienen formatos estandarizados como PDF
Se definen propiedades, campos sobre estos documentos, que van a ser el equivalente a campos de un registro. Pero puede que necesitemos definir funciones específicas para obtener ese campo a partir de un documento (porque el documento es algo arbitrario externo)
No tiene esquema
Algunos las usan justamente por no tener esquema, pero eso es un error, para eso podes usar otras como objetos o key/value
Key/value
La podes pensar como una base de objetos con mucha menos funcionalidad
La idea es que la key y el value sean arbitrarios tuyos
Son muy eficientes porque hacen algo muy limitado
La relación entre un registro y otro lo tenes que inventar vos. Es decir, si un value adentro referencia o apunta a otro key o varios key, eso lo tenes que implementar/inventar vos
Sirve cuando querés mucha eficiencia y estás dispuesto a pagar el costo de implementar todo lo extra que necesitas a una DB
Rol de absorber concurrencia y distribución
Dado que las bases de datos relacionales resuelven la concurrencia y distribución de datos nosotros no tenemos que programar esas cosas.
Pero eso nos limita a lo que las bases de datos hacen o pueden hacer.
[ejemplo de cuándo no podemos ignorar la concurrencia y tenemos que intervenir o puentear el manejo de concurrencia de la DB]
Transacciones ocultas (y transacciones)
Toda operación sobre una DB es transaccional. Es decir, ocurre en el contexto de una transacción. Incluso si estamos hablando de archivos. Los archivos, a nivel filesystem usan transacciones.
En los sistemas que solemos programar en ningún momento aparecen las transacciones ni sus productos secundarios: commit, abort, etc.
Lo que sucede en nuestros sistemas es que cuando atendemos un request, se abre una transacción y cuando se da la respuesta se hace commit. Incluso si hay un error hay que ver si no se hace commit también. No lo sé y depende del framework web.
Ejemplo de cuando no podemos ignorar las transacciones:
Como el commit se hace luego de responder el request
Y como necesito que alguien externo lea mi estado durante mi ejecución
Entonces necesito hacer commit ANTES de terminar de procesar el request
Un caso concreto surgió en un proyecto de Eryx donde disparábamos un evento que alguien desde afuera iba a propagar, entonces necesitaba leer los datos nuestros, pero para leerlos de forma consistente necesitaban estar en la base de datos (al ser un sistema externo los iba a leer con una nueva transacción, y como la actual no había terminado, no los iba a ver)
TDD
1 dia
Acá estoy suponiendo que el ingresante ya hizo TDD y la idea es refrescar la técnica y “afilarlo” en la misma. En caso de que NO haya hecho TDD la idea es introducirlo a ésta para que pueda acompañar al equipo e ir profundizando durante su trabajo en un proyecto. O de última con una capacitación aparte luego de ingresar al proyecto.
Empezamos modelando un solo objeto con TDD: un contador (que la haces click y suma uno)
Deberíamos tener tres tests: inicial en cero, si hago click me da 1 (hardcodeado) si hago dos clicks, da 2 (rompo el hardcodeo)
Eliminamos los dos últimos dos tests y recordamos la diferencia entre caso de test y dato de test
Hacemos el ejercicio del calendar de la materia.
Docker
3 horas
El objetivo es dar una introducción básica a los conceptos de Docker, Docker Compose y los entornos de desarrollo usando contenedores.
Empezamos
con una intro:
¿Qué es Docker?
VMs vs Containers
Dockerfile, Imagen y Container
Build por capas
Levantar container con la imagen de
hello-world
. Explicación pasos.
Mapear puertos y montar volúmenes
Comandos (build, run, exec, ps, etc)
Push/pull de imágenes con Dockerhub
Luego, hacemos un ejercicio práctico para mostrar los conceptos anteriores. Creamos una imagen para levantar un servidor FastApi:
Arrancamos con una imagen base (ej: python:3.10). Levantar y correr algo en el container
Agregamos requirements.txt con fastapi[standard] como dependencia. Build y run del container para probar
Agregamos un main.py como muestra el
tutorial
de FastAPI a la imagen
Ver como funciona el mapeo de puertos y montar volúmenes
Etiquetar la imagen y subirla a Dockerhub
Correr un container con la imagen desde Dockerhub desde otra máquina. Introducir un poco la idea de deployear a partir de esto
Docker Compose
Comentar que existe para levantar múltiples containers que hablen entre sí. Mostrar cómo se puede desarrollar sin instalar las dependencias locales (o en un virtualenv) usando el container como intérprete remoto.
Levantar un proyecto en IntelliJ + Docker Compose
Nota:
el documento original de esta capacitación, junto con temas que se excluyeron de este temario se puede encontrar
en este link
.

wiki/Tablero de Seguimiento.html
Wiki-eryx - Tablero de Seguimiento
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Tablero de Seguimiento
En los siguientes links se encontrarán los tableros de seguimiento de las reuniones de Mentoring y la de los eMe.
Deberás completar la cantidad de veces que te juntaste con tu
mentor
en el mes del semestre en que lo hiciste en la siguiente planilla:
TABLERO DE SEGUIMIENTO DE REUNIONES DE MENTORING
Deberás completar la cantidad de veces que te juntaste con tu
eMe
en el mes del semestre en que lo hiciste en la siguiente planilla:
TABLERO DE SEGUIMIENTO DE REUNIONES eMe

wiki/La Cartuchera del ery.html
Wiki-eryx - La Cartuchera del Ñery
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
La Cartuchera del Ñery
El manifiesto
Valores, principio, prácticas y heurísticas
Presentación de la cartuchera
(Videos)
Dudas y debates en
#la-cartuchera-del-ñery

wiki/Cabify.html
Wiki-eryx - Cabify
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Cabify
Servicio de transporte
Diferencial del servicio personal:
Tarifa plana que no depende de la demanda!
Cabify tiene cobertura en
Argentina, Chile, Colombia, España, México, Perú y Uruguay.
Viajes personales
Al momento de transferir los retiros, se hara el ajuste correspondiente por el monto del gasto mensual en Cabify personal del mes anterior.
Viajes laborales
Si llegás a hacer un viaje que pensás que Eryx debería pagar (si llevaste materiales pesados a una actividad por ejemplo) corre por cuenta de la Coope.
Al finalizar el viaje y teniendo el valor del mismo, debes completar el
siguiente form
para poder reembolsartelo. Elegís la categoría "Cabify Laboral" y llenas de la siguiente manera:
Forma de pago: tarj credito visa Eryx
Tipo de comprobante: factura A
Fecha de comprobante: el dia del viaje
Monto total abonado y sin iva: el valor del viaje.
Detalle de la compra: para qué se uso
Adjuntar comprobante: NO se adjunta nada
Socio: indicar nombre de quién lo uso.
Identificar
NO cancelado
para que no se le descuente del retiro.

wiki/Oficina.html
Wiki-eryx - Oficina
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
Oficina

wiki/Compras.html
Wiki-eryx - Compras
Skip to main content
Skip to navigation
Wiki-eryx
Inicio
Eryx
Team eryx
Historia Eryx
Cumpleaños de Eryx
¿Qué servicios ofrecemos?
Visión, Metas y Valores
Documentación Coope
Cooperativismo
¿Qué son las comisiones? ¿Qué hacen?
Toma de decisiones
Nuestros rituales y asambleas
¿Qué son las áreas? ¿Qué hacen?
Ñeryx
Legajos
Reglamento interno
Derechos y obligaciones
¿Qué licencias tenemos?
Retiro
Complemento de ADE por hijo
Tarjetas precargadas
Beneficios
Bonos mensuales
Comida del mediodía
Cabify
Prepaga: Plan OSDE 210
Beneficios autogestivos
Programa de Desarrollo Profesional
eMe
Pool de Mentores
Asignaciones eMe
Feedbacks
Guía Mentoring
Tablero de Seguimiento
Política de compra/recambio de laptops
Cartilla Eryx
Eryx Recomienda
Herramientas
Servicios Digitales
Slack
Loomio
Mostro
Oficina
Internet
Alarma
Ingreso Oficina
Cerrar Oficina
Impresora
Inventario
Protocolo de incendio
Biblioteca de libros
Procesos
Bajo desempeño
Compras
Compras personales en el exterior - TC Eryx
Pasos a seguir ante una licencia
Pedidos a Oficina
Chequeo médico anual
Facturación
Actualización de info
Capacitación de ingresante a dev
Contactos
Cartuchera
La Cartuchera del Ñery
Criptografía
CryptoHacks
Template de proyecto
Diseño a la gorra
Proyectos
Grant Aiken ZK
muun
FAQ cripto
Wiki-eryx
¿Que hacer ante una compra?
Si sos comprador asignado de un área o realizaste una compra a nombre de Eryx estos son los pasos a seguir:
1)
Controlar el presupuesto en
este link
desde la hoja "Nueva Visualización".
2
)
Efectuar la compra y asegurarse de tener el comprobante correcto.
SIEMPRE necesitamos factura o ticket que justifique nuestras compras.
Las facturas/tickets pueden ser letra A (en caso de que el proveedor este inscripto en Iva); o C (en caso de que el proveedor sea monotributista).
NUNCA recibir factura B.
Cada factura/ticket debe contener una serie de datos tanto del proveedor a quien estamos comprando como de Eryx.
Nuestros datos son los siguientes:
- RAZON SOCIAL: Cooperativa de Trabajo ERYX Ltda
- CUIT: 30-71593725-1
- CONDICION FRENTE AL IVA: Responsable inscripto
- CONDICION FRENTE A GANANCIAS: Exentos
- CONDICION FRENTE A IIBB: Exentos
- DOMICILIO COMERCIAL: Bonpland 1953 - CABA. CP 1414
- TELEFONO: (011) 4774-5604
En caso de que el proveedor solicite nuestros certificados de exención de impuestos, son los siguientes:
Ganancias:
https://drive.google.com/file/d/1NSFTSvwymOgjD9DEo0E7hv-9IpiiVp_6/view
IIBB CABA:
https://drive.google.com/file/d/1ozXycJfhHMP2O0bzsEsrBeIx3mfBU-ii/view
IIBB PBA:
https://drive.google.com/file/d/1w-neu9tZmAhWurley7SvOgIk6DQ2rGxl/view
3
) Efectuar el pago
.
Existen distintas formas de efectuar el pago de facturas:
Efectivo:
actualmente la coope no esta realizando extracciones bancarias, por lo que en caso de usar este mecanismo , el asociade haría uso de su efectivo y luego debería solicitar el reintegro (explicado en el punto 4).
Transferencia bancaria:
Si elegiste esta opción, te pedimos que completes el siguiente
FORM
con los datos necesarios para que Admin pueda darle curso. Las transferencias se realizan ùnicamente los días martes y jueves de cada semana.
Tarjeta de crédito:
utilizando alguna de las extensiones de Eryx. Los datos de todas las tarjetas se encuentran en Bitwarden.
4
)
Cargar la compra.
Cada vez que se realice una compra con o sin factura / ticket debe cargarse en el siguiente
FORM
completando cada uno de los campos solicitados y adjuntando la documentación de la compra como respaldo.
En caso de que el pago lo haya realizado un asociade y necesite pedir reintegro de la compra, en la sección "Pagador" deberás ingresar el nombre del ñery que hizo el pago y en la opción "Cancelado" Tildar NO. De esta manera, una vez cada 15 dias admin hace las devoluciones o reintegros a los asociades.
TANTO EL FORM DE COMPRAS COMO EL DE SOLICITUD DE TRANSFERENCIAS PODRAS UBICARLOS TAMBIEN EN EL CANAL DE SLACK DE ADMIN.
Esperamos desde admin que esta guia rápida sirva de ayuda a todos los ñerys!
