import os
from io import BytesIO

from PIL import Image
from google import genai
from google.genai.types import Modality

client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

# Read the PDF file as bytes
with open("wiki/protocolo_incendio.pdf", "rb") as file:
    pdf_bytes = file.read()

# Read the PDF file as bytes
with open("oficina.png", "rb") as file:
    oficina_bytes = file.read()

# Create a Part object with the bytes data
incendio = genai.types.Part.from_bytes(data=pdf_bytes, mime_type="application/pdf")
oficina = genai.types.Part.from_bytes(data=oficina_bytes, mime_type="image/png")

response = client.models.generate_content(
    model="gemini-2.0-flash-exp",
    contents=[incendio, oficina, "Remarcame todas las puertas blindadas. Explicame también donde están."],
    config=genai.types.GenerateContentConfig(
        response_modalities=[Modality.TEXT, Modality.IMAGE],
    ),
)

for part in response.candidates[0].content.parts:
    if part.text:
        print(part.text)
    elif part.inline_data:
        image = Image.open(BytesIO((part.inline_data.data)))
        image.save("example-image.png")
# print(response)
# print(response.text)
