#!/usr/bin/env python3

import os
import tempfile
import unittest
import shutil
import subprocess
import sys
from unittest.mock import patch

from count_classes import scan_directory, main

class TestIgnoreDirectories(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, rel_path, content):
        """Helper method to create test files with specific content"""
        full_path = os.path.join(self.test_dir, rel_path)
        # Ensure directory exists
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return full_path
    
    def test_ignore_single_directory(self):
        """Test ignoring a single directory"""
        # Create files in main directory
        self.create_test_file('main.py', 'class MainClass:\n    pass')
        
        # Create files in a directory that should be scanned
        self.create_test_file('include_dir/file.py', 'class IncludeClass:\n    pass')
        
        # Create files in a directory that should be ignored
        self.create_test_file('ignore_dir/file.py', 'class IgnoreClass:\n    pass')
        self.create_test_file('ignore_dir/subdir/file.py', 'class IgnoreSubClass:\n    pass')
        
        # Scan with ignore_dirs
        total, file_counts = scan_directory(self.test_dir, ignore_dirs=['ignore_dir'])
        
        # Should find only 2 classes (MainClass and IncludeClass)
        self.assertEqual(total, 2)
        self.assertEqual(len(file_counts), 2)
        
        # Make sure the ignored directory's files are not in the results
        for file_path in file_counts.keys():
            self.assertNotIn('ignore_dir', file_path)
    
    def test_ignore_multiple_directories(self):
        """Test ignoring multiple directories"""
        # Create files in main directory
        self.create_test_file('main.py', 'class MainClass:\n    pass')
        
        # Create files in directories that should be ignored
        self.create_test_file('ignore1/file.py', 'class Ignore1Class:\n    pass')
        self.create_test_file('ignore2/file.py', 'class Ignore2Class:\n    pass')
        self.create_test_file('keep/file.py', 'class KeepClass:\n    pass')
        
        # Scan with multiple ignore_dirs
        total, file_counts = scan_directory(self.test_dir, ignore_dirs=['ignore1', 'ignore2'])
        
        # Should find only 2 classes (MainClass and KeepClass)
        self.assertEqual(total, 2)
        self.assertEqual(len(file_counts), 2)
        
        # Make sure the ignored directories' files are not in the results
        for file_path in file_counts.keys():
            self.assertNotIn('ignore1', file_path)
            self.assertNotIn('ignore2', file_path)
    
    def test_ignore_nested_directories(self):
        """Test that ignoring a directory also ignores its subdirectories"""
        # Create a nested directory structure
        self.create_test_file('parent/file.py', 'class ParentClass:\n    pass')
        self.create_test_file('parent/child/file.py', 'class ChildClass:\n    pass')
        self.create_test_file('parent/child/grandchild/file.py', 'class GrandchildClass:\n    pass')
        self.create_test_file('other/file.py', 'class OtherClass:\n    pass')
        
        # Ignore the parent directory
        total, file_counts = scan_directory(self.test_dir, ignore_dirs=['parent'])
        
        # Should find only 1 class (OtherClass)
        self.assertEqual(total, 1)
        self.assertEqual(len(file_counts), 1)
        
        # Make sure none of the parent directory files are in the results
        for file_path in file_counts.keys():
            self.assertNotIn('parent', file_path)
    
    def test_ignore_with_same_name_files(self):
        """Test ignoring directories when files with the same name exist in different directories"""
        # Create files with the same name in different directories
        self.create_test_file('include/models.py', 'class IncludeModel:\n    pass')
        self.create_test_file('ignore/models.py', 'class IgnoreModel:\n    pass')
        
        # Ignore one directory
        total, file_counts = scan_directory(self.test_dir, ignore_dirs=['ignore'])
        
        # Should find only 1 class
        self.assertEqual(total, 1)
        self.assertEqual(len(file_counts), 1)
        
        # The file path should contain 'include' and not 'ignore'
        file_path = list(file_counts.keys())[0]
        self.assertIn('include', file_path)
        self.assertNotIn('ignore', file_path)
    
    def test_case_sensitivity(self):
        """Test that directory ignoring is case-sensitive"""
        # Create directories with different case
        self.create_test_file('Ignore/file.py', 'class UpperClass:\n    pass')
        self.create_test_file('ignore/file.py', 'class LowerClass:\n    pass')
        
        # Ignore only the lowercase version
        total, file_counts = scan_directory(self.test_dir, ignore_dirs=['ignore'])
        
        # Should find only the class in the 'Ignore' directory
        self.assertEqual(total, 1)
        self.assertEqual(len(file_counts), 1)
        
        # The file path should contain 'Ignore' (uppercase) and not 'ignore' (lowercase)
        file_path = list(file_counts.keys())[0]
        self.assertIn('Ignore', file_path)
    
    @patch('sys.argv', ['count_classes.py', 'dummy_dir', '--ignore', 'venv', '.git'])
    @patch('sys.exit')
    @patch('count_classes.scan_directory')
    def test_main_with_ignore_args(self, mock_scan, mock_exit):
        """Test the main function with ignore arguments"""
        mock_scan.return_value = (3, {'file1.py': 1, 'file2.py': 2})
        
        with patch('os.path.isdir', return_value=True):
            with patch('builtins.print') as mock_print:
                main()
                # Check that scan_directory was called with the correct ignore_dirs
                mock_scan.assert_called_once_with('dummy_dir', ['venv', '.git'])
                mock_print.assert_called()
                mock_exit.assert_not_called()

if __name__ == '__main__':
    unittest.main()
