#!/usr/bin/env python3

import os
import tempfile
import unittest
import shutil
from unittest.mock import patch, mock_open

from count_classes import ClassCounter

class TestClassCounter(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content):
        """Helper method to create test files with specific content"""
        file_path = os.path.join(self.test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_init_with_file_path(self):
        """Test initializing with a file path"""
        file_path = self.create_test_file('test.py', 'class TestClass:\n    pass')
        counter = ClassCounter(file_path)
        self.assertEqual(counter.file_path, file_path)
        self.assertIsNone(counter.content)
        self.assertEqual(counter.class_count, 0)
    
    def test_init_without_file_path(self):
        """Test initializing without a file path"""
        counter = ClassCounter()
        self.assertIsNone(counter.file_path)
        self.assertIsNone(counter.content)
        self.assertEqual(counter.class_count, 0)
    
    def test_count_classes_with_init_path(self):
        """Test counting classes using the path from initialization"""
        file_path = self.create_test_file('test.py', 'class TestClass:\n    pass')
        counter = ClassCounter(file_path)
        count = counter.count_classes_in_file()
        self.assertEqual(count, 1)
        self.assertEqual(counter.class_count, 1)
        self.assertIsNotNone(counter.content)
    
    def test_count_classes_with_method_path(self):
        """Test counting classes by providing a path to the method"""
        file_path = self.create_test_file('test.py', 'class TestClass:\n    pass')
        counter = ClassCounter()
        count = counter.count_classes_in_file(file_path)
        self.assertEqual(count, 1)
        self.assertEqual(counter.file_path, file_path)
        self.assertEqual(counter.class_count, 1)
    
    def test_count_classes_no_path(self):
        """Test counting classes without providing a path"""
        counter = ClassCounter()
        with self.assertRaises(ValueError):
            counter.count_classes_in_file()
    
    def test_callable_interface(self):
        """Test the callable interface of ClassCounter"""
        file_path = self.create_test_file('test.py', 'class TestClass:\n    pass')
        counter = ClassCounter()
        count = counter(file_path)
        self.assertEqual(count, 1)
        self.assertEqual(counter.class_count, 1)
    
    def test_multiple_files(self):
        """Test using the same counter for multiple files"""
        file1 = self.create_test_file('file1.py', 'class Class1:\n    pass\nclass Class2:\n    pass')
        file2 = self.create_test_file('file2.py', 'class Class3:\n    pass')
        
        counter = ClassCounter()
        count1 = counter(file1)
        self.assertEqual(count1, 2)
        self.assertEqual(counter.class_count, 2)
        
        count2 = counter(file2)
        self.assertEqual(count2, 1)
        self.assertEqual(counter.class_count, 1)  # Should be updated to the latest count
    
    def test_file_with_no_classes(self):
        """Test a file with no classes"""
        file_path = self.create_test_file('no_classes.py', 'def function():\n    pass')
        counter = ClassCounter(file_path)
        count = counter.count_classes_in_file()
        self.assertEqual(count, 0)
        self.assertEqual(counter.class_count, 0)
    
    def test_file_read_error(self):
        """Test handling of file read errors"""
        counter = ClassCounter("nonexistent.py")
        with patch('builtins.print') as mock_print:
            count = counter.count_classes_in_file()
            self.assertEqual(count, 0)
            self.assertEqual(counter.class_count, 0)
            mock_print.assert_called_once()
    
    def test_complex_file(self):
        """Test a more complex file with various class definitions"""
        content = """
# A comment
class Class1:
    def method(self):
        pass

def function():
    pass

class Class2(Class1):
    class NestedClass:
        pass
        
    def another_method(self):
        class MethodClass:
            pass
"""
        file_path = self.create_test_file('complex.py', content)
        counter = ClassCounter()
        count = counter(file_path)
        # Should find Class1, Class2, NestedClass, and MethodClass
        self.assertEqual(count, 4)

if __name__ == '__main__':
    unittest.main()
