#!/usr/bin/env python3

import os
import tempfile
import unittest
import shutil
import subprocess
import sys

class TestCountClassesIntegration(unittest.TestCase):

    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()

    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.test_dir)

    def create_test_file(self, filename, content):
        """Helper method to create test files with specific content"""
        file_path = os.path.join(self.test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path

    def test_script_execution(self):
        """Test executing the script as a command line tool"""
        # Create test files
        self.create_test_file('file1.py', 'class Class1:\n    pass\n\nclass Class2:\n    pass')
        self.create_test_file('file2.py', 'class Class3:\n    pass')

        # Create a subdirectory with a file
        subdir = os.path.join(self.test_dir, 'subdir')
        os.makedirs(subdir)
        subfile_path = os.path.join(subdir, 'sub.py')
        with open(subfile_path, 'w', encoding='utf-8') as f:
            f.write("class SubClass:\n    pass")

        # Run the script with the test directory as argument
        result = subprocess.run(
            [sys.executable, 'count_classes.py', self.test_dir],
            capture_output=True,
            text=True
        )

        # Check the output
        self.assertEqual(result.returncode, 0)
        self.assertIn("Found 4 Python classes", result.stdout)
        self.assertIn("Total Python classes: 4", result.stdout)

    def test_script_with_invalid_directory(self):
        """Test script behavior with an invalid directory"""
        invalid_dir = os.path.join(self.test_dir, 'nonexistent')

        # Run the script with an invalid directory
        result = subprocess.run(
            [sys.executable, 'count_classes.py', invalid_dir],
            capture_output=True,
            text=True
        )

        # Check the output
        self.assertEqual(result.returncode, 1)
        self.assertIn("is not a valid directory", result.stdout)

    def test_script_with_no_arguments(self):
        """Test script behavior with no arguments"""
        result = subprocess.run(
            [sys.executable, 'count_classes.py'],
            capture_output=True,
            text=True
        )

        # Check the output
        self.assertEqual(result.returncode, 1)
        self.assertIn("Usage:", result.stdout)

    def test_large_directory_structure(self):
        """Test script with a larger directory structure"""
        # Create a more complex directory structure
        for i in range(5):
            subdir = os.path.join(self.test_dir, f'subdir_{i}')
            os.makedirs(subdir)

            # Create files in each subdirectory
            for j in range(3):
                file_path = os.path.join(subdir, f'file_{j}.py')
                with open(file_path, 'w', encoding='utf-8') as f:
                    # Each file has i+j classes
                    classes = '\n'.join([f'class Class_{i}_{j}_{k}:\n    pass' for k in range(i+j)])
                    f.write(classes)

        # Run the script
        result = subprocess.run(
            [sys.executable, 'count_classes.py', self.test_dir],
            capture_output=True,
            text=True
        )

        # Calculate expected number of classes
        expected_classes = sum(i+j for i in range(5) for j in range(3))

        # Check the output
        self.assertEqual(result.returncode, 0)
        self.assertIn(f"Total Python classes: {expected_classes}", result.stdout)

    def test_script_with_ignore_option(self):
        """Test script with the --ignore option"""
        # Create a directory structure with some directories to ignore
        # Main directory with a class
        main_file = os.path.join(self.test_dir, 'main.py')
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write("class MainClass:\n    pass")

        # Directory to keep
        keep_dir = os.path.join(self.test_dir, 'keep')
        os.makedirs(keep_dir)
        keep_file = os.path.join(keep_dir, 'keep.py')
        with open(keep_file, 'w', encoding='utf-8') as f:
            f.write("class KeepClass:\n    pass")

        # Directory to ignore
        ignore_dir = os.path.join(self.test_dir, 'ignore')
        os.makedirs(ignore_dir)
        ignore_file = os.path.join(ignore_dir, 'ignore.py')
        with open(ignore_file, 'w', encoding='utf-8') as f:
            f.write("class IgnoreClass:\n    pass")

        # Run the script with the --ignore option
        result = subprocess.run(
            [sys.executable, 'count_classes.py', self.test_dir, '--ignore', 'ignore'],
            capture_output=True,
            text=True
        )

        # Should find 2 classes (MainClass and KeepClass)
        self.assertEqual(result.returncode, 0)
        self.assertIn("Ignoring directories: ignore", result.stdout)
        self.assertIn("Found 2 Python classes", result.stdout)
        self.assertIn("Total Python classes: 2", result.stdout)

        # Make sure the ignored directory is not in the output
        self.assertNotIn(ignore_file, result.stdout)

if __name__ == '__main__':
    unittest.main()
