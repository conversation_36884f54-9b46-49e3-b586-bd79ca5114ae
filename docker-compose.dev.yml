version: '3.3'

services:
  database:
    build:
      context: ./database
      dockerfile: ./Dockerfile
    image: run0km-db
    container_name: run0km-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: deliveryrun_dev
      MYSQL_USER: run0km
      MYSQL_PASSWORD: run0km1234
      MYSQL_ROOT_PASSWORD: root1234
    ports:
      - '8082:3306'
    expose:
      - '8082'
      - '3306'
    volumes:
      - ./database/msql-data:/var/lib/mysql
    networks:
      run0km-net:
        ipv4_address: ********

  rabbitmq:
    build:
      context: ./rabbitmq
      dockerfile: ./Dockerfile
    image: run0km-rabbitmq
    container_name: run0km-rabbitmq
    restart: unless-stopped
    ports:
      - '15672:15672'
      - '5672:5672'
    volumes:
      - ./rabbitmq/data:/var/lib/rabbitmq
    networks:
      run0km-net:
        ipv4_address: ********

  run0km-web:
    build:
      context: ./run0km
      dockerfile: ./Dockerfile
    command: >
      bash -c "pip install -r ./requirements/dev.txt && python manage.py migrate && python manage.py runserver 0.0.0.0:8000"
    image: run0km-web
    container_name: run0km-web
    volumes:
      - type: bind
        source: ./run0km
        target: /code
    ports:
      - '8000:8000'
    depends_on:
      - database
    restart: unless-stopped
    environment:
      DB_USER: run0km
      DB_PASSWORD: run0km1234
      DB_NAME: deliveryrun_dev
      DB_HOST: database
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
    networks:
      run0km-net:
        ipv4_address: ********

  run0km-celery:
    build:
      context: ./run0km
      dockerfile: ./Dockerfile
    image: run0km-celery
    container_name: run0km-celery
    restart: unless-stopped
    command: >
      bash -c "pip install -r ./requirements/dev.txt && celery -A run0km worker -l info"
    volumes:
      - type: bind
        source: ./run0km
        target: /code
    environment:
      DB_USER: run0km
      DB_PASSWORD: run0km1234
      DB_NAME: deliveryrun_dev
      DB_HOST: database
      RABBITMQ_HOST: ********
      RABBITMQ_PORT: 5672
      C_FORCE_ROOT: "true"
      IMPORT_CELERY_CONFIG: "true"
    depends_on:
      - database
      - rabbitmq
    networks:
        run0km-net:
            ipv4_address: ********

  dietrich-server:
    build:
      context: ./dietrich-server
      dockerfile: ./Dockerfile
    command: >
      bash -c "python -m flask run --host=0.0.0.0"
    volumes:
      - type: bind
        source: ./dietrich-server
        target: /code
    image: dietrich-server
    container_name: dietrich-server
    environment:
      FLASK_APP: app.py
    ports:
      - '5000:5000'
    restart: unless-stopped
    networks:
      run0km-net:
        ipv4_address: ********



networks:
  run0km-net:
    driver: bridge
    ipam:
      config:
        - subnet: ********/16
