#!/usr/bin/env python3
"""
Tests para la aplicación de estadísticas de código Python.
Empezamos con TDD - estos tests fallarán inicialmente.
"""

import unittest
import tempfile
import os
from pathlib import Path

from code_analyzer import PythonCodeAnalyzer


class TestPythonCodeStats(unittest.TestCase):
    """Tests básicos para contar clases en código Python."""
    
    def setUp(self):
        """Configuración para cada test."""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Limpieza después de cada test."""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content):
        """Helper para crear archivos de test."""
        file_path = os.path.join(self.test_dir, filename)
        # Crear directorio si no existe (solo si hay subdirectorios)
        dir_path = os.path.dirname(file_path)
        if dir_path != self.test_dir:
            os.makedirs(dir_path, exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path

    def test_count_single_class_in_file(self):
        """Test 1: Debe contar una clase en un archivo simple."""
        # Arrange
        content = """class MiClase:
    def __init__(self):
        pass
"""
        file_path = self.create_test_file('test.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 1)

    def test_count_multiple_classes_in_file(self):
        """Test 2: Debe contar múltiples clases en un archivo."""
        # Arrange
        content = """class PrimeraClase:
    pass

class SegundaClase:
    def metodo(self):
        return "hola"

class TerceraClase:
    def __init__(self):
        self.valor = 42
"""
        file_path = self.create_test_file('multiple.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 3)

    def test_count_no_classes_in_file(self):
        """Test 3: Debe retornar 0 cuando no hay clases."""
        # Arrange
        content = """def funcion_simple():
    return "no hay clases aquí"

variable = 42

def otra_funcion():
    print("tampoco hay clases")
"""
        file_path = self.create_test_file('no_classes.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 0)

    def test_ignore_classes_in_strings_and_comments(self):
        """Test 4: Debe ignorar 'class' en strings y comentarios."""
        # Arrange
        content = '''# Esta es una clase comentada: class ComentarioClase
def funcion():
    texto = "Esta string contiene class FalseClase pero no es real"
    return texto

"""
Docstring que menciona class DocstringClase
pero tampoco debería contarse
"""

class ClaseReal:
    def __init__(self):
        # Comentario interno con class InternaClase
        self.mensaje = "string con class StringClase"
        pass

# Esto debería ser detectado como clase falsa por nuestra regex simple:
    class ClaseIndentada:  # Esta está indentada pero debería ser detectada
        pass
'''
        file_path = self.create_test_file('strings_comments.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        self.assertEqual(result, 2)  # ClaseReal y ClaseIndentada (nuestra regex simple las detecta)

    def test_handle_nonexistent_file(self):
        """Test 5: Debe manejar archivos que no existen."""
        # Arrange
        nonexistent_path = os.path.join(self.test_dir, 'no_existe.py')

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(nonexistent_path)

        # Assert
        self.assertEqual(result, 0)  # Debe retornar 0 sin crashear

    def test_complex_class_scenarios(self):
        """Test 6: Casos complejos que podrían confundir una regex simple."""
        # Arrange
        content = '''
# Esto NO debería contarse (está en un string multilínea):
multiline_string = """
class FakeClass:
    pass
"""

# Esto SÍ debería contarse:
class RealClass:
    def method(self):
        # Esto NO debería contarse (en comentario):
        # class CommentClass:
        string_with_class = "class InStringClass:"
        return string_with_class

# Esto SÍ debería contarse (clase con herencia):
class ChildClass(RealClass):
    pass

# Esto SÍ debería contarse (clase con múltiples bases):
class MultipleInheritance(RealClass, object):
    pass
'''
        file_path = self.create_test_file('complex.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        # Debería contar: RealClass, ChildClass, MultipleInheritance = 3
        # AST correctamente ignora FakeClass porque está en un string
        self.assertEqual(result, 3)  # AST funciona correctamente

    def test_class_with_inheritance_and_nested(self):
        """Test 7: Debe contar clases con herencia y clases anidadas."""
        # Arrange
        content = '''
class ValidClass:
    pass

class InheritedClass(ValidClass):
    pass

def function():
    # Esta línea tiene clase anidada
    class NestedClass:
        pass
    return "done"
'''
        file_path = self.create_test_file('inheritance_test.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        # Debería contar: ValidClass, InheritedClass, NestedClass = 3 clases
        self.assertEqual(result, 3)

    def test_ignore_classes_in_multiline_strings(self):
        """Test 9: Debe ignorar clases dentro de strings multilínea (como en tests)."""
        # Arrange
        content = '''
def test_function():
    """Test que crea contenido con clases."""
    file_content = """
class FakeClassInString:
    pass

class AnotherFakeClass:
    def method(self):
        pass
"""
    return file_content

class RealClass:
    pass
'''
        file_path = self.create_test_file('multiline_test.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        # Solo debería contar RealClass, no las clases dentro del string multilínea
        self.assertEqual(result, 1)

    def test_count_lines_of_code_simple_file(self):
        """Test 10: Debe contar líneas de código sin comentarios."""
        # Arrange
        content = '''# Este es un comentario
class MiClase:
    def __init__(self):
        # Comentario interno
        self.valor = 42

    def metodo(self):
        """Docstring del método"""
        return self.valor  # Comentario al final

# Otro comentario
def funcion():
    pass
'''
        file_path = self.create_test_file('lines_test.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_lines_of_code_in_file(file_path)

        # Assert
        # Líneas de código: class, def __init__, self.valor=42, def metodo, return, def funcion, pass = 7
        self.assertEqual(result, 7)

    def test_count_lines_of_code_empty_and_whitespace(self):
        """Test 11: Debe ignorar líneas vacías y solo espacios."""
        # Arrange
        content = '''

class TestClass:

    def method(self):
        # Solo comentario

        x = 1


        return x

'''
        file_path = self.create_test_file('whitespace_test.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_lines_of_code_in_file(file_path)

        # Assert
        # Líneas de código: class, def method, x = 1, return x = 4
        self.assertEqual(result, 4)

    def test_count_lines_of_code_directory(self):
        """Test 12: Debe contar líneas de código en directorio recursivamente."""
        # Arrange
        self.create_test_file('main.py', '''
class MainClass:
    def method(self):
        return 42
''')
        self.create_test_file('subdir/sub.py', '''
# Comentario
def function():
    x = 1
    return x
''')

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_lines_of_code_in_directory(self.test_dir)

        # Assert
        # main.py: class, def method, return = 3 líneas
        # sub.py: def function, x = 1, return x = 3 líneas
        # Total = 6 líneas
        self.assertEqual(result, 6)

    def test_scan_directory_recursively(self):
        """Test 8: Debe escanear directorios recursivamente y contar todas las clases."""
        # Arrange - Crear estructura de directorios con archivos Python

        # Archivo en directorio raíz
        self.create_test_file('main.py', '''
class MainClass:
    pass

class AnotherMainClass:
    pass
''')

        # Crear subdirectorio
        subdir = os.path.join(self.test_dir, 'subdir')
        os.makedirs(subdir)
        subfile_path = os.path.join(subdir, 'sub.py')
        with open(subfile_path, 'w', encoding='utf-8') as f:
            f.write('''
class SubClass:
    pass
''')

        # Crear subdirectorio anidado
        nested_dir = os.path.join(subdir, 'nested')
        os.makedirs(nested_dir)
        nested_file_path = os.path.join(nested_dir, 'nested.py')
        with open(nested_file_path, 'w', encoding='utf-8') as f:
            f.write('''
class NestedClass:
    pass

class AnotherNestedClass:
    pass
''')

        # Archivo que no es Python (debería ser ignorado)
        txt_file_path = os.path.join(self.test_dir, 'readme.txt')
        with open(txt_file_path, 'w', encoding='utf-8') as f:
            f.write('class NotAPythonClass: pass')

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.scan_directory(self.test_dir)

        # Assert
        # Debería encontrar: MainClass, AnotherMainClass, SubClass, NestedClass, AnotherNestedClass = 5 clases
        self.assertEqual(result, 5)


if __name__ == '__main__':
    unittest.main()
