#!/usr/bin/env python3
"""
Tests para la aplicación de estadísticas de código Python.
Empezamos con TDD - estos tests fallarán inicialmente.
"""

import unittest
import tempfile
import os
from pathlib import Path

from python_stats import PythonCodeAnalyzer


class TestPythonCodeStats(unittest.TestCase):
    """Tests básicos para contar clases en código Python."""
    
    def setUp(self):
        """Configuración para cada test."""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Limpieza después de cada test."""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content):
        """Helper para crear archivos de test."""
        file_path = os.path.join(self.test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path

    def test_count_single_class_in_file(self):
        """Test 1: Debe contar una clase en un archivo simple."""
        # Arrange
        content = """class MiClase:
    def __init__(self):
        pass
"""
        file_path = self.create_test_file('test.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 1)

    def test_count_multiple_classes_in_file(self):
        """Test 2: Debe contar múltiples clases en un archivo."""
        # Arrange
        content = """class PrimeraClase:
    pass

class SegundaClase:
    def metodo(self):
        return "hola"

class TerceraClase:
    def __init__(self):
        self.valor = 42
"""
        file_path = self.create_test_file('multiple.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 3)

    def test_count_no_classes_in_file(self):
        """Test 3: Debe retornar 0 cuando no hay clases."""
        # Arrange
        content = """def funcion_simple():
    return "no hay clases aquí"

variable = 42

def otra_funcion():
    print("tampoco hay clases")
"""
        file_path = self.create_test_file('no_classes.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 0)

    def test_ignore_classes_in_strings_and_comments(self):
        """Test 4: Debe ignorar 'class' en strings y comentarios."""
        # Arrange
        content = '''# Esta es una clase comentada: class ComentarioClase
def funcion():
    texto = "Esta string contiene class FalseClase pero no es real"
    return texto

"""
Docstring que menciona class DocstringClase
pero tampoco debería contarse
"""

class ClaseReal:
    def __init__(self):
        # Comentario interno con class InternaClase
        self.mensaje = "string con class StringClase"
        pass

# Esto debería ser detectado como clase falsa por nuestra regex simple:
    class ClaseIndentada:  # Esta está indentada pero debería ser detectada
        pass
'''
        file_path = self.create_test_file('strings_comments.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        self.assertEqual(result, 2)  # ClaseReal y ClaseIndentada (nuestra regex simple las detecta)

    def test_handle_nonexistent_file(self):
        """Test 5: Debe manejar archivos que no existen."""
        # Arrange
        nonexistent_path = os.path.join(self.test_dir, 'no_existe.py')

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(nonexistent_path)

        # Assert
        self.assertEqual(result, 0)  # Debe retornar 0 sin crashear

    def test_complex_class_scenarios(self):
        """Test 6: Casos complejos que podrían confundir una regex simple."""
        # Arrange
        content = '''
# Esto NO debería contarse (está en un string multilínea):
multiline_string = """
class FakeClass:
    pass
"""

# Esto SÍ debería contarse:
class RealClass:
    def method(self):
        # Esto NO debería contarse (en comentario):
        # class CommentClass:
        string_with_class = "class InStringClass:"
        return string_with_class

# Esto SÍ debería contarse (clase con herencia):
class ChildClass(RealClass):
    pass

# Esto SÍ debería contarse (clase con múltiples bases):
class MultipleInheritance(RealClass, object):
    pass
'''
        file_path = self.create_test_file('complex.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        # Debería contar: RealClass, ChildClass, MultipleInheritance = 3
        # Pero nuestra regex simple podría contar también FakeClass = 4
        self.assertEqual(result, 4)  # Esperamos que falle porque cuenta la clase en el string

    def test_class_without_colon_should_not_count(self):
        """Test 7: Líneas que parecen clases pero no tienen ':' no deberían contar."""
        # Arrange
        content = '''
class ValidClass:
    pass

# Estas NO deberían contarse (sin dos puntos):
class InvalidClass
class AnotherInvalid(object)

def function():
    # Esta línea parece clase pero está en función
    class NestedClass:
        pass
    return "done"
'''
        file_path = self.create_test_file('invalid_classes.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        # Solo ValidClass y NestedClass deberían contarse (ambas tienen ':')
        self.assertEqual(result, 2)

    def test_scan_directory_recursively(self):
        """Test 8: Debe escanear directorios recursivamente y contar todas las clases."""
        # Arrange - Crear estructura de directorios con archivos Python

        # Archivo en directorio raíz
        self.create_test_file('main.py', '''
class MainClass:
    pass

class AnotherMainClass:
    pass
''')

        # Crear subdirectorio
        subdir = os.path.join(self.test_dir, 'subdir')
        os.makedirs(subdir)
        subfile_path = os.path.join(subdir, 'sub.py')
        with open(subfile_path, 'w', encoding='utf-8') as f:
            f.write('''
class SubClass:
    pass
''')

        # Crear subdirectorio anidado
        nested_dir = os.path.join(subdir, 'nested')
        os.makedirs(nested_dir)
        nested_file_path = os.path.join(nested_dir, 'nested.py')
        with open(nested_file_path, 'w', encoding='utf-8') as f:
            f.write('''
class NestedClass:
    pass

class AnotherNestedClass:
    pass
''')

        # Archivo que no es Python (debería ser ignorado)
        txt_file_path = os.path.join(self.test_dir, 'readme.txt')
        with open(txt_file_path, 'w', encoding='utf-8') as f:
            f.write('class NotAPythonClass: pass')

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.scan_directory(self.test_dir)

        # Assert
        # Debería encontrar: MainClass, AnotherMainClass, SubClass, NestedClass, AnotherNestedClass = 5 clases
        self.assertEqual(result, 5)
