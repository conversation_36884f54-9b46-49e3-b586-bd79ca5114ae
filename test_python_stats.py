#!/usr/bin/env python3
"""
Tests para la aplicación de estadísticas de código Python.
Empezamos con TDD - estos tests fallarán inicialmente.
"""

import unittest
import tempfile
import os
from pathlib import Path

from python_stats import PythonCodeAnalyzer


class TestPythonCodeStats(unittest.TestCase):
    """Tests básicos para contar clases en código Python."""
    
    def setUp(self):
        """Configuración para cada test."""
        self.test_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Limpieza después de cada test."""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content):
        """Helper para crear archivos de test."""
        file_path = os.path.join(self.test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path

    def test_count_single_class_in_file(self):
        """Test 1: Debe contar una clase en un archivo simple."""
        # Arrange
        content = """class MiClase:
    def __init__(self):
        pass
"""
        file_path = self.create_test_file('test.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 1)

    def test_count_multiple_classes_in_file(self):
        """Test 2: Debe contar múltiples clases en un archivo."""
        # Arrange
        content = """class PrimeraClase:
    pass

class SegundaClase:
    def metodo(self):
        return "hola"

class TerceraClase:
    def __init__(self):
        self.valor = 42
"""
        file_path = self.create_test_file('multiple.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 3)

    def test_count_no_classes_in_file(self):
        """Test 3: Debe retornar 0 cuando no hay clases."""
        # Arrange
        content = """def funcion_simple():
    return "no hay clases aquí"

variable = 42

def otra_funcion():
    print("tampoco hay clases")
"""
        file_path = self.create_test_file('no_classes.py', content)
        
        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)
        
        # Assert
        self.assertEqual(result, 0)

    def test_ignore_classes_in_strings_and_comments(self):
        """Test 4: Debe ignorar 'class' en strings y comentarios."""
        # Arrange
        content = '''# Esta es una clase comentada: class ComentarioClase
def funcion():
    texto = "Esta string contiene class FalseClase pero no es real"
    return texto

"""
Docstring que menciona class DocstringClase
pero tampoco debería contarse
"""

class ClaseReal:
    def __init__(self):
        # Comentario interno con class InternaClase
        self.mensaje = "string con class StringClase"
        pass
'''
        file_path = self.create_test_file('strings_comments.py', content)

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(file_path)

        # Assert
        self.assertEqual(result, 1)  # Solo debe contar ClaseReal

    def test_handle_nonexistent_file(self):
        """Test 5: Debe manejar archivos que no existen."""
        # Arrange
        nonexistent_path = os.path.join(self.test_dir, 'no_existe.py')

        # Act
        analyzer = PythonCodeAnalyzer()
        result = analyzer.count_classes_in_file(nonexistent_path)

        # Assert
        self.assertEqual(result, 0)  # Debe retornar 0 sin crashear


if __name__ == '__main__':
    unittest.main()
