from system.authentication_system import AuthenticationSystem


class AuthenticationSystemStub(AuthenticationSystem):

    def __init__(self):
        self._authentication_behaviour = lambda username, password: None

    def login(self, username, password):
        return self._authentication_behaviour(username, password)

    def change_behaviour(self, new_behaviour):
        self._authentication_behaviour = new_behaviour
