from datetime import <PERSON><PERSON><PERSON>
from unittest import TestCase

from model.manual_clock import <PERSON><PERSON><PERSON>
from model.merchant_processor_stub import MerchantProcessorStub
from model.month_of_year import MonthOfYear
from model.tests.publisher_test_objects_factory import PublisherTestObjectsFactory
from system.authentication_system_stub import AuthenticationSystemStub
from system.cart_session import CartSession
from system.tus_libros_system import TusLibrosSystem


class TusLibrosSystemTest(TestCase):

    def setUp(self):
        self._objects_factory = PublisherTestObjectsFactory()
        self._clock = ManualClock()
        self._authentication_system = AuthenticationSystemStub()
        self._system = TusLibrosSystem.new_with(self._authentication_system, self._objects_factory.a_catalog(),
                                                MerchantProcessorStub(), self._clock)

    'tests'

    def test_01_cannot_create_cart_using_invalid_credentials(self):
        def raise_authentication_error():
            raise RuntimeError(TusLibrosSystem.authentication_error_message())

        self._authentication_system.change_behaviour(lambda _client_id, _password: raise_authentication_error())

        self.should_raise_authentication_error_when(
            lambda: self._system.create_cart_for(self.an_invalid_client_id(), 'password'))

    def test_02_can_list_an_empty_cart(self):
        cart_id = self.create_cart_for_client()

        self.assertTrue(len(self._system.list_cart(cart_id)) == 0)

    def test_03_can_add_books_to_cart_and_list_its_content(self):
        cart_id = self.create_cart_for_client()

        self.add_book_to_cart(cart_id)

        self.assertEqual(self._system.list_cart(cart_id), self._objects_factory.a_bag_with_a_book_from_the_editorial())

    def test_04_cannot_add_to_cart_not_created_before(self):
        self.should_raise_cart_not_found_when(lambda: self.add_book_to_cart(self.an_invalid_cart_id()))

    def test_05_cannot_checkout_cart_not_created_before(self):
        self.should_raise_cart_not_found_when(lambda: self.checkout_cart(self.an_invalid_cart_id()))

    def test_06_can_list_purchases_of_a_successful_checkout(self):
        cart_id = self.create_cart_for_client()
        self.add_book_to_cart(cart_id)
        self.add_book_to_cart(cart_id)
        self.checkout_cart(cart_id)

        expected_books = [self._objects_factory.book_from_the_editorial(),
                          self._objects_factory.book_from_the_editorial()]
        expected_amount_spent = self._objects_factory.book_from_the_editorial_price() * 2
        self.assert_client_purchases_were(expected_books, expected_amount_spent)

    def test_07_purchases_list_of_a_client_with_no_purchases_is_empty(self):
        self.assert_client_made_no_purchases()

    def test_08_cannot_list_purchases_using_invalid_credentials(self):
        def raise_authentication_error():
            raise RuntimeError(TusLibrosSystem.authentication_error_message())

        self._authentication_system.change_behaviour(lambda _client_id, _password: raise_authentication_error())

        self.should_raise_authentication_error_when(
            lambda: self._system.list_purchases(self.an_invalid_client_id(), 'password'))

    def test_09_cannot_add_books_to_cart_after_session_is_expired(self):
        cart_id = self.create_cart_for_client()

        self.should_raise_session_expired_after_elapsed_time_asserting(lambda: self.add_book_to_cart(cart_id),
                                                                       self._system.session_duration() +
                                                                       timedelta(seconds=1),
                                                                       lambda: self.assertTrue(
                                                                           len(self._system.list_cart(cart_id)) == 0))

    def test_10_cannot_list_cart_after_session_is_expired(self):
        cart_id = self.create_cart_for_client()

        self.should_raise_session_expired_after_elapsed_time(lambda: self._system.list_cart(cart_id),
                                                             self._system.session_duration() + timedelta(seconds=1))

    def test_11_cannot_checkout_cart_after_session_is_expired(self):
        cart_id = self.create_cart_for_client()

        self.should_raise_session_expired_after_elapsed_time_asserting(lambda: self.checkout_cart(cart_id),
                                                                       self._system.session_duration() +
                                                                       timedelta(seconds=1),
                                                                       lambda: self.assert_client_made_no_purchases())

    def test_12_cannot_checkout_cart_after_session_is_expired(self):
        cart_id = self.create_cart_for_client()
        self._clock.advance_time(self._system.session_duration() - timedelta(seconds=1))

        self.add_book_to_cart(cart_id)
        self._clock.advance_time(timedelta(seconds=2))

        self.checkout_cart(cart_id)
        self.assert_client_made_one_purchase()

    'assertions'

    def should_raise_authentication_error_when(self, a_block):
        try:
            a_block()
            self.fail()
        except RuntimeError as exception:
            self.assertEqual(str(exception), TusLibrosSystem.authentication_error_message())

    def should_raise_cart_not_found_when(self, a_block):
        try:
            a_block()
            self.fail()
        except RuntimeError as exception:
            self.assertEqual(str(exception), TusLibrosSystem.cart_not_found_error_message())

    def assert_client_made_no_purchases(self):
        return self.assertTrue(len(self.list_purchases_of_client()[0]) == 0)

    def assert_client_made_one_purchase(self):
        return self.assertEqual(len(self.list_purchases_of_client()[0]), 1)

    def assert_client_purchases_were(self, expected_books, expected_spent):
        self.assertEqual(self.list_purchases_of_client()[0], expected_books)
        self.assertEqual(self.list_purchases_of_client()[1], expected_spent)

    def should_raise_session_expired_after_elapsed_time_asserting(self, using_cart_block, elapsed_time,
                                                                  assertion_block):
        self._clock.advance_time(elapsed_time)
        try:
            using_cart_block()
        except RuntimeError as exception:
            self.assertEqual(str(exception), CartSession.session_expired_error_message())
            self._clock.revert_time(elapsed_time)
            assertion_block()

    def should_raise_session_expired_after_elapsed_time(self, using_cart_block, elapsed_time):
        self.should_raise_session_expired_after_elapsed_time_asserting(using_cart_block, elapsed_time, lambda: None)

    'test objects'

    def a_valid_client_id(self):
        return 1

    def an_invalid_cart_id(self):
        return object()

    def an_invalid_client_id(self):
        return object()

    'private'

    def add_book_to_cart(self, cart_id):
        self._system.add_to_cart(cart_id, self._objects_factory.book_from_the_editorial(), 1)

    def checkout_cart(self, cart_id):
        return self._system.checkout_cart(cart_id, '1111 1111', MonthOfYear.current(), 'Juan Perez')

    def create_cart_for_client(self):
        return self._system.create_cart_for(self.a_valid_client_id(), 'correctPassword')

    def list_purchases_of_client(self):
        return self._system.list_purchases(self.a_valid_client_id(), 'correctPassword')
