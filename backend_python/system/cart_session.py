from model.cart import Cart


class CartSession:

    @classmethod
    def new_with(cls, a_customer, tus_libros_system):
        return cls(a_customer, tus_libros_system)

    @classmethod
    def session_expired_error_message(cls):
        return "Cart session has expired"

    'initialization'

    def __init__(self, a_customer, tus_libros_system):
        self._customer = a_customer
        self._system = tus_libros_system
        self._cart = Cart.accepting_items_of(tus_libros_system.catalog())
        self._last_access = tus_libros_system.now()

    'public'

    def add_to_cart(self, a_book, a_number_of_copies):
        self._when_not_expired_do(lambda: self._cart.add_with_quantity(a_book, a_number_of_copies))

    def checkout_cart_paying_with(self, a_credit_card):
        self._when_not_expired_do(lambda: self._system.checkout(self._cart, self._customer, a_credit_card))

    def list_cart(self):
        return self._when_not_expired_do(lambda: self._cart.contents())

    'assertions'

    def _assert_session_not_expired(self):
        if self._is_expired():
            raise RuntimeError(self.__class__.session_expired_error_message())

    'private'

    def _is_expired(self):
        return (self._system.now() - self._last_access) > self._system.session_duration()

    def _when_not_expired_do(self, a_block):
        self._assert_session_not_expired()
        try:
            return a_block()
        finally:
            self._last_access = self._system.now()
