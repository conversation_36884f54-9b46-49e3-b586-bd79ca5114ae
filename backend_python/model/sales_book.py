from model.money import PESO
from model.money.money import Money


class SalesBook:
    'initialization'

    def __init__(self):
        self._books_sold_by_customer = {}
        self._spent_by_customer = {}

    'accessing'

    def sold_items_to(self, a_customer):
        if a_customer not in self._books_sold_by_customer:
            return []
        return self._books_sold_by_customer[a_customer]

    def total_spent_by(self, a_customer):
        if a_customer not in self._spent_by_customer:
            return Money.amount_and_unit(0, PESO)
        return self._spent_by_customer[a_customer]

    'testing'

    def is_empty(self):
        return len(self._books_sold_by_customer) == 0

    'adding'

    def register_sale(self, a_customer, a_bag_of_items, a_total):
        books_sold_previously = self.sold_items_to(a_customer)
        self._books_sold_by_customer[a_customer] = books_sold_previously + a_bag_of_items

        spent_up_to_now = self.total_spent_by(a_customer)
        self._spent_by_customer[a_customer] = spent_up_to_now + a_total
