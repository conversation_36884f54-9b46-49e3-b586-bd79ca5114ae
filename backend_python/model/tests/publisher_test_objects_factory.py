from datetime import datetime

from model.cart import Cart
from model.cashier import Cashier
from model.credit_card import CreditCard
from model.money import PESO
from model.money.money import Money
from model.month_of_year import MonthOfYear
from model.sales_book import SalesBook


class PublisherTestObjectsFactory:
    def a_bag_with_a_book_from_the_editorial(self):
        return [self.book_from_the_editorial()]

    def a_cashier(self):
        return Cashier.registering_sales_on(self.an_empty_sales_book())

    def a_catalog(self):
        return {self.book_from_the_editorial(): self.book_from_the_editorial_price()}

    def a_customer(self):
        return '<PERSON>'

    def a_valid_credit_card(self):
        june_next_year = MonthOfYear(6, self.now().year + 1)
        return CreditCard.new_with('<PERSON>', 11111111, june_next_year)

    def an_empty_cart(self):
        return Cart.accepting_items_of(self.a_catalog())

    def an_empty_sales_book(self):
        return SalesBook()

    def an_expired_credit_card(self):
        june_last_year = MonthOfYear(6, self.now().year - 1)
        return CreditCard.new_with('<PERSON>', 11111111, june_last_year)

    def book_from_the_editorial(self):
        return 'ABC123'

    def book_from_the_editorial_price(self):
        return Money.amount_and_unit(1000, PESO)

    def cart_with_a_book(self):
        cart = self.an_empty_cart()
        cart.add(self.book_from_the_editorial())
        return cart

    def now(self):
        return datetime.now()
