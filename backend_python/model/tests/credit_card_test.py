from unittest import TestCase

from model.credit_card import CreditCard
from model.month_of_year import MonthOfYear


class CreditCardTest(TestCase):
    def test01_credit_card_owner_name_cannot_be_blank(self):
        try:
            CreditCard.new_with('', 11111111, MonthOfYear(6, 2023))
            self.fail()
        except RuntimeError as expected_exception:
            self.assertEqual(str(expected_exception), CreditCard.name_cannot_be_blank_error_message())

    def test02_can_get_credit_card_data_from_plastic(self):
        owner = '<PERSON>'
        number = '1111 1111'
        expiration_date = MonthOfYear.current()
        credit_card = CreditCard.new_with(owner, number, expiration_date)

        self.assertEqual(credit_card.owner(), owner)
        self.assertEqual(credit_card.number(), number)
        self.assertEqual(credit_card.expiration_date(), expiration_date)
