from model.merchant_processor import MerchantProcessor


class MerchantProcessorStub(MerchantProcessor):

    def __init__(self):
        self._simulated_behaviour = lambda amount_to_debit, credit_card: None

    def debit_credit_card(self, amount_to_debit, credit_card):
        return self._simulated_behaviour(amount_to_debit, credit_card)

    def change_behaviour(self, new_behaviour):
        self._simulated_behaviour = new_behaviour
