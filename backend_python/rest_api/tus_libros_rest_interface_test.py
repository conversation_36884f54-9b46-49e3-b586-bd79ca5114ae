from unittest import TestCase

from model.manual_clock import <PERSON><PERSON><PERSON>
from model.merchant_processor_stub import MerchantProcessorStub
from model.tests.publisher_test_objects_factory import PublisherTestObjectsFactory
from rest_api.http_request import HttpRequest
from rest_api.tus_libros_rest_interface import TusLibrosRestInterface
from system.authentication_system_stub import AuthenticationSystemStub
from system.tus_libros_system import TusLibrosSystem


class TusLibrosRestInterfaceTest(TestCase):

    def setUp(self):
        self._objects_factory = PublisherTestObjectsFactory()
        self._rest_interface = TusLibrosRestInterface.for_system(self.system())

    def test01_create_cart_returns_cart_id_on_success(self):
        response = self.create_cart_for_a_client()

        self.assert_is_ok_and_body_begins_with(response, '0|')

    def test02_create_cart_returns_error_on_exception(self):
        response = self.create_cart_for_a_client_with_invalid_credentials()

        self.assert_is_ok_and_body_begins_with(response, '1|')

    def test03_add_to_cart_returns_ok_on_success(self):
        a_valid_cart_id = self.create_cart_and_retrive_cart_id()

        response = self.add_to_cart_with_valid_parameters(a_valid_cart_id)

        self.assert_is_ok_and_body_equals(response, '0|OK')

    def test04_add_to_cart_returns_error_on_exception(self):
        response = self.add_to_cart_with_invalid_parameters()

        self.assert_is_ok_and_body_begins_with(response, '1|')

    def test05_list_cart_returns_cart_contents_on_success(self):
        a_valid_cart_id = self.create_cart_and_retrive_cart_id()
        self.add_to_cart_with_valid_parameters(a_valid_cart_id)

        response = self.list_cart(a_valid_cart_id)

        self.assert_is_ok_and_body_equals(response, f'0|{self._objects_factory.book_from_the_editorial()}|1')

    def test06_list_cart_returns_error_on_exception(self):
        response = self.list_cart(self.invalid_cart_id_field())

        self.assert_is_ok_and_body_begins_with(response, '1|')

    def test07_checkout_returns_transaction_id_on_success(self):
        response = self.add_to_cart_and_checkout()

        self.assert_is_ok_and_body_begins_with(response, '0|')

    def test08_checkout_returns_error_on_exception(self):
        response = self.checkout_cart(self.invalid_cart_id_field())

        self.assert_is_ok_and_body_begins_with(response, '1|')

    def test09_list_purchases_returns_purchases_information_on_success(self):
        self.add_to_cart_and_checkout()

        response = self.list_purchases_of_client()

        book = self._objects_factory.book_from_the_editorial()
        book_price = self._objects_factory.book_from_the_editorial_price()

        self.assert_is_ok_and_body_equals(response, f'0|{book}|1|{book_price}')

    def test10_list_purchases_returns_error_on_exception(self):
        response = self.list_purchases_of_client_with_invalid_credentials()

        self.assert_is_ok_and_body_begins_with(response, '1|')

    'test objects'

    def a_valid_credit_card_number_field(self):
        return '1111 1111 1111 1111'

    def a_valid_credit_card_owner_field(self):
        return 'Juan Perez'

    def a_valid_expiration_date_field(self):
        return '032099'

    def invalid_cart_id_field(self):
        return '0'

    def invalid_password_field_for_client(self):
        return 'wrongPassword'

    def system(self):
        authentication_system = AuthenticationSystemStub()

        def test_behaviour(user, password):
            if not (user == self.valid_client_id_field() and password == self.valid_password_field_for_client()):
                raise RuntimeError(TusLibrosSystem.authentication_error_message())

        authentication_system.change_behaviour(lambda user, password: test_behaviour(user, password))

        return TusLibrosSystem.new_with(authentication_system, self._objects_factory.a_catalog(),
                                        MerchantProcessorStub(), ManualClock())

    def valid_client_id_field(self):
        return '1'

    def valid_password_field_for_client(self):
        return 'password'

    'private - test requests'

    def add_to_cart_with_invalid_parameters(self):
        http_request = HttpRequest.with_fields({'cartId': self.invalid_cart_id_field(),
                                                'bookIsbn': self._objects_factory.book_from_the_editorial(),
                                                'bookQuantity': 1})
        return self._rest_interface.add_to_cart(http_request)

    def add_to_cart_with_valid_parameters(self, a_valid_cart_id):
        http_request = HttpRequest.with_fields({'cartId': a_valid_cart_id,
                                                'bookIsbn': self._objects_factory.book_from_the_editorial(),
                                                'bookQuantity': 1})
        return self._rest_interface.add_to_cart(http_request)

    def checkout_cart(self, a_valid_cart_id):
        http_request = HttpRequest.with_fields({'cartId': a_valid_cart_id,
                                                'ccn': self.a_valid_credit_card_number_field(),
                                                'cced': self.a_valid_expiration_date_field(),
                                                'cco': self.a_valid_credit_card_owner_field()})
        return self._rest_interface.checkout_cart(http_request)

    def create_cart_for_a_client(self):
        http_request = HttpRequest.with_fields({'clientId': self.valid_client_id_field(),
                                                'password': self.valid_password_field_for_client()})
        return self._rest_interface.create_cart(http_request)

    def create_cart_for_a_client_with_invalid_credentials(self):
        http_request = HttpRequest.with_fields({'clientId': self.valid_client_id_field(),
                                                'password': self.invalid_password_field_for_client()})
        return self._rest_interface.create_cart(http_request)

    def list_cart(self, a_valid_cart_id):
        http_request = HttpRequest.with_fields({'cartId': a_valid_cart_id})
        return self._rest_interface.list_cart(http_request)

    def list_purchases_of_client(self):
        http_request = HttpRequest.with_fields({'clientId': self.valid_client_id_field(),
                                                'password': self.valid_password_field_for_client()})
        return self._rest_interface.list_purchases(http_request)

    def list_purchases_of_client_with_invalid_credentials(self):
        http_request = HttpRequest.with_fields({'clientId': self.valid_client_id_field(),
                                                'password': self.invalid_password_field_for_client()})
        return self._rest_interface.list_purchases(http_request)

    'private - chained requests'

    def add_to_cart_and_checkout(self):
        a_valid_cart_id = self.create_cart_and_retrive_cart_id()

        self.add_to_cart_with_valid_parameters(a_valid_cart_id)

        return self.checkout_cart(a_valid_cart_id)

    def create_cart_and_retrive_cart_id(self):
        response = self.create_cart_for_a_client()
        return self._cart_id_from(response.body())

    'private'

    def _cart_id_from(self, a_create_cart_response_body):
        return a_create_cart_response_body[2:]

    'assertions'

    def assert_is_ok_and_body_begins_with(self, response, a_string):
        self.assertEqual(response.status_code(), 200)
        self.assertTrue(response.body().startswith(a_string))

    def assert_is_ok_and_body_equals(self, response, a_string):
        self.assertEqual(response.status_code(), 200)
        self.assertEqual(response.body(), a_string)
