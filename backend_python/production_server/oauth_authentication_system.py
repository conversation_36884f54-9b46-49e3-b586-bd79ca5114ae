from system.authentication_system import AuthenticationSystem
from system.tus_libros_system import TusLibrosSystem


class OAuthAuthenticationSystem(AuthenticationSystem):

    def login(self, username, password):
        # Acá implementaríamos la comunicación con el sistema externo - Luciano.

        if not (username == 'juan' and password == 'laclavedejuan'):
            raise RuntimeError(TusLibrosSystem.authentication_error_message())
