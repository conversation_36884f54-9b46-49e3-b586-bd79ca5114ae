#!/usr/bin/env python3
"""
Interfaz de línea de comandos para el analizador de estadísticas de código Python.
Proporciona una CLI amigable para analizar archivos y directorios Python.
"""

import sys
import os
import argparse
from code_analyzer import PythonCodeAnalyzer


def main():
    """Función principal para la interfaz de línea de comandos."""
    parser = argparse.ArgumentParser(
        description='Analiza estadísticas de código Python',
        prog='python_stats_cli.py'
    )
    
    parser.add_argument(
        'path',
        help='Ruta al archivo o directorio Python a analizar'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Mostrar información detallada'
    )
    
    parser.add_argument(
        '--list-names', '-l',
        action='store_true',
        help='Listar los nombres de las clases encontradas'
    )

    args = parser.parse_args()
    
    # Verificar que la ruta existe
    if not os.path.exists(args.path):
        print(f"Error: La ruta '{args.path}' no existe.", file=sys.stderr)
        sys.exit(1)
    
    # Crear analizador
    analyzer = PythonCodeAnalyzer()
    
    # Determinar si es archivo o directorio
    if os.path.isfile(args.path):
        if not args.path.endswith('.py'):
            print(f"Error: '{args.path}' no es un archivo Python.", file=sys.stderr)
            sys.exit(1)
        
        if args.list_names:
            class_names = analyzer.get_class_names_in_file(args.path)
            print(f"Archivo: {args.path}")
            print(f"Clases encontradas: {len(class_names)}")
            if class_names:
                print("Nombres de clases:")
                for name in class_names:
                    print(f"  - {name}")
        else:
            class_count = analyzer.count_classes_in_file(args.path)
            print(f"Archivo: {args.path}")
            print(f"Clases encontradas: {class_count}")
        
    elif os.path.isdir(args.path):
        if args.list_names:
            class_info = analyzer.get_directory_class_info(args.path)
            total_classes = sum(len(classes) for classes in class_info.values())
            print(f"Directorio: {args.path}")
            print(f"Total de clases encontradas: {total_classes}")
            if class_info:
                print("Clases por archivo:")
                for file_path, class_names in class_info.items():
                    print(f"  {file_path}:")
                    for name in class_names:
                        print(f"    - {name}")
        else:
            class_count = analyzer.scan_directory(args.path)
            print(f"Directorio: {args.path}")
            print(f"Total de clases encontradas: {class_count}")
        
    else:
        print(f"Error: '{args.path}' no es un archivo ni directorio válido.", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
