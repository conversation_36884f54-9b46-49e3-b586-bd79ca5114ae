#!/usr/bin/env python3

import os
import re
import sys


class ClassCounter:
    """Class for counting Python classes in files."""

    def __init__(self, file_path=None):
        """Initialize the counter with an optional file path."""
        self.file_path = file_path
        self.content = None
        self.class_count = 0

    def count_classes_in_file(self, file_path=None):
        """Count Python classes defined in a file.

        Args:
            file_path: Path to the file to analyze. If None, uses the path from initialization.

        Returns:
            int: Number of classes found in the file.
        """
        if file_path is not None:
            self.file_path = file_path

        if self.file_path is None:
            raise ValueError("No file path provided")

        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
                # Simple regex to find class definitions
                class_matches = re.findall(r'^\s*class\s+\w+', self.content, re.MULTILINE)
                self.class_count = len(class_matches)
                return self.class_count
        except Exception as e:
            print(f"Error reading {self.file_path}: {e}")
            self.class_count = 0
            return 0

    def __call__(self, file_path=None):
        """Make the object callable, equivalent to count_classes_in_file."""
        return self.count_classes_in_file(file_path)


def scan_directory(directory, ignore_dirs=None):
    """Scan directory recursively and count Python classes.

    Args:
        directory: The directory to scan
        ignore_dirs: List of directory names to ignore (e.g., ['venv', '.git'])
                    These are matched against the basename of directories

    Returns:
        tuple: (total_classes, file_counts) where file_counts is a dict mapping
               file paths to class counts
    """
    total_classes = 0
    file_counts = {}

    # Create a ClassCounter instance to reuse
    counter = ClassCounter()

    # Convert ignore_dirs to a set for faster lookups
    ignore_set = set(ignore_dirs) if ignore_dirs else set()

    for root, dirs, files in os.walk(directory):
        # Modify dirs in-place to skip ignored directories
        if ignore_dirs:
            dirs[:] = [d for d in dirs if d not in ignore_set]

        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                class_count = counter(file_path)
                if class_count > 0:
                    file_counts[file_path] = class_count
                    total_classes += class_count

    return total_classes, file_counts

def main():
    if len(sys.argv) != 2:
        print("Usage: python count_classes.py <directory>")
        sys.exit(1)

    directory = sys.argv[1]
    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        sys.exit(1)

    total, file_counts = scan_directory(directory)

    print(f"\nFound {total} Python classes in {len(file_counts)} files:")
    for file_path, count in sorted(file_counts.items()):
        print(f"{file_path}: {count} classes")
    print(f"\nTotal Python classes: {total}")

# Compatibility function for existing tests
def count_classes_in_file(file_path):
    """Compatibility function that uses ClassCounter."""
    counter = ClassCounter()
    return counter(file_path)


if __name__ == "__main__":
    main()