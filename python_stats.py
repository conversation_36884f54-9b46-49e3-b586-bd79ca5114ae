#!/usr/bin/env python3
"""
Módulo para analizar estadísticas de código Python.
Implementación inicial siguiendo TDD.
"""

import re
import os


class PythonCodeAnalyzer:
    """Analizador de código Python para obtener estadísticas."""

    def count_classes_in_file(self, file_path):
        """
        Cuenta las clases definidas en un archivo Python.

        Args:
            file_path (str): Ruta al archivo Python a analizar

        Returns:
            int: Número de clases encontradas
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Buscar definiciones de clases usando regex
            # Patrón: 'class' seguido de espacios, nombre de clase, y ':'
            class_pattern = r'^\s*class\s+\w+.*:'
            class_matches = re.findall(class_pattern, content, re.MULTILINE)

            return len(class_matches)

        except Exception as e:
            # En caso de error, retornar 0
            return 0
