#!/usr/bin/env python3
"""
Módulo para analizar estadísticas de código Python.
Implementación inicial siguiendo TDD.
"""

import re
import os
import sys
import argparse


class PythonCodeAnalyzer:
    """Analizador de código Python para obtener estadísticas."""

    def count_classes_in_file(self, file_path):
        """
        Cuenta las clases definidas en un archivo Python.

        Args:
            file_path (str): Ruta al archivo Python a analizar

        Returns:
            int: Número de clases encontradas
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Buscar definiciones de clases usando regex
            # Patrón: 'class' seguido de espacios, nombre de clase, y ':'
            class_pattern = r'^\s*class\s+\w+.*:'
            class_matches = re.findall(class_pattern, content, re.MULTILINE)

            return len(class_matches)

        except Exception as e:
            # En caso de error, retornar 0
            return 0

    def scan_directory(self, directory_path):
        """
        Escanea un directorio recursivamente buscando archivos Python y cuenta todas las clases.

        Args:
            directory_path (str): Ruta al directorio a escanear

        Returns:
            int: Número total de clases encontradas en todos los archivos Python
        """
        total_classes = 0

        try:
            # Recorrer el directorio recursivamente
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    # Solo procesar archivos Python
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        classes_in_file = self.count_classes_in_file(file_path)
                        total_classes += classes_in_file

            return total_classes

        except Exception as e:
            # En caso de error, retornar 0
            return 0


def main():
    """Función principal para la interfaz de línea de comandos."""
    parser = argparse.ArgumentParser(
        description='Analiza estadísticas de código Python',
        prog='python_stats.py'
    )

    parser.add_argument(
        'path',
        help='Ruta al archivo o directorio Python a analizar'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Mostrar información detallada'
    )

    args = parser.parse_args()

    # Verificar que la ruta existe
    if not os.path.exists(args.path):
        print(f"Error: La ruta '{args.path}' no existe.", file=sys.stderr)
        sys.exit(1)

    # Crear analizador
    analyzer = PythonCodeAnalyzer()

    # Determinar si es archivo o directorio
    if os.path.isfile(args.path):
        if not args.path.endswith('.py'):
            print(f"Error: '{args.path}' no es un archivo Python.", file=sys.stderr)
            sys.exit(1)

        class_count = analyzer.count_classes_in_file(args.path)
        print(f"Archivo: {args.path}")
        print(f"Clases encontradas: {class_count}")

    elif os.path.isdir(args.path):
        class_count = analyzer.scan_directory(args.path)
        print(f"Directorio: {args.path}")
        print(f"Total de clases encontradas: {class_count}")

    else:
        print(f"Error: '{args.path}' no es un archivo ni directorio válido.", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
