#!/usr/bin/env python3
"""
Módulo para analizar estadísticas de código Python.
Implementación inicial siguiendo TDD.
"""

import re
import os
import sys
import argparse
import ast


class PythonCodeAnalyzer:
    """Analizador de código Python para obtener estadísticas."""

    def count_classes_in_file(self, file_path):
        """
        Cuenta las clases definidas en un archivo Python.

        Args:
            file_path (str): Ruta al archivo Python a analizar

        Returns:
            int: Número de clases encontradas
        """
        class_names = self.get_class_names_in_file(file_path)
        return len(class_names)

    def get_class_names_in_file(self, file_path):
        """
        Obtiene los nombres de las clases definidas en un archivo Python.

        Args:
            file_path (str): Ruta al archivo Python a analizar

        Returns:
            list: Lista de nombres de clases encontradas
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Buscar definiciones de clases usando regex
            # Patrón: 'class' seguido de espacios, capturar nombre de clase
            class_pattern = r'^\s*class\s+(\w+).*:'
            class_matches = re.findall(class_pattern, content, re.MULTILINE)

            return class_matches

        except Exception as e:
            # En caso de error, retornar lista vacía
            return []

    def scan_directory(self, directory_path):
        """
        Escanea un directorio recursivamente buscando archivos Python y cuenta todas las clases.

        Args:
            directory_path (str): Ruta al directorio a escanear

        Returns:
            int: Número total de clases encontradas en todos los archivos Python
        """
        class_info = self.get_directory_class_info(directory_path)
        return sum(len(classes) for classes in class_info.values())

    def get_directory_class_info(self, directory_path):
        """
        Escanea un directorio recursivamente y obtiene información de clases por archivo.

        Args:
            directory_path (str): Ruta al directorio a escanear

        Returns:
            dict: Diccionario con archivo como clave y lista de nombres de clases como valor
        """
        class_info = {}

        try:
            # Recorrer el directorio recursivamente
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    # Solo procesar archivos Python
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        class_names = self.get_class_names_in_file(file_path)
                        if class_names:  # Solo agregar si tiene clases
                            class_info[file_path] = class_names

            return class_info

        except Exception as e:
            # En caso de error, retornar diccionario vacío
            return {}


def main():
    """Función principal para la interfaz de línea de comandos."""
    parser = argparse.ArgumentParser(
        description='Analiza estadísticas de código Python',
        prog='python_stats.py'
    )

    parser.add_argument(
        'path',
        help='Ruta al archivo o directorio Python a analizar'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Mostrar información detallada'
    )

    parser.add_argument(
        '--list-names', '-l',
        action='store_true',
        help='Listar los nombres de las clases encontradas'
    )

    args = parser.parse_args()

    # Verificar que la ruta existe
    if not os.path.exists(args.path):
        print(f"Error: La ruta '{args.path}' no existe.", file=sys.stderr)
        sys.exit(1)

    # Crear analizador
    analyzer = PythonCodeAnalyzer()

    # Determinar si es archivo o directorio
    if os.path.isfile(args.path):
        if not args.path.endswith('.py'):
            print(f"Error: '{args.path}' no es un archivo Python.", file=sys.stderr)
            sys.exit(1)

        if args.list_names:
            class_names = analyzer.get_class_names_in_file(args.path)
            print(f"Archivo: {args.path}")
            print(f"Clases encontradas: {len(class_names)}")
            if class_names:
                print("Nombres de clases:")
                for name in class_names:
                    print(f"  - {name}")
        else:
            class_count = analyzer.count_classes_in_file(args.path)
            print(f"Archivo: {args.path}")
            print(f"Clases encontradas: {class_count}")

    elif os.path.isdir(args.path):
        if args.list_names:
            class_info = analyzer.get_directory_class_info(args.path)
            total_classes = sum(len(classes) for classes in class_info.values())
            print(f"Directorio: {args.path}")
            print(f"Total de clases encontradas: {total_classes}")
            if class_info:
                print("Clases por archivo:")
                for file_path, class_names in class_info.items():
                    print(f"  {file_path}:")
                    for name in class_names:
                        print(f"    - {name}")
        else:
            class_count = analyzer.scan_directory(args.path)
            print(f"Directorio: {args.path}")
            print(f"Total de clases encontradas: {class_count}")

    else:
        print(f"Error: '{args.path}' no es un archivo ni directorio válido.", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
