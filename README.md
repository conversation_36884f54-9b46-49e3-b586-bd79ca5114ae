# Instalación en Ubuntu (para desarrollo)

- Instalar MySQL Server: seguir las instrucciones de http://dev.mysql.com/doc/mysql-apt-repo-quick-guide/en/#apt-repo-fresh-install y para Ubuntu < 16.04 elegir una versión de mysql-server<=5.6 (recordar el usuario y password que serán usados más adelante)
- Instalar este paquete que contiene mysql-config: `sudo apt-get install libmysqlclient-dev`(ver https://www.digitalocean.com/community/tutorials/how-to-install-mysql-on-ubuntu-16-04)
- Crear virtualenv con interpreter Python 2.7 y activarla
- Instalar los requirements de dev:
```
pip install -r dev.txt
```
- Bajar base de datos con el fabfile (o de alguna otra fuente ie: 
https://drive.google.com/a/eryxsoluciones.com.ar/file/d/0B4mc15y3NZEWLTAyNThOVThmWkk/view?usp=sharing) e instalar de esta forma:
```
mysql -u root -pROOT_PASSWORD -e "CREATE DATABASE deliveryrun_dev;"
mysql -u root -pROOT_PASSWORD deliveryrun_dev < deliveryrun-structure.sql
mysql -u root -pROOT_PASSWORD deliveryrun_dev < db-data.sql
```
- Copiar local_settings.py.example a local_settings.py con la configuración de la base instalada en el primer paso
  - En DATABASES - default - cambiar 'USER' y 'PASSWORD' por los que se definieron al instalar mysql en el primer paso
- Asegurarse de que existan archivos que apunten a la configuracion de celery. Ejemplo con rutas absolutas:
```
ln -s conf/celery/local_schedule.py ~/work/projects/delivery/deliveryrun/run0km/run0km/celery_schedule.py
```
- Hacer `python manage.py migrate` en la primera carpeta *run0km*


# Instalación en Windows (para desarrollo)

- Instalar MySQL Server: bajar el installer del server soportado por el Windows en uso (ie: win10 puede usar mysql server 5.7.13, y alcanza con *solo* instalar el server)
- Agregar el directorio de binarios de MySQL a la variable de entorno global de PATH de Windows
- Crear virtualenv con interpreter Python 2.7 y activarla, configurar el distutils.cfg de Lib para que use Mingw32 para buildear
- Instalar pycrypto y MySQL-python==1.2.5 con easy_install en ese orden y el resto de los requerimientos de dev con pip
- Bajar base de datos con el fabfile (o de alguna otra fuente ie:
https://drive.google.com/a/eryxsoluciones.com.ar/file/d/0B4mc15y3NZEWLTAyNThOVThmWkk/view?usp=sharing) e instalar de esta forma:
```
mysql -u root -pROOT_PASSWORD -e "CREATE DATABASE deliveryrun_dev;"
mysql -u root -pROOT_PASSWORD deliveryrun_dev < deliveryrun-structure.sql
mysql -u root -pROOT_PASSWORD deliveryrun_dev < db-data.sql
```
- Copiar local_settings.py.example a local_settings.py con la configuración de la base instalada en el primer paso
  - En DATABASES - default - cambiar 'USER' y 'PASSWORD' por los que se definieron al instalar mysql
- Hacer `python manage.py migrate` en la primera carpeta *run0km*


# Para el desarrollo con MySQL es necesario ejecutar los siguientes comandos:
**EN UBUNTU:**
- $ `mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql`
- $ `mysql -u root -p -e "flush tables;" mysql`

NOTA: Afectan sólo información meta, no tienen relación con la base

**EN WINDOWS:**

If your system is one that has no zoneinfo database (for example, Windows), you can use a package that is available for download at the MySQL Developer Zone: http://dev.mysql.com/downloads/timezones.html. (POSIX standard)

Download a time zone package that contains SQL statements and unpack it, then load the package file contents into the time zone tables:

`shell> mysql -u ROOT_USERNAME -pROOT_PASSWORD mysql < file_name`

Then restart the server. 

# Installing Memcache (solo producción)
sudo apt-get install memcached

# Installing Celery
sudo apt-get install rabbitmq-server

# Celery Produccion (deben utilizarse paths absolutos)
ln -s conf/celery/produccion_schedule.py run0km/run0km/celery_schedule.py
ln -s conf/celery/produccion_config.py run0km/run0km/local_celeryconfig.py

# Celery Staging (deben utilizarse paths absolutos)
ln -s conf/celery/local_schedule.py run0km/run0km/celery_schedule.py
ln -s conf/celery/staging_config.py run0km/run0km/local_celeryconfig.py


# Uso de Celery en Desarollo
cd run0km
celery -A run0km worker --loglevel=INFO
