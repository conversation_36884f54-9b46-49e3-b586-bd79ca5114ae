# -*- coding: utf-8 -*-
# http://www.revsys.com/12days/async-workers-celery/
# https://www.caktusgroup.com/blog/2014/09/29/celery-production/
# actualizar_pedidos	diariamente a las 3:15
# deshabilitar_inactivos	dia 1 de cada mes
# setear_credito_mensual	mensualmente el dia 1.

# Envios de mails
# alarma_sin_asignar	diariamente a las 3:45
# alarma_inactividad diariamente a las 3hs
# enviar_reportes_programados cada lunes a las 5:00 y el 1ero de mes

# Servicios externos
# actualizar_estado_de_envios	cada hora (el primer minuto de cada hora).
# enviar_y_recibir_mensajes_de_whatsapp	cada minuto
# pedir_informacion_de_telefonos	cada 3 minutos
# pedir_informacion_de_redes_sociales	cada 15 minutos


# from datetime import timedelta

from celery.schedules import crontab
from django.utils import timezone

CELERY_BEAT_SCHEDULE = {
    # Testing
    # 'prueba_periodica': {
    #     'task': 'vendedores.tasks.cantidad_vendedores',
    #     #'schedule': crontab(minute=1),
    #     'schedule': crontab(minute='*/1'),
    #     'args': ()
    # },
    # Cambios de estados
    'actualizacion_diaria_de_pedidos_de_prospectos': {
        'task': 'prospectos.tasks.actualizar_pedidos_de_prospectos',
        'schedule': crontab(hour=3, minute=15),
        'args': ()
    },
    'procesar_peticiones_de_prospectos_de_vendedor': {
        'task': 'prospectos.tasks.procesar_peticiones_de_prospectos_de_vendedor',
        'schedule': crontab(minute='*/5'),
        'args': ()
    },
    'circular_prospectos': {
        'task': 'prospectos.tasks.circular_prospectos',
        'schedule': crontab(minute='*/15'),
        'args': ()
    },
    'actualizacion_diaria_de_solicitudes_de_amistad': {
        'task': 'occ.tasks.actualizar_solicitudes_de_amistad_enviadas',
        'schedule': crontab(hour=4, minute=0),
        'kwargs': {'fecha_desde': timezone.now() - timezone.timedelta(days=1), 'fecha_hasta': timezone.now()}
    },
    'deshabilitar_vendedores_inactivos': {
        'task': 'vendedores.tasks.deshabilitar_vendedores_inactivos',
        'schedule': crontab(minute=0, hour=0, day_of_month=1),
        'args': ()
    },
    'setear_credito_mensual': {
        'task': 'occ.tasks.setear_credito_mensual',
        'schedule': crontab(minute=10, hour=0, day_of_month=1),
        'args': ()
    },
    # Envios de mails
    'enviar_reportes_mensuales': {
        'task': 'reportes.tasks.enviar_reportes_programados',
        'schedule': crontab(minute=30, hour=4, day_of_month=1),
        'args': ()
    },
    'enviar_reportes_semanales': {
        'task': 'reportes.tasks.enviar_reportes_programados',
        'schedule': crontab(minute=0, hour=5, day_of_week='mon'),
        'args': ()
    },
    'alarmas_por_prospectos_no_asignados': {
        'task': 'vendedores.tasks.enviar_mails_por_prospectos_no_asignados',
        'schedule': crontab(hour=3, minute=43),
        'args': ()
    },
    'enviar_mails_por_inactividad_a_vendedores': {
        'task': 'vendedores.tasks.enviar_mails_por_inactividad_a_vendedores',
        'schedule': crontab(hour=3, minute=3),
        'args': ()
    },
    'enviar_mails_por_inactividad_a_supervisores': {
        'task': 'vendedores.tasks.enviar_mails_por_inactividad_a_supervisores',
        'schedule': crontab(hour=3, minute=13),
        'args': ()
    },
    'enviar_mails_de_no_asignados_a_supervisores': {
        'task': 'vendedores.tasks.enviar_mails_de_no_asignados_a_supervisores',
        'schedule': crontab(hour=3, minute=18),
        'args': ()
    },
    'enviar_mails_a_logistica': {
        'task': 'vendedores.tasks.enviar_mails_a_logistica',
        'schedule': crontab(hour=3, minute=23),
        'args': ()
    },
    # # Servicios
    'actualizar_estado_de_envios_de_sms': {
        'task': 'occ.tasks.actualizar_estado_de_envios_de_sms',
        'schedule': crontab(hour='*/1', minute=10),
        'args': ()
    },
    'enviar_y_recibir_mensajes_de_whatsapp': {
        'task': 'conversaciones.tasks.enviar_y_recibir_mensajes_de_whatsapp',
        'schedule': crontab(minute='*/5'),
        'args': ()
    },
    # # Deshabilitado porque el servicio de redes sociales no funciona.
    # # 'pedir_informacion_de_redes_sociales': {
    # #     'task': 'prospectos.tasks.pedir_informacion_de_redes_sociales',
    # #     'schedule': crontab(minute='*/16'),
    # #     'args': ()
    # # },
    'enviar_integraciones': {
        'task': 'occ.tasks.enviar_integraciones',
        'schedule': crontab(minute=55, hour='22-23,0-8'),
        'args': ()
    },
    'actualizar_modelos_del_crm': {
        'task': 'propuestas.tasks.actualizar_modelos_del_crm',
        'schedule': crontab(hour=1, minute=55),
        'args': ()
    },
    'sincronizar_cuentas_de_email': {
        'task': 'occ.tasks.sincronizar_cuentas_de_email',
        'schedule': crontab(minute='*/4'),
        'args': ()
    },
    # 'backup_database': {
    #     'task': 'core.tasks.backup_database',
    #     'schedule': crontab(hour=2, minute=35),
    #     'args': ()
    # },
    # # 'enviar_notificacion_fcm_de_mensajes_de_las_ultimas_horas': {
    # #     'task': 'mobileapi.tasks.enviar_notificacion_fcm_de_mensajes_de_las_ultimas_horas',
    # #     'schedule': crontab(hour='*/3'),
    # #     'args': ()
    # # },

}
