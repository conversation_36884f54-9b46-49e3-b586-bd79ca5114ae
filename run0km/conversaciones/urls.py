from django.conf.urls import url

from conversaciones.views.conversacion_unificada_json_view import ConversacionUnificadaJSONView
from conversaciones.views.enviar_mensaje_view_html import EnviarMensajeViewHtml
from conversaciones.views.enviar_mensaje_view_json import EnviarMensajeViewJson
from conversaciones.views.marcar_conversacion_como_leida_view import MarcarConversacionComoLeidaView
from conversaciones.views.eliminar_conversacion_view import EliminarConversacionView
from conversaciones.views.conversacion_unificada_html_view import ConversacionUnificadaHTMLView
from conversaciones.views.conversaciones_view import ConversacionesView

urlpatterns = [
    url(r'^todas/$', ConversacionesView.as_view(), name='lista-de-conversaciones'),
    url(r'^conversacion-unificada/(?P<objeto_id>\d+)/(?P<tipo>\w+)$',
        ConversacionUnificadaHTMLView.as_view(), name='conversacion-unificada'),
    url(r'^conversacion-unificada/(?P<objeto_id>\d+)/(?P<tipo>\w+)/json$',
        ConversacionUnificadaJSONView.as_view(), name='conversacion-unificada-json'),
    url(r'^enviar-mensaje/$', EnviarMensajeViewHtml.as_view(), name='enviar-mensaje'),
    url(r'^enviar-mensaje/json/$', EnviarMensajeViewJson.as_view(), name='enviar-mensaje-json'),
    url(r'^eliminar/$', EliminarConversacionView.as_view(), name='eliminar-conversacion'),
    url(r'^marcar-conversacion-como-leida/$',
        MarcarConversacionComoLeidaView.as_view(), name='marcar-conversacion-como-leida'),
]
