from django.db import models
from django.db.models import Q
from django.utils import timezone
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist


class ConversacionQuerySet(models.QuerySet):
    def de_vendedor(self, vendedor):
        # Los supervisores pueden ver las conversaciones de SMS de sus vendedores, pero no las de Whatsapp o Chat.
        if vendedor.es_supervisor():
            return self.filter(Q(prospecto__vendedor=vendedor) |
                               Q(prospecto__responsable=vendedor, prospecto__vendedor__isnull=False,
                                 tipo=self.model.TIPO_SMS),
                               eliminada=False)
        return self.filter(prospecto__vendedor=vendedor, eliminada=False)

    def ordenar_por_fecha_de_ultima_respuesta(self):
        return self.order_by("-fecha_ultima_respuesta")

    def conversacion_de_tipo(self, prospecto, tipo):
        return self.get(prospecto=prospecto, tipo=tipo)

    def finalizadas(self):
        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_COMO_MENSAJE_NO_LEIDO)
        return self.filter(fue_leida=False, fecha_ultimo_mensaje__lt=fecha_limite)

    def no_leidas(self):
        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_COMO_MENSAJE_NO_LEIDO)
        return self.filter(fue_leida=False, fecha_ultimo_mensaje__gte=fecha_limite)

    def leidas(self):
        return self.filter(fue_leida=True)

    def de_medio(self, tipo_de_medio):
        return self.filter(tipo=tipo_de_medio)

    def excluir_conversaciones_de_chat_mal_formadas(self):
        """
        TODO: verificar como pueden generarse estas conversaciones, agregar validacn en el Model
        """
        return self.exclude(tipo=self.model.TIPO_CHAT, chat_de_ventas_convertido__isnull=True)


    def eliminar_conversacion_de_prospecto(self, prospecto):
        # Por ahora solo se eliminan conversaciones de Whatsapp o Chat
        try:
            conversacion = self.get(prospecto=prospecto, tipo__in=[self.model.TIPO_WHATSAPP, self.model.TIPO_CHAT])
        except ObjectDoesNotExist:
            return False
        conversacion.eliminada = True
        conversacion.save()
        conversacion.prospecto.mensajes.update(eliminado=True)
        return True

    def cantidad_no_leidas_de_vendedor(self, vendedor):
        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_COMO_MENSAJE_NO_LEIDO)
        cantidad = self.filter(prospecto__vendedor=vendedor, eliminada=False, fue_leida=False,
                               fecha_ultima_respuesta__gte=fecha_limite).count()
        return cantidad

    def cantidad_de_mensajes_no_leidos_de_vendedor(self, vendedor):
        from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
        from occ.models import RespuestaDeMensaje, EnvioDeEmail

        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_COMO_MENSAJE_NO_LEIDO)
        lista_de_prospectos_del_vendedor = list(
            self.filter(prospecto__vendedor=vendedor, eliminada=False, fue_leida=False,
                        fecha_ultima_respuesta__gte=fecha_limite).values_list('prospecto_id',
                                                                              flat=True))

        cantidad_de_mensajes_de_sms_no_leidos = RespuestaDeMensaje.objects.filter(
            envio__prospecto__in=lista_de_prospectos_del_vendedor, leida=False).count()

        cantidad_de_mensajes_de_whatsapp_no_leidos = MensajesWhatsapp.objects.filter(
            prospecto__in=lista_de_prospectos_del_vendedor, emisor=MensajesWhatsapp.CLIENTE).exclude(
            estado=MensajesWhatsapp.COMPLETO).count()

        cantidad_de_mensajes_de_email_no_leidos = EnvioDeEmail.objects.filter(
            _prospecto__in=lista_de_prospectos_del_vendedor, _emisor=EnvioDeEmail.CLIENTE, _leido=False).count()

        cantidad_de_no_leidos = cantidad_de_mensajes_de_sms_no_leidos + cantidad_de_mensajes_de_whatsapp_no_leidos + \
                                cantidad_de_mensajes_de_email_no_leidos
        return cantidad_de_no_leidos

    def marcar_conversacion_como_leida(self, prospecto, tipo):
        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_COMO_MENSAJE_NO_LEIDO)
        self.filter(prospecto=prospecto, tipo=tipo, fecha_ultimo_mensaje__gte=fecha_limite).update(fue_leida=True)

    def conversacion_whatsapp_de_prospecto(self, prospecto):
        return self.conversacion_de_tipo(prospecto=prospecto, tipo=self.model.TIPO_WHATSAPP)

    def conversacion_email_de_prospecto(self, prospecto):
        return self.conversacion_de_tipo(prospecto=prospecto, tipo=self.model.TIPO_EMAIL)


