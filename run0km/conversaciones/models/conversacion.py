from django.conf import settings
from django.db import models
from django.utils import timezone

from conversaciones.managers.conversacion_query_set import ConversacionQuerySet
from conversaciones.medios import MedioDeConversacion
from core.notificador.decorators import sincronizar_conversacion_modificada, sincronizar_creacion_de_conversacion


class Conversacion(models.Model):
    TIPO_SMS = 'S'
    TIPO_WHATSAPP = 'W'
    TIPO_CHAT = 'C'
    TIPO_EMAIL = 'E'

    OPCIONES_TIPO = ((TIPO_SMS, 'SMS'), (TIPO_WHATSAPP, 'Whatsapp'), (TIPO_CHAT, 'Chat'), (TIPO_EMAIL, 'Email'))

    prospecto = models.ForeignKey('prospectos.Prospecto', related_name='conversacion')
    tipo = models.CharField(max_length=1, choices=OPCIONES_TIPO)
    fecha_ultima_respuesta = models.DateTimeField(blank=True, null=True)
    fecha_ultimo_mensaje = models.DateTimeField()
    fue_leida = models.BooleanField(default=True)
    eliminada = models.BooleanField(default=False)

    objects = ConversacionQuerySet.as_manager()

    class Meta:
        unique_together = ('prospecto', 'tipo')

    def marcar_como_leida(self):
        self.medio().marcar_como_leidos_mensajes_de(self.prospecto)
        self.fue_leida = True
        self.full_clean()
        self.save()

    def eliminar(self):
        self.medio().eliminar_mensajes_de(self.prospecto)
        self.eliminada = True
        self.save()

    def es_de_whatsapp(self):
        return self.es_de_tipo(self.TIPO_WHATSAPP)

    def es_de_chat(self):
        return self.es_de_tipo(self.TIPO_CHAT)

    def es_de_email(self):
        return self.es_de_tipo(self.TIPO_EMAIL)

    def es_de_tipo(self, tipo):
        return self.tipo == tipo

    def tiene_telefono_para_whatsapp(self):
        return self.prospecto.tiene_telefono_para_whatsapp()

    def imagen_de_perfil(self):
        return self.prospecto.imagen_de_perfil()

    def nombre(self):
        return self.prospecto.nombre or self.prospecto.nombre_alternativo or "(Sin nombre)"

    def fue_leida_o_es_vieja(self):
        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_COMO_MENSAJE_NO_LEIDO)
        return self.fue_leida or self.fecha_ultimo_mensaje < fecha_limite

    def esta_leida(self):
        return self.fue_leida

    def mensajes(self, cantidad=None):
        medio = self.medio()
        mensajes = medio.mensajes_para(self, cantidad)
        return mensajes

    def cantidad_de_mensajes(self):
        return len(self.mensajes())

    def medio(self):
        medio = MedioDeConversacion.nuevo_para(self.tipo)
        return medio

    def nombre_de_medio(self):
        return self.medio().nombre_para(self)

    def obtener_vendedor(self):
        return self.prospecto.vendedor

    @sincronizar_conversacion_modificada
    def _hacer_modificaciones_luego_de_agregar_un_mensaje(self, mensaje):
        fecha = mensaje.obtener_fecha()
        self.fecha_ultimo_mensaje = fecha

        if mensaje.proveniente_de_cliente():
            self.fecha_ultima_respuesta = fecha
            self.fue_leida = False

        self.eliminada = False
        self.full_clean()
        self.save()
        return self

    @classmethod
    @sincronizar_creacion_de_conversacion
    def nuevo(cls, tipo, prospecto, fecha_ultima_respuesta, fecha_ultimo_mensaje, fue_leida, eliminada):
        conversacion = cls.objects.create(tipo=tipo, prospecto=prospecto, fecha_ultima_respuesta=fecha_ultima_respuesta,
                                          fecha_ultimo_mensaje=fecha_ultimo_mensaje, fue_leida=fue_leida,
                                          eliminada=eliminada)
        return conversacion

    @classmethod
    def nuevo_mensaje(cls, mensaje):
        prospecto_id = mensaje.id_de_prospecto()
        tipo = mensaje.tipo_de_chat()
        fecha = mensaje.fecha
        if not fecha:
            fecha = timezone.now()
        defaults = {'fecha_ultimo_mensaje': fecha,
                    'eliminada': False}

        proveniente_de_cliente = mensaje.proveniente_de_cliente()
        if proveniente_de_cliente:
            defaults['fecha_ultima_respuesta'] = mensaje.fecha
            defaults['fue_leida'] = False
        else:
            defaults['fecha_ultima_respuesta'] = None
            defaults['fue_leida'] = True

        try:
            conversacion = cls.objects.get(prospecto_id=prospecto_id, tipo=tipo)
            return conversacion._hacer_modificaciones_luego_de_agregar_un_mensaje(mensaje)
        except cls.DoesNotExist:
            from prospectos.models import Prospecto
            prospecto = Prospecto.objects.get(id=prospecto_id)
            cls.nuevo(tipo=tipo, prospecto=prospecto, **defaults)

    @classmethod
    def tipos(cls):
        return [cls.TIPO_SMS, cls.TIPO_WHATSAPP, cls.TIPO_CHAT, cls.TIPO_EMAIL]