# coding=utf-8
from django.db import models
from django.utils import timezone

from conversaciones.managers import MensajesWhatsappManager, MensajesWhatsappQuerySet
from conversaciones.models.conversacion import Conversacion
from occ.models import OrigenDesconocido


class MensajesWhatsapp(models.Model):
    """
    Cuando se escribe un mensaje WP en un chat, se crea un objeto de esta clase.
    Esta clase modela un mensaje en ese contexto, en ese chat.
    Tanto si es del vendedor al posible comprador como al revés.
    Modela no solo el mensaje sino el estado de envio del mismo.
    Forma implicitamente una cola de envio de mensajes.
    Un proceso externo (Celery) los va procesando y enviando efectivamente.

    Nota:
    La clase WhastappMessage modela un mensaje de Whatsapp sin un prospecto asociado.
    """
    VENDEDOR = 'V'
    CLIENTE = 'C'

    PENDIENTE = 'P'
    INCOMPLETO = 'I'
    COMPLETO = 'C'
    OPCIONES_EMISOR = ((VENDEDOR, 'Vendedor'), (CLIENTE, 'Cliente'))
    OPCIONES_ESTADO = ((PENDIENTE, 'Pendiente'), (INCOMPLETO, 'Incompleto'), (COMPLETO, 'Completo'))
    # Si el emisor es el Cliente el estado puede ser Pendiente (No leido) o Completo (Leido)
    # Si es el Vendedor el estado puede ser Pendiente (No enviado por API), Incompleto (Enviado por API pero no al
    # telefono) o Completo (Enviado por API y tambien al telefono)

    prospecto = models.ForeignKey('prospectos.Prospecto', related_name='mensajes')
    _origen = models.ForeignKey('occ.OrigenDeMensaje', related_name='_mensajes_whatsapp')
    telefono = models.CharField(max_length=64, blank=True, default='')
    mensaje = models.CharField(max_length=600, blank=True, default='')
    emisor = models.CharField(max_length=1, choices=OPCIONES_EMISOR, default=VENDEDOR)
    estado = models.CharField(max_length=1, choices=OPCIONES_ESTADO, default=PENDIENTE)
    fecha = models.DateTimeField()
    eliminado = models.BooleanField(default=False)
    #TODO: verificar si esto esta ok (descomentar para generar migracion)

    objects = MensajesWhatsappManager.from_queryset(MensajesWhatsappQuerySet)()

    def origen(self):
        return self._origen

    def cambiar_origen(self, origen):
        self._origen = origen
        self.full_clean()
        self.save()

    def obtener_emisor(self):
        return self.emisor

    def obtener_fecha(self):
        return self.fecha

    def obtener_estado(self):
        return self.estado

    def texto(self):
        return self.mensaje

    # Mensaje de conveniencia
    def obtener_texto(self):
        return self.texto()

    def tipo_de_chat(self):
        return Conversacion.TIPO_WHATSAPP

    def proveniente_de_cliente(self):
        return self.emisor == self.CLIENTE

    def nombre_de_emisor(self):
        if self.proveniente_de_cliente():
            return self.prospecto.nombre
        else:
            return self.prospecto.vendedor.full_name()

    def fue_leido(self):
        return self.estado == self.COMPLETO

    def fue_enviado_por_api(self):
        return self.estado == self.INCOMPLETO or self.estado == self.COMPLETO

    def fue_enviado_al_telefono(self):
        return self.estado == self.COMPLETO

    def id_de_prospecto(self):
        return self.prospecto_id

    def responsable(self):
        return self.prospecto.obtener_vendedor() or self.prospecto.obtener_responsable()

    @classmethod
    def nuevo_mensaje(cls, prospecto, mensaje, telefono, fecha=None):
        mensaje = cls.nuevo(prospecto=prospecto, telefono=telefono, mensaje=mensaje,
                            emisor=cls.VENDEDOR, fecha=fecha or timezone.now())
        return mensaje

    @classmethod
    def nueva_respuesta(cls, prospecto, mensaje, telefono):
        mensaje = cls.nuevo(prospecto=prospecto, mensaje=mensaje, telefono=telefono,
                            emisor=cls.CLIENTE, fecha=timezone.now())
        return mensaje

    @classmethod
    def nuevo(cls, prospecto, mensaje, telefono, emisor, fecha, estado=PENDIENTE):
        obj = cls(prospecto=prospecto, mensaje=mensaje, telefono=telefono,
                  emisor=emisor, fecha=fecha, estado=estado, _origen=OrigenDesconocido.nuevo())
        obj.save()
        Conversacion.nuevo_mensaje(obj)
        return obj

    @classmethod
    def nuevo_ya_enviado(cls, prospecto, mensaje, telefono, emisor, fecha):
        obj = cls.nuevo(prospecto=prospecto,
                        mensaje=mensaje,
                        telefono=telefono,
                        emisor=emisor,
                        fecha=fecha,
                        estado=cls.COMPLETO)
        return obj

    class Meta:
        verbose_name = 'Mensaje de whatsapp'
        verbose_name_plural = 'Mensajes de whatsapp'

    def __str__(self):
        return self.telefono