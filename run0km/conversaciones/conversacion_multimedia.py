# coding=utf-8
"""

La Conversacion Multimedia reune todas las Conversaciones del Prospecto (SMS, Mail, Whatsapp, Chat).

"""


class ConversacionMultimedia(object):
    @classmethod
    def nueva(cls, prospecto):
        conversacion = cls(prospecto=prospecto)
        return conversacion

    def __init__(self, prospecto, **kwargs):
        self.prospecto = prospecto

    def mensajes(self):
        conversaciones = self.prospecto.obtener_conversaciones()
        todos_los_mensajes = []
        for conversacion in conversaciones:
            mensajes = conversacion.mensajes()
            todos_los_mensajes += mensajes
        return todos_los_mensajes
