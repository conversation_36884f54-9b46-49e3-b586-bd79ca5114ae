from django.core.exceptions import PermissionDenied
from django.http import JsonResponse
from django.views import View

from conversaciones.renderer_conversacion_multimedio import ConversacionMultimedioRender
from layers.application.commands.conversaciones.enviar_mensaje_multimedio import EnviarMensajeMultimedio


class EnviarMensajeViewJson(View):
    def dispatch(self, request, *args, **kwargs):
        user = request.user
        if not user.is_vendedor():
            raise PermissionDenied()
        return super().dispatch(request, *args, **kwargs)

    def post(self, request):
        user = request.user
        id_objeto = request.POST.get('prospecto_pk')
        texto = request.POST.get('texto', '').strip()
        tipo = request.POST.get('tipo')

        command = EnviarMensajeMultimedio()
        command.set_sender(user)
        command.set_id_de_objeto(id_objeto)
        command.set_tipo(tipo)
        command.set_contenido(texto)
        result = command.execute()

        return self._handle_result(result)

    # --- métodos privados ---

    def _handle_result(self, result):
        if result.is_successful():
            response = {
                'status': True
            }
        else:
            response = {'status': False, 'mensaje': result.errors_as_string()}
        return JsonResponse(response)