from django.http import JsonResponse
from django.views import View

from conversaciones.renderer_conversacion_multimedio import ConversacionMultimedioRender
from layers.application.commands.conversaciones.ver_conversacion_multimedio import VerConversacionMultimedio

'''
    Se diferencia de ConversacionUnificadaHTMLView ya que si bien ambas responden a un json,
    la HTML responde a una conversación expresada en HTML, en cambio ConversacionUnificadaJSONView
    responde a la conversación expresada en JSON.
'''

class ConversacionUnificadaJSONView(View):
    def get(self, request, objeto_id, tipo):
        user = request.user

        command = VerConversacionMultimedio()
        command.set_lector(user)
        command.set_id_de_objeto(objeto_id)
        command.set_tipo(tipo)
        result = command.execute()
        return self._manejar_resultado(result)

    def _manejar_resultado(self, result):
        # TODO: Esto lo estamos haciendo "manualmente" aca porque el JSONBuilder no soporta bien este conversion
        # de resultado a json. Este endpoint no parece estar respetando la misma convencion de como devolver los jsons
        # que los otros endpoints si cumplen.
        from rest_framework import status

        # if result.is_successful():
        #     mensaje = 'Operacion exitosa'
        # else:
        #     mensaje = 'Error al abrir la conversación. Intente nuevamente más tarde.'

        contexto = result.get_object()
        conversacion_json = contexto.evaluar()
        variables = conversacion_json['variables']
        mensajes = conversacion_json['mensajes']
        fue_leido = True
        json_resultado = {'status': result.is_successful(), 'mensajes': mensajes, 'variables': variables, 'fue_leido': fue_leido}

        return JsonResponse(json_resultado, status=status.HTTP_200_OK)
