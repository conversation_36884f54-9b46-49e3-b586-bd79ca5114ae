from django.http import JsonResponse
from django.views import View

from conversaciones.models import Conversacion
from occ.models.eavisos import ConversacionDeEAvisos



"""
    TODO: unificar el uso del gestor de conversaciones
"""

class MarcarConversacionComoLeidaView(View):
    def post(self, request):
        try:
            data = request.POST
            conversacion_id = data['conversacion_id']

            if data['tipo_conversacion'] == ConversacionDeEAvisos.TIPO:
                conversacion = ConversacionDeEAvisos.objects.get(id=conversacion_id)
            else:
                conversacion = Conversacion.objects.get(id=conversacion_id)

            conversacion.marcar_como_leida()
            return JsonResponse({
                'status': True
            })
        except Conversacion.DoesNotExist:
            return JsonResponse({
                'status': False
            })
