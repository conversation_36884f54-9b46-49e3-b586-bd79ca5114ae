from datetime import timed<PERSON><PERSON>
from freezegun import freeze_time

from django.core.exceptions import ValidationError
from conversaciones.gestor import GestorDeConversaciones
from conversaciones.medios import MedioWhatsapp
from conversaciones.meta.canal_de_comunicacion_via_meta import CanalDeComunicacionDeWhatsappViaMeta
from conversaciones.meta.criterio_de_seleccion_para_operador_de_meta import CriterioDeSeleccionParaOperadorDeMeta
from conversaciones.meta.notificador_de_conversaciones_meta_mock import NotificadorDeConversacionesMetaMock
from conversaciones.meta.whatsapp_meta_service_mock import WhatsappMetaServiceMock
from conversaciones.models import MensajeDeWhatsappConProspecto
from core.date_helper import DatetimeHelper
from core.models import Sistema
from notificaciones.tests.soporte import NotificacionesTestHelper
from prospectos.models import Prospecto
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.base import BaseFixturedTest
from testing.test_utils import reload_model
from whatsapp.meta.models.operador import GrupoOperadores, Operador


class TestConversacionConProspectoMedianteMeta(BaseFixturedTest):

    def setUp(self):
        super().setUp()
        self._calendario = DatetimeHelper()

        self.supervisor_uno = self.fixture['sup_1']
        self.vendedor_uno = self.fixture['vend_1']
        self.vendedor_dos = self.fixture['vend_2']
        self.telefono_del_prospecto = '48764549'
        self.prospecto_de_vendedor_dos = self._crear_prospecto_vendedor_dos()
        self.operador = self._crear_operador()
        grupo_de_operadores = GrupoOperadores.nuevo_con(nombre='grupo_uno', operadores=[self.operador])
        self.vendedor_uno.obtener_concesionaria().agregar_grupo_de_operadores(grupo_de_operador=grupo_de_operadores)

        self.prospecto_de_vendedor_uno = self._crear_prospecto_vendedor_uno()
        self.prospecto_viejo_de_vendedor_uno = self._crear_prospecto_viejo_vendedor_uno()
        self.prospecto_nuevo_de_vendedor_uno = self._crear_prospecto_nuevo_vendedor_uno()

        # Meta
        self._servicio_de_meta = WhatsappMetaServiceMock.nuevo()
        self._canal_via_meta = self._crear_canal_via_meta()
        self._gestor_de_conversaciones_del_vendedor_uno = self._crear_gestor_para_vendedor_uno()
        self._gestor_de_conversaciones_del_vendedor_dos = self._crear_gestor_para_vendedor_dos()
        self._configurar_al_servicio_de_meta_para_acepte_nuevas_conversaciones()
        self._configurar_al_servicio_de_meta_para_acepte_nuevos_mensajes(
            self._id_de_meta_del_mensaje_enviado_por_el_vendedor())

        # Notificaciones
        self._notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._notificaciones_helper.habilitar_whatsapp_para(self.vendedor_uno)
        self._notificaciones_helper.habilitar_whatsapp_para(self.vendedor_dos)

    def test_los_mensajes_en_espera_son_parte_de_las_conversaciones_de_delivery(self):
        # Dado / Parte del setup

        # Cuando
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(), medio=MedioWhatsapp.nuevo())

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_uno, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())

    def test_las_respuestas_del_destinatario_son_parte_de_las_conversaciones_en_delivery(self):
        # Dado
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(), medio=MedioWhatsapp.nuevo())

        numero_de_telefono_del_remitente = '549' + self.telefono_del_prospecto
        notificacion_con_respuesta_del_destinatario = self._respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente)

        # Cuando
        self._canal_via_meta.actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_uno, cantidad_de_mensajes=2, tipo=MedioWhatsapp.tipo())
        conversacion = self._gestor_de_conversaciones_del_vendedor_uno.conversacion_multimedia_para(
            self.prospecto_de_vendedor_uno)
        respuesta_de_cliente = conversacion.mensajes()[0]
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

    def test_al_recibir_una_respuesta_de_un_telefono_normalizado_se_asocia_al_prospecto_con_su_telefono_sin_normalizar(
            self):
        # Dado
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_viejo_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        # Normalizado significa con el codigo de pais!
        numero_de_telefono_del_remitente = '54948764400'
        notificacion_con_respuesta_del_destinatario = self._respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente)

        # Cuando
        self._canal_via_meta.actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_nuevo_de_vendedor_uno, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())
        respuesta_de_cliente = MensajeDeWhatsappConProspecto.objects.filter(
            _prospecto=self.prospecto_nuevo_de_vendedor_uno).last()
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

        # TODO: VERIFICAR SI ES UN MENSAJE O DOS LOS QUE DEBERÍAN TENER EN LA CONVERSACION

    def test_al_recibir_una_respuesta_del_destinatario_se_utiliza_el_prospecto_mas_nuevo_que_contenga_su_telefono(self):
        # Dado
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_viejo_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        numero_de_telefono_del_remitente = '48764400'
        notificacion_con_respuesta_del_destinatario = self._respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente)

        # Cuando
        self._canal_via_meta.actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_nuevo_de_vendedor_uno, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())
        respuesta_de_cliente = MensajeDeWhatsappConProspecto.objects.filter(
            _prospecto=self.prospecto_nuevo_de_vendedor_uno).last()
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

    def test_al_recibir_una_respuesta_no_contampla_como_receptor_un_telefono_extra_inactivo(
            self):
        # Dado
        telefono_nuevo = '48764401'
        self._configurar_telefono_prospecto_viejo_de_venedor_uno(telefono=telefono_nuevo)
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor_uno)
        telefono_extra = gestor.agregar_telefono_extra_a_prospecto(self.prospecto_nuevo_de_vendedor_uno,
                                                                   telefono=telefono_nuevo)
        gestor.toggle_telefono_extra_activo(prospecto=self.prospecto_nuevo_de_vendedor_uno,
                                            id_telefono=telefono_extra.id)

        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_viejo_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        # Normalizado significa con el codigo de pais!
        numero_de_telefono_del_remitente = telefono_nuevo
        notificacion_con_respuesta_del_destinatario = self._respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente)

        # Cuando
        self._canal_via_meta.actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_viejo_de_vendedor_uno, cantidad_de_mensajes=2, tipo=MedioWhatsapp.tipo())

    def test_al_tener_mas_de_un_numero_de_telefono_se_asocia_segun_un_telefono_activo(self):
        # Dado
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor_uno)
        gestor.agregar_telefono_extra_a_prospecto(self.prospecto_de_vendedor_uno, telefono='48764401')
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        numero_de_telefono_del_remitente = '48764401'
        notificacion_con_respuesta_del_destinatario = self._respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente)

        # Cuando
        self._canal_via_meta.actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_uno, cantidad_de_mensajes=2, tipo=MedioWhatsapp.tipo())
        respuesta_de_cliente = MensajeDeWhatsappConProspecto.objects.filter(
            _prospecto=self.prospecto_de_vendedor_uno).last()
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

    def test_al_iniciar_una_conversacion_se_crea_un_enlace_entre_operador_receptor_prospecto(self):
        # Dado Parte del setup

        # Cuando
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            self.prospecto_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(), medio=MedioWhatsapp.nuevo())

        # Entonces
        self.assertTrue(self._canal_via_meta.existe_un_enlace_entre(
            operador=self._canal_via_meta.operador_para_prospecto(self.prospecto_de_vendedor_uno),
            telefono_destinatario=self.telefono_del_prospecto,
            prospecto=self.prospecto_de_vendedor_uno))

    def test_al_recibir_una_respuesta_no_contampla_como_receptor_un_telefono_principal_inactivo(
            self):
        # Dado
        telefono_nuevo = '48764401'
        self._configurar_telefono_prospecto_viejo_de_venedor_uno(telefono=telefono_nuevo)
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor_uno)
        gestor.agregar_telefono_extra_a_prospecto(
            prospecto=self.prospecto_viejo_de_vendedor_uno, telefono=telefono_nuevo)
        gestor.toggle_telefono_activo(prospecto=self.prospecto_viejo_de_vendedor_uno)

        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            prospecto=self.prospecto_viejo_de_vendedor_uno,
            texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        notificacion_con_respuesta_del_destinatario = self._respuesta_del_usuario_a_un_mensaje(telefono_nuevo)

        # Cuando
        self._canal_via_meta.actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_viejo_de_vendedor_uno,
            cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())
        conversacion = self._canal_via_meta.conversacion_con_operador(
            telefono_destinatario=telefono_nuevo,
            operador=self.operador)
        self.assertTrue(conversacion.is_open())

    def test_si_ya_existe_un_enlace_con_otro_vendedor_con_variante_de_numero_lanza_error(self):
        # Dado
        self._configurar_al_prospecto_vendedor_dos_con_mismo_telefono_prospecto_vendedor_uno_con_codigo_pais()
        self._enviar_whatsapp_al_prospecto_vendedor_uno()

        # Cuando / Entonces
        self.assertRaisesMessage(ValidationError, 'No es posible iniciar conversación con este número',
                                 self._gestor_de_conversaciones_del_vendedor_dos.enviar_a,
                                 prospecto=self.prospecto_de_vendedor_dos,
                                 texto=self._texto_enviado_por_el_vendedor(),
                                 medio=MedioWhatsapp.nuevo())

    def test_si_ya_existe_un_enlace_para_otro_vendedor_lanza_error(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()

        # Cuando / Entonces
        self.assertEqual(self.prospecto_de_vendedor_uno.telefono_para_whatsapp(),
                         self.prospecto_de_vendedor_dos.telefono_para_whatsapp())
        self.assertRaisesMessage(ValidationError, 'No es posible iniciar conversación con este número',
                                 self._gestor_de_conversaciones_del_vendedor_dos.enviar_a,
                                 prospecto=self.prospecto_de_vendedor_dos,
                                 texto=self._texto_enviado_por_el_vendedor(),
                                 medio=MedioWhatsapp.nuevo())

    def test_cuando_el_vendedor_tiene_enlace_con_el_prospecto_puede_enviar_multiples_mensajes(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._recibir_respuesta_del_prospecto_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_uno()

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_uno, cantidad_de_mensajes=3, tipo=MedioWhatsapp.tipo())

    def test_solo_el_vendedor_con_un_enlace_previo_puede_enviarle_whatsapp_a_prospectos_nuevos_con_mismo_destinatario(
            self):
        # Dado
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            prospecto=self.prospecto_viejo_de_vendedor_uno,
            texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        # Cuando
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            prospecto=self.prospecto_nuevo_de_vendedor_uno,
            texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_nuevo_de_vendedor_uno, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())

    def test_si_al_enviar_un_mensaje_ya_existe_un_enlace_no_debe_crearse_otro_igual(self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
                self.prospecto_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(),
                medio=MedioWhatsapp.nuevo())
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))

            # Cuando
            self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
                self.prospecto_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(),
                medio=MedioWhatsapp.nuevo())

            # Entonces
            enlaces = self._canal_via_meta.enlaces_entre(
                operador=self._canal_via_meta.operador_para_prospecto(self.prospecto_de_vendedor_uno),
                telefonos_destinatarios=[self.telefono_del_prospecto])
            self.assertEqual(enlaces.count(), 1)
            enlace = enlaces.first()
            self.assertTrue(enlace.tiene_fecha_ultima_actualizacion(self._calendario.now()))

    def test_al_enviar_un_nuevo_mensaje_al_mismo_destinario_pero_con_codigo_pais_actualiza_el_enlace_existente(self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
                self.prospecto_de_vendedor_uno, texto=self._texto_enviado_por_el_vendedor(),
                medio=MedioWhatsapp.nuevo())
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))

            # Cuando
            telefono_con_codigo_pais = '549' + self.prospecto_de_vendedor_uno.telefono
            self.prospecto_de_vendedor_uno.telefono = telefono_con_codigo_pais
            self.prospecto_de_vendedor_uno.save()
            self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
                prospecto=self.prospecto_de_vendedor_uno,
                texto=self._texto_enviado_por_el_vendedor(),
                medio=MedioWhatsapp.nuevo())

            # Entonces
            enlaces = self._canal_via_meta.enlaces_entre(
                operador=self._canal_via_meta.operador_para_prospecto(self.prospecto_de_vendedor_uno),
                telefonos_destinatarios=[self.telefono_del_prospecto])
            self.assertEqual(enlaces.count(), 1)
            enlace = enlaces.first()
            self.assertTrue(enlace.tiene_fecha_ultima_actualizacion(self._calendario.now()))

    def test_enlaces_previos_caducos_por_prospectos_finalizados_no_restringen_envio_de_mensajes_a_otro_vendedor(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_dos, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())

    def test_enlaces_previos_caducos_por_prospectos_vendidos_no_restringen_envio_de_mensajes_a_otro_vendedor(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._cargar_venta_al_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_dos, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())

    def test_cuando_los_enlaces_previos_estan_caducos_con_ventana_cerrada_otro_vendedor_puede_generar_un_nuevo_enlace(
            self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self.assertTrue(self._canal_via_meta.existe_un_enlace_entre(
            operador=self._canal_via_meta.operador_para_prospecto(self.prospecto_de_vendedor_uno),
            telefono_destinatario=self.telefono_del_prospecto,
            prospecto=self.prospecto_de_vendedor_dos))

    def test_cuando_los_enlaces_previos_estan_caducos_con_ventana_abierta_otro_vendedor_puede_generar_un_nuevo_enlace(
            self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._recibir_respuesta_del_prospecto_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self.assertTrue(self._canal_via_meta.existe_un_enlace_entre(
            operador=self._canal_via_meta.operador_para_prospecto(self.prospecto_de_vendedor_uno),
            telefono_destinatario=self.telefono_del_prospecto,
            prospecto=self.prospecto_de_vendedor_dos))

    def test_no_permite_enviar_whatsapp_si_existe_mas_de_un_enlace_posterior_entre_operador_y_destinatario(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()
        self._enviar_whatsapp_al_prospecto_vendedor_dos()
        self._reactivar_seguimiento_prospecto_del_vendedor_uno()

        # Cuando / Entonces
        self.assertRaisesMessage(ValidationError, 'No es posible iniciar conversación con este número',
                                 self._gestor_de_conversaciones_del_vendedor_uno.enviar_a,
                                 prospecto=self.prospecto_de_vendedor_uno,
                                 texto=self._texto_enviado_por_el_vendedor(),
                                 medio=MedioWhatsapp.nuevo())

    def test_solo_considera_vigente_al_ultimo_enlace_actualizado_entre_operador_y_destinatario(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()
        self._enviar_whatsapp_al_prospecto_vendedor_dos()
        self._reactivar_seguimiento_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_dos, cantidad_de_mensajes=2, tipo=MedioWhatsapp.tipo())

    def test_enlaces_previos_caducos_por_dias_de_aniguedad_vendidos_no_restringen_envio_de_mensajes_a_otro_vendedor(
            self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._enviar_whatsapp_al_prospecto_vendedor_uno()
            tiempo_freezado.tick(timedelta(
                days=Sistema.instance().tiempo_antiguedad_maximo_para_enlace_vigente) + timedelta(seconds=1))

            # Cuando
            self._enviar_whatsapp_al_prospecto_vendedor_dos()

            # Entonces
            self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
                prospecto=self.prospecto_de_vendedor_dos, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())

    def test_xx(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_uno()
        self._rechazar_prospecto_de_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._notificaciones_helper.assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self.prospecto_de_vendedor_dos, cantidad_de_mensajes=1, tipo=MedioWhatsapp.tipo())

    def _rechazar_prospecto_de_vendedor_uno(self):
        gestor = GestorDeProspecto.nuevo_para(rol=self.supervisor_uno)
        prospectos = Prospecto.objects.con_ids([self.prospecto_de_vendedor_uno.id])
        gestor.rechazar_prospectos(prospectos=prospectos)

    def _enviar_whatsapp_al_prospecto_vendedor_dos(self):
        self._gestor_de_conversaciones_del_vendedor_dos.enviar_a(
            prospecto=self.prospecto_de_vendedor_dos,
            texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

    def _recibir_respuesta_del_prospecto_vendedor_uno(self):
        numero_de_telefono_del_remitente = self.telefono_del_prospecto
        notificacion_con_respuesta_del_destinatario = self._respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente)
        self._canal_via_meta.actualizar_desde(notificacion_con_respuesta_del_destinatario)

    def _enviar_whatsapp_al_prospecto_vendedor_uno(self):
        self._gestor_de_conversaciones_del_vendedor_uno.enviar_a(
            prospecto=self.prospecto_de_vendedor_uno,
            texto=self._texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

    def _configurar_al_servicio_de_meta_para_acepte_nuevas_conversaciones(self, id_de_mensaje_en_meta=None):
        self._servicio_de_meta.cuando_inicia_una_conversacion_con_template_hace(lambda: {
            'messaging_product': 'whatsapp', 'contacts': [{'input': '1140583623', 'wa_id': '5491140583623'}],
            'messages': [
                {'id': id_de_mensaje_en_meta or self._id_de_meta_del_mensaje_template(), 'message_status': 'accepted'}]
        })

    def _configurar_al_servicio_de_meta_para_acepte_nuevos_mensajes(self, id_de_mensaje_en_meta=None):
        self._servicio_de_meta.cuando_se_envia_un_mensaje_hace(lambda: {
            'messaging_product': 'whatsapp', 'contacts': [{'input': '1140583623', 'wa_id': '5491140583623'}],
            'messages': [
                {'id': id_de_mensaje_en_meta or self._id_de_meta_del_mensaje_template()}]
        })

    def _configurar_telefono_prospecto_viejo_de_venedor_uno(self, telefono):
        self.prospecto_viejo_de_vendedor_uno.telefono = telefono
        self.prospecto_viejo_de_vendedor_uno.save()
        reload_model(self.prospecto_viejo_de_vendedor_uno)

    def _configurar_al_prospecto_vendedor_dos_con_mismo_telefono_prospecto_vendedor_uno_con_codigo_pais(self):
        prospecto_vendedor_dos_largo = '549' + self.telefono_del_prospecto
        self.prospecto_de_vendedor_dos.telefono = prospecto_vendedor_dos_largo
        self.prospecto_de_vendedor_dos.save()

    def _respuesta_del_usuario_a_un_mensaje(self, numero_de_telefono_del_remitente, aturl=None):
        return {
            "marca": "Marca Blanca",
            "data": {
                "variable1": "valor1",
                "variable2": "valor2",
                "AtUrl": aturl
            },
            "telefono": numero_de_telefono_del_remitente,
            "operator_name": None,
            "marca_asociada_al_operador": None,
            "campania": "generica-sms",
            "mensaje": self._texto_entrante_de_prospecto(),
            "nombre": "Maximiliano"
        }

    def _texto_enviado_por_el_vendedor(self):
        return 'Hello client!'

    def _texto_entrante_de_prospecto(self):
        return 'Hola! Esto es una prueba de mensaje'

    def _id_de_meta_del_mensaje_template(self):
        return 'wamid.HBgNNTQ5MTE0MDU4MzYyMxUCABEYEjMwQzk0RDBBRTEwNjdEOTRCNAA='

    def _id_de_meta_del_mensaje_enviado_por_el_vendedor(self):
        return "wamid.HBgNNTQ5MTEyMjM2ODUxNRUCABEYEjVGNjZEQjlDMzZFQkE3NDhENwA="

    def _crear_prospecto_vendedor_uno(self):
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, telefono='', es_telefono_movil=True,
            email='<EMAIL>')
        prospecto.telefono = self.telefono_del_prospecto
        prospecto.save()
        return reload_model(prospecto)

    def _crear_prospecto_viejo_vendedor_uno(self):
        return self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, telefono='48764400', es_telefono_movil=True, email='<EMAIL>',
            fecha=self._calendario.datetime(year=2025, month=1, day=1),
            fecha_de_asignacion_a_vendedor=self._calendario.datetime(year=2025, month=1, day=1))

    def _crear_prospecto_nuevo_vendedor_uno(self):
        return self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, telefono='48764400', es_telefono_movil=True, email='<EMAIL>',
            fecha=self._calendario.datetime(year=2025, month=4, day=4),
            fecha_de_asignacion_a_vendedor=self._calendario.datetime(year=2025, month=4, day=4))

    def _crear_gestor_para_vendedor_uno(self):
        return GestorDeConversaciones.nuevo_para(
            rol=self.vendedor_uno, canal_via_meta=self._canal_via_meta, usa_meta=True)

    def _crear_gestor_para_vendedor_dos(self):
        return GestorDeConversaciones.nuevo_para(
            rol=self.vendedor_dos, canal_via_meta=self._canal_via_meta, usa_meta=True)

    def _crear_prospecto_vendedor_dos(self):
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_dos, telefono='', es_telefono_movil=True, email='<EMAIL>',
            fecha=self._calendario.datetime(year=2025, month=2, day=2),
            fecha_de_asignacion_a_vendedor=self._calendario.datetime(year=2025, month=4, day=4))
        prospecto.telefono = self.telefono_del_prospecto
        prospecto.save()
        return reload_model(prospecto)

    def _crear_canal_via_meta(self):
        return CanalDeComunicacionDeWhatsappViaMeta.nuevo(
            servicio_de_meta=self._servicio_de_meta,
            notificador=NotificadorDeConversacionesMetaMock.nuevo())

    def _finalizar_prospecto_del_vendedor_uno(self):
        gestor_del_prospecto = GestorDeProspecto.nuevo_para(self.vendedor_uno)
        gestor_del_prospecto.finalizar_prospecto(prospecto=self.prospecto_de_vendedor_uno)

    def _cargar_venta_al_prospecto_del_vendedor_uno(self):
        gestor_del_prospecto = GestorDeProspecto.nuevo_para(self.vendedor_uno)
        gestor_del_prospecto.cargar_venta(
            prospecto=self.prospecto_de_vendedor_uno, marca='ford', modelo='ecosport',
            fecha_de_realizacion=self._calendario.now(), numero_de_contrato='1', precio=100000)

    def _reactivar_seguimiento_prospecto_del_vendedor_uno(self):
        gestor_del_prospecto = GestorDeProspecto.nuevo_para(self.vendedor_uno)
        gestor_del_prospecto.reactivar_seguimiento(prospecto=self.prospecto_de_vendedor_uno)

    def _crear_operador(self, activo=True, tipo=None):
        operador = Operador.objects.create(
            id_operador="458578827350095",
            nombre="Test CRM Uno",
            activo=activo,
            tipo=tipo or Operador.SALIENTE)
        return operador
