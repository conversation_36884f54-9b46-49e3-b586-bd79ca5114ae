# coding=utf-8
from django.utils import timezone


class MedioDeConversacion(object):
    def mensajes_para(self, conversacion, cantidad=None):
        raise NotImplementedError('subclass responsibility')

    def marcar_como_leidos_mensajes_de(self, prospecto):
        raise NotImplementedError('subclass responsibility')

    def eliminar_mensajes_de(self, prospecto):
        raise ValueError('Las conversaciones de tipo %s no pueden ser eleminadas' % self.tipo)

    def enviar_via(self, gestor, prospecto, texto):
        raise NotImplementedError('subclass responsibility')

    def nombre_para(self, conversacion):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def nuevo_para(cls, tipo):
        subclases = cls.__subclasses__()
        for each in subclases:
            if each.es_de_tipo(tipo):
                return each.nuevo()
        raise ValueError('Tipo incorrecto: %s' % tipo)

    @classmethod
    def nuevo(cls):
        return cls()

    @classmethod
    def es_de_tipo(cls, tipo):
        return tipo == cls.tipo()

    @classmethod
    def tipo(cls):
        raise NotImplementedError('subclass responsibility')


class MedioWhatsapp(MedioDeConversacion):
    def mensajes_para(self, conversacion, cantidad=None):
        from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
        mensajes_deprecados = MensajesWhatsapp.objects.filter(prospecto=conversacion.prospecto_id)

        from conversaciones.models import MensajeDeWhatsappConProspecto
        mensajes = MensajeDeWhatsappConProspecto.objects.filter(_prospecto=conversacion.prospecto_id)

        mensajes = list(mensajes) + list(mensajes_deprecados)
        mensajes.sort(key=lambda mensaje: mensaje.fecha, reverse=True)
        if cantidad is not None:
            mensajes = mensajes[:cantidad]
        return mensajes

    def enviar_via(self, gestor, prospecto, texto):
        return gestor.enviar_whatsapp_a(prospecto, texto)

    @classmethod
    def tipo(cls):
        from conversaciones.models import Conversacion
        return Conversacion.TIPO_WHATSAPP

    def marcar_como_leidos_mensajes_de(self, prospecto):
        from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
        MensajesWhatsapp.objects.marcar_conversacion_como_leida(prospecto)

    def eliminar_mensajes_de(self, prospecto):
        prospecto.mensajes.update(eliminado=True)

    def nombre_para(self, conversacion):
        return 'Whatsapp'


class MedioEAviso(MedioDeConversacion):
    """
    Esta clase modela el medio de comunicacion con un potencial comprador
    a través de una publicación en un sitio (MercadoLibre, OLX, SoloAutos, etc)
    """
    def enviar_via(self, gestor, prospecto, texto):
        pass

    def mensajes_para(self, conversacion, cantidad=None):

        from occ.models.eavisos import PreguntaDePublicacionEAvisos
        mensajes_in = PreguntaDePublicacionEAvisos.objects.de_conversacion(conversacion).ordenar_por_fecha()
        from occ.models.eavisos import RespuestaAMensajeDePublicacionEAvisos
        mensajes_out = RespuestaAMensajeDePublicacionEAvisos.objects.de_conversacion(conversacion).ordenar_por_fecha()
        mensajes = self._merge_mensajes(list(mensajes_in), list(mensajes_out))
        if cantidad is not None:
            mensajes = mensajes[-cantidad:]
        return mensajes

    def _merge_mensajes(self, mensajes_in, mensajes_out):
        ahora = timezone.now()
        mensajes = mensajes_in + mensajes_out
        return sorted(mensajes, key=lambda each: each.created_at() or ahora)

    def marcar_como_leidos_mensajes_de(self, conversacion):
        preguntas = conversacion.preguntas()
        preguntas.marcar_como_leidas()

    def nombre_para(self, conversacion):
        publicacion = conversacion.publicacion()
        return publicacion.nombre_de_lugar_de_publicacion()

    @classmethod
    def tipo(cls):
        from occ.models.eavisos import ConversacionDeEAvisos
        return ConversacionDeEAvisos.TIPO


class MedioChat(MedioDeConversacion):
    def enviar_via(self, gestor, prospecto, texto):
        return gestor.enviar_chat_a(prospecto, texto)

    def mensajes_para(self, conversacion, cantidad=None):
        chat_ventas_convertido = conversacion.chat_de_ventas_convertido
        mensajes = chat_ventas_convertido.chat.lista_de_mensajes()
        if cantidad is not None:
            mensajes = list(mensajes.reverse()[:cantidad])
            mensajes.reverse()
        return mensajes

    @classmethod
    def tipo(cls):
        from conversaciones.models import Conversacion
        return Conversacion.TIPO_CHAT

    def marcar_como_leidos_mensajes_de(self, prospecto):
        from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
        MensajesWhatsapp.objects.marcar_conversacion_como_leida(prospecto)

    def nombre_para(self, conversacion):
        return 'Chat'


class MedioSMS(MedioDeConversacion):
    def enviar_via(self, gestor, prospecto, texto):
        return gestor.enviar_sms_a(prospecto, texto)

    def mensajes_para(self, conversacion, cantidad=None):
        from occ.models import EnvioDeMensaje, RespuestaDeMensaje
        mensajes_in = EnvioDeMensaje.objects.filter(prospecto=conversacion.prospecto_id).order_by('fecha')
        mensajes_out = RespuestaDeMensaje.objects.filter(
            envio__prospecto_id=conversacion.prospecto_id).order_by('fecha')
        mensajes = self._merge_mensajes_sms(list(mensajes_in), list(mensajes_out))
        if cantidad is not None:
            mensajes = mensajes[-cantidad:]
        return mensajes

    def _merge_mensajes_sms(self, mensajes_in, mensajes_out):
        ahora = timezone.now()
        mensajes = mensajes_in + mensajes_out
        return sorted(mensajes, key=lambda x: x.fecha or ahora)

    def marcar_como_leidos_mensajes_de(self, prospecto):
        from occ.models import RespuestaDeMensaje
        RespuestaDeMensaje.objects.marcar_conversacion_como_leida(prospecto)

    def nombre_para(self, conversacion):
        return 'SMS'

    @classmethod
    def tipo(cls):
        from conversaciones.models import Conversacion
        return Conversacion.TIPO_SMS


class MedioEmail(MedioDeConversacion):
    def enviar_via(self, gestor, prospecto, texto):
        return gestor.enviar_email_a(prospecto, texto)

    def mensajes_para(self, conversacion, cantidad=None):
        from occ.models import EnvioDeEmail
        mensajes = EnvioDeEmail.objects.filter(_prospecto=conversacion.prospecto_id).order_by('_fecha')
        if cantidad is not None:
            mensajes = list(mensajes.reverse()[:cantidad])
            mensajes.reverse()
        return mensajes

    @classmethod
    def tipo(cls):
        from conversaciones.models import Conversacion
        return Conversacion.TIPO_EMAIL

    def marcar_como_leidos_mensajes_de(self, prospecto):
        from occ.models import EnvioDeEmail
        EnvioDeEmail.objects.marcar_conversacion_como_leida(prospecto)

    def nombre_para(self, conversacion):
        return 'Email'
