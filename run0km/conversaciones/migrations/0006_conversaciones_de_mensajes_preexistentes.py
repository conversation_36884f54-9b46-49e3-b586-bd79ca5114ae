# -*- coding: utf-8 -*-


from django.db import models, migrations


def crear_conversaciones_whatsapp(apps):
    MensajesWhatsapp = apps.get_model("conversaciones", "MensajesWhatsapp")
    Conversacion = apps.get_model("conversaciones", "Conversacion")
    tipo = 'W'
    for mensaje in MensajesWhatsapp.objects.filter(eliminado=False).order_by('-fecha'):
        prospecto_id = mensaje.prospecto_id
        fecha = mensaje.fecha
        defaults = {'fecha_ultimo_mensaje': fecha, }
        proveniente_de_cliente = mensaje.emisor == 'C'
        if proveniente_de_cliente:
            defaults['fecha_ultima_respuesta'] = fecha
            defaults['fue_leida'] = mensaje.estado == 'C'
        conversacion, created = Conversacion.objects.get_or_create(prospecto_id=prospecto_id,
                                                                   tipo=tipo,
                                                                   defaults=defaults)
        if not created:
            if proveniente_de_cliente:
                if not conversacion.fecha_ultima_respuesta:
                    conversacion.fecha_ultima_respuesta = fecha
                    conversacion.fue_leida = defaults['fue_leida']
        conversacion.full_clean()
        conversacion.save()


def crear_conversaciones_sms(apps):
    EnvioDeMensaje = apps.get_model("occ", "EnvioDeMensaje")
    Conversacion = apps.get_model("conversaciones", "Conversacion")
    tipo = 'S'
    for envio in EnvioDeMensaje.objects.filter(estado='R').order_by('fecha'):
        prospecto_id = envio.prospecto_id
        fecha = envio.fecha
        defaults = {'fecha_ultimo_mensaje': fecha,
                    'fue_leida': False}
        conversacion, created = Conversacion.objects.get_or_create(prospecto_id=prospecto_id,
                                                                   tipo=tipo,
                                                                   defaults=defaults)
        conversacion.full_clean()
        conversacion.save()

    RespuestaDeMensaje = apps.get_model("occ", "RespuestaDeMensaje")
    for respuesta in RespuestaDeMensaje.objects.all().select_related('envio'):
        prospecto_id = respuesta.envio.prospecto_id
        fecha = respuesta.fecha
        defaults = {'fecha_ultimo_mensaje': fecha,
                    'fecha_ultima_respuesta': fecha,
                    'fue_leida': respuesta.leida, }
        conversacion, created = Conversacion.objects.get_or_create(prospecto_id=prospecto_id,
                                                                   tipo=tipo,
                                                                   defaults=defaults)
        if not created:
            if not conversacion.fecha_ultima_respuesta or conversacion.fecha_ultima_respuesta < fecha:
                conversacion.fecha_ultima_respuesta = fecha
                conversacion.fue_leida = defaults['fue_leida']
        conversacion.full_clean()
        conversacion.save()


def crear_conversaciones(apps, schema_editor):
    crear_conversaciones_whatsapp(apps)
    crear_conversaciones_sms(apps)


class Migration(migrations.Migration):

    dependencies = [
        ('conversaciones', '0005_auto_20160511_0031'),
        ('occ', '0008_auto_20160511_1821'),
    ]

    operations = [
        migrations.RunPython(crear_conversaciones),
    ]
