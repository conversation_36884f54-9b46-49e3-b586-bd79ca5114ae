# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2025-02-24 21:04
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0140_circulaciondeprospecto'),
        ('whatsapp', '0013_conversationinmeta_messageinmeta'),
        ('conversaciones', '0017_auto_20241218_1154'),
    ]

    operations = [
        migrations.CreateModel(
            name='MensajeDeWhatsapp',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
        ),
        migrations.CreateModel(
            name='MensajeDeWhatsappConProspecto',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_fecha', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='MensajeDeWhatsappEnMeta',
            fields=[
                ('mensajedewhatsapp_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='conversaciones.MensajeDeWhatsapp')),
                ('_mensaje', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='whatsapp.MessageInMeta')),
            ],
            bases=('conversaciones.mensajedewhatsapp',),
        ),
        migrations.AddField(
            model_name='mensajedewhatsappconprospecto',
            name='_mensaje',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='conversaciones.MensajeDeWhatsapp'),
        ),
        migrations.AddField(
            model_name='mensajedewhatsappconprospecto',
            name='_prospecto',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='_mensajes_de_whatsapp', to='prospectos.Prospecto'),
        ),
    ]
