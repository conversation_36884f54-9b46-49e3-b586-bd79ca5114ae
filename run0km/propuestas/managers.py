from django.db import models
from django.db.models import Q

from core.querysets import DeliveryQuerySet
from users.models import AbstractUser


class PlanManager(models.Manager):
    def con_identificador(self, identificador):
        return self.get(_identificador=identificador)

    def filtrar_por_identificador(self, identificador):
        return self.filter(_identificador=identificador)


class ConcesionariaExternaManager(models.Manager):
    def con_identificador(self, identificador):
        return self.get(_identificador=identificador)

    def filtrar_por_identificador(self, identificador):
        return self.filter(_identificador=identificador)


class TipoDePlanManager(models.Manager):
    def con_identificador(self, identificador):
        return self.get(_identificador=identificador)

    def filtrar_por_identificador(self, identificador):
        return self.filter(_identificador=identificador)


class PropuestaQuerySet(DeliveryQuerySet):
    def habilitadas(self):
        propuestas = self.filter(_habilitada=True)
        return propuestas

    def con_identificador(self, identificador):
        return self.get(_identificador=identificador)

    def filtrar_por_identificador(self, identificador):
        return self.filter(_identificador=identificador)

    def para_chats(self):
        propuestas = self.filter(_texto_chat__isnull=False).exclude(_texto_chat__exact='') | \
                     self.filter(_imagen_chat__isnull=False).exclude(_imagen_chat__exact='')
        return propuestas

    def para_prospectos(self):
        propuestas = self.filter(_texto_whatsapp__isnull=False).exclude(_texto_whatsapp__exact='') | \
                     self.filter(_imagen_whatsapp__isnull=False).exclude(_imagen_whatsapp__exact='') | \
                     self.filter(_texto_sms__isnull=False).exclude(_texto_sms__exact='') | \
                     self.filter(_html_email__isnull=False).exclude(_html_email__exact='')
        return propuestas

    def para_vendedor(self, vendedor):
        concesionaria = vendedor.obtener_concesionaria()
        gerentes = concesionaria.obtener_gerentes()
        usuarios = list(gerentes.values_list('user', flat=True)) + [vendedor.usuario()]
        if vendedor.tiene_cargo_vendedor():
            # Un vendedor puede ver las propuestas de si mismo, su supervisor y su gerente.
            usuarios.append(vendedor.responsable().usuario())
        return self.habilitadas().filter(_user__in=usuarios)
