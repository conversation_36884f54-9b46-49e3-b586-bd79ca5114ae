from django.conf import settings
from django.conf.urls import url
from django.conf.urls.static import static
from django.views.decorators.csrf import csrf_exempt

from proposalsproxy import views as proposal_views
from propuestas.views import ActualizarPropuestasView, EnvioDePropuestasParaProspectoView, PropuestasView, \
    PropuestasParaProspectoViaChatView, EnvioDePropuestasAChatView

urlpatterns = [
    url(r'^api/?$', ActualizarPropuestasView.as_view(), name='actualizar-propuestas'),
    url(r'^enviar-propuesta-a-prospecto/(?P<pk>\d+)/$', EnvioDePropuestasParaProspectoView.as_view(),
        name='enviar_propuesta_a_prospecto'),
    url(r'^chat/(?P<pk>\d+)/lista/$', PropuestasParaProspectoViaChatView.as_view(), name='propuestas_para_chat'),
    url(r'^enviar-propuesta-a-chat/(?P<pk>\d+)/$', EnvioDePropuestasAChatView.as_view(),
        name='enviar_propuesta_a_chat'),
    url(r'^graphql/', csrf_exempt(proposal_views.GraphQLProxy.as_view()), name='proposals-graphql-proxy'),
    url(r'^$', PropuestasView.as_view(), name='proposals-index'),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)