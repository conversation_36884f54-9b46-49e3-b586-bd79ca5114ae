
definitions = {
    "date": {
        "type": "string",
        # "format": "%Y-%m-%d",
        "format": "date",
        # "pattern": "[0-9]{4}-[0-9]{2}-[0-9]{2}$"
    },
    "datetime": {
        "type": "string",
        "format": "date-time",
        # "format": "%Y-%m-%d-%H:%M:%S",
        # "pattern": "[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9]{2}:[0-9]{2}:[0-9]{2}$"
    },
    "brand": {
        "type": "object",
        "properties": {
            "id": {"type": "number", "minimum": 0},
        },
        "required": ["id"],
    },
    "plan": {
        "type": "object",
        "properties": {
            "id": {"type": "number", "minimum": 0},
        },
        "required": ["id"],
    },
    "proposal": {
        "type": "object",
        "properties": {
            "id": {"type": "number", "minimum": 0},
            "title": {"type": "string", },
            "enabled": {"type": "boolean"},
            "created": {"$ref": "#/definitions/datetime"},
            "modified": {"$ref": "#/definitions/datetime"},
            "brand": {"$ref": "#/definitions/brand"},
            "plan": {"$ref": "#/definitions/plan"},
        },
        "required": ["id", "brand", "plan"],
    },
}

concesionaria_externa = {
    "type": "object",
    "properties": {
        "id": {"type": "number", "minimum": 0},
        "name": {"type": "string"},
        "logo": {"type": ["string", "null"]},
        "featured": {"type": "boolean"},
        "timetable": {"type": "string"},
        "enabled": {"type": "boolean"},
        "brands": {"type": "array", "items": {"type": "number", "minimum": 0}}

    },
    "required": ["id", "name"],
}

marca = {
    "type": "object",
    "properties": {
        "id": {"type": "number", "minimum": 0},
        "code": {"type": "string"},
        "name": {"type": "string"},
        "logo": {"type": ["string", "null"]},
        "enabled": {"type": "boolean"}
    },
    "required": ["id", "code", "name"],
}

modelo = {
    "type": "object",
    "properties": {
        "id": {"type": "number", "minimum": 0},
        "code": {"type": "string", },
        "name": {"type": "string", },
        "logo": {"type": "string", },
        "enabled": {"type": "boolean"}
    },
    "required": ["id", "code", "name"],
}

tipo_de_plan = {
    "type": "object",
    "properties": {
        "id": {"type": "number", "minimum": 0},
        "code": {"type": "string", },
        "name": {"type": "string", },
    },
    "required": ["id", "code", "name"],
}

plan = {
    "type": "object",
    "properties": {
        "id": {"type": "number", "minimum": 0},
        "plan_type": tipo_de_plan,
        "model": modelo,
    },
    "required": ["id", "plan_type", "model"],
}

propuesta = {
    "name": "lista_de_propuestas",
    "definitions": definitions,
    "properties": {
        "proposal": {
            "type": "object"
        },
        "user": {
            "type": "string",
        },
    },
    "required": ["proposal", "user"],
}
