import json

from django.urls import reverse
from rest_framework import status
from rest_framework.authtoken.models import Token

from propuestas.models import Propuesta
from propuestas.tests.tests_core import PropuestasTest


class ActualizarPropuestasViewTest(PropuestasTest):
    def setUp(self):
        super(ActualizarPropuestasViewTest, self).setUp()
        self.user = self.fixture['usr_1']
        token = Token(user=self.user)
        self.token = token.key
        token.save()
        self._borrar_marcas()

    def _post_agregar_propuesta(self, datos, token_key):
        url = reverse('actualizar-propuestas')
        response = self.client.post(path=url,
                                    data=json.dumps(datos),
                                    HTTP_AUTHORIZATION='Token %s' % token_key,
                                    content_type='application/json')
        return response

    def _delete_propuesta(self, datos, token_key):
        url = reverse('actualizar-propuestas')
        response = self.client.delete(
            path=url,
            data=json.dumps(datos),
            HTTP_AUTHORIZATION='Token %s' % token_key,
            content_type='application/json')
        return response

    def _json_marca(self, marca):
        if marca:
            return {
                "logo": marca.logo(),
                "enabled": marca.esta_habilitada(),
                "id": marca.identificador(),
                "name": marca.nombre()
            }
        else:
            return {
                "logo": "http://localhost:8000/media/brands/chevrolet/chevrolet.jpg",
                "enabled": True,
                "id": 6,
                "name": "Chevrolet"
            }

    def _json_plan(self, plan):
        if plan is None:
            return None
        identificador = plan.identificador()
        return {
            "id": identificador,
            "adjudicated": False,
            "total_price": 370000.0,
            "install_count": 84,
            "name": "Chevrolet Tracker",
            "finalized": False,
            "started": False,
            "install_delivery": None,
            "published_date": "2017-03-30",
            "paid_installs": 0,
            "plan_type": {
                "id": 1,
                "small_print": "Plan 100/100, financia el 100% de la unidad hasta en 84 cuotas. Permite adjudicar desde la cuota 2 por sorteo y liciaci\\u00f3n. Las cuotas incluyen seguro de vida, gastos, sellados, impuestos y derecho de admisi\\u00f3n; la cuota pura permite adelantar o cancelar cuotas. Permite cambio de modelo al adjudicar. Consulte grandes bonificaciones vigentes!",
                "name": "Plan 100%"
            },
            "price_0km": 0.0,
            "installs": [],
            "install_price": 3759.0,
            "published": True,
            "model": {
                "available_colors": [
                    "VARIOS"
                ],
                "name": "Chevrolet Tracker",
                "enabled": True,
                "other_photos": [

                ],
                "main_photo": "http://localhost:8000/media/brands/chevrolet/models/tracker.png",
                "equipment": ".",
                "id": 97
            },
            "finalized_date": None,
            "adjudicated_description": "",
            "small_print": ""
        }

    def _json_propuesta(self, identificador, marca=None, plan=None):
        return {
            "email_html": None,
            "created": "2017-11-13T18:46:13.499470+00:00",
            "modified": "2017-11-23T18:46:13.499470+00:00",
            "enabled": True,
            "brand": self._json_marca(marca),
            "title": "Tracker 2",
            "sms": "",
            "whatsapp_text": "hello world",
            "whatsapp_image": "http://localhost:8000/media/whatsapp/74-whatsapp/None-/media/data/programacion/planesahorro/media/proposals/None-mm__rmZ4GZ6.jpg",
            "chat_text": "hello chat",
            "email_text": None,
            "chat_image": "http://localhost:8000/media/chat/74.jpg",
            "plan":  self._json_plan(plan),
            "description_text": "<p><strong>Marca:</strong> Chevrolet</p>\n<p><strong>Plan:</strong> Chevrolet Tracker</p>\n<p><strong>Tipo de plan</strong> Plan 100%</p>\n<p><strong>Colores disponibles:</strong> VARIOS</p>\n<p><strong>Equipamento:</strong> .</p>\n<p><strong>Valor de cuota:</strong> 3.759,00</p>\n<p><strong>Valor 0Km</strong> 370.000,00</p>",
            "other_plan": "",
            "other_brand": "",
            "id": identificador
        }

    def _json_propuesta_completa(self):
        return {
            "email_html": None,
            "created":"2017-07-05",
            "enabled":True,
            "brand":{
                "logo":"http://localhost:8000/media/brands/chevrolet/chevrolet.jpg",
                "enabled":True,
                "id":6,
                "name":"Chevrolet",
                "code":"chevrolet",
            },
            "title":"Tracker 2",
            "sms":"",
            "modified":"2017-09-25",
            "whatsapp_image":"http://localhost:8000/media/whatsapp/74-whatsapp/None-/media/data/programacion/planesahorro/media/proposals/None-mm__rmZ4GZ6.jpg",
            "chat_text":None,
            "email_text":None,
            "photos":[
                {
                    "size":100075,
                    "width":1024,
                    "name":"mm_gal_item_c2_0.img_resize.img_stage._3_vpISme8.jpg",
                    "created":"2017-07-05",
                    "photo":"http://localhost:8000/media/proposals/None-mm_gal_item_c2_0.img_resize.img_stage._3_vpISme8.jpg",
                    "height":576,
                    "id":131,
                    "modified":"2017-07-05"
                },
                {
                    "size":306418,
                    "width":979,
                    "name":"chevrolet-tracker-camioneta-urbana-rojo-barroco_G1Yazva.jpg",
                    "created":"2017-07-05",
                    "photo":"http://localhost:8000/media/proposals/None-chevrolet-tracker-camioneta-urbana-rojo-barroco_G1Yazva.jpg",
                    "height":422,
                    "id":130,
                    "modified":"2017-07-05"
                },
                {
                    "size":143063,
                    "width":1024,
                    "name":"Chevrolet-Tracker-4_FnfijNa.jpg",
                    "created":"2017-07-05",
                    "photo":"http://localhost:8000/media/proposals/None-Chevrolet-Tracker-4_FnfijNa.jpg",
                    "height":768,
                    "id":129,
                    "modified":"2017-07-05"
                },
                {
                    "size":41774,
                    "width":600,
                    "name":"chevrolet-tracker_CdoXtSO.jpg",
                    "created":"2017-07-05",
                    "photo":"http://localhost:8000/media/proposals/None-chevrolet-tracker_CdoXtSO.jpg",
                    "height":300,
                    "id":128,
                    "modified":"2017-07-05"
                },
                {
                    "size":47303,
                    "width":648,
                    "name":"2013-chevrolet-tracker-desempeno-648x409_MmT1xkw.jpg",
                    "created":"2017-07-05",
                    "photo":"http://localhost:8000/media/proposals/None-2013-chevrolet-tracker-desempeno-648x409_MmT1xkw.jpg",
                    "height":409,
                    "id":127,
                    "modified":"2017-07-05"
                }
            ],
            "chat_image":"http://localhost:8000/media/chat/74.jpg",
            "plan":{
                "adjudicated":False,
                "total_price":370000.0,
                "install_count":84,
                "name":"Chevrolet Tracker",
                "finalized":False,
                "started":False,
                "install_delivery":None,
                "published_date":"2018-01-12T14:59:28.508474-03:00",
                "paid_installs":0,
                "plan_type":{
                    "id":1,
                    "small_print":"Plan 100/100, financia el 100% de la unidad hasta en 84 cuotas. Permite adjudicar desde la cuota 2 por sorteo y liciaci\\u00f3n. Las cuotas incluyen seguro de vida, gastos, sellados, impuestos y derecho de admisi\\u00f3n; la cuota pura permite adelantar o cancelar cuotas. Permite cambio de modelo al adjudicar. Consulte grandes bonificaciones vigentes!",
                    "name":"Plan 100%",
                    "code": "plan-100%"
                },
                "price_0km":0.0,
                "installs":[
                    {
                        "include_subscription":False,
                        "price":4937.0,
                        "initial_install":1,
                        "last_install":1,
                        "id":651
                    },
                    {
                        "include_subscription":False,
                        "price":3759.0,
                        "initial_install":2,
                        "last_install":11,
                        "id":741
                    },
                    {
                        "include_subscription":False,
                        "price":3983.0,
                        "initial_install":12,
                        "last_install":15,
                        "id":742
                    },
                    {
                        "include_subscription":False,
                        "price":4564.0,
                        "initial_install":16,
                        "last_install":21,
                        "id":743
                    },
                    {
                        "include_subscription":False,
                        "price":6471.0,
                        "initial_install":22,
                        "last_install":50,
                        "id":744
                    },
                    {
                        "include_subscription":False,
                        "price":5152.0,
                        "initial_install":51,
                        "last_install":84,
                        "id":745
                    }
                ],
                "install_price":3759.0,
                "published":True,
                "model":{
                    "available_colors":[
                        "VARIOS"
                    ],
                    "name":"Chevrolet Tracker",
                    "code":"chevrolet-tracker",
                    "enabled":True,
                    "other_photos":[

                    ],
                    "main_photo":"http://localhost:8000/media/brands/chevrolet/models/tracker.png",
                    "equipment":".",
                    "id":97
                },
                "finalized_date":None,
                "adjudicated_description":"",
                "id":150,
                "small_print":""
            },
            "description_text":"<p><strong>Marca:</strong> Chevrolet</p>\n<p><strong>Plan:</strong> Chevrolet Tracker</p>\n<p><strong>Tipo de plan</strong> Plan 100%</p>\n<p><strong>Colores disponibles:</strong> VARIOS</p>\n<p><strong>Equipamento:</strong> .</p>\n<p><strong>Valor de cuota:</strong> 3.759,00</p>\n<p><strong>Valor 0Km</strong> 370.000,00</p>",
            "whatsapp_text":None,
            "other_plan":"",
            "other_brand":"",
            "id":74
        }

    def test_pedido_sin_token_responde_permiso_denegado(self):
        datos = {}
        response = self._post_agregar_propuesta(datos, 'erronea')
        self.assertContains(response,
                            text='Invalid token',
                            status_code=status.HTTP_401_UNAUTHORIZED)

    def test_borrar_propuesta_sin_token_responde_permiso_denegado(self):
        datos = {}
        response = self._delete_propuesta(datos, 'erronea')
        self.assertContains(response,
                            text='Invalid token',
                            status_code=status.HTTP_401_UNAUTHORIZED)

    def test_agregar_propuesta_con_formato_no_respetado_responde_pedido_erroneo(self):
        datos = {}
        response = self._post_agregar_propuesta(datos, self.user.auth_token.key)
        self.assertContains(response,
                            text='\'proposal\' is a required property',
                            status_code=status.HTTP_400_BAD_REQUEST)

    def test_borrar_propuesta_con_formato_no_respetado_responde_pedido_erroneo(self):
        datos = {}
        response = self._delete_propuesta(datos, self.user.auth_token.key)
        self.assertContains(response,
                            text='\'proposal\' is a required property',
                            status_code=status.HTTP_400_BAD_REQUEST)

    def test_creacion_de_propuesta_responde_creado(self):
        marca = self._crear_marca(identificador=6)
        plan = self._crear_plan(identificador=150)
        json_propuesta = self._json_propuesta(identificador=74, marca=marca, plan=plan)
        datos = self._data_para_agregar_propuesta(json_propuesta, self.user)
        response = self._post_agregar_propuesta(datos, self.user.auth_token.key)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertNotIn('error', response.data)
        self._assert_propuesta(json_propuesta)

    def test_borrar_propuesta_inexistente_responde_pedido_erroneo(self):
        json_propuesta = self._json_propuesta(identificador=74)
        datos = self._data_para_agregar_propuesta(json_propuesta, self.user)
        response = self._delete_propuesta(datos, self.user.auth_token.key)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self._assert_errores(
            data=response.data,
            errores_esperados={'propuesta': 'No existe la propuesta'})

    def test_borrar_propuesta_borrada_exitosamente_responde_ok(self):
        identificador = 74
        marca = self._crear_marca(identificador=6)
        plan = self._crear_plan(identificador=150)
        self._crear_propuesta(identificador, user=self.user, marca=marca, plan=plan)
        json_propuesta = self._json_propuesta(identificador=identificador)
        datos = self._data_para_agregar_propuesta(json_propuesta, self.user)
        response = self._delete_propuesta(datos, self.user.auth_token.key)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self._assert_propuesta_borrada(identificador=identificador, user=self.user)

    def test_actualizacion_de_propuesta_responde_creado(self):
        marca = self._crear_marca(identificador=6)
        plan = self._crear_plan(identificador=150)
        self._crear_propuesta(identificador=150, user=self.user, marca=marca, plan=plan)
        json_propuesta = self._json_propuesta(identificador=150, marca=marca, plan=plan)
        datos = self._data_para_agregar_propuesta(json_propuesta, self.user)
        response = self._post_agregar_propuesta(datos, self.user.auth_token.key)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertNotIn('error', response.data)
        self._assert_propuesta(json_propuesta)

    def test_actualizacion_de_propuesta_sin_plan_responde_creado(self):
        marca = self._crear_marca(identificador=6)
        json_propuesta = self._json_propuesta(identificador=150, marca=marca, plan=None)
        datos = self._data_para_agregar_propuesta(json_propuesta, self.user)
        response = self._post_agregar_propuesta(datos, self.user.auth_token.key)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertNotIn('error', response.data)
        self._assert_propuesta(json_propuesta)

    def _assert_errores(self, data, errores_esperados):
        self.assertIn('error', data)
        errores = data['error']
        self.assertEqual(json.loads(errores), errores_esperados)

    def _assert_propuesta_borrada(self, identificador, user):
        self.assertFalse(self._propuesta_para(identificador, user).exists())

    def _assert_propuesta(self, json_propuesta):
        identificador = json_propuesta['id']
        query_propuesta = self._propuesta_para(identificador, self.user)
        self.assertTrue(query_propuesta.exists())
        propuesta = query_propuesta.first()
        self.assertEqual(propuesta.titulo(), json_propuesta['title'])
        plan = json_propuesta['plan']
        if plan is None:
            self.assertIsNone(propuesta.plan())
        else:
            self.assertEqual(propuesta.plan().identificador(), plan['id'])
        self.assertEqual(propuesta.marca().identificador(), json_propuesta['brand']['id'])
        self.assertEqual(propuesta.texto_whatsapp(), json_propuesta['whatsapp_text'])
        self.assertEqual(propuesta.imagen_whatsapp(), json_propuesta['whatsapp_image'])
        self.assertEqual(propuesta.texto_sms(), json_propuesta['sms'])
        self.assertEqual(propuesta.texto_chat(), json_propuesta['chat_text'])
        self.assertEqual(propuesta.imagen_chat(), json_propuesta['chat_image'])

    def _data_para_agregar_propuesta(self, json_propuesta, user):
        return {'proposal': json_propuesta, 'user': user.username}

    def _propuesta_para(self, identificador, user):
        return Propuesta.objects.filter(_identificador=identificador, _user__username=user.username)
