from propuestas.models import TipoDePlan, Propuesta, Plan
from prospectos.models import <PERSON><PERSON>
from testing.base import BaseFixturedTest


class PropuestasTest(BaseFixturedTest):
    def _borrar_marcas(self):
        Marca.objects.all().exclude(_codigo=Marca.codigo_marca_blanca()).delete()

    def _crear_marca(self, identificador, nombre=None):

        try:
            marca = Marca.objects.con_identificador(identificador)
        except <PERSON><PERSON>.DoesNotExist:
            nombre = nombre or 'citroen'
            marca = Marca.nueva(
                identificador=identificador, logo='http://www.run0km.com/media/brands/citroen/citroen.jpg',
                codigo=nombre, habilitada=True, nombre=nombre.title())
        return marca

    def _crear_plan(self, identificador):
        tipo = TipoDePlan.nuevo(codigo='plan-7030', identificador=2, nombre='Plan 70/30')
        plan = Plan.nuevo(
            identificador=identificador, nombre='test', cantidad_instalados=84, cantidad_instalaciones_pagas=0,
            tipo=tipo, precio_de_instalacion=0, precio_minimo_de_instalacion=0, precio_adjudicado=0,
            precio_0_km=0, precio_total=0, esta_finalizado=False, esta_iniciado=True, esta_publicado=True,
            esta_normalizado=True, esta_adjudicado=False, es_plan_secundario=False, es_plan_principal=True,
        )
        return plan

    def _crear_propuesta(self, identificador, user, marca, plan,
                         texto_whatsapp=None, imagen_whatsapp=None, texto_sms=None, html_email=None, texto_chat=None,
                         imagen_chat=None):
        propuesta = Propuesta.nueva(
            identificador=identificador, user=user, titulo='temporal',
            marca=marca, plan=plan,
            texto_whatsapp=texto_whatsapp, imagen_whatsapp=imagen_whatsapp,
            texto_sms=texto_sms, html_email=html_email, texto_chat=texto_chat, imagen_chat=imagen_chat)
        return propuesta
