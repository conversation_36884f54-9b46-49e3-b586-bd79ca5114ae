from propuestas.constructores import ConstructorDeConcesionariasExternas, ConstructorException
from propuestas.models import ConcesionariaExterna
from propuestas.tests.test_helper import CRMTestValidator
from propuestas.tests.tests_core import PropuestasTest


class GenerarConcesionariasExternasTest(PropuestasTest):
    def setUp(self):
        self.constructor = ConstructorDeConcesionariasExternas()
        self.validador_crm = CRMTestValidator()
        ConcesionariaExterna.objects.all().delete()
        self._borrar_marcas()
        self.marca_uno = self._crear_marca('1', nombre='ford')
        self.marca_dos = self._crear_marca('2', nombre='fiat')

    def test_se_genera_concesionaria_externa_sin_marcas_partir_de_json_valido(self):
        concesionaria_externa_json = self._concesionaria_externa_json(
            ids_de_marcas=[], horario_de_atencion='', identificador=19)
        self.constructor.construir(concesionaria_externa_json)
        self._validar_existe_concesionaria_con(concesionaria_externa_json)

    def test_se_genera_concesionaria_externa_con_marcas_a_partir_de_json_valido(self):
        ids_de_marcas = [self.marca_uno.identificador(), self.marca_dos.identificador()]
        concesionaria_externa_json = self._concesionaria_externa_json(ids_de_marcas=ids_de_marcas, identificador=19)
        self.constructor.construir(concesionaria_externa_json)
        self._validar_existe_concesionaria_con(concesionaria_externa_json)

    def test_no_se_genera_concesionaria_externa_a_partir_de_json_sin_identificador(self):
        concesionaria_externa_json = self._concesionaria_externa_json(ids_de_marcas=[], identificador=None)
        self.assertRaises(ConstructorException, self.constructor.construir, concesionaria_externa_json)
        self._validar_no_existe_concesionaria_con(concesionaria_externa_json)

    def test_no_se_genera_concesionaria_externa_a_partir_de_json_sin_nombre(self):
        concesionaria_externa_json = self._concesionaria_externa_json(
            ids_de_marcas=[], nombre='', identificador=19)
        self.assertRaises(ConstructorException, self.constructor.construir, concesionaria_externa_json)
        self._validar_no_existe_concesionaria_con(concesionaria_externa_json)

    def test_se_genera_concesionaria_externa_con_marca_inexistente_si_llega_en_el_json(self):
        ids_de_marcas = [134520934567823456]
        concesionaria_externa_json = self._concesionaria_externa_json(
            ids_de_marcas=ids_de_marcas, identificador=19)
        self.constructor.construir(concesionaria_externa_json)
        self._validar_existe_concesionaria_con(concesionaria_externa_json)

    def _validar_existe_concesionaria_con(self, concesionaria_externa_json):
        self.assertEqual(ConcesionariaExterna.objects.all().count(), 1)
        concesionaria_externa = ConcesionariaExterna.objects.first()
        self.validador_crm.validar_concesionaria_externa_contra_pedido(
            concesionaria_externa=concesionaria_externa, concesionaria_externa_json=concesionaria_externa_json)

    def _validar_no_existe_concesionaria_con(self, json_concesionaria_externa):
        self.assertRaises(ConstructorException, self.constructor.construir, datos=json_concesionaria_externa)
        self.assertEqual(ConcesionariaExterna.objects.all().count(), 0)

    def _concesionaria_externa_json(self, ids_de_marcas, identificador, nombre=None, horario_de_atencion=None):
        nombre = nombre if nombre is not None else 'Run0km'
        horario_de_atencion = horario_de_atencion if horario_de_atencion is not None else 'LMX'
        return {
            "id": identificador,
            "name": nombre,
            "logo": "http://www.run0km.com/media/concessioners/icon-run0km-192x192.png",
            "featured": True,
            "timetable": horario_de_atencion,
            "enabled": True,
            "brands": ids_de_marcas
        }

