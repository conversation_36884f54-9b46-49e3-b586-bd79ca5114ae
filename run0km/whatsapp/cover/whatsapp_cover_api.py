import json
from datetime import datetime, timedelta

import requests
from django.conf import settings
from django.utils import timezone

from lib.api_client import UnexpectedStatus
from whatsapp.service import logger


class WhatsappCoverApi(object):
    """
        Movi esta clase [juan], no es de mi autoria.
    """

    NOTIFICACION_PREFIJO = '0'
    WHATSAPP_PREFIX = '9'

    def enviar_mensajes(self, token, mensajes, mensajes_encolados):
        codigo = timezone.now().strftime("%Y%m%d%H%M%S")
        return self._ejecutar_request_de_envio_desde_token(token, mensajes, mensajes_encolados, codigo)

    def recibir_mensajes(self, token):
        respuesta = self._enviar_pedido_de_mensajes_recibidos(token)
        return json.loads(respuesta)

    def _ejecutar_request_de_envio_desde_token(self, token, mensajes, mensajes_encolados, codigo):
        data = self._informacion_de_pedido_de_envio(token, mensajes, mensajes_encolados, codigo)
        return self._ejecutar_request_de_envio(data)

    def _informacion_de_pedido_de_envio(self, token, mensajes, mensajes_encolados, codigo):
        ahora = timezone.now()
        hoy = timezone.make_aware(
            datetime(year=ahora.year, month=ahora.month, day=ahora.day), timezone.get_current_timezone())
        despues = hoy + timedelta(days=2)
        data_mensajes = []
        self._agregar_mensajes_whatsapp(data_mensajes, mensajes)
        self._agregar_notificaciones(data_mensajes, mensajes_encolados)

        data = {
            "Group": '',
            "Messages": data_mensajes,
            "Name": 'Run0km',
            "ExtCode": codigo,
            "Token": token,
            "StartDate": hoy.strftime('%Y-%m-%d'),
            "EndDate": despues.strftime('%Y-%m-%d'),
            "StartTime": "1900-01-01 00:01",
            "EndTime": "1900-01-01 23:59",

        }
        return data

    def _agregar_notificaciones(self, data_mensajes, mensajes_encolados):
        for mensaje in mensajes_encolados:
            whatsapp_message = mensaje.message
            mensaje_json = self._mensaje_json(prefijo=self.NOTIFICACION_PREFIJO,
                                              mensaje_id=whatsapp_message.id,
                                              texto=whatsapp_message.text,
                                              nombre=mensaje.receiver_name,
                                              telefono=whatsapp_message.phone)
            data_mensajes.append(mensaje_json)

    def _agregar_mensajes_whatsapp(self, data_mensajes, mensajes):
        for mensaje in mensajes:
            mensaje_json = self._mensaje_json(
                prefijo=self.WHATSAPP_PREFIX,
                mensaje_id=mensaje.id,
                texto=mensaje.mensaje,
                nombre=mensaje.prospecto.nombre,
                telefono=mensaje.telefono)
            data_mensajes.append(mensaje_json)

    def _mensaje_json(self, prefijo, mensaje_id, texto, nombre, telefono):
        telefono_normalizado = telefono.replace('-', '')
        telefono_normalizado = telefono_normalizado.replace(' ', '')
        return {
            "Email": "",
            "ExtCode": '%s%s' % (prefijo, mensaje_id),
            "Message": texto,
            "Name": nombre,
            "Phone": telefono_normalizado
        }

    def _ejecutar_request_de_envio(self, data):
        if len(data['Messages']) > 0:
            headers = {'Content-Type': 'text/plain'}
            req = requests.post(url=settings.WHATSAPP_ENVIAR_URL, data=json.dumps(data), headers=headers)
            logger.debug("WHATSAPP Request: %s", data)
            logger.debug("WHATSAPP Response: %s\n%s", req, req.content)
            if req.status_code == 200:
                return True

    def _enviar_pedido_de_mensajes_recibidos(self, token):
        headers = {'Content-Type': 'text/plain'}
        response = requests.post(url=settings.WHATSAPP_RECIBIR_URL, data=token, headers=headers)

        logger.debug(response.content)
        if response.status_code == 200:
            return response.content
        else:
            raise UnexpectedStatus(message='The response status should be 20X but is %s' % response.status_code,
                                   request='sin datos', response=response)
