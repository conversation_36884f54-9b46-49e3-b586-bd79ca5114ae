import json

from whatsapp.cover.whatsapp_cover_api import WhatsappCoverApi


class FakeWhatsappCoverApi(WhatsappCoverApi):

    def __init__(self, mensajes_enviados=None):
        super().__init__()
        self.mensajes_enviados = mensajes_enviados or {}

    @classmethod
    def nuevo_para_token(cls, token, mensajes):
        return cls(mensajes_enviados={token: mensajes})

    def _ejecutar_request_de_envio(self, data):
        self.mensajes_enviados[data['Token']] = data['Messages']
        return True

    def _enviar_pedido_de_mensajes_recibidos(self, token):
        respuesta = []
        mensajes = self.mensajes_enviados.pop(token, [])
        for mensaje in mensajes:
            respuesta.append({
                'ItemType': 'IncomingMsgNewsModel',
                'Data': {
                    "ExtCode": mensaje['ExtCode'],
                    "Message": 'Respuesta mje:' + mensaje['Message'],
                    "Phone": mensaje['Phone'], },
            })
        return json.dumps(respuesta)
