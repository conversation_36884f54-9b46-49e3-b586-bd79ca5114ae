from rest_framework import serializers

class ProfileSerializer(serializers.Serializer):
    name = serializers.Char<PERSON><PERSON>(max_length=255, required=False)

class ContactSerializer(serializers.Serializer):
    profile = ProfileSerializer()
    wa_id = serializers.CharField(max_length=255)

class TextSerializer(serializers.Serializer):
    body = serializers.CharField(max_length=255, required=False)

class MessageSerializer(serializers.Serializer):
    from_ = serializers.Char<PERSON>ield(max_length=255, source='from')
    id = serializers.Char<PERSON>ield(max_length=255)
    timestamp = serializers.CharField(max_length=255)
    text = TextSerializer(required=False)
    type = serializers.CharField(max_length=50, required=False)

class OriginSerializer(serializers.Serializer):
    type = serializers.Char<PERSON>ield(max_length=255)

class ConversationSerializer(serializers.Serializer):
    id = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=255)
    expiration_timestamp = serializers.Cha<PERSON><PERSON><PERSON>(max_length=255, required=False)
    origin = OriginSerializer()

class PricingSerializer(serializers.Serializer):
    billable = serializers.BooleanField()
    pricing_model = serializers.Char<PERSON>ield(max_length=255)
    category = serializers.CharField(max_length=255)

class StatusSerializer(serializers.Serializer):
    id = serializers.CharField(max_length=255)
    status = serializers.CharField(max_length=255)
    timestamp = serializers.CharField(max_length=255)
    recipient_id = serializers.CharField(max_length=255)
    conversation = ConversationSerializer(required=False)
    pricing = PricingSerializer(required=False)

class MetadataSerializer(serializers.Serializer):
    display_phone_number = serializers.CharField(max_length=255)
    phone_number_id = serializers.CharField(max_length=255)

class ChangeValueSerializer(serializers.Serializer):
    messaging_product = serializers.CharField(max_length=255)
    metadata = MetadataSerializer()
    contacts = ContactSerializer(many=True, required=False)
    messages = MessageSerializer(many=True, required=False)
    statuses = StatusSerializer(many=True, required=False)

class ChangeSerializer(serializers.Serializer):
    value = ChangeValueSerializer()
    field = serializers.CharField(max_length=255)

class EntrySerializer(serializers.Serializer):
    id = serializers.CharField(max_length=255)
    changes = ChangeSerializer(many=True)

# Serializador para el formato api_response_accepted
class ContactSerializer(serializers.Serializer):
    input = serializers.CharField()
    wa_id = serializers.CharField()

class MessageSerializer(serializers.Serializer):
    id = serializers.CharField()
    message_status = serializers.CharField()

class ApiResponseAcceptedSerializer(serializers.Serializer):
    messaging_product = serializers.CharField()
    contacts = ContactSerializer(many=True)
    messages = MessageSerializer(many=True)
class ConversationOriginSerializer(serializers.Serializer):
    type = serializers.CharField()

class ConversationSerializer(serializers.Serializer):
    id = serializers.CharField()
    origin = ConversationOriginSerializer()

class PricingSerializer(serializers.Serializer):
    billable = serializers.BooleanField()
    pricing_model = serializers.CharField()
    category = serializers.CharField()

# Serializador para el formato webhook_delivered
class WebhookDeliveredSerializer(serializers.Serializer):
    id = serializers.CharField()
    status = serializers.CharField()
    timestamp = serializers.CharField()
    recipient_id = serializers.CharField()
    conversation = ConversationSerializer()
    pricing = PricingSerializer()

class ConversationSentSerializer(serializers.Serializer):
    id = serializers.CharField()
    expiration_timestamp = serializers.CharField()
    origin = ConversationOriginSerializer()

# Serializador para el formato webhook_sent
class WebhookSentSerializer(serializers.Serializer):
    id = serializers.CharField()
    status = serializers.CharField()
    timestamp = serializers.CharField()
    recipient_id = serializers.CharField()
    conversation = ConversationSentSerializer()
    pricing = PricingSerializer()

# Serializador para el formato webhook_read
class WebhookReadSerializer(serializers.Serializer):
    id = serializers.CharField()
    status = serializers.CharField()
    timestamp = serializers.CharField()
    recipient_id = serializers.CharField()


class ErrorDataSerializer(serializers.Serializer):
    details = serializers.CharField()

class ErrorSerializer(serializers.Serializer):
    code = serializers.IntegerField()
    title = serializers.CharField()
    message = serializers.CharField()
    error_data = ErrorDataSerializer()
    href = serializers.URLField()

# Serializador para el formato webhook_failed
class WebhookFailedSerializer(serializers.Serializer):
    id = serializers.CharField()
    status = serializers.CharField()
    timestamp = serializers.CharField()
    recipient_id = serializers.CharField()
    errors = ErrorSerializer(many=True)

#serializador de mensajes entrante para el webhook
class WebhookMensajeSerializer(serializers.Serializer):
    marca = serializers.CharField()
    data = serializers.DictField()
    telefono = serializers.CharField()
    operator_name = serializers.CharField(allow_null=True)
    marca_asociada_al_operador = serializers.CharField(allow_null=True)
    campania = serializers.CharField(allow_null=True)
    mensaje = serializers.CharField()
    nombre = serializers.CharField()
