from whatsapp.meta.meta_changes import MetaChanges
from whatsapp.meta.meta_conversation_changes import MetaConversationChanges
from whatsapp.meta.meta_status_fail import MetaStatusFailError
from whatsapp.meta.models.message_in_meta import MessageInMeta


class MetaStatusChanges(MetaChanges):

    @classmethod
    def new_example_status_change_to(cls,
                                     id_meta_message,
                                     status,
                                     phone=None, conversation_expiration_timestamp=None, conversation_id=None):
        return cls.new_from({
            "value": {
                "messaging_product": "whatsapp",
                "metadata": {
                    "display_phone_number": "5491127471997",
                    "phone_number_id": "458578827350094"
                },
                "statuses": [
                    {
                        "id": id_meta_message,
                        "status": status,
                        "timestamp": "1740582872",
                        "recipient_id": phone or '15551496593',
                        "conversation": (
                            cls._conversacion_segun_tipo_de_estado(
                                conversation_expiration_timestamp,
                                conversation_id,
                                status)),
                        "pricing": {
                            "billable": True,
                            "pricing_model": "CBP",
                            "category": "service"
                        }
                    }
                ]
            },
            "field": "messages"
        })


    @classmethod
    def _conversacion_segun_tipo_de_estado(cls, conversation_expiration_timestamp, conversation_id, status):
        if status == 'sent':
            conversation = {
                "id": conversation_id or 'f4e6e634e0f94459eae1b0d6e486cdaa',
                "expiration_timestamp": conversation_expiration_timestamp or "1748980980",
                "origin": {
                    "type": "service"
                }
            }
        else:
            conversation = {
                "id": conversation_id or 'f4e6e634e0f94459eae1b0d6e486cdaa',
                "origin": {
                    "type": "service"
                }
            }
        return conversation

    @classmethod
    def new_example_status_failed(cls, id_meta, recipient_id, code, fail_title, details, href, operator_id):
        return cls({"value": {
                "messaging_product": "whatsapp",
                "metadata": {
                    "display_phone_number": "5491127471997",
                    "phone_number_id": operator_id
                },
                "statuses": [
                    {
                        "id": id_meta,
                        "status": "failed",
                        "timestamp": "1748889159",
                        "recipient_id": recipient_id,
                        "errors": [
                            {
                                "code": code,
                                "title": fail_title,
                                "message": "Business eligibility payment issue",
                                "error_data": {
                                    "details": details
                                },
                                "href": href
                            }
                        ]
                    }
                ]
            },
            "field": "messages"
        })

    @classmethod
    def new_example_statuses_failed(cls, operator_id, id_meta, recipient_id, code_one, code_two, fail_title_one,
                                    fail_title_two, details_one, details_two, href_one, href_two):
        return cls({"value": {
            "messaging_product": "whatsapp",
            "metadata": {
                "display_phone_number": "5491127471997",
                "phone_number_id": operator_id
            },
            "statuses": [
                {
                    "id": id_meta,
                    "status": "failed",
                    "timestamp": "1748889159",
                    "recipient_id": recipient_id,
                    "errors": [
                        {
                            "code": code_one,
                            "title": fail_title_one,
                            "message": "Business eligibility payment issue",
                            "error_data": {
                                "details": details_one
                            },
                            "href": href_one
                        },
                        {
                            "code": code_two,
                            "title": fail_title_two,
                            "message": "Business eligibility payment issue",
                            "error_data": {
                                "details": details_two
                            },
                            "href": href_two
                        }
                    ]
                }
            ]
        },
            "field": "messages"
        })

    def as_status_appended_with(self, meta_changes_response_to_merge):
        response = dict(self._response_as_dict)
        response['value']['statuses'] = self.statuses() + meta_changes_response_to_merge.statuses()
        return MetaChanges.new_from(response)

    def is_change_of_statuses(self):
        return True

    def with_statuses_changes_do(self, not_fail_clousure, fail_clousure):
        responses = []
        for change_of_status in self.statuses():
            status = change_of_status['status']
            if status == MessageInMeta.FAILED:
                operator_id = self._response_as_dict['value']['metadata']['phone_number_id']
                response = fail_clousure(change_of_status['id'], operator_id, change_of_status['recipient_id'], self._errors_of(change_of_status))
            else:
                response = not_fail_clousure(change_of_status['id'], status, self._conversation_of(change_of_status))
            responses.append(response)
        return responses

    def _errors_of(self, change_of_status):
        meta_status_fail_errors = change_of_status.get('errors',[])
        return  [MetaStatusFailError.new_from(meta_status_fail_error) for meta_status_fail_error in meta_status_fail_errors]

    def statuses(self):
        return self._response_as_dict['value']['statuses']

    def _conversation_of(self, status):
        conversation_data = status.get('conversation')
        if conversation_data is not None:
            return MetaConversationChanges.new_from(conversation_data)
        else:
            return None

