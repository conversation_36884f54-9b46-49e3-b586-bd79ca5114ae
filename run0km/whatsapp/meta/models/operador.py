from django.db import models
from whatsapp.meta.models.meta_template import MetaTemplate
class GrupoOperadores(models.Model):
    nombre = models.CharField(max_length=50, unique=True)
    
    class Meta:
        verbose_name = 'Grupo de Operadores'
        verbose_name_plural = 'Grupos de Operadores'

    def __str__(self):
        return self.nombre

class Operador(models.Model):
    nombre = models.CharField(max_length=50, unique=True)
    id_operador = models.CharField(max_length=50, unique=True)
    activo = models.BooleanField(default=False)
    templates = models.ManyToManyField(MetaTemplate, related_name='operador_templates')
    grupo_operadores = models.ForeignKey(GrupoOperadores, related_name='operador_grupo_operadores', blank=True, null=True, on_delete=models.CASCADE)
    
    class Meta:
        verbose_name = 'Operador'
        verbose_name_plural = 'Operadores'
        
    def __str__(self):
        return f'{self.nombre} - {self.grupo_operadores}'

    def obtener_nombre(self):
        return self.nombre

    def obtener_id_operador(self):
        return self.id_operador