from django.core.exceptions import ValidationError
from django.db import models

from whatsapp.meta.models.conversation_in_meta import ConversationInMeta
from whatsapp.meta.querysets.message_in_meta_queryset import MessageInMetaQuerySet


class MessageInMeta(models.Model):
    """
        - Esta clase modela los mensajes de whatsapp, modela tanto mensajes "comunes" (de texto) como envios de
        templates

        - Usar esta misma clase para modelar tanto los salientes como los entrantes, para ello deberíamos preguntarle
        cual de los dos es.
    """
    # Status
    PENDING_TO_SEND_TO_META = "pending_to_send_to_meta"
    ACCEPTED = "accepted"
    DELIVERED = "delivered"
    SENT = "sent"
    READ = "read"
    FAILED = "failed"
    RECEIVED = "received"

    STATUS_HIERARCHY = {
        "pending_to_send_to_meta": ["accepted", "failed"],
        "accepted": ["delivered", "sent", "read", "failed"],
        "sent": ["delivered", "read", "failed"],
        "delivered": ["read", "failed"],
        "read": ["failed"],
        "failed": ["pending_to_send_to_meta"],
        "received": ["received"]
    }
    STATUS = [
        (PENDING_TO_SEND_TO_META, "pending to send to meta"),
        (ACCEPTED, "accepted"),
        (DELIVERED, "delivered"),
        (SENT, "sent"),
        (READ, "read"),
        (FAILED, "failed"),
        (RECEIVED, "received")
    ]

    # Actions
    ACTIONS = [
        (SENT, "sent"),
        (RECEIVED, "received")
    ]

    _meta_id = models.CharField(max_length=64, null=True)
    _status = models.CharField(
        max_length=24,
        choices=STATUS,
        default=PENDING_TO_SEND_TO_META,
    )

    _created_at = models.DateTimeField(auto_now_add=True)
    _phone_number = models.CharField(max_length=14)
    _conversation = models.ForeignKey(ConversationInMeta, on_delete=models.CASCADE, related_name="messageinmeta")
    _template_name = models.CharField(max_length=64, null=True)
    _text = models.TextField(null=True)
    _action = models.CharField(
        max_length=24,
        choices=ACTIONS,
        default=SENT
    )

    objects = MessageInMetaQuerySet.as_manager()

    @classmethod
    def new_pending(cls, phone_number, conversation, text):
        return cls._new_with(
            meta_id=None,
            template_name=None,
            phone_number=phone_number, conversation=conversation, text=text, action=cls.SENT,
        status=cls.PENDING_TO_SEND_TO_META)

    @classmethod
    def new_template(cls, meta_id, status, template_name, phone_number, conversation):
        return cls._new_with(
            meta_id=meta_id,
            status=status,
            template_name=template_name,
            phone_number=phone_number, conversation=conversation, text=None, action=cls.SENT
        )

    @classmethod
    def new_received(cls, meta_id, status, phone_number, conversation, text, template_name=None):
        return cls._new_with(meta_id=meta_id, status=status, template_name=template_name, phone_number=phone_number,
                      conversation=conversation, text=text, action=cls.RECEIVED)

    @classmethod
    def new_sent(cls, meta_id, phone_number, conversation, text):
        return cls._new_with(
            meta_id=meta_id,
            phone_number=phone_number, conversation=conversation, text=text, action=cls.SENT,
            status=cls.ACCEPTED)


    @classmethod
    def _new_with(cls, meta_id, status, phone_number, conversation, text, template_name=None, action=None):
        message = cls(_meta_id=meta_id, _status=status, _template_name=template_name,
                      _phone_number=phone_number,
                      _conversation=conversation, _text=text, _action=action)
        message.save()
        return message


    # Accessors
    def conversation(self):
        return self._conversation

    def text(self):
        return self._text

    def action(self):
        return self._action

    def phone_number(self):
        return self._phone_number

    def created_at(self):
        return self._created_at

    # Status
    def has_accepted_status(self):
        return self._has_status(self.ACCEPTED)

    def has_sent_status(self):
        return self._has_status(self.SENT)

    def has_delivered_status(self):
        return self._has_status(self.DELIVERED)

    def has_read_status(self):
        return self._has_status(self.READ)

    def has_failed_status(self):
        return self._has_status(self.FAILED)

    def has_recieved_action(self):
        return self._has_action(self.RECEIVED)

    def has_sent_action(self):
        return self._has_action(self.SENT)

    def _has_status(self, status):
        return self._status == status

    def _has_action(self, action):
        return self._action == action

    # Testing
    def has_template_with_name(self, template_name):
        return self._template_name == template_name

    # Changes
    def change_status_to(self, status):
        if status == self.FAILED:
            self._status = status
            self.save()
            return
        self._check_hierarchy_status(status)
        self._status = status
        self.save()

    def _check_hierarchy_status(self, status):
        current_status = self._status
        allowed_transitions = self.STATUS_HIERARCHY.get(current_status, [])
        if status not in allowed_transitions:
            raise ValidationError(f"No se puede cambiar el estado de '{current_status}' a '{status}'")

    def change_meta_id(self, meta_id):
        self._meta_id = meta_id
        self.save()




