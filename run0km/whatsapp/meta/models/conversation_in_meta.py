from datetime import timedelta

from django.db import models
from django.utils import timezone

from whatsapp.meta.models.operador import Operador
from whatsapp.meta.querysets.conversation_in_meta_query_set import ConversationInMetaQuerySet


class ConversationInMeta(models.Model):
    _meta_id = models.CharField(max_length=64, blank=True, null=True)
    _expiration_datetime = models.DateTimeField(blank=True, null=True)
    _created_at = models.DateTimeField(auto_now_add=True)
    _operator = models.ForeignKey(Operador, on_delete=models.DO_NOTHING, blank=True, null=True)

    objects = ConversationInMetaQuerySet.as_manager()

    @classmethod
    def new(cls, operator):
        return cls.objects.create(_operator=operator)

    def meta_id(self):
        return self._meta_id

    def expiration_datetime(self):
        return self._expiration_datetime

    def operator(self):
        return self._operator

    def change_expiration_datetime(self, expiration_datetime):
        self._expiration_datetime = expiration_datetime
        self.save()

    def change_meta_id(self, meta_id):
        self._meta_id = meta_id
        self.save()

    def message_received(self):
        message_in_meta_received = self.messageinmeta.messages_received().all()
        return message_in_meta_received

    def has_message_received(self):
        return self.message_received().exists()

    def last_message_recieved(self):
        message_in_meta_received = self.message_received().order_by_created_at().last()
        return message_in_meta_received

    def first_template_sent(self):
        message_in_meta_sent = self.messageinmeta.templates().order_by_created_at().first()
        return message_in_meta_sent

    def pending_messages(self):
        return self.messageinmeta.pending_messages()

    def is_open(self):
        return self.has_message_received() and self.close_datetime() > timezone.now()

    def close_datetime(self):
        if self.has_message_received():
            return self.last_message_recieved().created_at() + timedelta(hours=24)
        else:
            return None

    def has_conversation(self):
        return self.objects.filter()

    def has_template_sent(self):
        messagge_sent = self.messageinmeta.templates().exists()
        return messagge_sent

    def is_expired(self):
        if self._expiration_datetime is None:
            if self.has_template_sent() and not self.has_message_received():
                return self.first_template_sent().created_at() + timedelta(hours=24) < timezone.now()
            if self.has_message_received():
                return self.last_message_recieved().created_at() + timedelta(hours=24) < timezone.now()
        # return not self.expiration_datetime() or self.expiration_datetime() > timezone.now()
        #TODO que pasa si la fecha de expiracion no se renueva pero se sigue recibiendo mensajes?
        return self.expiration_datetime() is not None and self.expiration_datetime() < timezone.now()

    def add_pending_message(self, phone_number, text):
        from whatsapp.meta.models.message_in_meta import MessageInMeta
        return MessageInMeta.new_pending(phone_number=phone_number,
                                         conversation=self, text=text)

    class Meta:
        verbose_name_plural = 'Conversaciones en Meta'