class WhatsappMetaServiceMock:

    def __init__(self):
        self._bloque_para_iniciar_una_conversacion = lambda: None
        self._bloque_para_enviar_mensaje = lambda: None
        self._conversaciones_iniciadas = []
        self._mensajes_enviados = []

    @classmethod
    def nuevo(cls):
        return cls()

    # Metodos polimorficos a WsMetaService
    def iniciar_conversacion(self, telefono_destinatario, marca, dni, id_operador, nombre_template, url_notificacion):
        respuesta = self._bloque_para_iniciar_una_conversacion()
        self._conversaciones_iniciadas.append({
            "token": "",
            "number": telefono_destinatario,
            "fromOperator": id_operador,
            "template": nombre_template,
            "templateLanguage": "es_AR",
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
            "sendNotificationsTo": url_notificacion,
        })
        return respuesta

    def enviar_mensaje(self, telefono_destinatario, marca, dni, id_operador, texto, url_notificacion):
        respuesta = self._bloque_para_enviar_mensaje()
        self._mensajes_enviados.append({
            "number": telefono_destinatario,
            "fromOperator": id_operador,
            "message": texto,
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
            "sendNotificationsTo": url_notificacion,
        })
        return respuesta

    # Fin Metodos polimorficos a WsMetaService

    def a_iniciado_una_conversacion_con(self, telefono_destinatario, marca=None, dni=None, id_operador=None,
                                        nombre_template=None, url_notificacion=None):
        return {
            "token": "",
            "number": telefono_destinatario,
            "fromOperator": id_operador,
            "template": nombre_template,
            "templateLanguage": "es_AR",
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
            "sendNotificationsTo": url_notificacion,
        } in self._conversaciones_iniciadas


    def a_enviado_un_mensaje_a(self, telefono_destinatario, texto, marca=None, dni=None, id_operador=None, url_notificacion=None):
        return {
            "number": telefono_destinatario,
            "fromOperator": id_operador,
            "message": texto,
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
            "sendNotificationsTo": url_notificacion,
        } in self._mensajes_enviados

    def cantidad_conversaciones_iniciadas_con(self, telefono_destinatario, marca=None, dni=None, id_operador=None,
                                              nombre_template=None, url_notificacion=None):
        return self._conversaciones_iniciadas.count({
            "token": "",
            "number": telefono_destinatario,
            "fromOperator": id_operador,
            "template": nombre_template,
            "templateLanguage": "es_AR",
            "scriptData": {
                "marca": marca,
                "dni": dni
            },
            "sendNotificationsTo": url_notificacion,
        })


    def cuando_inicia_una_conversacion_con_template_hace(self, bloque):
        self._bloque_para_iniciar_una_conversacion = bloque


    def cuando_se_envia_un_mensaje_hace(self, bloque):
        self._bloque_para_enviar_mensaje = bloque


