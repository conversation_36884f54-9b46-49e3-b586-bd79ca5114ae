from django.utils import timezone

class MetaConversationChanges(object):
    def __init__(self, response_as_dict):
        self._response_as_dict = response_as_dict

    @classmethod
    def new_from(cls, response_as_dict):
        return cls(response_as_dict)

    def id_meta(self):
        return self._response_as_dict['id']

    def expiration_datetime(self):
        expiration_datetime_string = self._expiration_timestamp_as_string()
        if expiration_datetime_string is  None:
            return None
        else:
            return self._datetime_from_timestamp(expiration_datetime_string)

    def _expiration_timestamp_as_string(self):
        return self._response_as_dict.get('expiration_timestamp')

    def _datetime_from_timestamp(self, timestamp_string):
        expiration_timestamp = int(timestamp_string)
        from core.date_helper import DatetimeHelper
        calendario = DatetimeHelper()
        return calendario.from_timestamp(expiration_timestamp, timezone=timezone.utc)