from django.db import models


class WhatsappMessageDeliveryManager(models.Manager):
    def pending_deliveries_for(self, token):
        from whatsapp.models import WhatsappMessageDelivery
        return self.filter(status=WhatsappMessageDelivery.PENDING, token=token)

    def successful_deliveries(self):
        from whatsapp.models import WhatsappMessageDelivery
        return self.filter(status=WhatsappMessageDelivery.SENT)

    def mark_deliveries_as(self, deliveries_ids, status):
        list_to_avoid_circle_query = list(deliveries_ids)
        self.filter(id__in=list_to_avoid_circle_query).update(status=status)

    def mark_deliveries_as_sent(self, deliveries_ids):
        from whatsapp.models import WhatsappMessageDelivery
        self.mark_deliveries_as(deliveries_ids, WhatsappMessageDelivery.SENT)

    def mark_deliveries_as_sending(self, deliveries):
        from whatsapp.models import WhatsappMessageDelivery
        self.mark_deliveries_as(deliveries.values_list('id', flat=True), WhatsappMessageDelivery.SENDING)
