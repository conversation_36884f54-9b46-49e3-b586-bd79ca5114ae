import mock
from unittest import TestCase
from whatsapp.meta.models.meta_template import MetaTemplate

class TestMetaTemplate(TestCase):

    def test_variables_al_crear_template(self):
        template = MetaTemplate.objects.create(nombre="saludo1_fiat", contenido="Hola, soy {{marca}} y necesito tu DNI {{dni}}")
        self.assertTrue(template.variables.count() == 2)
        
    def test_variables_al_actualizar_template(self):
        template = MetaTemplate.objects.create(nombre="saludo1_fiat", contenido="Hola, soy {{marca}} y necesito tu DNI {{dni}}")
        template.contenido = "Hola, soy {{marca}} y necesito tu DNI {{dni}} y tu nombre {{nombre}}"
        template.save()
        self.assertTrue(template.variables.count() == 3)
    
    def test_variables_al_actualizar_template_quitando_variables(self):
        template = MetaTemplate.objects.create(nombre="saludo1_fiat", contenido="Ho<PERSON>, soy {{marca}} y necesito tu DNI {{dni}}")
        template.contenido = "Hola, soy {{marca}}"
        template.save()
        self.assertTrue(template.variables.count() == 1)
    
    def test_nombre_de_variables(self):
        template = MetaTemplate.objects.create(nombre="saludo1_fiat", contenido="Hola, soy {{marca}} y necesito tu DNI {{dni}}")
        self.assertTrue(template.variables.first().nombre == "marca")
        self.assertTrue(template.variables.last().nombre == "dni")
        template.contenido = "Hola, soy {{marca}} y necesito tu DNI {{dni}} y tu nombre {{nombre}}"
        template.save()
        self.assertTrue(template.variables.last().nombre == "nombre")
        self.assertListEqual(list(template.variables.values_list("nombre", flat=True)), ["marca", "dni", "nombre"])

