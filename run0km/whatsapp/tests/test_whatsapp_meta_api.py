from unittest import skipIf

import mock
from django.test import TestCase, override_settings
from whatsapp.meta.models.meta_template import MetaTemplate
from whatsapp.meta.models.operador import Operador
from whatsapp.meta.models.repositorio_de_mensaje import RepositorioDeMensaje
from whatsapp.meta.send_meta_api import SendMetaAP<PERSON>
from whatsapp.meta.serializadores.serializadores_api import WebhookDeliveredSerializer, WebhookSentSerializer, \
    WebhookReadSerializer, WebhookFailedSerializer
from whatsapp.meta.services.ws_meta_service import WsMetaService


class ResponseMock:
    @staticmethod
    def ok_with_json_content(content):
        response = mock.Mock()
        response.status_code = 200
        response.json.return_value = content
        return response


api_response_accepted = {
    "messaging_product": "whatsapp",
    "contacts": [{"input": "5491121698525", "wa_id": "5491121698525"}],
    "messages": [
        {"id": "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=", "message_status": "accepted"}]}

webhook_delivered = {
    "id": "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=", "status": "delivered",
    "timestamp": "1737381219", "recipient_id": "5491121698525",
    "conversation": {"id": "b0d94b6403c4eaf1b105bdbe199ab247", "origin": {"type": "marketing"}},
    "pricing": {"billable": True, "pricing_model": "CBP", "category": "marketing"}}
webhook_sent = {
    "id": "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=", "status": "sent", "timestamp": "1737381218",
    "recipient_id": "5491121698525",
    "conversation": {"id": "b0d94b6403c4eaf1b105bdbe199ab247", "expiration_timestamp": "1737467400",
                     "origin": {"type": "marketing"}},
    "pricing": {"billable": True, "pricing_model": "CBP", "category": "marketing"}}

webhook_read = {
    "id": "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=",
    "status": "read",
    "timestamp": "**********",
    "recipient_id": "5491121698525"}

webhook_failed = {
    "id": "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=",
    "status": "failed",
    "timestamp": "**********",
    "recipient_id": "5491121698525",
    "errors": [
        {"code": 131049, "title": "This message was not delivered to maintain healthy ecosystem engagement.",
         "message": "This message was not delivered to maintain healthy ecosystem engagement.",
         "error_data": {
             "details": "In order to maintain a healthy ecosystem engagement, the message failed to be delivered."},
         "href": "https://developers.facebook.com/docs/whatsapp/cloud-api/support/error-codes/"}]}


@skipIf(True, reason='Deprecado, falta revisar si otros tests cubren estos casos y borrarlos')
@override_settings(WS_META_TOKEN='falso_token')
class TestWhatsappMeta(TestCase):


    def setUp(self):
        self.webhook_delivered_serializer = WebhookDeliveredSerializer(data=webhook_delivered)
        self.webhook_delivered_serializer.is_valid()

        self.webhook_sent_serializer = WebhookSentSerializer(data=webhook_sent)
        self.webhook_sent_serializer.is_valid()

        self.webhook_read_serializer = WebhookReadSerializer(data=webhook_read)
        self.webhook_read_serializer.is_valid()

        self.webhook_failed_serializer = WebhookFailedSerializer(data=webhook_failed)
        self.webhook_failed_serializer.is_valid()
        self.meta_template = MetaTemplate.objects.create(nombre="saludo1_fiat",
                                                         contenido="Hola, soy {{marca}} y necesito tu DNI {{dni}}")

        self.operador, _ = Operador.objects.get_or_create(id_operador="***************",
                                                          defaults={"nombre": "Pani", "activo": True})

    @mock.patch('whatsapp.meta.send_template_meta_api.SendTemplateMetaAPI.call',
                return_value=ResponseMock.ok_with_json_content(api_response_accepted))
    def xxtest_iniciar_conversacion(self, send_template_mock):
        service_api_ws = WsMetaService.nuevo()
        mensaje = service_api_ws.obsoleto_iniciar_conversacion('1121698525', 'fiat', '25724484', self.operador,
                                                               self.meta_template, {"marca": "fiat", "dni": "25724484"})
        self.assertEquals(mensaje.id_meta_mensaje, api_response_accepted["messages"][0]["id"])
        self.assertEquals(mensaje.status, api_response_accepted["messages"][0]["message_status"])
        mensaje.eliminar()

    @mock.patch('whatsapp.meta.send_template_meta_api.SendTemplateMetaAPI.call',
                return_value=ResponseMock.ok_with_json_content(api_response_accepted))
    def xxtest_iniciar_conversacion_con_variables_incorrectas(self, send_template_mock):
        service_api_ws = WsMetaService.nuevo()
        with self.assertRaises(ValueError):
            service_api_ws.obsoleto_iniciar_conversacion('1121698525', 'fiat', '25724484', self.operador,
                                                         self.meta_template, {"marcaca": "incorrect_value"})

    @mock.patch('whatsapp.meta.send_template_meta_api.SendTemplateMetaAPI.call',
                return_value=ResponseMock.ok_with_json_content(api_response_accepted))
    def xxtest_handler_status_service(self, send_template_mock):
        service_api_ws = WsMetaService.nuevo()
        mensaje = service_api_ws.obsoleto_iniciar_conversacion('1121698525', 'fiat', '25724484', self.operador,
                                                               self.meta_template, {})

        service_api_ws.handler(self.webhook_delivered_serializer.data)
        repositorio_de_mensaje = RepositorioDeMensaje.traer(
            "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=")
        self.assertEquals(repositorio_de_mensaje.status, 'delivered')

        service_api_ws.handler(self.webhook_sent_serializer.data)
        repositorio_de_mensaje = RepositorioDeMensaje.traer(
            "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=")
        self.assertEquals(repositorio_de_mensaje.status, 'sent')

        service_api_ws.handler(self.webhook_read_serializer.data)
        repositorio_de_mensaje = RepositorioDeMensaje.traer(
            "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=")
        self.assertEquals(repositorio_de_mensaje.status, 'read')

        service_api_ws.handler(self.webhook_sent_serializer.data)
        repositorio_de_mensaje = RepositorioDeMensaje.traer(
            "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=")
        self.assertEquals(repositorio_de_mensaje.status, 'read')

        service_api_ws.handler(self.webhook_failed_serializer.data)
        repositorio_de_mensaje = RepositorioDeMensaje.traer(
            "wamid.HBgNNTQ5MTEyMTY5ODUyNRUCABEYEjhERDczNjg3RTAxODQ5QTE4OAA=")
        self.assertEquals(repositorio_de_mensaje.status, 'failed')

        mensaje.eliminar()


class TestWhatsappMetaAPIFake(TestCase):
    """
        No es un test, solo un ejemplo de uso.
    """

    def xxtest_example_conversacion_solo_saliente(self):
        cliente = SendMetaAPI.new_to("https://chat.smsmasivos.biz:8087/OutboundCampaign/StartConversations")
        telefonos = []
        plantilla = None
        request = {
            "token": "b498f01a-2498-4e13-a3ae-f92865bb17b4",
            "numbers": telefonos,
            "operator": "***************",
            "template": plantilla,
            "templateLanguage": "es_AR",
            "outboundCampaign": "test1"
        }
        response = cliente.call(request)

        print(response)

    def xxtest_example_conversacion_total_meta(self):
        cliente = SendMetaAPI.new_to("https://chat.soybot.com:8087/WhatsAppProxy/SendMessage")
        request = {
            "token": "C1129FB0-56E6-4D6E-94CD-DA09480346A3",
            "number": "5491123059633",
            "fromOperator": "***************",
            "template": "saludo1_fiat",
            "templateLanguage": "es_AR",
            "scriptData": {
                "marca": "Chery",
                "dni": "123456"
            }
        }
        response = cliente.call(request)

        print(response)

    def xxtest_example_envio_de_mensaje_pendiente(self):
        cliente = SendMetaAPI.new_to("https://chat.soybot.com:8087/WhatsAppProxy/SendMessage")
        request = {
            "token": "C1129FB0-56E6-4D6E-94CD-DA09480346A3",
            "number": "5491123059633",
            "fromOperator": "***************",
            "message": "Hola, te interesaría comprar un Chery?",
            "scriptData": {
                "marca": "Chery",
                "dni": "123456"
            }
        }
        response = cliente.call(request)

        print(response.json())
