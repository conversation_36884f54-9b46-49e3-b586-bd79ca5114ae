from django.test import TestCase

from whatsapp.models import WhatsappMessage, WhatsappMessageDelivery
from whatsapp.service import WhatsappService


class TestsWhatsappService(TestCase):
    def setUp(self):
        self.messages = [WhatsappMessage.new_with(phone='1143435465', text='test') for _ in range(5)]
        self.whatsapp_service = WhatsappService()

    def test_send_messages_without_token_configuration_fails(self):
        configuration = {}
        self.assertRaises(ValueError, self.whatsapp_service.send, messages=self.messages, configuration=configuration)
        configuration = {'other': 'key', 'than': 'token'}
        self.assertRaises(ValueError, self.whatsapp_service.send, messages=self.messages, configuration=configuration)

    def test_send_messages_with_token_configuration_works_and_creates_deliveries_successfully(self):
        configuration = {'token': 'test-token'}
        self.assertEqual(WhatsappMessageDelivery.objects.count(), 0)
        self.whatsapp_service.send(messages=self.messages, configuration=configuration)
        self.assertEqual(WhatsappMessageDelivery.objects.count(), len(self.messages))
        for message in self.messages:
            try:
                WhatsappMessageDelivery.objects.get(message=message)
            except WhatsappMessageDelivery.DoesNotExist:
                self.fail('The message %s shoud be created', message)
