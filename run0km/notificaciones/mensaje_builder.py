from django.core.mail import EmailMultiAlternatives

from lib.smscover import SMSMessage
from whatsapp.models import WhatsappMessage


class MensajeBuilder(object):
    def mensajes_para(self, contenido):
        raise NotImplementedError('Subclass Responsibility')


class SMSMessageBuilder(MensajeBuilder):

    def mensajes_para(self, contenido):
        mensajes = [self.mensaje_para(contenido, destinatario) for destinatario in
                    contenido.destinatarios()]
        return mensajes

    def mensaje_para(self, contenido, destinatario):
        return SMSMessage.new_from_text(
            sending_id=contenido.id(),
            string=contenido.texto(),
            phone_number=destinatario)


class EmailMessageBuilder(MensajeBuilder):
    def mensajes_para(self, contenido):
        return [self._mensaje_para(contenido)]

    def _mensaje_para(self, contenido):
        headers = {'Reply-To': contenido.responder_a()} if contenido.responder_a() else None
        email = EmailMultiAlternatives(
            subject=contenido.asunto(),
            body=contenido.cuerpo(),
            from_email=contenido.remitente(),
            to=contenido.destinatarios(),
            bcc=contenido.destinatarios_en_copia_oculta(),
            headers=headers
        )
        contenido.configurar_tipo_de_contenido_en(email)
        return email


class WhatsappMessageBuilder(MensajeBuilder):

    def mensajes_para(self, contenido):
        mensajes = [self.mensaje_para(contenido, destinatario) for destinatario in
                    contenido.destinatarios()]
        return mensajes

    def mensaje_para(self, contenido, destinatario):
        return WhatsappMessage.new_with(phone=destinatario, text=contenido.cuerpo())
