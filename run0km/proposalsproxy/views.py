from functools import wraps
import json
import requests as http_requests
from django.views.generic import View, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin as BaseLoginRequiredMixin
from django.http import HttpResponse, HttpResponseForbidden
from .utils import Utils


class ProposalIndex(BaseLoginRequiredMixin, TemplateView):
    template_name = 'proposalsproxy/index.html'
    
    
class LoginRequiredMixin(BaseLoginRequiredMixin):
    raise_exception = True


class GraphQLProxy(LoginRequiredMixin, View):
    def get(self, request):
        return Utils.get_graphql(request)
    
    def post(self, request):
        return Utils.post_graphql(request)
    