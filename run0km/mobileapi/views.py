# -*- coding: utf-8 -*-
import logging

from rest_framework import exceptions
from rest_framework import serializers
from rest_framework import status
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rest_framework.views import APIView

from conversaciones.models import Conversacion
from mobileapi.adapters import ChatInformationAdapter, ProposalAdapter
from mobileapi.errors import VersionDeAPIInvalida, ErrorDeAutenticacion, \
    NumeroDeSecuenciaInvalido, ParametroRequerido, SincronizacionLockeada, SesionExpirada
from mobileapi.sincronizador import Sincronizador
from mobileapi.token_authentication import StrictTokenAuthentication
from occ.request_adapters import RequestAdapterToReadFromData
from occ.servicio_de_chat_de_ventas import ServicioDeChatDeVentas
from occ.views import OCCAceptarCompulsaView, OCCRechazarCompulsaView, OCCMarcarChatComoLeidoView
from occ.views.chat import OCCEnviarMensajeDeChatView
from propuestas.models import Propuesta
from propuestas.views import PropuestasParaProspectoViaChatView, EnvioDePropuestasAChatView
from vendedores.control_de_actividad import ControladorDeActividad

HTTP_422_UNPROCESSABLE_ENTITY = 422
logger = logging.getLogger(__name__)


# Por ahora usamos canales publicos, ya que los privados tenemos que resolver como
# pasar el token (es decir le tenemos que pasar el token a Pusher para que lo reeenvie y poder
# detectar que vendedor se quiere suscribir ). Como solo se le envia notificaciones #ready_to_sync
# no es importante que sea privado
# @method_decorator(csrf_exempt, name='dispatch')
# class ChannelAuthenticateView(View):
#     def post(self, request):
#         token_key = request.POST.get('token_key', None)
#         channel_name = request.POST.get('channel_name', None)
#         socket_id = request.POST.get('socket_id', None)
#         sincronizador = Sincronizador()
#         try:
#             authentication = sincronizador.suscribirse_a_canal(token_key, channel_name, socket_id)
#         except ErrorDeAutenticacion:
#             return self.permiso_denegado(mensaje='Usuario invalido')
#         else:
#             return JsonResponse(json.dumps(authentication))
#
#     def permiso_denegado(self, mensaje):
#         return Response(status=status.HTTP_403_FORBIDDEN,
#                         data={'message': mensaje})


class MobileLoginView(ObtainAuthToken):
    def post(self, request, *args, **kwargs):
        user = self._validate_user(request)
        sincronizador = Sincronizador()
        try:
            version_api = request.data.get('version_api', '')
            fcm_token = request.data.get('fcm_token', '')
            token_key = sincronizador.login(user, version_api, fcm_token)
        except ErrorDeAutenticacion as exc:
            self._not_authenticated(str(exc))
        except VersionDeAPIInvalida as exc:
            logger.debug('[%s] Version invalida. Parametros: %s', user.username, request.data)
            return Response(status=status.HTTP_403_FORBIDDEN,
                            data={'message': str(exc),
                                  'version_api': sincronizador.version_api_actual()})
        except ParametroRequerido as exc:
            logger.debug('[%s] Parametro requerido y no enviado: %s', user.username, request.data)
            logger.debug('[%s] %s', user.username, str(exc))
            return Response(status=status.HTTP_403_FORBIDDEN,
                            data={'message': str(exc)})
        else:
            vendedor = user.vendedor
            sesion = sincronizador.sesion_de(vendedor=vendedor)
            canal = sincronizador.nombre_de_canal_para(user.vendedor)
            numero_de_lote_de_prospectos = sesion.ultimo_numero_de_secuencia_de_prospectos_exitoso()
            numero_de_lote_de_conversaciones = sesion.ultimo_numero_de_secuencia_de_conversaciones_exitoso()
            logger.debug('[%s] Se ha loggeado: %s asignandole sync_sequence_number: %s ', user.username,
                         vendedor.full_name(), numero_de_lote_de_prospectos)
            return Response({'token': token_key, 'channel': canal,
                             'prospect_sync_sequence_number': numero_de_lote_de_prospectos,
                             'conversation_sync_sequence_number': numero_de_lote_de_conversaciones})

    def _validate_user(self, request):
        serializer = self.serializer_class(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError:
            self._not_authenticated('Usuario y contraseña invalidos.')
        else:
            user = serializer.validated_data['user']
            return user

    def _not_authenticated(self, detail):
        raise exceptions.NotAuthenticated(detail=detail)


class MobileAPIOperationView(APIView):
    authentication_classes = (StrictTokenAuthentication,)

    def __init__(self, **kwargs):
        super(MobileAPIOperationView, self).__init__(**kwargs)
        self._sincronizador = Sincronizador()

    def handle_exception(self, exc):
        if isinstance(exc, exceptions.AuthenticationFailed):
            return self._token_expirado()
        elif isinstance(exc, VersionDeAPIInvalida):
            return self._responder_api_invalida(exc)
        else:
            return super(MobileAPIOperationView, self).handle_exception(exc)

    def _token_expirado(self):
        user = self.request.user
        logger.debug('[%s] Token expirado', user.username)
        return Response(status=status.HTTP_403_FORBIDDEN,
                        data={'message': 'invalid token'})

    def permission_denied(self, request, message=None):
        user = self.request.user
        logger.debug('[%s] %s - permiso denegado: %s', user.username, self.__class__.__name__, message)
        super(MobileAPIOperationView, self).permission_denied(request, message)

    def _responder_api_invalida(self, exc):
        user = self.request.user
        operacion = self.__class__.__name__
        logger.debug('[%s] %s: version de api fallida - %s', user.username, operacion, str(exc))
        return Response(status=status.HTTP_403_FORBIDDEN,
                        data={'message': str(exc),
                              'version_api': self._sincronizador.version_api_actual()})

    def _registrar_actividad_para(self, user):
        """
            Por ahora lo hacemos a mano!, ver si usamos un middleware
        """
        controlador_de_actividad = ControladorDeActividad()
        controlador_de_actividad.registrar_actividad(user)


class ListaInicialDeModelosView(MobileAPIOperationView):
    tipo_sincronizable = None  # esto es necesario para usar as_view con parametros

    def get(self, request, *args, **kwargs):
        user = request.user
        vendedor = user.vendedor
        try:
            lista_inicial = self.lista_inicial_de_modelos(vendedor)
        except ErrorDeAutenticacion as exc:
            self._not_authenticated(str(exc))
        else:
            lista_inicial_serializada = self._serializar_modelos(lista_inicial)
            self._registrar_actividad_para(user)
            nombre_del_tipo = self.tipo_sincronizable.clave_de_serializacion_plural()
            logger.debug('[%s] Lista inicial de %d %s.', vendedor.username(), len(lista_inicial_serializada),
                         nombre_del_tipo)
            return Response({nombre_del_tipo: lista_inicial_serializada})

    def _serializar_modelos(self, modelos):
        adapter = self.tipo_sincronizable.adapter()
        modelos_serializados = [adapter.adapt_this(modelo) for modelo in modelos]
        return modelos_serializados

    def lista_inicial_de_modelos(self, vendedor):
        return self._sincronizador.lista_inicial_de_modelos_para(vendedor, self.tipo_sincronizable)

    def _not_authenticated(self, detail):
        raise exceptions.NotAuthenticated(detail=detail)


class SincronizacionDeModelosView(MobileAPIOperationView):
    tipo_sincronizable = None  # esto es necesario para usar as_view con parametros

    def get_parsers(self):
        return [self.tipo_sincronizable.parser()]

    def post(self, request, *args, **kwargs):
        user = request.user
        vendedor = user.vendedor
        nombre_del_tipo = self.tipo_sincronizable.clave_de_serializacion_plural()
        username = vendedor.username()
        logger.debug('[%s] Sincronizacion de %s para vendedor: %s', username, nombre_del_tipo,
                     vendedor.full_name())
        try:
            pedido_de_sincronizacion_json = request.data
        except exceptions.ParseError as error:
            logger.debug('[%s] Pedido de sincronizacion con: %s', username, request.data)
            logger.debug('[%s] %s - Exception: %s', username, self.__class__.__name__, error)
            return self._responder_error(error.detail)
        logger.debug('[%s] Pedido de sincronizacion con: %s', username, pedido_de_sincronizacion_json)
        response = self._realizar_sincronizacion(pedido_de_sincronizacion_json, vendedor)
        self._registrar_actividad_para(vendedor.user)
        return response

    def _realizar_sincronizacion(self, pedido_de_sincronizacion_json, vendedor):
        pedido = self.tipo_sincronizable.armar_pedido_para(pedido_de_sincronizacion_json)
        username = vendedor.username()
        try:
            result = self._sincronizador.sincronizar_lote(pedido=pedido, vendedor=vendedor)
        except NumeroDeSecuenciaInvalido as exc:
            logger.debug('[%s] Sincronizacion fallida: numero de version invalido - %s', username, str(exc))
            return Response(status=status.HTTP_400_BAD_REQUEST,
                            data={'message': str(exc), 'sync_sequence_number': pedido.numero_de_secuencia()})
        except SincronizacionLockeada as exc:
            logger.debug('[%s] Sincronizacion lockeada para %s - Lote: %s', username, vendedor,
                         pedido.numero_de_secuencia())
            return Response(status=status.HTTP_429_TOO_MANY_REQUESTS, data={'message': str(exc)})
        except SesionExpirada as exc:
            logger.debug('[%s] Sesion expirada', username)
            return Response(status=status.HTTP_403_FORBIDDEN, data={'message': str(exc)})
        else:
            logger.debug('[%s] Sincronizacion exitosa: %s', username, result)
            return Response(data=result)

    def _responder_error(self, mensaje_de_error):
        return Response(data={'error': mensaje_de_error}, status=status.HTTP_400_BAD_REQUEST)


class AceptarCompulsaView(MobileAPIOperationView):
    def post(self, request, compulsa_pk):
        return OCCAceptarCompulsaView().dispatch(request, compulsa_pk=compulsa_pk)


class RechazarCompulsaView(MobileAPIOperationView):
    def post(self, request, compulsa_pk):
        return OCCRechazarCompulsaView().dispatch(request, compulsa_pk=compulsa_pk)


class EnviarMensajeDeChatView(MobileAPIOperationView):
    def post(self, request):
        return OCCEnviarMensajeDeChatView(request_adapter_class=RequestAdapterToReadFromData).dispatch(request)


class MarcarChatComoLeidoView(MobileAPIOperationView):
    def post(self, request, chat_pk):
        return OCCMarcarChatComoLeidoView().dispatch(request, chat_pk=chat_pk)


class DatosDeChatView(MobileAPIOperationView):
    def get(self, request, chat_pk, *args, **kwargs):
        user = request.user
        vendedor = user.vendedor
        chat = get_object_or_404(vendedor.chats_de_ventas, pk=chat_pk)
        chat_adapter = ChatInformationAdapter()
        data = chat_adapter.adapt_this(chat)
        return Response(status=status.HTTP_200_OK, data=data)


class PropuestasParaChatView(MobileAPIOperationView):
    def get(self, request, chat_pk):
        return PropuestasParaProspectoViaChatView().dispatch(request, pk=chat_pk)


class EnviarPropuestaParaChatView(MobileAPIOperationView):
    def post(self, request, chat_pk):
        return EnvioDePropuestasAChatView(request_adapter_class=RequestAdapterToReadFromData).dispatch(request,
                                                                                                       pk=chat_pk)


class PropuestasView(MobileAPIOperationView):
    def get(self, request, *args, **kwargs):
        user = request.user
        vendedor = user.vendedor
        propuestas = Propuesta.objects.para_vendedor(vendedor).select_related('_plan___modelo', '_marca')
        proposal_adapter = ProposalAdapter()
        data = proposal_adapter.adapt_all(propuestas)
        return Response(status=status.HTTP_200_OK, data=data)


class CantidadDeMensajesNoLeidosView(MobileAPIOperationView):
    def get(self, request, *args, **kwargs):
        user = request.user
        vendedor = user.vendedor
        servicio_de_chat_de_ventas = ServicioDeChatDeVentas()
        cantidad = Conversacion.objects.cantidad_de_mensajes_no_leidos_de_vendedor(vendedor)
        cantidad += servicio_de_chat_de_ventas.cantidad_de_mensajes_no_leidos_para(vendedor)
        data = {'cantidad_de_mensajes_no_leidos': cantidad}
        return Response(status=status.HTTP_200_OK, data=data)


class ListaChatsVigentesView(MobileAPIOperationView):
    def get(self, request, *args, **kwargs):
        from occ.models import ChatDeVentas

        user = request.user
        vendedor = user.vendedor
        chats_del_vendedor = ChatDeVentas.objects.ultimos_chats_para_vendedor(vendedor)
        chat_adapter = ChatInformationAdapter()
        data = chat_adapter.adapt_all(chats_del_vendedor)
        return Response(status=status.HTTP_200_OK, data=data)


class ModificarTokenFCMView(MobileAPIOperationView):
    def post(self, request, *args, **kwargs):
        user = request.user
        vendedor = user.vendedor
        username = vendedor.username()
        try:
            fcm_token = request.data.get('fcm_token', '')
            self._sincronizador.modificar_fcm(vendedor, fcm_token)
        except ParametroRequerido as exc:
            logger.debug('[%s] Parametro requerido y no enviado: %s', username, request.data)
            logger.debug('[%s] %s', username, str(exc))
            return Response(status=status.HTTP_403_FORBIDDEN,
                            data={'message': str(exc)})
        else:
            self._registrar_actividad_para(user)
            logger.debug('[%s] Modificacion exitosa de token fcm: %s', username, fcm_token)
            return Response(status=status.HTTP_200_OK, data={})


class FakeView(APIView):
    authentication_classes = (StrictTokenAuthentication,)

    def post(self, request, *args, **kwargs):
        return Response(status=status.HTTP_403_FORBIDDEN,
                        data={'message': 'invalid token'})
