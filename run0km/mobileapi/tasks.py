import logging

from run0km import celery_app as app
from django.conf import settings

from mobileapi.communication import CommunicationChannel
from mobileapi.fcm import DeliveryFCM

logger = logging.getLogger(__name__)


@app.task(name="mobileapi.tasks.enviar_notification_fcm")
def enviar_notification_fcm(registration_id):
    logger.debug('enviar_notification_fcm: %s' % registration_id)
    servicio_fcm = DeliveryFCM()
    servicio_fcm.notificar_asignacion_de(registration_id=registration_id)


@app.task(name="mobileapi.tasks.enviar_notificacion_fcm_de_mensajes_de_las_ultimas_horas")
def enviar_notificacion_fcm_de_mensajes_de_las_ultimas_horas():
    from mobileapi.sincronizador import Sincronizador
    from vendedores.models import Vendedor
    from conversaciones.models import Conversacion

    sincronizador = Sincronizador()
    vendedores_con_app_habilitada = Vendedor.objects.con_app_habilitada()
    vendedores_a_los_cuales_notificar = [vendedor for vendedor in vendedores_con_app_habilitada if
                                         Conversacion.objects.cantidad_no_leidas_de_vendedor(vendedor) > 0]
    sesiones = sincronizador.sesiones_de(vendedores_a_los_cuales_notificar)
    fcm_tokens_de_vendedores = [item for item in sesiones.values_list('_fcm_token', flat=True) if item is not None]
    logger.debug('enviar_notificacion_fcm_de_mensajes_de_las_ultimas_horas - tokens: %s' % fcm_tokens_de_vendedores)

    servicio_fcm = DeliveryFCM()
    servicio_fcm.notificar_mensajes_nuevos_para(fcm_tokens_de_vendedores)


@app.task(name="mobileapi.tasks.enviar_notificacion_push")
def enviar_notificacion_push(vendedor_pk, channel_name, operacion, argumentos):
    # logger.debug('Envio de Notificacion a vendedor: pk=%s, canal: %s' % (vendedor_pk, channel_name))
    # logger.debug('Notificacion: %s argumentos %s' % (operacion, argumentos))
    canal = CommunicationChannel.new_with(settings.PUSHER_APP_ID,
                                          settings.PUSHER_KEY,
                                          settings.PUSHER_SECRET)
    canal.send_message(channels=channel_name, event_name=operacion, message=argumentos)
