from django.db import models


class SincronizacionManager(models.Manager):
    def exitosas_de(self, sesion, numero_de_secuencia):
        return self.filter(_sesion=sesion, _estado=self.model.EXITOSA, _numero_de_secuencia=numero_de_secuencia)

    def rechazadas_de(self, sesion, numero_de_secuencia):
        return self.filter(_sesion=sesion, _estado=self.model.RECHAZADA, _numero_de_secuencia=numero_de_secuencia)

    def pendientes_de(self, sesion, numero_de_secuencia):
        return self.filter(_sesion=sesion, _estado=self.model.PENDIENTE, _numero_de_secuencia=numero_de_secuencia)

    def de_modelo_y_sesion(self, sesion, id_model, tipo):
        return self.filter(_sesion=sesion, _id_model=id_model, _tipo=tipo)

    def existe_para_modelo_y_sesion(self, sesion, id_model, tipo):
        return self.filter(_sesion=sesion, _id_model=id_model, _tipo=tipo).exists()

    def de_numero_y_sesion(self, numero_de_secuencia, sesion):
        return self.filter(_sesion=sesion, _numero_de_secuencia=numero_de_secuencia)

    def de_modelo_numero_y_sesion(self, sesion, id_model, tipo, numero, accion=None):
        """
            Hacemos este filter porque no podemos garantizar que haya una sola sincronizacion por condiciones de
            carrera.
        """
        accion = accion or self.model.MODIFICACION
        sincronizaciones = self.filter(
            _sesion=sesion, _id_model=id_model, _numero_de_secuencia=numero, _accion=accion, _tipo=tipo)
        sincronizacion = sincronizaciones.first()
        if sincronizacion is None:
            raise self.model.DoesNotExist
        else:
            return sincronizacion

    def existe_para_modelo_numero_y_sesion(self, sesion, id_model, tipo, numero):
        return self.filter(_sesion=sesion, _id_model=id_model, _numero_de_secuencia=numero, _tipo=tipo).exists()

    def crear_todas_a_partir_de(self, sesion, tipo, ids_model, mensaje, numero_de_secuencia_actual):
        sincronizaciones = []
        for id_model in ids_model:
            sincronizacion = self.model(_sesion=sesion, _id_model=id_model, _mensaje=mensaje,
                                        _numero_de_secuencia=numero_de_secuencia_actual,
                                        _estado=self.model.RECHAZADA, _tipo=tipo)
            sincronizaciones.append(sincronizacion)
        return self.bulk_create(sincronizaciones)
