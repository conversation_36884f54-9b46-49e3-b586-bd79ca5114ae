# coding=utf-8
import logging

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.utils import timezone
from django.utils.timezone import datetime

from mobileapi.errors import ComandoException, EvaluacionDeComandoException
from mobileapi.parsers import MobileAPIParser
from prospectos.models import LlamadaRealizada, MotivoDeFinalizacion
from prospectos.models.exceptions import GestorDeProspectoError

logger = logging.getLogger(__name__)


class Comando(object):
    FORMATO_FECHA = settings.API_MOBILE_FORMATO_FECHA_HORA

    def __init__(self, argumentos, tipo_sincronizable):
        super(Comando, self).__init__()
        self.argumentos = argumentos
        self._tipo_sincronizable = tipo_sincronizable

    @classmethod
    def nombre(cls):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def nuevo_para(cls, nombre, argumentos, tipo_sincronizable):
        subclases = cls.__subclasses__()
        for subclase in subclases:
            if subclase.nombre() == nombre:
                subclase.validar_formato(argumentos)
                return subclase(argumentos=argumentos, tipo_sincronizable=tipo_sincronizable)
        raise ValueError('No existe ningun comando para el nombre %s' % nombre)

    @classmethod
    def validar_formato(cls, argumentos):
        cls.parser().validate(data=argumentos)

    @classmethod
    def nuevo_desde(cls, comando_json, tipo_sincronizable):
        operacion = comando_json.get('operation', None)
        argumentos = comando_json.get('arguments', [])
        if not operacion or argumentos is None:
            raise ValidationError('comando invalid %s' % comando_json)
        comando = cls.nuevo_para(nombre=operacion, argumentos=argumentos, tipo_sincronizable=tipo_sincronizable)
        return comando

    @classmethod
    def parser(cls):
        raise NotImplementedError('subclass responsibility')

    def ejecutar(self, modelo, vendedor):
        self._tipo_sincronizable.validar_comando(self)
        try:
            with transaction.atomic():
                return self._ejecutar_comando(modelo, vendedor)
        except ValidationError as exc:
            raise EvaluacionDeComandoException(str(exc))
        except (ComandoException, GestorDeProspectoError) as exc:
            raise EvaluacionDeComandoException(str(exc))
        except IntegrityError as exc:
            raise EvaluacionDeComandoException(str(exc))

    def _ejecutar_comando(self, modelo, vendedor):
        raise NotImplementedError('subclass responsibility')

    def _model_con_id(self, model_class, prospecto_id):
        try:
            modelo = model_class.objects.get(pk=prospecto_id)
        except model_class.DoesNotExist:
            raise ComandoException.modelo_inexistente(model_class, prospecto_id)
        return modelo

    def _formatear_fecha(self, fecha_inicio_string):
        return timezone.make_aware((datetime.strptime(fecha_inicio_string, self.FORMATO_FECHA)))


class FinalizacionComando(Comando):
    @classmethod
    def nombre(cls):
        return 'end_prospect_tracking'

    @classmethod
    def parser(cls):
        return MobileAPIParser.finalizar_seguimiento()

    def _ejecutar_comando(self, prospecto, vendedor):
        # TODO: por ahora quitamos la restriccion de reason hasta que desde mobile resuelvan.
        descripcion_de_motivo = self.argumentos.get('reason', None)
        try:
            motivo = MotivoDeFinalizacion.para(descripcion=descripcion_de_motivo)
        except MotivoDeFinalizacion.DoesNotExist:
            motivo = None
            otro_motivo = self.argumentos.get('other_reason_description', '')
            otro_motivo = otro_motivo[:64]
            if not otro_motivo:
                raise ValidationError('"%s" no es un motivo de finalización válido' % descripcion_de_motivo)
        else:
            otro_motivo = ''
        comentario = self.argumentos.get('comment', '')
        self._agregar_finalizacion_de_prospecto(motivo=motivo,
                                                otro_motivo=otro_motivo,
                                                comentario=comentario,
                                                prospecto=prospecto,
                                                vendedor=vendedor)

    def _agregar_finalizacion_de_prospecto(self, motivo, otro_motivo, comentario, prospecto, vendedor):
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        if not prospecto.finalizado:
            gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
            gestor.finalizar_prospecto(prospecto=prospecto, motivo=motivo,
                                       otro_motivo=otro_motivo, comentario=comentario,
                                       debe_sincronizar=False)
        else:
            logger.debug('Finalizacion ignorada, ya existe una finalizacion para el prospecto (id=%s)' % prospecto.pk)


class ReactivarSeguimientoComando(Comando):
    @classmethod
    def nombre(cls):
        return 'reactivate_prospect_tracking'

    @classmethod
    def parser(cls):
        return MobileAPIParser.reactivar_seguimiento()

    def _ejecutar_comando(self, prospecto, vendedor):
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        if not prospecto.vendido and not prospecto.en_proceso:
            gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
            gestor.reactivar_seguimiento(prospecto, debe_sincronizar=False)


class CargarVentaComando(Comando):
    FORMATO_FECHA = settings.API_MOBILE_FORMATO_FECHA

    @classmethod
    def nombre(cls):
        return 'load_prospect_sale'

    @classmethod
    def parser(cls):
        return MobileAPIParser.agregar_cargar_venta()

    def _ejecutar_comando(self, prospecto, vendedor):
        venta = self.argumentos['sale']
        marca = venta['brand']
        modelo = venta['model']
        precio = venta['price']
        numero_de_contrato = venta.get('contract_number', None)
        fecha_realizacion_string = venta['date']
        fecha_realizacion = self._formatear_fecha(fecha_realizacion_string)
        self._cargar_venta(prospecto=prospecto, vendedor=vendedor, marca=marca, modelo=modelo, precio=precio,
                           fecha_de_realizacion=fecha_realizacion, numero_de_contrato=numero_de_contrato)

    def _cargar_venta(self, prospecto, vendedor, marca, modelo, fecha_de_realizacion, numero_de_contrato, precio):
        if prospecto.vendido:
            logger.info("El prospecto %s ya estaba vendido. Datos extra: (vendedor, %s), (marca, %s), (modelo, %s), "
                        "(fecha de realizacion, %s), (numero de contrato, %s), (precio, %s)"
                        % (prospecto.id, vendedor.id, marca, modelo, fecha_de_realizacion, numero_de_contrato, precio))
            return
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        gestor = GestorDeProspecto.nuevo_para(vendedor)
        gestor.cargar_venta(
            prospecto=prospecto, marca=marca, modelo=modelo, precio=precio,
            fecha_de_realizacion=fecha_de_realizacion, numero_de_contrato=numero_de_contrato)


class NuevoLlamadoRealizadoComando(Comando):
    @classmethod
    def nombre(cls):
        return 'add_made_call'

    @classmethod
    def parser(cls):
        return MobileAPIParser.agregar_llamada_realizada_argumentos()

    def _ejecutar_comando(self, prospecto, vendedor):
        llamada = self.argumentos['call']
        fecha_inicio_string = llamada['start_time']
        fecha_inicio = self._formatear_fecha(fecha_inicio_string)
        duracion = llamada.get('duration')

        self._agregar_llamada_realizada(fecha_inicio, duracion, prospecto, vendedor)

    def _agregar_llamada_realizada(self, fecha_inicio, duracion, prospecto, vendedor):
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        gestor.agregar_llamada_realizada(prospecto, fecha_inicio=fecha_inicio, duracion=duracion)


class AgregarFormularioDeLlamadaRealizadaComando(Comando):
    @classmethod
    def nombre(cls):
        return 'add_made_call_form'

    @classmethod
    def parser(cls):
        return MobileAPIParser.agregar_formulario_de_interes()

    def _ejecutar_comando(self, prospecto, vendedor):
        llamada = self.argumentos['call']
        fecha_inicio_string = llamada['start_time']
        fecha_inicio = self._formatear_fecha(fecha_inicio_string)
        duracion = llamada.get('duration')
        respuestas = self._generar_respuestas_de(self.argumentos['answered_questions'])
        self._agregar_formulario_de_llamada_realizada(fecha_inicio, duracion, prospecto, vendedor, respuestas)

    def _agregar_formulario_de_llamada_realizada(self, fecha_inicio, duracion, prospecto, vendedor, respuestas):
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        try:
            gestor.agregar_formulario_de_llamada_realizada(
                prospecto, fecha_inicio=fecha_inicio, duracion=duracion, respuestas=respuestas)
        except LlamadaRealizada.DoesNotExist:
            fecha_inicio_string = fecha_inicio.strftime(self.FORMATO_FECHA)
            raise ComandoException.llamada_inexistente(prospecto.pk, fecha_inicio_string, duracion)

    def _generar_respuestas_de(self, respuestas):
        return [(each['question'], each['answer']) for each in respuestas]


class AgregaComentarioComando(Comando):
    @classmethod
    def nombre(cls):
        return 'add_comment'

    @classmethod
    def parser(cls):
        return MobileAPIParser.agregar_comentario_argumentos()

    def _ejecutar_comando(self, prospecto, vendedor):
        comentario = self.argumentos['comment']
        fecha_string = comentario['date']
        fecha = self._formatear_fecha(fecha_string)
        texto = comentario['text']
        es_automatico = comentario['is_automatic']
        self._agregar_nuevo_comentario(prospecto, texto, fecha, es_automatico, vendedor)

    def _agregar_nuevo_comentario(self, prospecto, texto, fecha, es_automatico, vendedor):
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        gestor.comentar_prospecto(
            prospecto=prospecto, fecha=fecha, texto=texto, es_automatico=es_automatico, debe_sincronizar=False)


class AgregaLlamadaProgramadaComando(Comando):
    @classmethod
    def nombre(cls):
        return 'add_programmed_call'

    @classmethod
    def parser(cls):
        return MobileAPIParser.agregar_llamada_programada()

    def _ejecutar_comando(self, prospecto, vendedor):
        llamda_programada = self.argumentos['programmed_call']
        fecha_string = llamda_programada['date']
        fecha = self._formatear_fecha(fecha_string)

        self._agregar_nueva_llamada_programada(prospecto, fecha, vendedor)

    def _agregar_nueva_llamada_programada(self, prospecto, fecha, vendedor):
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        gestor.programar_nuevo_llamado_para(prospecto, fecha=fecha, debe_sincronizar=False)


class EnviarPropuestaComando(Comando):
    @classmethod
    def nombre(cls):
        return 'send_proposal'

    @classmethod
    def parser(cls):
        return MobileAPIParser.enviar_propuesta_argumentos()

    def _ejecutar_comando(self, prospecto, vendedor):
        id_propuesta = self.argumentos['id']

        self._enviar_propuesta(id_propuesta, prospecto, vendedor)

    def _enviar_propuesta(self, id_propuesta, prospecto, vendedor):
        from propuestas.gestion import GestorDePropuesta
        from propuestas.models import Propuesta

        gestor = GestorDePropuesta.nuevo_para(user=vendedor.user)
        gestion_de_envio = gestor.gestion_de_envio_a_prospecto(prospecto)

        try:
            propuesta = gestion_de_envio.propuesta_con_id(id_propuesta)
            gestion_de_envio.enviar(propuesta)
        except Propuesta.DoesNotExist:
            logger.error("No se pudo enviar la propuesta %s para el prospecto %s y el vendedor %s" %
                         (id_propuesta, prospecto, vendedor))


class AgregaMensajeDeWhatsappAConversacionComando(Comando):
    @classmethod
    def nombre(cls):
        return 'add_whatsapp_message_to_conversation'

    @classmethod
    def parser(cls):
        return MobileAPIParser.agregar_mensaje_de_whatsapp_a_conversacion()

    def _ejecutar_comando(self, conversacion, vendedor):
        from conversaciones.models import Conversacion
        mensaje = self.argumentos['message']
        comando = AgregarMensajesAProspectoComando.nuevo_con(mensaje, Conversacion.TIPO_WHATSAPP)
        prospecto = conversacion.prospecto
        comando.ejecutar(prospecto, vendedor)


class AgregarMensajesAProspectoComando(Comando):
    @classmethod
    def nombre(cls):
        return 'add_messages_to_prospect'

    @classmethod
    def parser(cls):
        return MobileAPIParser.agregar_mensajes_a_prospecto()

    @classmethod
    def nuevo_con(cls, mensaje, tipo):
        from mobileapi.tipo_sincronizable import TipoProspectoSincronizable
        return cls({"messages": [mensaje], "type": tipo}, TipoProspectoSincronizable())

    def _ejecutar_comando(self, prospecto, vendedor):
        tipo = self.argumentos['type']
        mensajes = self.argumentos['messages']
        self._agregar_mensajes(prospecto, mensajes, tipo)

    def _agregar_mensajes(self, prospecto, mensajes, tipo):
        from conversaciones.models import Conversacion

        if tipo == Conversacion.TIPO_WHATSAPP:
            for mensaje in mensajes:
                self._agregar_nuevo_mensaje(prospecto, mensaje)
        elif tipo == Conversacion.TIPO_EMAIL:
            # TODO: falta hacer, no es trivial
            pass

    def _agregar_nuevo_mensaje(self, prospecto, mensaje):
        from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
        fecha_string = mensaje['date']
        fecha = self._formatear_fecha(fecha_string)
        texto = mensaje['text']
        telefono = prospecto.telefono
        MensajesWhatsapp.nuevo(prospecto, texto, telefono, MensajesWhatsapp.VENDEDOR, fecha)


class MarcaConversacionComoLeidaComando(Comando):
    @classmethod
    def nombre(cls):
        return 'mark_conversation_as_read'

    @classmethod
    def parser(cls):
        return MobileAPIParser.marcar_conversacion_como_leida()

    def _ejecutar_comando(self, conversacion, vendedor):
        conversacion.marcar_como_leida()
