from unittest import TestCase

import mock

from mobileapi.communication import CommunicationChannel
from mobileapi.errors import ComunicationChannelException


def trigger_raise_exception(channels, event_name, data):
    raise ValueError()


class CommunicationChannelTest(TestCase):

    def setUp(self):
        super(CommunicationChannelTest, self).setUp()
        self.received_message = None

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_messange_succesfully_sent(self, mock_pusher_trigger):
        channel = CommunicationChannel.new_with(app_id='1', key='2', secret='shh')
        message = {'message': 'Houston tenemos un pushlema'}
        channel.send_message(channels='my-channel', event_name='my-event', message=message)

    @mock.patch('pusher.Pusher.trigger', side_effect=trigger_raise_exception)
    def test_messange_should_raise_exception_when_the_connection_fails(self, mock_pusher_trigger):
        channel = CommunicationChannel.new_with(app_id='1', key='2', secret='shh')
        message = {'message': 'Houston tenemos un pushlema'}
        self.assertRaises(ComunicationChannelException, channel.send_message,
                          channels='my-channel', event_name='my-event', message=message)

    def _callback(self, message):
        self.received_message = message
