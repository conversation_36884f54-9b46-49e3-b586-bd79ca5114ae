import jsonschema
from django.test import TestCase

from mobileapi.parsers import MobileAPIParser


class MobileAPIParserTest(TestCase):
    def test_validar_vacio_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.sincronizacion_de_prospectos_base()
        self.assertRaises(jsonschema.ValidationError, parser.validate, data={})

    def test_validar_con_sincronizacion_menor_a_cero_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.sincronizacion_de_prospectos_base()
        datos = {"sync_sequence_number": -1, "changes": []}
        self.assertRaises(jsonschema.ValidationError, parser.validate, data=datos)

    def test_validar_sin_cambios_no_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.sincronizacion_de_prospectos_base()
        datos = {"sync_sequence_number": 1, "changes": []}
        self.assertNotRaises(jsonschema.ValidationError, parser.validate, data=datos)

    def test_validar_con_cambios_para_id_de_prospecto_erroneo_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.sincronizacion_de_prospectos_base()
        datos = {"sync_sequence_number": 1, "changes": [{"id": -1, "actions": []}]}
        self.assertRaises(jsonschema.ValidationError, parser.validate, data=datos)

    def test_validar_con_operacion_invalida_no_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.sincronizacion_de_prospectos_base()
        datos = {
            "sync_sequence_number": 1,
            "changes": [{"id": 1,
                         "actions": [{"operation": "xxx", "arguments": {}}]}]
        }
        self.assertNotRaises(jsonschema.ValidationError, parser.validate, data=datos)

    def test_validar_agregar_llamada_realizada_argumentos_mal_formado_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.agregar_llamada_realizada_argumentos()
        self.assertRaises(jsonschema.ValidationError, parser.validate, data={})

    def test_validar_agregar_llamada_realizada_argumentos_bien_formado_no_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.agregar_llamada_realizada_argumentos()
        datos = {
            "call": {
                "start_time": "2017-02-20-11:51:35",
                "duration": 345
            }
        }
        self.assertNotRaises(jsonschema.ValidationError, parser.validate, data=datos)

    def test_validar_agregar_llamada_realizada_argumentos_con_duracion_negativa_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.agregar_llamada_realizada_argumentos()
        datos = {
            "call": {
                "start_time": "2017-02-20-11:51:35",
                "duration": -345
            }
        }
        self.assertRaises(jsonschema.ValidationError, parser.validate, data=datos)

    def test_validar_agregar_llamada_realizada_argumentos_con_fecha_mal_formada_debe_lanzar_excepcion(self):
        parser = MobileAPIParser.agregar_llamada_realizada_argumentos()
        datos = {
            "call": {
                "start_time": "21-02-2017-11:51:35",
                "duration": 345
            }
        }
        self.assertRaises(jsonschema.ValidationError, parser.validate, data=datos)
    #
    # def test_validar_agregar_llamada_realizada_argumentos_fecha_invalida_debe_lanzar_excepcion(self):
    #     parser = MobileAPIParser.agregar_llamada_realizada_argumentos()
    #     datos = {
    #         "call": {
    #             "start_time": "2017-02-29-11:51:35",
    #             "duration": 345
    #         }
    #     }
    #     self.assertRaises(jsonschema.ValidationError, parser.validate, data=datos)
    #
    # def test_validar_con_cambios_para_comando_agregar_comentario_con_formato_valido_no_debe_lanzar_excepcion(self):
    #     parser = MobileAPIParser.sincronizacion()
    #     comentario = {
    #                 "is_automatic": True,
    #                 "date": "2017-02-28-11:51:35",
    #                 "text": "Hello world"
    #             }
    #     datos = {
    #         "sync_sequence_number": 1,
    #         "changes": [{"prospect_id": 1,
    #                      "actions": [{"operation": "add_comment", "arguments": {"comment": comentario}}]}]
    #     }
    #     self.assertNotRaises(jsonschema.ValidationError, parser.validate, data=datos)
    #
    # def test_validar_con_cambios_para_comando_agregar_comentario_con_fecha_invalida_debe_lanzar_excepcion(
    #         self):
    #     parser = MobileAPIParser.sincronizacion()
    #     comentario = {
    #                 "is_automatic": True,
    #                 "date": "2017-02-29-11:51:35",
    #                 "text": "Hello world"
    #             }
    #     datos = {
    #         "sync_sequence_number": 1,
    #         "changes": [{"prospect_id": 1,
    #                      "actions": [{"operation": "add_comment", "arguments": {"comment": comentario}}]}]
    #     }
    #     # parser.validate(datos)
    #     self.assertRaises(jsonschema.ValidationError, parser.validate, data=datos)
    #
    # def test_validar_con_cambios_para_finalizar_prospecto_con_formato_valido_no_debe_lanzar_excepcion(
    #         self):
    #     parser = MobileAPIParser.sincronizacion()
    #     argumentos = {
    #             'reason': "otro",
    #             'other_reason_description': "",
    #             'comment': "hello"
    #         }
    #     datos = {
    #         "sync_sequence_number": 1,
    #         "changes": [{"prospect_id": 1,
    #                      "actions": [{"operation": "end_prospect_tracking", "arguments": argumentos}]}]
    #     }
    #     self.assertNotRaises(jsonschema.ValidationError, parser.validate, data=datos)
    #
    # def test_validar_con_cambios_para_agregar_llamada_programada_con_formato_valido_no_debe_lanzar_excepcion(
    #         self):
    #     parser = MobileAPIParser.sincronizacion()
    #     argumentos = {"programmed_call": {"date": "2017-02-28-11:51:35"}}
    #     datos = {
    #         "sync_sequence_number": 1,
    #         "changes": [{"prospect_id": 1,
    #                      "actions": [{"operation": "add_programmed_call", "arguments": argumentos}]}]
    #     }
    #     self.assertNotRaises(jsonschema.ValidationError, parser.validate, data=datos)
    #
    # def test_validar_con_cambios_para_agregar_llamada_programada_con_fecha_invalida_debe_lanzar_excepcion(
    #         self):
    #     parser = MobileAPIParser.sincronizacion()
    #     argumentos = {"programmed_call": {"date": "2017-02-29-11:51:35"}}
    #     datos = {
    #         "sync_sequence_number": 1,
    #         "changes": [{"prospect_id": 1,
    #                      "actions": [{"operation": "add_programmed_call", "arguments": argumentos}]}]
    #     }
    #     self.assertRaises(jsonschema.ValidationError, parser.validate, data=datos)
    #
    # def test_validar_con_cambios_para_agregar_llamada_programada_con_formato_valido_no_debe_lanzar_excepcion(
    #         self):
    #     parser = MobileAPIParser.sincronizacion()
    #     argumentos = {
    #         'sale': {
    #             'brand': "ford",
    #             'model': "ecosport",
    #             'price': "150",
    #             'contract_number': "",
    #             'date': "2017-02-20"
    #         }
    #     }
    #     datos = {
    #         "sync_sequence_number": 1,
    #         "changes": [{"prospect_id": 1,
    #                      "actions": [{"operation": "add_programmed_call", "arguments": argumentos}]}]
    #     }
    #     self.assertNotRaises(jsonschema.ValidationError, parser.validate, data=datos)

    def assertNotRaises(self, excClass, callableObj=None, *args, **kwargs):
        try:
            callableObj(*args, **kwargs)
        except excClass as exc:
            self.fail('AssertionError: %s was raised. %s' % (excClass, exc))
