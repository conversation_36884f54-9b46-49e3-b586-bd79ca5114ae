import mock
from django.urls import reverse
from django.test import override_settings
from django.utils import timezone
from django.utils.timezone import datetime

from core.support import make_aware_when_is_naive
from mobileapi.tests.test_core import SincronizadorCoreTest
from prospectos.models import Prospecto
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.creador_de_contexto import CreadorDeContexto


@override_settings(API_MOBILE_LIMITE_LISTA_INICIAL_DE_PROSPECTOS=5, API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
class ListaInicialDeProspectosTest(SincronizadorCoreTest):
    def setUp(self):
        super(ListaInicialDeProspectosTest, self).setUp()
        self.vendedor = self.fixture['vend_1']
        self.creador_de_contexto = CreadorDeContexto(
            supervisor=self.vendedor.supervisor, fixture=self.fixture)
        self._quitar_prospectos_de(vendedor=self.vendedor)

    def test_lista_inicial_debe_responder_error_al_tener_finalizada_sesion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        self._invalidar_sesion_para(sesion, version_api='version_vieja')
        self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[self.vendedor], cantidad=5)
        response = self._get_lista_inicial(self.vendedor)
        self._assert_respuesta_version_invalida(response)
        self._assert_no_se_registro_actividad_para(self.vendedor)

    def test_lista_inicial_debe_reiniciar_colas_de_sincronizacion_para_un_vendedor(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        cantidad_de_nuevos = 5
        prospectos = self._agregar_prospectos_nuevos_y_registrar_sincronizacion(cantidad_de_nuevos)
        self.assertEqual(sesion.sincronizaciones_de_prospectos().count(), cantidad_de_nuevos)
        response = self._get_lista_inicial(self.vendedor)
        self._assert_respuesta_contiene(response, prospectos=prospectos[0:5])
        self.assertEqual(sesion.sincronizaciones_de_prospectos().count(), 0)

    @mock.patch('django.utils.timezone.now', return_value=make_aware_when_is_naive(datetime(day=1, month=5, year=2017)))
    def test_lista_inicial_debe_registrar_actividad(self, mock_now):
        self._iniciar_sesion_para(self.vendedor)
        prospectos = self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[self.vendedor], cantidad=5)
        response = self._get_lista_inicial(self.vendedor)
        self._assert_respuesta_contiene(response, prospectos=prospectos[0:5])
        self._assert_se_registro_actividad_para(self.vendedor, mock_now)

    def test_lista_inicial_para_vendedor_sin_prospectos_debe_ser_vacia(self):
        self._iniciar_sesion_para(self.vendedor)
        response = self._get_lista_inicial(self.vendedor)
        self._assert_lista_de_prospectos_vacia(response=response)

    def test_prospectos_nuevos_superior_al_limite_debe_responder_nuevos_mas_recientes(self):
        self._iniciar_sesion_para(self.vendedor)
        cantidad_de_nuevos = 10
        cantidad_de_nuevos_mas_recientes = 5
        self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[self.vendedor], cantidad=cantidad_de_nuevos,
                                                                fecha=timezone.now() - timezone.timedelta(hours=3))
        prospectos_mas_recientes = self.creador_de_contexto.asignar_prospectos_nuevos_para(
            vendedores=[self.vendedor], cantidad=cantidad_de_nuevos_mas_recientes, fecha=timezone.now())
        response = self._get_lista_inicial(self.vendedor)
        self._assert_respuesta_contiene(response, prospectos=prospectos_mas_recientes)

    def test_prospectos_nuevos_insuficientes_debe_priorizar_aquellos_con_proximos_llamados(self):
        """
            El vendedor tiene 3 nuevos, 1 con llamado en 5 min, otro en 10 y otro en 15.
            La lista inicial responde los prospectos nuevos y aquellos con llamados en 5 y 10 min.
        """
        self._iniciar_sesion_para(self.vendedor)
        prospectos = self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[self.vendedor], cantidad=6)
        self._agregar_llamada_programada(prospectos[3], delta_minutos=5)
        self._agregar_llamada_programada(prospectos[4], delta_minutos=10)
        self._agregar_llamada_programada(prospectos[5], delta_minutos=15)

        response = self._get_lista_inicial(self.vendedor)
        self._assert_respuesta_contiene(response, prospectos=prospectos[0:5])

    def test_prospectos_nuevos_insuficientes_no_debe_priorizar_llamados_vencidos(self):
        """
            El vendedor tiene 3 nuevos, 1 con llamado en vencido, otro en 10miny otro en 15min.
            La lista inicial responde los prospectos nuevos y aquellos con llamados en 10 y 15min.
        """
        self._iniciar_sesion_para(self.vendedor)
        prospectos = self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[self.vendedor], cantidad=6)
        self._agregar_llamada_programada(prospectos[3], delta_minutos=-5)
        self._agregar_llamada_programada(prospectos[4], delta_minutos=10)
        self._agregar_llamada_programada(prospectos[5], delta_minutos=15)

        response = self._get_lista_inicial(self.vendedor)
        self._assert_respuesta_contiene(response, prospectos=prospectos[0:3]+prospectos[4:6])

    def test_prospectos_nuevos_y_con_proximos_llamados_insuficientes_debe_priorizar_aquellos_prospectos_recientes(self):
        """
            El vendedor tiene 2 nuevos, 1 con llamado en 5min y otro con llamado vencido.
            La lista inicial responde los prospectos nuevos y el prospecto con llamado en 5min y
            aquellos prospectos en proceso mas recientes incluso aquel con llamado vencido.
        """
        self._iniciar_sesion_para(self.vendedor)
        fecha = make_aware_when_is_naive(timezone.now())
        prospectos = self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[self.vendedor], cantidad=3)
        self._agregar_llamada_programada(prospectos[2], delta_minutos=5)
        prospecto_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            self.vendedor, fecha=fecha - timezone.timedelta(minutes=5))
        prospecto_dos = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            self.vendedor, fecha=fecha - timezone.timedelta(minutes=15))
        prospecto_tres = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            self.vendedor, fecha=fecha - timezone.timedelta(minutes=25))
        self._agregar_comentario(prospecto_uno)
        self._agregar_comentario(prospecto_dos)
        self._agregar_llamada_programada(prospecto_dos, delta_minutos=-15)
        self._agregar_comentario(prospecto_tres)

        response = self._get_lista_inicial(self.vendedor)
        self._assert_respuesta_contiene(response, prospectos=prospectos+[prospecto_uno, prospecto_dos])

    def _get_lista_inicial(self, vendedor):
        token_key = self._token_key_de(vendedor.user)
        lista_inicial_url = reverse('initial-prospects-list')
        response = self.client.get(path=lista_inicial_url, HTTP_AUTHORIZATION='Token %s' % token_key)
        return response

    def _agregar_comentario(self, prospecto):
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor)
        gestor.comentar_prospecto(prospecto=prospecto, fecha=timezone.now(), texto='Hola', es_automatico=False)

    def _agregar_prospectos_nuevos_y_registrar_sincronizacion(self, cantidad_de_nuevos):
        """
            Asigna prospectos y actualiza las sincronizaciones ya que el creador de contexto no lo hace.
        """
        prospectos = self.creador_de_contexto.asignar_prospectos_nuevos_para(
            vendedores=[self.vendedor], cantidad=cantidad_de_nuevos)
        for prospecto in prospectos:
            self.sincronizador.agregar_sincronizacion_para_prospectos(self.vendedor, prospecto)
        return prospectos

    def _quitar_prospectos_de(self, vendedor):
        repartidor = RepartidorDeProspectos.nuevo()
        repartidor.quitar_asignacion_a_prospectos(prospectos=vendedor.todos_los_prospectos(), debe_sincronizar=False)

    def _obtener_prospectos_desde(self, prospectos_json):
        ids = [prospecto_data['id'] for prospecto_data in prospectos_json]
        prospectos = Prospecto.objects.filter(id__in=ids)
        return prospectos

    def _assert_lista_de_prospectos_vacia(self, response):
        self.assertTrue(response.data['prospects'] == [])

    def _assert_respuesta_contiene(self, response, prospectos):
        prospectos_devueltos = self._obtener_prospectos_desde(prospectos_json=response.data['prospects'])
        self.assertEqual(set(prospectos_devueltos), set(prospectos))
