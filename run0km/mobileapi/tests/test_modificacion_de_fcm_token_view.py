import mock
from django.urls import reverse
from django.test import override_settings
from django.utils.timezone import datetime
from rest_framework import status

from core.support import make_aware_when_is_naive
from mobileapi.tests.test_core import SincronizadorCoreTest


@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
class ModificarTokenFCMViewTest(SincronizadorCoreTest):
    def setUp(self):
        super(ModificarTokenFCMViewTest, self).setUp()
        self.vendedor = self.fixture['vend_1']

    def test_cambiar_token_fcm_debe_responder_error_al_tener_finalizada_sesion(self):
        fcm_token = 'xxxx'
        sesion = self._iniciar_sesion_para(self.vendedor, fcm_token=fcm_token)
        self._invalidar_sesion_para(sesion, version_api='version_vieja')
        response = self._post_cambiar_token_fcm(self.vendedor, fcm_token='1234')
        self._assert_respuesta_version_invalida(response)
        self._assert_sesion_con_fcm_token(fcm_token, sesion)
        self._assert_no_se_registro_actividad_para(self.vendedor)

    def test_cambiar_token_fcm_a_usuario_inhabilitado_debe_responder_error(self):
        fcm_token = 'xxxx'
        sesion = self._iniciar_sesion_para(self.vendedor, fcm_token=fcm_token)
        self._deshabilitar_vendedor(vendedor=self.vendedor)
        response = self._post_cambiar_token_fcm(vendedor=self.vendedor, fcm_token='1234')
        self._assert_respuesta_no_autorizado(response)
        self._assert_sesion_con_fcm_token(fcm_token, sesion)
        self._assert_no_se_registro_actividad_para(self.vendedor)

    def test_cambiar_token_con_valor_vacio_debe_responder_error(self):
        fcm_token = 'xxxx'
        sesion = self._iniciar_sesion_para(self.vendedor, fcm_token=fcm_token)
        response = self._post_cambiar_token_fcm(vendedor=self.vendedor, fcm_token='')
        self._assert_respuesta_fcm_token_requerido(response)
        self._assert_sesion_con_fcm_token(fcm_token, sesion)
        self._assert_no_se_registro_actividad_para(self.vendedor)

    @mock.patch('django.utils.timezone.now', return_value=make_aware_when_is_naive(datetime(day=1, month=5, year=2017)))
    def test_cambiar_token_fcm_debe_modificar_token_de_la_sesion(self, mock_now):
        sesion = self._iniciar_sesion_para(self.vendedor, fcm_token='xxxx')
        nuevo_token = 'nuevo'
        response = self._post_cambiar_token_fcm(vendedor=self.vendedor, fcm_token=nuevo_token)
        self.assert_response_status_code(response, status.HTTP_200_OK)
        self._assert_sesion_con_fcm_token(nuevo_token, sesion)
        self._assert_se_registro_actividad_para(self.vendedor, mock_now)

    def _post_cambiar_token_fcm(self, vendedor, fcm_token, version_api=None):
        version_api = version_api or self.version_api
        token_key = self._token_key_de(vendedor.user)
        url = reverse('cambiar-fcm-token')
        data = {'version_api': version_api, 'fcm_token': fcm_token}
        response = self.client.post(path=url, HTTP_AUTHORIZATION='Token %s' % token_key, data=data)
        return response

    def _assert_sesion_con_fcm_token(self, fcm_token, sesion):
        sesion.refresh_from_db()
        self.assertEqual(sesion.fcm_token(), fcm_token)
