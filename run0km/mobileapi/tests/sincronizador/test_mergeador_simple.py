# coding=utf-8
import mock
from django.test import override_settings

from mobileapi.comandos import Comando
from mobileapi.mergeador import MergeadorDeConflictosSimple
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable
from mobileapi.tests.creador_de_comandos import CreadorDeComandosMobile
from mobileapi.tests.test_core import SincronizadorCoreTest
from testing.test_utils import reload_model


@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
class MergeadorDeConflictosSimpleTest(SincronizadorCoreTest):
    def setUp(self):
        super(MergeadorDeConflictosSimpleTest, self).setUp()
        self.mergeador = MergeadorDeConflictosSimple.nuevo()
        self.creador_de_comandos = CreadorDeComandosMobile.nuevo()
        self.prospecto = self.fixture['p_1']
        self.vendedor = self.prospecto.vendedor
        self.tipo_sincronizable = TipoProspectoSincronizable()

    def test_merge_prospecto_sin_cambios_locales(self):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self.assertEqual(self.prospecto.comentarios.count(), 0)
        fecha_inicio_string = '2017-01-23-11:51:35'
        duracion = 345
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(
            modelo=self.prospecto,
            comandos=[self._comando_agregar_llamado_realizado(fecha_inicio_string, duracion)],
            modelo_fue_modificado=modelo_fue_modificado)
        self._assert_prospecto_tiene_llamada_realizada(
            self.prospecto, duracion=duracion, fecha_inicio_string=fecha_inicio_string)

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_merge_con_cambios_locales_debe_ignorar_comandos(self, pusher_mock):
        sesion = self._iniciar_sesion_para(vendedor=self.vendedor)
        self.assertEqual(self.prospecto.comentarios.count(), 0)
        self.sincronizador.prospecto_modificado(self.prospecto)
        sesion = reload_model(sesion)
        self.assertEqual(sesion.sincronizaciones_de_prospectos().count(), 1)
        modelo_fue_modificado = sesion.modelo_fue_modificado(id_model=self.prospecto.id,
                                                             tipo_sincronizable=self.tipo_sincronizable)
        self.mergeador.resolver_conflictos(modelo=self.prospecto,
                                           comandos=[self._comando_agregar_llamado_realizado()],
                                           modelo_fue_modificado=modelo_fue_modificado)
        self.assertEqual(self.prospecto.llamados_realizados().count(), 0)

    def _comando_agregar_llamado_realizado(self, fecha_inicio_string='2017-01-23-11:51:35', duracion=345):
        comando_json = self.creador_de_comandos.comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_inicio_string, duracion=duracion)
        comando = Comando.nuevo_desde(comando_json, self.tipo_sincronizable)
        return comando
