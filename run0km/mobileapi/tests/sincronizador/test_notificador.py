import mock
from django.utils import timezone

from core.support import make_aware_when_is_naive
from mobileapi.notificador import NotificadorDeSincronizaciones
from mobileapi.tests.test_core import SincronizadorCoreTest


@mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
class NotificadorDeSincronizacionesTest(SincronizadorCoreTest):
    def setUp(self):
        super(NotificadorDeSincronizacionesTest, self).setUp()
        self._notificador = NotificadorDeSincronizaciones()
        self.vendedor = self.fixture['vend_1']

    def _en_horario_laboral(self):
        return make_aware_when_is_naive(timezone.datetime(
            day=10, month=8, year=2017, hour=12, minute=35))

    def test_notificar_debe_enviar_notificacion(self, pusher_trigger_mock):
        self._notificador.enviar_pedido_de_sincronizacion_de_prospectos_para(self.vendedor)
        self._assert_envio_de_comando_prospecto_modificado(
            pusher_trigger_mock, self.vendedor, verificar_call_once=True)

    # def test_notificar_teniendo_en_cuenta_horario_debe_enviar_notificacion_en_horario_laboral(
    #         self, pusher_trigger_mock):
    #     with freeze_time(self._en_horario_laboral()):
    #         self._notificador.enviar_pedido_de_sincronizacion_para(self.vendedor, tener_en_cuenta_horario=True)
    #         self._assert_envio_de_comando_prospecto_modificado(
    #             pusher_trigger_mock, self.vendedor, verificar_call_once=True)
    #
    # def test_notificar_teniendo_en_cuenta_horario_no_debe_enviar_notificacion_fuera_de_horario_laboral(
    #         self, pusher_trigger_mock):
    #     with freeze_time(self._fuera_de_horario_laboral()):
    #         self._notificador.enviar_pedido_de_sincronizacion_para(self.vendedor, tener_en_cuenta_horario=True)
    #         self._deny_envio_de_comando_prospecto_modificado(pusher_trigger_mock, self.vendedor)