from mobileapi.sincronizador import Sincronizador
from vendedores.gestor import GestorDeVendedores


class ContextoMobile(object):

    def __init__(self):
        self.sincronizador = Sincronizador()
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()
        self.version_api = '1.1-delivery-run-android-alpha'

    def habilitar_app_del_vendedor(self, vendedor):
        supervisor = vendedor.responsable()
        concesionaria = vendedor.obtener_concesionaria()
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=vendedor, app_habilitada=True)
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=supervisor, app_habilitada=True)
        config_de_servicios_de_la_concesionaria = concesionaria.configuracion_de_servicios()
        config_de_servicios_de_la_concesionaria.habilitar_app()

    def habilitar_whatsapp_del_vendedor(self, vendedor):
        config_de_servicios_del_vendedor = vendedor.configuracion_de_servicios()
        config_de_servicios_del_vendedor.habilitar_whatsapp()
        supervisor = vendedor.responsable()
        config_de_servicios_del_supervisor = supervisor.configuracion_de_servicios()
        config_de_servicios_del_supervisor.habilitar_whatsapp()
        concesionaria = vendedor.obtener_concesionaria()
        config_de_servicios_de_la_concesionaria = concesionaria.configuracion_de_servicios()
        config_de_servicios_de_la_concesionaria.habilitar_whatsapp()

    def iniciar_sesion_para(self, vendedor, version_api=None, fcm_token='1234'):
        self.habilitar_app_del_vendedor(vendedor=vendedor)
        version_api = version_api or self.version_api
        self.sincronizador.login(vendedor.user, version_api, fcm_token)
        sesion = self.sincronizador.sesion_de(vendedor)
        return sesion

    def deshabilitar_app_del_vendedor(self, vendedor):
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=vendedor, app_habilitada=False)

    def token_key_de(self, user):
        if hasattr(user, 'auth_token'):
            token_key = user.auth_token.key
        else:
            token_key = 'fruta'
        return token_key
