# coding=utf-8


import mock
from django.test import override_settings

from mobileapi.errors import NumeroDeSecuenciaInvalido, SincronizacionLockeada, SesionExpirada
from mobileapi.pedido_de_sincronizacion import PedidoDeSincronizacionDeLote
from mobileapi.tests.test_core import SincronizadorCoreTest
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable


@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
class SincronizacionTest(SincronizadorCoreTest):
    def setUp(self):
        super(SincronizacionTest, self).setUp()
        self.prospecto = self.fixture['p_1']
        self.vendedor = self.prospecto.vendedor
        self.tipo_sincronizable = TipoProspectoSincronizable()

    # Ante un rollback responder 402 (o similar)
    def test_sin_cambios_remotos_y_sin_cambios_locales_se_responde_vacio(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_respuesta_exitosa_sin_sincronizaciones(resultado, sync_sequence_number)
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=0,
                                           cantidad_rechazadas=0)

    def test_sin_cambios_remotos_y_con_cambios_locales_se_responden_las_modificaciones(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        self.sincronizador.agregar_sincronizacion_para_prospectos(self.vendedor, self.prospecto)
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_numero_de_secuencia_menor_al_esperado_debe_lanzar_excepcion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        self.tipo_sincronizable.incrementar_ultimo_numero_de_secuencia_exitoso_para(sesion)
        self.sincronizador.agregar_sincronizacion_para_prospectos(self.vendedor, self.prospecto)
        sync_sequence_number = self.tipo_sincronizable.ultimo_numero_de_secuencia_exitoso_para(sesion) - 1
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        self.assertRaises(NumeroDeSecuenciaInvalido,
                          self.sincronizador.sincronizar_lote,
                          pedido=pedido,
                          vendedor=self.vendedor)
        self._assert_sesion_con_pendientes(sesion, cantidad_pendientes=1)

    def test_pedido_con_numero_de_secuencia_mayor_al_esperado_debe_lanzar_excepcion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        self.tipo_sincronizable.incrementar_ultimo_numero_de_secuencia_exitoso_para(sesion)
        self.sincronizador.agregar_sincronizacion_para_prospectos(self.vendedor, self.prospecto)
        sync_sequence_number = self.tipo_sincronizable.ultimo_numero_de_secuencia_exitoso_para(sesion) + 2
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        self.assertRaises(NumeroDeSecuenciaInvalido,
                          self.sincronizador.sincronizar_lote,
                          pedido=pedido,
                          vendedor=self.vendedor)
        self._assert_sesion_con_pendientes(sesion, cantidad_pendientes=1)

    @override_settings(API_MOBILE_LIMITE_DE_MODIFICACIONES=2)
    def test_pedido_a_sesion_con_cantidad_de_modificaciones_mayor_al_limite_debe_responder_error_de_autenticacion(
            self):
        """
            Alcanzado el limite de modificaciones se expira la sesion, y con esto se fuerza la asignacion inicial
        """
        otro_prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor)
        sesion = self._iniciar_sesion_para(self.vendedor)
        self.tipo_sincronizable.incrementar_ultimo_numero_de_secuencia_exitoso_para(sesion)
        self.sincronizador.agregar_sincronizacion_para_prospectos(self.vendedor, self.prospecto)
        self.sincronizador.agregar_sincronizacion_para_prospectos(self.vendedor, otro_prospecto)

        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[],
                                                    tipo_sincronizable=self.tipo_sincronizable)

        self.assertRaises(SesionExpirada,
                          self.sincronizador.sincronizar_lote,
                          pedido=pedido,
                          vendedor=self.vendedor)
        self._assert_sesion_con_pendientes(sesion, cantidad_pendientes=2)

    @mock.patch('core.locker.mem_locker.Locker.do_locking')
    def test_pedido_loqueado_debe_tirar_excepcion(self, mock_locker):
        # Dado
        mock_locker.side_effect = self.do_locking_raise_resource_locked
        sesion = self._iniciar_sesion_para(self.vendedor)
        self.sincronizador.agregar_sincronizacion_para_prospectos(self.vendedor, self.prospecto)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[],
                                                    tipo_sincronizable=self.tipo_sincronizable)

        # Cuando / Entonces
        self.assertRaises(SincronizacionLockeada,
                          self.sincronizador.sincronizar_lote,
                          pedido=pedido,
                          vendedor=self.vendedor)
        self._assert_sesion_con_pendientes(sesion, cantidad_pendientes=1)

    def test_pedido_con_comando_agregar_llamada_realizada_debe_realizar_operacion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        fecha_string = "2017-01-23-11:51:35"
        duracion = 345
        comando_agregar_llamada_realizada = self.creador_de_comandos.comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_string,
            duracion=duracion
        )
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_agregar_llamada_realizada])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_prospecto_tiene_llamada_realizada(self.prospecto, duracion, fecha_string)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_comando_finalizacion_debe_realizar_operacion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        comando_finalizacion = self.creador_de_comandos.comando_finalizacion_prospecto(
            descripcion_motivo='Usuario Pierde Interes',
            texto_otro_motivo='Me quiso robar mi chupetin',
            comentario='Tambien me robo una pantufla')
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_finalizacion])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_prospecto_tiene_nueva_finalizacion(prospecto=self.prospecto,
                                                        vendedor=self.vendedor,
                                                        otro_motivo='',
                                                        descripcion_de_motivo='Usuario Pierde Interes',
                                                        comentario='Tambien me robo una pantufla')
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_comando_finalizacion_con_otro_motivo_debe_realizar_operacion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        comando_finalizacion = self.creador_de_comandos.comando_finalizacion_prospecto(
            descripcion_motivo='blablaba',
            texto_otro_motivo='Me quiso robar mi chupetin',
            comentario='Tambien me robo una pantufla')
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_finalizacion])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_prospecto_tiene_nueva_finalizacion(prospecto=self.prospecto,
                                                        vendedor=self.vendedor,
                                                        otro_motivo='Me quiso robar mi chupetin',
                                                        descripcion_de_motivo=None,
                                                        comentario='Tambien me robo una pantufla',
                                                        es_otro_motivo=True)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_comando_finalizacion_con_otro_motivo_sin_descripcion_debe_indicar_error(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        comando_finalizacion = self.creador_de_comandos.comando_finalizacion_prospecto(
            descripcion_motivo='blablaba',
            texto_otro_motivo='',
            comentario='Tambien me robo una pantufla')
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_finalizacion])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self.assertFalse(self.prospecto.finalizado)
        mensaje_de_error = str(['"blablaba" no es un motivo de finalización válido'])
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[], modelos_modificados_fallidos={
                self.prospecto.id: mensaje_de_error}, modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=0,
                                           cantidad_rechazadas=1)
        self._assert_sincronizacion_fallida_para_prospecto(
            sesion, sync_sequence_number, self.prospecto.id, mensaje_de_error)

    def test_pedido_con_comando_reactivar_seguimiento_debe_realizar_operacion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        self._finalizar_seguimiento(self.prospecto, self.vendedor)
        comando = self.creador_de_comandos.comando_reactivar_seguimiento()
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_prospecto_reactivado(self.prospecto)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_comando_reactivar_seguimiento_no_falla_al_intentar_reactivar_un_prospecto_nuevo(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        comando = self.creador_de_comandos.comando_reactivar_seguimiento()
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_prospecto_reactivado(self.prospecto)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_agregar_formulario_de_llamada_debe_realizar_operacion(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        duracion = 345
        fecha_inicio_string = "2017-01-23-11:51:35"
        self._agregar_llamada_realizada_a(self.prospecto, self.vendedor, duracion, fecha_inicio_string)
        pregunta = "¿Hubo interés?"
        respuesta = "true"
        comando_agregar_formulario = self.creador_de_comandos.comando_agregar_formulario_de_llamado(
            pregunta, respuesta, fecha_inicio_string, duracion)

        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_agregar_formulario])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_llamada_realizada_tiene_formulario_de_interes(self.prospecto, pregunta, respuesta)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_comando_agregar_formulario_de_llamada_crea_la_llamada_al_no_existir(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        duracion = 345
        fecha_inicio_string = "2017-01-23-11:51:35"
        pregunta = "¿Hubo interés?"
        respuesta = "true"
        comando_agregar_formulario = self.creador_de_comandos.comando_agregar_formulario_de_llamado(
            pregunta, respuesta, fecha_inicio_string, duracion)

        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_agregar_formulario])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)

        self._assert_prospecto_tiene_llamada_realizada(self.prospecto, duracion, fecha_inicio_string)
        self._assert_llamada_realizada_tiene_formulario_de_interes(self.prospecto, pregunta, respuesta)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_modificados_fallidos={}, modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        self._assert_sincronizacion_exitosa_para_prospecto(sesion, sync_sequence_number, self.prospecto)

    def test_pedido_con_comando_agregar_llamada_realizada_ya_existente_responde_exitosamente_la_existente(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        duracion = 345
        fecha_string = "2017-01-23-11:51:35"
        self._agregar_llamada_realizada_a(self.prospecto, self.vendedor, duracion, fecha_string)

        comando_agregar_llamada_realizada = self.creador_de_comandos.comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_string,
            duracion=duracion
        )
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_agregar_llamada_realizada])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[],
                                                            modelos_modificados_exitosos=[self.prospecto],
                                                            modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=1,
                                           cantidad_rechazadas=0)
        # mensaje_de_error = 'UNIQUE constraint failed: prospectos_llamadarealizada.prospecto_id, ' \
        #                    'prospectos_llamadarealizada.fecha_comienzo, prospectos_llamadarealizada.duracion'
        # self._assert_resultado_de_sincronizacion_de_modelos(
        #     resultado=resultado,
        #     prospectos_modificados_fallidos={self.prospecto.id: mensaje_de_error},
        #     prospectos_asignados=[],
        #     numero_de_sinc=sync_sequence_number)
        # self._assert_sesion_sin_pendientes(sesion, sync_sequence_number, cantidad_exitosas=0, cantidad_rechazadas=1)

    def test_pedido_con_comando_agregar_llamada_realizada_con_formato_invalido_responde_sincronizacion_fallida(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        duracion = 345
        fecha_string = "2017-02-29-11:51:35"

        comando_agregar_llamada_realizada = self.creador_de_comandos.comando_agregar_llamada_realizada(
            fecha_inicio_string=fecha_string,
            duracion=duracion
        )
        cambio = self._cambios_json_para_prospecto(self.prospecto, comandos_json=[comando_agregar_llamada_realizada])
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[cambio],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)
        mensaje_de_error = self._mensaje_de_error_de_validacion_de_la_fecha_de_inicio_de_llamada_para(fecha_string)
        self._assert_resultado_de_sincronizacion_de_modelos(
            resultado=resultado, numero_de_sinc=sync_sequence_number,
            tipo_sincronizable=self.tipo_sincronizable,
            modelos_asignados=[], modelos_modificados_fallidos={self.prospecto.id: mensaje_de_error})
        self._assert_sesion_sin_pendientes(
            sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=0, cantidad_rechazadas=1)
        self._assert_sincronizacion_fallida_para_prospecto(
            sesion, sync_sequence_number, self.prospecto.id, mensaje_de_error)

    def test_reiteracion_de_pedido_fallido_vuelve_a_responder_el_mismo_resultado(self):
        sesion = self._iniciar_sesion_para(self.vendedor)
        mensaje_de_error = 'UNIQUE constraint failed: prospectos_llamadarealizada.prospecto_id, ' \
                           'prospectos_llamadarealizada.fecha_comienzo, prospectos_llamadarealizada.duracion'
        sesion.agregar_sincronizacion_fallida_para(id_model=self.prospecto.id,
                                                   tipo_sincronizable=self.tipo_sincronizable,
                                                   mensaje=mensaje_de_error)
        sesion.incrementar_ultimo_numero_de_secuencia_de_prospectos_exitoso()
        sync_sequence_number = sesion.ultimo_numero_de_secuencia_de_prospectos_exitoso()
        pedido = PedidoDeSincronizacionDeLote.nuevo(sync_sequence_number, cambios_json=[],
                                                    tipo_sincronizable=self.tipo_sincronizable)
        resultado = self.sincronizador.sincronizar_lote(pedido, vendedor=self.vendedor)

        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[], modelos_modificados_fallidos={
                self.prospecto.id: mensaje_de_error}, modelos_removidos=[])
        self._assert_sesion_sin_pendientes(sesion, self.tipo_sincronizable, sync_sequence_number, cantidad_exitosas=0,
                                           cantidad_rechazadas=1)
        self._assert_sincronizacion_fallida_para_prospecto(
            sesion, sync_sequence_number, self.prospecto.id, mensaje_de_error)

    def _assert_respuesta_exitosa_sin_sincronizaciones(self, resultado, sync_sequence_number):
        self._assert_resultado_de_sincronizacion_de_modelos(resultado=resultado, numero_de_sinc=sync_sequence_number,
                                                            tipo_sincronizable=self.tipo_sincronizable,
                                                            modelos_asignados=[], modelos_modificados_exitosos=[])

    def _assert_sesion_con_pendientes(self, sesion, cantidad_pendientes):
        sesion.refresh_from_db()
        sync_sequence_number = self.tipo_sincronizable.numero_de_secuencia_actual_para(sesion)
        self._assert_sesion(sesion,
                            self.tipo_sincronizable,
                            sync_sequence_number=sync_sequence_number,
                            sync_sequence_number_actual_esperado=sync_sequence_number,
                            cantidad_exitosas=0,
                            cantidad_rechazadas=0,
                            cantidad_pendientes=cantidad_pendientes)

    def _cambios_json_para_prospecto(self, prospecto, comandos_json):
        return {'id': prospecto.id, 'actions': comandos_json}

    def _mensaje_de_error_de_validacion_de_la_fecha_de_inicio_de_llamada_para(self, fecha_como_string):
        return (f"'{fecha_como_string}' is not a 'date-time'\n\n"
                "Failed validating 'format' in schema['properties']['call']['properties']['start_time']:\n    "
                "{'format': 'date-time', 'type': 'string'}\n\n"
                "On instance['call']['start_time']:\n"
                f"    '{fecha_como_string}'")


"""
    "'2017-02-29-11:51:35' is not a 'date-time'

Failed validating 'format' in schema['properties']['call']['properties']['start_time']:
{'format': 'date-time', 'type': 'string'}

On instance['call']['start_time']:
'2017-02-29-11:51:35'"

'2017-02-29-11:51:35' is not a 'date-time'\n\nFailed validating 'format' in schema['properties']['call']['properties']['start_time']:\n    {'format': 'date-time', 'type': 'string'}\n\nOn instance['call']['start_time']:\n    '2017-02-29-11:51:35'", 'id': 1} 
'2017-02-29-11:51:35' is not a 'date-time'\n\nFailed validating 'format' in schema['properties']['call']['properties']['start_time']:\n    {'format': 'date-time', 'type': 'string'}\n\nOn instance['call']['start_time']:\n    '2017-02-29-11:51:35'
"""