import mock
from django.test import override_settings

from mobileapi.models import SesionAppMobile
from mobileapi.sincronizador import Sincronizador
from mobileapi.tests.test_core import SincronizadorCoreTest
from mobileapi.tipo_sincronizable import TipoProspectoSincronizable


@override_settings(API_MOBILE_VERSION='1.1-delivery-run-android-alpha')
class SesionTest(SincronizadorCoreTest):
    def setUp(self):
        super(SesionTest, self).setUp()
        self.sincronizador = Sincronizador()
        self.prospecto = self.fixture['p_1']
        self.supervisor = self.fixture['sup_1']
        self.vendedor = self.prospecto.vendedor
        self.version_api = '1.1-delivery-run-android-alpha'
        self.sesion = SesionAppMobile.obtener_o_crear(
            vendedor=self.vendedor, version_api=self.version_api)
        self.tipo_sincronizable = TipoProspectoSincronizable()

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_al_modificar_un_prospecto_este_es_agregado_a_la_sesion_como_sincronizacion(self, pusher_mock):
        self.habilitar_app_del_vendedor(self.vendedor)
        self.sincronizador.prospecto_modificado(self.prospecto)
        sesion_actualizada = self.sincronizador.sesion_de(vendedor=self.vendedor)
        self._assert_sesion_con(sesion_actualizada, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[], modelos_modificados=[self.prospecto])

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_modificar_el_prospecto_mas_de_una_vez_solo_genera_una_sincronizacion(self, pusher_mock):
        self.habilitar_app_del_vendedor(self.vendedor)
        self.sincronizador.prospecto_modificado(self.prospecto)
        self.sincronizador.prospecto_modificado(self.prospecto)
        self.sincronizador.prospecto_modificado(self.prospecto)
        sesion_actualizada = self.sincronizador.sesion_de(vendedor=self.vendedor)
        self._assert_sesion_con(sesion_actualizada, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[], modelos_modificados=[self.prospecto])

    @mock.patch('pusher.Pusher.trigger', side_effect=lambda channels, event_name, data: None)
    def test_al_desasignar_prospecto_del_vendedor_se_remueven_todas_sus_sincronizaciones_y_se_mantiene_la_remocion(
            self, pusher_mock):
        self.habilitar_app_del_vendedor(self.vendedor)
        self.sincronizador.prospecto_modificado(self.prospecto)
        sesion_actualizada = self.sincronizador.sesion_de(vendedor=self.vendedor)
        self._assert_sesion_con(sesion_actualizada, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[], modelos_modificados=[self.prospecto])
        self.sincronizador.vendedor_removido_de(prospecto=self.prospecto, vendedor=self.vendedor)
        sesion_actualizada = self.sincronizador.sesion_de(vendedor=self.vendedor)
        self._assert_sesion_con(sesion_actualizada, self.tipo_sincronizable, modelos_asignados=[],
                                ids_modelos_removidos=[self.prospecto.id], modelos_modificados=[])

