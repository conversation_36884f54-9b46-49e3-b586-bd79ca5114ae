import logging

from mobileapi import tasks

logger = logging.getLogger(__name__)


class NotificadorDeSincronizaciones(object):
    TIPO_PROSPECTOS = 'PROSPECTOS'
    TIPO_CONVERSACIONES = 'CONVERSACIONES'

    READY_SYNC_FOR = {
        TIPO_PROSPECTOS: 'ready_sync_prospects',
        TIPO_CONVERSACIONES: 'ready_sync_conversations',
    }
    CHANNEL_NAME_TEMPLATE = 'vendedor-%s'

    def nombre_de_canal_para(self, vendedor):
        return self.CHANNEL_NAME_TEMPLATE % vendedor.pk

    def enviar_pedido_de_sincronizacion_de_prospectos_para(self, vendedor):
        self._enviar_pedido_de_sincronizacion_para(self.TIPO_PROSPECTOS, vendedor)

    def enviar_pedido_de_sincronizacion_de_conversaciones_para(self, vendedor):
        self._enviar_pedido_de_sincronizacion_para(self.TIPO_CONVERSACIONES, vendedor)

    def _enviar_pedido_de_sincronizacion_para(self, tipo, vendedor):
        # logger.debug('Notificar modificacion a: %s' % vendedor.pk)
        notificacion = self._notificacion_para_pedido_de_sincronizacion_de_tipo(tipo)
        self._enviar(notificacion, vendedor)

    def _notificacion_para_pedido_de_sincronizacion_de_tipo(self, tipo):
        return {
            'operation': self.READY_SYNC_FOR[tipo],
            'arguments': {}
        }

    def _enviar(self, notificacion, vendedor):
        operacion = notificacion['operation']
        argumentos = notificacion['arguments']
        channel_name = self.nombre_de_canal_para(vendedor)
        tasks.enviar_notificacion_push.delay(
            vendedor_pk=vendedor.pk,
            channel_name=channel_name,
            operacion=operacion,
            argumentos=argumentos)
