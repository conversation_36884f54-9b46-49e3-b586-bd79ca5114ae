
select {
    /*PARCHE, arreglar el CSS general*/
    height: auto;
}
.switch-wrapper {
    display: inline-flex;
}
.panel-inverse>.panel-heading {
   background: #0044b2;
}
.panel-title {
    font-size: 16px;
}
.panel-title a {
    text-decoration: none;
}
.panel-danger>.panel-heading, .panel-info>.panel-heading, .panel-inverse>.panel-heading, .panel-primary>.panel-heading, .panel-success>.panel-heading, .panel-warning>.panel-heading {
    color: #fff;
}
.panel-heading-btn {
    float: right;
}
.panel-heading-btn p {
    display: inline;
    color: antiquewhite;
}

.reportes .titulo-seccion {
    margin-top: 30px;
    background-color: #0076BC;
    box-shadow: inset 0 -2px 10px -3px #000;
    padding: 5px;
}

.reportes .titulo-seccion h3 {
    display: block;
    font-size: 18px;
    line-height: 32px;
    margin-top: 3px;
    text-align: center;
    margin-bottom: 3px;
    color: #FFF;
    text-shadow: 1px 1px 1px #3A7C79;
}