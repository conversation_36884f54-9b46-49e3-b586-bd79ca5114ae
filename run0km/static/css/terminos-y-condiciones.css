/****************************
**  Style for all devices  **
****************************/

/*
General
*/

body {
    font-family: sans-serif;
}

section {
    border-bottom: solid thin rgb(220, 220, 220);
}

/*
Page header
*/

header {
    margin: -0.5em -0.5em 0 -0.5em;
    text-align: center;
}

/*
Section headings
 */

h1, h2, h3, h4, h5, h6, h7 {
    font-family: serif;
    font-weight: normal;
    text-align: justify;
}

h1 {
    text-align: center;
}

/*
Navigation
*/

nav a {
    text-decoration: none;
    color: black;
}

nav a:hover {
    color: #b91e27;
}

nav h1 {
    font-family: serif;
    margin: 0 0 0.5em 0;
}

/*
General content elements and styles
*/

ul, ol {
    color: #b91e27;
    font-weight: bold;
    font-family: serif;
    font-size: 1.2em;
}

ul {
    list-style: square outside;
}

ol span {
    color: black;
    font-weight: normal;
    font-family: sans-serif;
    font-size: 0.84em;
}

ul span {
    color: black;
    font-weight: normal;
    font-family: sans-serif;
    font-size: 0.84em;
}

blockquote {
    border-left: solid thick #b91e27;
}

em {
    font-style: italic;
}

strong {
    font-weight: bold;
    color: #b91e27;
}

/* Nav to-top elements */

nav.to-top {
    display: block;
    padding: 0.5em 0.5em 0.5em 2.5em;
    margin: 0;
    width: auto;
    border-style: none;
    background-color: transparent;
    float: right;
}

nav.to-top a {
    margin: 0;
    border: 0;
    text-decoration: none;
    color: royalblue;
    font-size: 0.8em;
    font-weight: bold;
    font-family: sans-serif;
}

nav.to-top a:hover {
    text-decoration: underline;
}

nav ul {
    font-size: x-small;
}

/********************************************
**  Style for devices with small displays  **
********************************************/
@media (max-width: 46.9em) {

    /*
    General
    */
    section {
        margin: 0 -0.5em 1em -0.5em;
        padding: 0 0.5em 1em 0.5em;
    }

    /*
    Page header
    */
    header h1 {
        padding: 0.5em 0 0 0;
        font-size: 2.5em;
    }

    /*
    Section headings
     */
    h2, h3, h4, h5, h6, h7 {
        margin: 0.2em 0 0 1em;
        padding: 0.1em;
    }

    h1 {
        font-size: 1.5em;
        /*  margin: 0 0 1em 0;*/
    }

    h2 {
        font-size: 1.2em;
    }

    h3 {
        font-size: 1.13em;
    }

    h4 {
        font-size: 1.07em;
    }

    /*
    Navigation
    */
    /* Nav bar */
    nav {
        margin: 1em 10% 1em 10%;
        padding: 1em 0 0 0;
        width: 22em;
        border: solid thin rgb(240, 240, 240);
    }

    nav ul {
        margin: 0;
        list-style: square outside;
    }

    nav a {
        font-family: serif;
    }

    nav a:hover {
        font-weight: bold;
    }

}

/********************************************
**  Style for devices with large displays  **
********************************************/
@media (min-width: 47em) {

    /*
    General
    */
    section {
        padding: 1em 8% 2em 8%;
        margin-top: 1em;
        margin-bottom: 1em;
    }

    /*
    Section headings
     */
    h2, h3, h4, h5, h6 {
        padding: 0.2em;
        margin-top: 1em;
        color: #000;
        text-transform: none;
    }

    h1 {
        font-size: 1.5em;
        text-align: center;
        margin: 0 0 1em 0;
        color: #000;
        text-transform: none;
    }

    h2 {
        font-size: 1.2em;
    }

    h3 {
        font-size: 1.1em;
    }

    h4 {
        font-size: 1em;
    }

    /*
    Other content elements
    */
    blockquote {
        padding: 0 0 0 1.2em;
        margin: 0 6em 0 4em;
    }

    ul, ol {
        padding: 0 6em 0 6em;
    }

    /*
    Navigation
    */
    /* Nav bar */
    nav {
        margin: 0;
    }

    nav h1 {
        display: none; /* (display off-page?) */
    }

    nav ul {
        padding: 0;
        border: solid thin rgb(240, 240, 240);
        list-style: none outside;
        text-align: center;
    }

    nav li {
        margin: 1em;
        display: inline-block;
    }

    nav li ul {
        display: none;
    }

    nav a {
        font-size: 1.1em;
        font-weight: bold;
        font-family: sans-serif;
    }

}

/**************************
**  Style for printouts  **
**************************/
@media print {

    nav.to-top {
        display: none;
    }

}