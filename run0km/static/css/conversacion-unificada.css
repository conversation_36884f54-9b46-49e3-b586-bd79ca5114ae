body {
    margin: 0;
    padding: 0;
}

p, a, h1, h2, h3, h4, table, label, select, li {
    font-family: Arial, Helvetica, sans-serif;
    margin: 0;
}

.capaGris {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .8);
    color: #ffffff;
    z-index: 101;
}

.popup-conversacion-unificada {
    background-color: #20509A;
}

.contenedor-popup-conversacion {
    border-radius: 20px;
    margin: 0 auto;
    width: 600px;
    padding: 20px 30px 30px;
}

/* ----- windows chat header ----- */

.header-popup {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
}

button {
    border: none;
    background: none;
    cursor: pointer;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

button.close-chat {
    color: white;
    font-size: 30px;
    line-height: 20px;
    padding: 0;
    width: 20px;
}

button.close-chat:focus {
    outline: none;
}

.header-popup-content {
    align-items: flex-end;
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.user-status {
    display: flex;
    width: 65%;
}

a.profile-link {
    align-items: center;
    background-color: white;
    display: flex;
    height: 60px;
    justify-content: center;
    width: 60px;
}

a.profile-link img {
    height: 100%;
    width: auto;
}

.user-info {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding-left: 20px;
    width: 100%;
}

p.label-name {
    color: white;
    font-size: 14px;
}

p.name {
    color: white;
    font-size: 18px;
    line-height: initial;
}

.user-info .personal-data {
    align-items: flex-end;
    display: inline-flex;
    width: 100%;
}

.user-info .personal-data img {
    height: 24px;
    padding-left: 10px
}

form.filter {
    display: flex;
    justify-content: flex-end;
    margin: 0 0 5px 0;
    width: 35%;
}

form.filter input[type="search"] {
    border-radius: 5px 0 0 5px;
    border-style: none;
    font-size: 14px;
    height: 26px;
    padding: 5px;
}

form.filter input[type="image"] {
    background-color: white;
    border-radius: 0 5px 5px 0;
    height: 26px;
    overflow: hidden;
}

/* ----- windows chat body ----- */

.conversation-messages-panel {
    height: 380px;
    overflow-y: scroll;
    background-color: #0066bd;
    border-radius: 7px;
}

/* --------------------------- message ---------------------------------- */

.chat-divisor {
    background-color: #0C2F65;
    color: white;
    display: flex;
    font-size: 14px;
    justify-content: center;
    line-height: 22px;
    margin: 3px 0;
    text-align: center;
    width: 100%;
}

.chat-divisor:first-child {
    margin-top: 0;
}

.msj-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.-send {
    align-items: flex-end;
    padding: 5px 35px 5px 0;
}

.-received {
    align-items: flex-start;
    padding: 5px 0 5px 35px;
}

.msj-send {
    background-color: #CFEBF9;
    border-radius: 7px;
    margin-top: 10px;
    max-width: 300px;
    min-width: 80px;
    padding: 10px 15px 25px 15px;
    position: relative;
}

.msj-send:first-child {
    margin: 0;
}

.msj-send::after {
    bottom: 8px;
    content: url(../img/chat-unificado/saliente.png);
    position: absolute;
    right: -8px;
}

.msj-received {
    background-color: white;
    border-radius: 7px;
    margin-top: 10px;
    max-width: 300px;
    min-width: 100px;
    padding: 10px 15px 25px 15px;
    position: relative;
}

.msj-received:first-child {
    margin: 0;
}

.msj-received::after {
    bottom: 8px;
    content: url(../img/chat-unificado/entrante.png);
    position: absolute;
    left: -8px;
}

.msj-disparador {
    background-color: #fdecd5;
    border-radius: 7px;
    width: 100%;
    padding: 10px 15px 13px 15px;
    position: relative;
}

p.msj {
    font-size: 12px;
    color: #444;
    line-height: 17px;
    word-break: break-word;
}

p.msj-del-sistema{
    font-size: 12px;
    color: #444;
    line-height: 17px;
    word-break: break-word;
}

p.hora {
    color: #444;
    font-size: 12px;
    font-style: italic;
    line-height: 17px;
    position: absolute;
    right: 5px;
}

p.hora::after {
    content: url(../img/chat-unificado/tick-1.png);
}

.message-type {
    border-radius: 0 0 45px 45px;
    bottom: 0;
    display: flex;
    left: 0;
    overflow: hidden;
    position: absolute;
    width: 100%;
}

.message-type .sms {
    background-color: #da27f4;
    flex-grow: 4;
    height: 5px;
}

.message-type .mail {
    background-color: #e23530;
    flex-grow: 4;
    height: 5px;
}

.message-type .whatsapp {
    background-color: #2cb742;
    flex-grow: 4;
    height: 5px;
}

.message-type .eaviso {
    background-color: #2aa6f2;
    flex-grow: 4;
    height: 5px;
}

.message-type .deliveryChat {
    background-color: #268af7;
    flex-grow: 4;
    height: 5px;
}

/* ------------------------- text area ---------------------------------- */
.input-panel {
    margin: 15px 0 0 0;
}

.textarea-container {
    /* Para que haga de ancla de la imagen que hace de piquito de la caja de texto, un absolute que hay mas abajo */
    position: relative;
}

.textarea-container div.editable-div {
    /* Para que tenga margenes y el textarea no salga de la columna central */
    box-sizing: border-box;
    padding: 10px 15px;

    /* Para que ocupe todo el ancho de la columna central */
    display: block;
    width: 100%;

    border-radius: 7px;
    height: 70px;
    font-family: arial;
    font-size: 13px;
    resize: none;
    outline: none;
    opacity: 0.99;
}

.hint-respuestas-disponibles {
    text-align: right;
    color: white;
    font-size: 10px;
    margin-bottom: 3px;
}

.hint-respuestas-disponibles .hint-cantidad {
    font-size: 12px;
}

.editable-div {
    -moz-appearance: textfield-multiline;
    -webkit-appearance: textarea;
    border: 1px solid gray;
    font: medium -moz-fixed;
    height: 28px;
    overflow: auto;
    padding: 2px;
    /* resize: both; */
    width: 400px;
    background-color: white;
    color: black;
}

.editable-div.deshabilitado {
    background-color: darkgray;
}

.editable-div[contenteditable]:empty::before {
    content: attr(placeholder);
    color: gray;
}

.editable-div[contenteditable=false] {
    background: #dedfe0;
}

.textarea-container textarea:disabled {
    background: #dedfe0;
}

/*
  Conservamos esta definición porque el piquito, usado para señalar qué medio está seleccionado,
  es muy probable que vuelva
*/
.textarea-piquito {
    background-color: white;
    height: 20px;
    left: 30px;
    position: absolute;
    transform: rotate(45deg);
    top: 60px;
    width: 20px;
}

/* ----- windows chat footer ----- */

.conversation-actions-container {
    display: flex;
    justify-content: space-between;
}

.conversation-actions-container input[type="submit"] {
    background-color: #2cb742;
    border-radius: 3px;
    border: none;
    font-size: 14px;
    overflow: hidden;
    color: white;
    padding: 10px 20px;
}

.conversation-actions-container input[type="submit"]:disabled {
    background: #c2c3c4;
}

.conversation-media-choice-container {
    display: flex;
    justify-content: space-around;
    margin-left: 20px;
}

.conversation-media-action-container {
    margin-top: 20px;
    position: relative;
}

/* -------------------------- input checkbox ---------------------------- */
input[type=radio] {
    display: none;
}

input[type=radio] + label {
    margin-right: 10px;
}

input[type=radio] + label:last-child {
    margin-right: 0;
}

input[type=radio]:disabled + label.delivery-chat-checkbox {
    background-image: url(../img/chat-unificado/delivery-chat-disabled.png);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio] + label.delivery-chat-checkbox {
    background-image: url(../img/chat-unificado/delivery-chat-enabled.png);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:checked + label.delivery-chat-checkbox {
    background-image: url(../img/chat-unificado/delivery-chat-selected.png);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:disabled + label.whatsapp-checkbox {
    background-image: url(../img/chat-unificado/whatsapp-disabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio] + label.whatsapp-checkbox {
    background-image: url(../img/chat-unificado/whatsapp-enabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:checked + label.whatsapp-checkbox {
    background-image: url(../img/chat-unificado/whatsapp-selected.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:disabled + label.sms-checkbox {
    background-image: url(../img/chat-unificado/sms-disabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio] + label.sms-checkbox {
    background-image: url(../img/chat-unificado/sms-enabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:checked + label.sms-checkbox {
    background-image: url(../img/chat-unificado/sms-selected.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:disabled + label.mail-checkbox {
    background-image: url(../img/chat-unificado/mail-disabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio] + label.mail-checkbox {
    background-image: url(../img/chat-unificado/mail-enabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:checked + label.mail-checkbox {
    background-image: url(../img/chat-unificado/mail-selected.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:disabled + label.eaviso-checkbox {
    background-image: url(../img/chat-unificado/e-aviso-disabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio] + label.eaviso-checkbox {
    background-image: url(../img/chat-unificado/e-aviso-enabled.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

input[type=radio]:checked + label.eaviso-checkbox {
    background-image: url(../img/chat-unificado/e-aviso-selected.svg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 61px;
    width: 45px;
}

span.cssTextDivMessageError {
    color: #FF0000;
    text-decoration: line-through;
}