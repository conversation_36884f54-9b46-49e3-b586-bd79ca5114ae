.chatarea {
    position: fixed;
    bottom: -6px;
    right: 0;
    margin: 0;
    padding: 0;
    z-index: 1;
}

.chatarea .chat {
    padding: 0;
    width: 250px;
    display: inline-block;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
    -moz-border-radius-topleft: 5px;
    -moz-border-radius-topright: 5px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    overflow: hidden;
    margin: 0;
    -webkit-box-shadow: 0 1px 5px 0 rgba(50, 50, 50, 0.75);
    -moz-box-shadow: 0 1px 5px 0 rgba(50, 50, 50, 0.75);
    box-shadow: 0 1px 5px 0 rgba(50, 50, 50, 0.75);
    margin-right: 3px;
}
.chatarea .chat:last-child {
    margin: 0 30px 0 0;
}


.send-proposal-button {
    width: 250px;
    background-color: #008CBA;
    border: none;
    color: white;
    padding: 10px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    cursor: pointer;
}

.chatarea .chat .chathead {
    width: 250px;
    background-color: #1a1a1a;
    box-sizing: border-box;
    height: 42px;
    color: #ffffff;
    padding-top: 10px;
    padding-left: 13px;
    cursor: pointer;
}

.chatarea .chat .chathead.online.inactive {
    background-color: #868686;
    border-bottom: 3px solid #828282;
}

.chatarea .chat .chathead.online {
    background-color: #0000b3;
    border-bottom: 3px #53a93f solid;
}

.chathead button.toggle {
    float: right;
    border: 0;
    background-color: transparent;
    margin-right: 5px;
    color: #fff;
    font-weight: bold;
    font-size: 21px;
    line-height: 21px;
    outline: none;
}
.chathead button.toggle:hover {
    background-color: rgba(255, 255, 255, .2);
}
.chathead button.toggle.plegado {
    line-height: 5px;
    padding-top: 3px;
    padding-bottom: 16px;
}
.chatarea .chat .chathead.unread-message {
    background-color: #53a93f;
    -webkit-animation: title_widget_alert 1.5s 3;
    animation: title_widget_alert 1.5s 3;
    animation-name: title_widget_alert;
    animation-duration: 1.5s;
    animation-timing-function: initial;
    animation-delay: initial;
    animation-iteration-count: 3;
    animation-direction: initial;
    animation-fill-mode: initial;
    animation-play-state: initial;
}

@-webkit-keyframes title_widget_alert {
    0% {
        background-color: #53a93f
    }
    50% {
        background-color: #8cc474
    }
    100% {
        background-color: #53a93f
    }
}
@keyframes title_widget_alert {
    0% {
        background-color: #53a93f
    }
    50% {
        background-color: #8cc474
    }
    100% {
        background-color: #53a93f
    }
}

@-webkit-keyframes blink-animation {
    to {
        visibility: hidden;
    }
}

@keyframes blink-animation {
    to {
        visibility: hidden;
    }
}

div.chathead label {
    color: #fff;
    font-size: 14px;
    line-height: 22px;
    display: inline-block;
}
.chatarea .chat .chathead img {
    width: 20px;
    vertical-align: middle;
    padding-right: 10px;
}
.chatarea .chat .settings {
    width: 100%;
    min-height: 35px;
    padding-left: 10px;
    font-size: 12px;
    line-height: 34px;
    border-bottom: 1px solid #E0E0E0;
    background-color: #fff;
    position: relative;
    box-sizing: border-box;
}
.chatarea .chat .settings button.toggle-datos {
    float: right;
    border: 0;
    background-color: transparent;
    margin-top: 6px;
    margin-right: 10px;
    height: 21px;
    width: 22px;
    background-image: url('images/chats/iconos-de-estado-datos.jpg');
    background-position: center -1px;
    background-repeat: no-repeat;
}

.chatarea .chat .settings button.toggle-ayuda {
    float: right;
    border: 0;
    background-color: transparent;
    margin-top: 6px;
    margin-right: 10px;
    height: 22px;
    width: 22px;
    background-image: url('images/chats/ayuda.jpg');
    background-position: center -1px;
    background-repeat: no-repeat;
}

.chatarea .chat .settings span.falta-dato {
    color: red;
    vertical-align: -4px;
    font-size: 23px;
    font-weight: bold;
    padding: 3px;
}

.blink{
  animation: blink-animation 0.7s steps(5, start) infinite;
  -webkit-animation: blink-animation 0.7s steps(5, start) infinite;
}

.chatarea .chat .settings span.separador {
    padding: 3px;
}

.chatarea .chat .settings img {
    width: 15px;
    padding-top: 7px;
    padding-left: 10px;
}

.chatarea .chat .feed {
    padding: 10px;
    background-color: #f2f2f2;
    max-height: 300px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.chatarea .chat .feed img {
    width: 100%;
}

.chatarea .chat .feed .other, .me {
    position: relative;
    margin-top: 10px;

}

.chatarea .chat .feed .other {
    position: relative;
}

.chatarea .chat .feed .other .profile {
    position: absolute;
    top: 0;
    left: 0;
}

.chatarea .chat .feed .other .message {
    min-height: 30px;
    margin-left: 0;
    margin-right: 30px;
    font-size: 13px;
    border: 1px solid #E0E0E0;
    background-color: #ffffff;
    padding: 10px;
    color: #1a1a1a;
    border-radius: 4px;
    word-break: break-all;
}

.chatarea .chat .feed .me .profile {
    position: absolute;
    bottom: 0;
    right: 0;
}

.chatarea .chat .feed .me .message {
    min-height: 30px;
    max-width: 183px;
    margin-left: 30px;
    font-size: 13px;
    border: 1px solid #E0E0E0;
    background-color: #e0ecce;
    padding: 10px;
    color: #1a1a1a;
    border-radius: 4px;
    word-break: break-all;
}

.chatarea .chat .feed .me .message a {
    text-decoration: underline;
}

.chatarea .chat .feed .meta {
    padding-top: 5px;
    font-size: 12px;
    color: #808080;
}

.chatarea .chat .messagebox {
    width: 250px;
    border-top: 1px solid #e0e0e0;
    background-color: #ffffff;
    /*TODO: Eliminar esta altura cuando tengamos el botón de Propuestas*/
    height: 51px;

}

.chatarea .chat .disabled {
    background-color: #eee;
    cursor: not-allowed;
}

.chatarea .chat .messagebox textarea[disabled=disabled] {
    background-color: #eee;
    cursor: not-allowed;
}

.chatarea .chat .messagebox textarea[disabled=disabled] + .imgSubmit{
    cursor: not-allowed;
    background: #eee none;
    margin: 0;
}

.chatarea .chat .messagebox textarea {
    display: inline-block;
    box-sizing: border-box;
    height: 46px;
    border: 0;
    width: 198px;
    font-family: Arial, sans-serif;
    font-size: 13px;
    border-left: 1px solid #ddd;
    padding: 5px;
    -webkit-transition: all .5s ease;
    transition: all .5s ease;
    resize: none;
    outline: none;
}
.chatarea .chat .messagebox button.proposal-button.meta-hide{
    display: none;
}
.chatarea .chat .messagebox button.proposal-button {
    display: inline-block;
    border: 0;
    margin: 5px;
    width: 40px;
    height: 40px;
    background: no-repeat url(../img/botones/icono-enviar-propuestas.png) transparent;
    cursor: pointer;
    outline: none;
}

.chatarea .chat .messagebox .clearfix{
    clear: both;
}

.chatarea .chat .feed::-webkit-scrollbar {
    height: 14px;
    width: 10px;
    background: #eee;
    border-left: 1px solid #ddd;
}
.chatarea .chat .feed::-webkit-scrollbar-thumb {
    background: #ddd;
    border: 1px solid #cfcfcf;
}
.chatarea .chat .feed::-webkit-scrollbar-thumb:hover {
    background: #b2b2b2;
    border: 1px solid #b2b2b2;
}
.chatarea .chat .feed::-webkit-scrollbar-thumb:active {
    background: #b2b2b2;
    border: 1px solid #b2b2b2;
}


/* Propuestas */

.chatarea .proposals {
    display: none;
}

.chatarea .proposals .propuestas-de-chat{
    font-size: small;
}


/* Datos Cliente */
.chatarea .datosCliente {
    display: none;
    padding: 0;
    width: 250px;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
    -moz-border-radius-topleft: 5px;
    -moz-border-radius-topright: 5px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    margin: 0;
    -webkit-box-shadow: 0 1px 5px 0 rgba(50, 50, 50, 0.75);
    -moz-box-shadow: 0 1px 5px 0 rgba(50, 50, 50, 0.75);
    box-shadow: 0 1px 5px 0 rgba(50, 50, 50, 0.75);
}

.chatarea .datosCliente .infoCliente {
    background-color: #f2f2f2;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: auto;
}

.chatarea .datosCliente ul {
    display: block;
    margin: 0;
    background-color: #fff;
    padding: 20px 5px 10px 14px;
    width: max-content;
}
.chatarea .datosCliente ul li {
    font-size: 12px;
    line-height: 22px;
    color: #666;
    list-style: none;
}
.chatarea .datosCliente ul li span.dato {
    display: inline-block;
    height: 18px;
    vertical-align: top;
    line-height: 19px;
    padding-left: 3px;
}

.chatarea .datosCliente ul li span.dato-faltante {
    font-style: italic;
    color: #999;
}

.chatarea .datosCliente ul li span.dato input[type="text"] {
    display: inline-block;
    height: 18px;
    font-size: 12px;
    vertical-align: top;
    line-height: 19px;
    padding-left: 3px;
    border: 0;
    color: #666;
}
.chatarea .datosCliente ul li span.estado {
    display: inline-block;
    width: 24px;
    height: 23px;
    vertical-align: -7px;
    cursor: pointer;
}
.chatarea .datosCliente ul li span strong.falta {
    color: #eb0909;
    font-size: 15px;
}
.chatarea .datosCliente ul li span.estado.ver {
    background-image: url('images/chats/iconos-de-estado-datos.jpg');
    background-position: center -90px;
    background-repeat: no-repeat;
}
.chatarea .datosCliente ul li span.estado.nombre.ver {
    vertical-align: -8px;
    background-image: url('images/chats/iconos-de-estado-datos.jpg');
    background-position: center -60px;
    background-repeat: no-repeat;
}
.chatarea .datosCliente ul li span.estado.masDatos {
    width: 20px;
    background-image: url('images/chats/iconos-de-estado-datos.jpg');
    background-position: center -193px;
    background-repeat: no-repeat;
}
.chatarea .datosCliente ul li button.mas {
    margin-top: 30px;
    margin-bottom: 15px;
    border: 0;
    background-color: transparent;
    text-decoration: underline;
    color: #666;
}
.chatarea .datosCliente ul li button.save {
    line-height: 25px;
    margin-top: 3px;
    margin-bottom: 11px;
    border: 0;
    background-color: #eee;
    color: #666;
}
.datosHead {
    width: 250px;
    background-color: #00acde;
    box-sizing: border-box;
    height: 42px;
    color: #fff;
    padding-top: 10px;
    padding-left: 13px;
    border-radius: 5px 5px 0 0;
}
.datosHead button.toggle-chat {
    float: right;
    height: 28px;
    width: 28px;
    border: 0;
    background-color: transparent;
    margin-right: 19px;
    color: #fff;
    font-weight: bold;
    font-size: 21px;
    line-height: 21px;
    outline: none;
    background-image: url('images/chats/back-chat.jpg');
    background-position: center -2px;
}
.datosHead button.toggle-chat:hover {
    background-color: rgba(255, 255, 255, .2);
}
.datosHead button.toggle-chat.plegado {
    line-height: 5px;
    padding-top: 3px;
    padding-bottom: 16px;
}
.datosHead label {
    color: #fff;
    font-size: 14px;
    line-height: 22px;
    display: inline-block;
}

.chatarea .chat .chathead.meta{
    background-color: #5b9453;
}

