var PusherService = function (appKey, appCluster, channelName) {
    this.client = new Pusher(appKey, {
        cluster: appCluster
    });

    this.channel = this.client.subscribe(channelName);
};


PusherService.prototype.handle = function (eventName, withCallback, context) {
    this.channel.bind(eventName, withCallback, context)
};


deliveryRunEvents = {
    PROSPECTO_ASIGNADO: 'prospecto-asignado',
    CHAT_INICIADO: 'inicio-chat',
    COMPULSA_INICIADA: 'iniciar-compulsa',
    COMPULSA_CERRADA: 'cerrar-compulsa',
    CHAT_EXPIRADO: 'chat-expirado',
    MENSAJE_DE_CHAT: 'mensaje-chat',
    CHAT_FINALIZADO: 'finalizar-chat',
    ENTREGA_DE_PROSPECTO_PETICIONADO: 'entrega-de-prospecto-peticionado',
    ENTREGA_FALLIDA_DE_PROSPECTO_PETICIONADO: 'entrega-fallida-de-prospecto-peticionado'
};
