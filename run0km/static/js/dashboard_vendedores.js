//Requiere notificador.js, spinner.js

DashboardParaVendedorControlador = function (urlMetricas, urlListadoDeProspectos) {
    this._urlMetricas = urlMetricas;
    this._urlListadoDeProspectos = urlListadoDeProspectos;
    this._metricaProspectosEnRojo = "prospectosEnRojo";
    this._metricaLlamadosVencidos = "prospectosConLlamadosVencidos";
    this._metricaProspectosSinAgendar = "prospectosSinAgendar";
};

DashboardParaVendedorControlador.prototype.configurar = function () {
    this._configurarMetricaProspectosEnRojo();
    this._configurarMetricaProspectosLlamadosVencidos();
    this._configurarMetricaProspectosSinAgendar();
};

DashboardParaVendedorControlador.prototype.refresh = function() {
    this.obtenerMetricasPrincipales();
};

DashboardParaVendedorControlador.prototype._filtroPorMetrica = function(metrica){
    var filtros = {"prospectosEnRojo": {"filter_estado": "antiguo"}, "prospectosConLlamadosVencidos": {"filter_estado": "vencido"},
                   "prospectosSinAgendar": {"sin_agendar": "1"}};
    return filtros[metrica]
};

DashboardParaVendedorControlador.prototype._configurarMetricaProspectosEnRojo = function () {
    $('#metrica-prospectos-en-rojo-valor').parent().click((function () {
        this._irAListadoDeProspectosPara(this._metricaProspectosEnRojo);
    }).bind(this));
};

DashboardParaVendedorControlador.prototype._configurarMetricaProspectosLlamadosVencidos = function () {
    $('#metrica-prospectos-vencidos-valor').parent().click((function () {
        this._irAListadoDeProspectosPara(this._metricaLlamadosVencidos);
    }).bind(this));
};

DashboardParaVendedorControlador.prototype._configurarMetricaProspectosSinAgendar = function () {
    $('#metrica-prospectos-sin-agendar-valor').parent().click((function () {
        this._irAListadoDeProspectosPara(this._metricaProspectosSinAgendar);
    }).bind(this));
};

DashboardParaVendedorControlador.prototype._irAListadoDeProspectosPara = function (nombreDeMetrica) {
    var filtro = this._filtroPorMetrica(nombreDeMetrica);
    window.location.href = this._urlListadoDeProspectos + "?" + $.param(filtro);
};

DashboardParaVendedorControlador.prototype.obtenerMetricasPrincipales = function () {
    var spinner_options = {
        lines: 7, length: 7, width: 10, radius: 10, corners: 1, rotate: 0, direction: 1,
        color: '#00A', speed: 1, trail: 60, className: 'spinner',
        zIndex: 1,
        top: '0px',
        left: '0px'
    };
    var spinner = AgregarSpinner('spin-holder-filtrar-integrantes-equipo');
    var spinner_prospectos_en_rojo = AgregarSpinner('spinner_prospectos_en_rojo', spinner_options);
    var spinner_llamados_vencidos = AgregarSpinner('spinner_llamados_vencidos', spinner_options);
    var spinner_sin_agendar = AgregarSpinner('spinner_sin_agendar', spinner_options);
    var spinners = [spinner, spinner_prospectos_en_rojo, spinner_llamados_vencidos, spinner_sin_agendar];

    $('#metrica-prospectos-en-rojo-valor').text("");
    $('#metrica-prospectos-vencidos-valor').text("");
    $('#metrica-prospectos-sin-agendar-valor').text("");

    this._obtenerYDibujarDatosDelServidor(this._urlMetricas, function () {
        for (var i = 0; i < spinners.length; i++) {
            spinners[i].stop();
        }
    });
};

DashboardParaVendedorControlador.prototype._refrescarMetricasCon = function (datos) {
    $('#metrica-prospectos-en-rojo-valor').html(datos['cantidad_prospectos_en_rojo']);
    $('#metrica-prospectos-vencidos-valor').html(datos['cantidad_prospectos_vencidos']);
    $('#metrica-prospectos-sin-agendar-valor').html(datos['cantidad_prospectos_sin_agendar']);
};

DashboardParaVendedorControlador.prototype._obtenerYDibujarDatosDelServidor = function (url, successCallback) {
    this._getDatos(url, {}, (function (response) {
        this._refrescarMetricasCon(response.datos);
        successCallback();
    }).bind(this));
};

DashboardParaVendedorControlador.prototype._getDatos = function (url, queryData, successCallback) {
    var jqxhr = $.get(url, $.param(queryData, true));
    jqxhr.done((function (data) {
        if (data.status)
            successCallback(data);
        else
        // me quedo esta relacion de conocimiento indebida, pero deberia modelar un Fetcher...
            notificador.notificarError(data.mensaje);
    }).bind(this));
    jqxhr.fail((function () {
        systemUnavailable();
    }).bind(this));
};
