/**
 * Created by eryx on 11/08/17.
 *
 * Requiere reporte_distribucion_graficos.js
 * Requiere dataTables
 */


ReporteDeDistribucionControlador = function(fechaDesde, fechaHasta, marcas, dataSetDeGrilla){
    this._fechaDesde = fechaDesde;
    this._fechaHasta = fechaHasta;
    this._grilla = new GrillaDeProspectosRecibidos("grilla-de-entregas", dataSetDeGrilla);
    this._colorMapper = new ColorMappper(marcas);
    this._graficosDeEntregas = [];
    this._graficosSinAsignar = [];
};


ReporteDeDistribucionControlador.prototype.configurar = function() {
    this._configurarFiltros();
    this._configurarGrilla();
};

ReporteDeDistribucionControlador.prototype.agregarGraficoParaCategoria = function(categoriaId, nombre, datos) {
    var grafico = new GraficoDeEntregas(
        nombre,
        datos,
        this._fechaDesde,
        this._fechaHasta,
        "id-grafico-total-" + categoriaId,
        "id-grafico-por-marcas-" + categoriaId,
         "switcher-" + categoriaId,
        this._colorMapper
    );
    this._graficosDeEntregas.push(grafico);
    grafico.render();
};

ReporteDeDistribucionControlador.prototype.agregarGraficoSinAsignar = function(categoriaId, nombre, total, datos) {
    var grafico = new GraficoSinAsignar(
        nombre,
        total,
        datos,
        "id-grafico-sin-asignar-" + categoriaId,
        this._colorMapper
    );
    this._graficosSinAsignar.push(grafico);
    grafico.render();
};

ReporteDeDistribucionControlador.prototype._configurarFiltros = function(){
    // this._configurarFiltroProveedores();
    // $('#id_marcas').multiSelect();
};

ReporteDeDistribucionControlador.prototype._configurarFiltroProveedores = function(){
    $('#id_proveedores').multiSelect({
      selectableHeader: "<input type='text' class='search-input' autocomplete='off' placeholder='filtrar'>",
      selectionHeader: "<input type='text' class='search-input' autocomplete='off' placeholder='filtrar'>",
      afterInit: function(ms){
        var that = this,
            $selectableSearch = that.$selectableUl.prev(),
            $selectionSearch = that.$selectionUl.prev(),
            selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
            selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

        that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
        .on('keydown', function(e){
          if (e.which === 40){
            that.$selectableUl.focus();
            return false;
          }
        });

        that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
        .on('keydown', function(e){
          if (e.which == 40){
            that.$selectionUl.focus();
            return false;
          }
        });
      },
      afterSelect: function(){
        this.qs1.cache();
        this.qs2.cache();
      },
      afterDeselect: function(){
        this.qs1.cache();
        this.qs2.cache();
      }
    });
};

ReporteDeDistribucionControlador.prototype._configurarGrilla = function(){
    this._grilla.configurar();
};


GrillaDeProspectosRecibidos = function(grilla_id, dataSet){
    this._elementoGrillaId = grilla_id;
    this._tabla = null;
    this._dataSet = dataSet;
};

GrillaDeProspectosRecibidos.prototype.configurar = function() {
    this._tabla = this._crearTabla();
    this._configurarExpandirDetalles(this._tabla);
};

GrillaDeProspectosRecibidos.prototype._crearTabla = function() {
    // var dataSet = [
    //         {
    //             "categoria": "Ahora-84",
    //             "marca": "Toyota",
    //             "cantidad_pedida": "50",
    //             "cantidad_entregada": "25",
    //             "porcentaje_entregado": "50",
    //             "detalle": {
    //                 "amba": {"cantidad_pedida": "40", "cantidad_entregada": "20", "porcentaje_entregado": "50"},
    //                 "resto": {"cantidad_pedida": "10", "cantidad_entregada": "5", "porcentaje_entregado": "50"}
    //             }
    //         }
    //   ];
    var self = this;
    var table = $("#" + this._elementoGrillaId).DataTable( {
        //"ajax": "http://datatables.net/examples/ajax/data/objects.txt",
        "data": this._dataSet,
        "pageLength": 100,
        "lengthMenu": [100, 200, 300, "All"],
        "columnDefs": [
            {"className": "dt-center", "targets": [3, 4, 5]},
            { "visible": false, "targets": 1 },
            {
                "targets": 2,
                "render": function (data, type, full, meta) {
                    let label = new LabelMarca(data);
                    return label.texto();
                }
            },
            {
                "targets": [3, 4],
                 "render": function (data, type, full, meta) {
                     return '<p class="center-and-right">'+data+'</p>';
                 }
            },
            {
                "targets": 5,
                "render": function (data, type, full, meta) {
                    return self._renderPorcenje(data);
                }
            }
        ],
        "columns": [
            {
                "className":      'details-control',
                "orderable":      false,
                "data":           null,
                "defaultContent": ''
            },
            { "data": "categoria" },
            { "data": "marca" },
            { "data": "cantidad_pedida" },
            { "data": "cantidad_entregada" },
            { "data": "porcentaje_entregado" }
        ],
        "order": [[1, 'asc']],
        "drawCallback": function (settings) {
            var api = this.api();
            var rows = api.rows( {page:'current'} ).nodes();
            var last=null;

            api.column(1, {page:'current'} ).data().each( function ( group, i ) {
                if ( last !== group ) {
                    $(rows).eq( i ).before(
                        '<tr class="group"><td colspan="5">'+group+'</td></tr>'
                    );
                    last = group;
                }
            } );
        }
    });
    return table
};

GrillaDeProspectosRecibidos.prototype._renderPorcenje = function(porsentaje) {
    var color = this._colorSegunPorcenje(porsentaje);
    return '<p class="center-and-right" style="color: '+color+';">'+porsentaje+'%</p>'
};

GrillaDeProspectosRecibidos.prototype._colorSegunPorcenje = function(porcentaje) {
    var color = 'green';
    if (porcentaje < 50)
        color = 'red';
    if (porcentaje < 100)
        color = 'goldenrod';
    return color
};

GrillaDeProspectosRecibidos.prototype._configurarExpandirDetalles = function(tabla) {
    // Add event listener for opening and closing details
    var self = this;
   $("#" + this._elementoGrillaId).find('tbody').on('click', 'td.details-control', function () {
	     var tr  = $(this).closest('tr'),
		 row = tabla.row(tr);

	     if (row.child.isShown()) {
	       tr.next('tr').removeClass('details-row');
	       row.child.hide();
	       tr.removeClass('shown');
	     }
	     else {
	       row.child(self._generarDetalles(row.data())).show();
	       tr.next('tr').addClass('details-row');
	       tr.addClass('shown');
	     }
	});
};

GrillaDeProspectosRecibidos.prototype._generarDetalles = function(data) {
    return '<div class="details-container">'+
        '<table cellpadding="5" cellspacing="0" border="0" class="details-table">'+
        this._generarDetallesPara('AMBA', data.detalle.amba)+
        this._generarDetallesPara('Resto', data.detalle.resto)+
        '</table>'+
        '</div>';
};

GrillaDeProspectosRecibidos.prototype._generarDetallesPara = function(titulo, data) {
    return  '<tr>'+
        '<td class="title subheader" colspan="6">'+titulo+'</td>'+
        '</tr>'+
        '<tr>'+
        '<td>Cantidad Pedida:</td>'+
        '<td>'+data.cantidad_pedida+'</td>'+
        '<td>Entregada:</td>'+
        '<td>'+data.cantidad_entregada+'</td>'+
        '<td>Porcentaje:</td>'+
        '<td>'+this._renderPorcenje(data.porcentaje_entregado)+'</td>'+
        '</tr>'
};


ColorMappper = function(valores){
    this._mappeo = {};
    this._colors = function(){
         return {
            red: 'rgb(255, 99, 132)',
            orange: 'rgb(255, 159, 64)',
            yellow: 'rgb(255, 205, 86)',
            green: 'rgb(75, 192, 192)',
            blue: 'rgb(54, 162, 235)',
            purple: 'rgb(153, 102, 255)',
            grey: 'rgb(201, 203, 207)'
        };
    };

    this._randomColor = function () {
        let r = Math.floor(Math.random() * 255);
        let g = Math.floor(Math.random() * 255);
        let b = Math.floor(Math.random() * 255);
        return "rgb(" + r + "," + g + "," + b + ")";
    };

    let coloresDefault = this._colors();
    let colores = Object.keys(coloresDefault).map(function(key){ return coloresDefault[key];});
    let cantidad_colores_default = colores.length;
    var self = this;
    valores.forEach(function (valor, index) {
        var valor_minuscula = valor.toLocaleLowerCase();
        if (index < cantidad_colores_default)
            self._mappeo[valor_minuscula] = colores[index];
        else
            self._mappeo[valor_minuscula] = self._randomColor();
    });
};


ColorMappper.prototype.colorPara = function(valor){
    return this._mappeo[valor.toLocaleLowerCase()];
};


LabelMarca = function(marca) {
    this._marca = marca;
};

LabelMarca.prototype.texto = function () {
    if (this._marca == '')
        return 'Marca Blanca';
    else
        return this._primerLetraEnMayuscula(this._marca);
};


LabelMarca.prototype._primerLetraEnMayuscula = function (string)
{
    return string.charAt(0).toUpperCase() + string.slice(1);
};