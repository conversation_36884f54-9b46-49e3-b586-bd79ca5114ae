/**
 * Created by eryx on 02/02/18.
 */

function capitalizarPrimeraLetra(string) {
    return string[0].toUpperCase() + string.slice(1);
}

function obtenerSelectorParaTipo(tipo_de_dato){
    return $("#" + tipo_de_dato + "_del_prospecto_a_llenar")[0].selectize;
}

function obtenerCampoHTMLParaMarca(id_prospecto){
    return obtenerCampoHTMLParaTipoYProspecto(id_prospecto, 'marca');
}
function obtenerCampoHTMLParaTipoYProspecto(id_prospecto, tipo_de_dato){
    return $("#" + tipo_de_dato + "s-para-prospecto-" + String(id_prospecto));
}

function modificarCantidadSeleccionableDeSelectorDeModelos(id_prospecto) {
    var div_marcas = obtenerCampoHTMLParaMarca(id_prospecto);
    var marcas_seleccionadas = div_marcas.text().replace(/\s/g,'');
    if (marcas_seleccionadas == ""){
        selector_modelos[0].selectize.settings.maxItems = 1;
    }else{
        selector_modelos[0].selectize.settings.maxItems = 3;
    }
}

function abrirModalParaMarcasOModelos(id_prospecto, tipo_de_dato, etiqueta){
    var url_destino_por_tipo = {'modelo': modelos_de_prospecto_url, 'marca': marcas_de_prospecto_url};
    var url_destino = url_destino_por_tipo[tipo_de_dato];
    var query_data_por_tipo = {'modelo': {'pk_prospecto': id_prospecto}, 'marca': {}};
    var jqxhr = $.get(url_destino, $.param(query_data_por_tipo[tipo_de_dato], true), function (data) {
                    var nombres_de_instancias_limpio = JSON.parse("[" + data['nombres_de_instancias'] + "]")[0];
                    cargarInstanciasSeleccionables(nombres_de_instancias_limpio, tipo_de_dato);
                    cargarInstanciasSeleccionadas(id_prospecto, tipo_de_dato);
                    modificarCantidadSeleccionableDeSelectorDeModelos(id_prospecto);
                    var selector = obtenerSelectorParaTipo(tipo_de_dato);
                    selector.focus();
                });
    jqxhr.fail(systemUnavailable);
    var titulo = "Agregar " + etiqueta;
    var dialog = $("#"+tipo_de_dato+"_del_prospecto");
    var campo_valor = "#"+tipo_de_dato+"_del_prospecto_a_llenar";
    dialog.dialog({
        title: titulo,
        autoOpen: false,
        height: 360,
        width: 700,
        modal: true,
        buttons: {
            "Aceptar": {
                'text': 'Aceptar',
                'class': 'btn-aceptar',
                'click': function () {
                    var valor = $(campo_valor).val();
                    enviarDatos(id_prospecto, tipo_de_dato, valor, url_destino);
                    dialog.dialog("close");
                }
            },
            "Cancelar": {
                'text': 'Cancelar',
                'class': 'btn-cancelar',
                'click': function () {
                    dialog.dialog("close");
                }
            }
        }
    });
    dialog.dialog("open");
}

function sonDiferentes(arr1, arr2){
    // Si ambos arrays tienen longitudes distintas, son diferentes.
    if (arr1.length != arr2.length) {
        return true;
    }

    // Si ambos arrays son vacios, no son diferentes.
    if (arr1.length == 0 && arr2.length == 0) {
        return false;
    }

    // Si un array es vacio y otro no, son diferentes.
    if ((arr1.length == 0 && arr2.length !=0) || (arr1.length !=0 && arr2.length == 0)){
        return true;
    }

    // Si los arrays tienen igual longitud (distinta de 0), son diferentes si y solo si sus elementos son distintos.
    arr1 = arr1.sort();
    arr2 = arr2.sort();
    for (i = 0; i < arr1.length; i++){
        if (arr1[i] != arr2[i]){
            return true;
        }
    }
    return false;
}

function _nombresVacio(nombres){
    return !!(nombres.length == 0 || (nombres.length == 1 && nombres[0] == ""));

}

function cargarInstanciasSeleccionadas(id_prospecto, tipo_de_dato){
    var campo_html_a_cargar = obtenerCampoHTMLParaTipoYProspecto(id_prospecto, tipo_de_dato);
    var nombres = campo_html_a_cargar.text().replace(/\s+/g, ' ').split("-");
    var selector = obtenerSelectorParaTipo(tipo_de_dato);
    var ids_elementos_del_selector = [];
    // Si las instancias seleccionadas del selector son diferentes a las del campo HTML, piso con las del campo.
    if (sonDiferentes(selector.items, nombres) && !(_nombresVacio(nombres))){
        for (var i = 0; i < nombres.length; i++){
            // Object.keys(selector.options) te da un array con todas las opciones del selectize.
            var item_a_agregar = selector.search(nombres[i]).items[0];
            if (Boolean(item_a_agregar)){
                var nombre_id = item_a_agregar.id;
                ids_elementos_del_selector.push(nombre_id);
            }
        }
        selector.setValue(ids_elementos_del_selector);
    }
}

function enviarDatos(id_prospecto, tipo_de_dato, nombres_de_datos, url_destino) {
    var spinner = AgregarSpinner('spin_'+tipo_de_dato+'_'+id_prospecto);
    var mensaje_de_exito_por_tipo = {'modelo': "Los modelos fueron modificados exitosamente.",
        "marca": "Las marcas fueron modificadas exitosamente"};
    var query_data = {'pk_prospecto': id_prospecto, 'nombres_de_instancias_a_agregar': nombres_de_datos};
    var jqxhr = $.post(url_destino, $.param(query_data, true), function (response) {
        if (response['status']){
            notificador.notificarExito(mensaje_de_exito_por_tipo[tipo_de_dato]);
            refrescarInstanciasDe(id_prospecto, response);
            spinner.stop();
        }
        else{
            notificador.notificarError(response['error_message']);
        }
    });
    jqxhr.fail(systemUnavailable);
}

function refrescarInstanciasDe(id_prospecto, response) {
    var tipos_modificados = response['tipos_modificados'];
    for (var i = 0; i < tipos_modificados.length; i++){
        var tipo_de_dato = tipos_modificados[i];
        var nombres_de_instancias_agregadas = response[tipo_de_dato.concat('s')];
        var campo_html = obtenerCampoHTMLParaTipoYProspecto(id_prospecto, tipo_de_dato);
        var nombres_de_instancias = nombres_de_instancias_agregadas.join(" - ");
        campo_html.text(nombres_de_instancias);
    }

}

function cargarInstanciasSeleccionables(nombres_de_instancias, tipo_de_dato){
    var selector = obtenerSelectorParaTipo(tipo_de_dato);
    selector.clearOptions();
    for (i = 0; i < nombres_de_instancias.length; i++){
        selector.addOption({nombre: nombres_de_instancias[i]});
    }
}
