/**
 * Created by eryx on 17/03/17.
 */

function ActualizarGerentesPorCriterioActivo(){
    var select_gerentes = $("#alias-gerente");
    var criterio_activo_seleccionado = $("#activos-gerente")[0].checked;
    var cantidad_de_gerentes = select_gerentes[0].children.length;
    var gerentes = select_gerentes[0].children;
    OcultarUsuariosInactivos(cantidad_de_gerentes, gerentes, criterio_activo_seleccionado);

}

function ActualizarSupervisoresPorCriterioActivo() {
    var select_supervisores = $("#alias-supervisor");
    var criterio_activo_seleccionado = $("#activos-supervisor")[0].checked;
    var cantidad_de_supervisores = select_supervisores[0].children.length;
    var supervisores = select_supervisores[0].children;
    OcultarUsuariosInactivos(cantidad_de_supervisores, supervisores, criterio_activo_seleccionado);
}


function ActualizarVendedoresPorCriterioActivo(){
    var select_vendedores = $("#alias-vendedor");
    var criterio_activo_seleccionado = $("#activos-vendedor")[0].checked;
    var cantidad_de_vendedores = select_vendedores[0].children.length;
    var vendedores = select_vendedores[0].children;
    OcultarUsuariosInactivos(cantidad_de_vendedores, vendedores, criterio_activo_seleccionado);
}

function OcultarUsuariosInactivos(cantidad_de_usuarios, usuarios, criterio_activo_seleccionado){
    for(var i=0; i<cantidad_de_usuarios; i++){
        var usuario = usuarios[i];
        var usuario_es_activo = ($(usuario).attr('class') == "True");
        if(criterio_activo_seleccionado == true && usuario_es_activo == false){
            $(usuario).hide();
        }else{
            $(usuario).show();
        }
    }
}
