/*
    Requiere admin_pedidos.js porque usa:
     - ObtenerSeleccionesDeMultiselect
 */

var ultimo_request = 0;
var actualizar_cantidades_filtradas = false;

var cantidad_filtrada_url;
var cantidad_filtrada_distribucion_url;
var orden_de_filtros;
var spinners_asignacion;
var categorias;
var campanias;
var vendedores;
var pedidos;
var permitir_multiples_origenes;
var para_asignacion;
var formularioDeAsingacion;


function InicializarFormDeAsignacion() {
    ActualizarTipoRechazo(true);
    $('#id_tipo_rechazo').change(ActualizarTipoRechazo);
    $('#id_rechazado_por').change(function () {
        ActualizarCantidadFiltrada('rechazado_por')
    });
    $('#id_fecha_desde').change(ManejarCambioDeFecha);
    $('#id_fecha_hasta').change(ManejarCambioDeFecha);
    $('#id_origen').change(ManejarCambioDeOrigen);
    $('#id_categorias').change(ActualizarCampaniasCheckboxes);

    $('#campanias_container').find('ul').change(CambioSeleccionCampanias);
    $('#marcas_container').find('ul').change(CambioSeleccionMarcas);
    $('#provincias_container').find('ul').change(CambioSeleccionProvincias);
    $('#prefijos_container').find('ul').change(CambioSeleccionPrefijos);

    $('#all_campanias').change(function () {
        ToggleSelectAll('campanias')
    });

    $('#all_marcas').change(function () {
        ToggleSelectAll('marcas')
    });
    $('#all_provincias').change(function () {
        ToggleSelectAll('provincias')
    });
    $('#all_prefijos').change(function () {
        ToggleSelectAll('prefijos')
    });


    ActualizarCategoriasDeCampaniaAI();
    InicializarEventosDeCampanias();

    obtenerTextoPara('marcas');
    obtenerTextoPara('provincias');
    obtenerTextoPara('prefijos');

    if (usar_seleccion_previa){
        LlenarCamposConSeleccionPrevia();
        actualizar_cantidades_filtradas = true;
        ActualizarCantidadFiltrada('prefijos');
    }
    else{
        actualizar_cantidades_filtradas = true;
        ActualizarCantidadFiltrada('---');
    }

    // $('#id_asignar_a').change(ActualizarFormSegunAsignarA);
    // ActualizarFormSegunAsignarA();

    // ActualizarFormSegunAccion();
    // $('#id_accion').change(ActualizarFormSegunAccion);

    // $('#id_asignar_segun').change(actualizarOpcionesDeCondicionesDeAsignacion);


    // if (para_asignacion) {
    //
    //     // ActualizarVendedoresParaAsignacion();
    //     // var id_responsables = $('#id_responsables');
    //     // id_responsables.change(ActualizarVendedoresParaAsignacion);
    //     // id_responsables.change(ActualizarDescuentoAPedidos);
    // }

    formularioDeAsingacion = new FormularioDeAsignacion(equipos_de_supervisores, pedidos);
    formularioDeAsingacion.inicializar();
}


function obtenerTextoPara(identificador) {
    $('input:checkbox[name="' + identificador + '"]').each(function (i, v) {
        var text = $(v).parent().text();
        $(v).attr('text', text);
        if ($(v).val() == 'on'){
            $(v).val('')
        }
    });
}

function ActualizarPedidos(supervisores) {
    var id_pedido = $('#id_pedido');
    id_pedido.attr('size', '3');
    id_pedido.children().each(function (i, v) {
        var id = $(v).val();
        var id_supervisor = pedidos[id];
        if (supervisores.indexOf(id_supervisor) != -1 || id == '') {
            $(v).show();
            $(v).removeAttr('disabled');
        }
        else {
            $(v).hide();
            $(v).attr('disabled', 'disabled');
        }
    });
    var ninguno = id_pedido.children()[0];
    $(ninguno).attr('selected', 'selected');
}

function ManejarCambioDeOrigen() {
    ActualizarCategoriasDeCampaniaAI();
    ActualizarCantidadFiltrada('origen');
}

function ManejarCambioDeFecha() {
    ActualizarCantidadFiltrada('fecha');
}

function ActualizarTipoRechazo(omitir_actualizar_cantidad_filtrada) {
    var tipo = $('#id_tipo_rechazo').val();
    if (tipo == 'rechazados') {
        $('#id_rechazado_por').removeAttr('disabled');
    }
    else {
        $('#id_rechazado_por').attr('disabled', 'disabled');
    }
    if (omitir_actualizar_cantidad_filtrada !== undefined)
        ActualizarCantidadFiltrada('tipo_rechazo');
}


function ReiniciarFiltrosMenores(filtro) {
    // Marca las opciones de los filtros indicados como seleccionadas por defecto
    var posicion_filtro = orden_de_filtros.indexOf(filtro);
    for (var i = posicion_filtro + 1; i < orden_de_filtros.length; i++) {
        var nombre_filtro = orden_de_filtros[i];
        DefinirValorDeSeleccionParaCheckboxes(nombre_filtro, true);
    }
}

function ActualizarCantidadFiltrada(filtro_modificado) {
    if (!actualizar_cantidades_filtradas)
        return;

    ReiniciarFiltrosMenores(filtro_modificado);
    var query_data = GetFilterDataDeAsignacionInicial(filtro_modificado);
    ultimo_request += 1;
    var nro_request = ultimo_request;
    var url;
    if (para_asignacion)
        url = cantidad_filtrada_url;
    else
        url = cantidad_filtrada_distribucion_url;
    AgregarSpinnersYOcultarFiltros(filtro_modificado);
    var jqxhr = $.post(url, $.param(query_data, true), function (data) {
        MostrarCantidadFiltrada(data, filtro_modificado, nro_request);
        $('#filtros').children().show();
    });
    jqxhr.fail(function () {
        if (nro_request == ultimo_request)
            QuitarSpinnersYMostrarFiltros(filtro_modificado);
        systemUnavailable();
    });
}

function MostrarCantidadFiltrada(data, filtro_modificado, nro_request) {
    if (!data['status']) {
        alert('No se pudo realizar esta accion. Intente mas tarde nuevamente por favor.');
        return false;
    }
    if (nro_request == ultimo_request) {
        $("#total_filtrado").html(data['total_prospectos']);
        $('#id_cantidad').val(data['total_prospectos']);
        ActualizarCantidadesDeSelectorPorCheckbox(data['marcas'], 'marcas');
        ActualizarCantidadesDeCampanias(data['campanias']);
        ActualizarCantidadesDeSelectorPorCheckbox(data['provincias'], 'provincias');
        ActualizarCantidadesDeSelectorPorCheckbox(data['prefijos'], 'prefijos');
        QuitarSpinnersYMostrarFiltros(filtro_modificado);
    }
}

function ObtenerCheckboxesSeleccionados(nombre, checkear_seleccion_masiva) {
    if (checkear_seleccion_masiva) {
        if ($("#all_" + nombre).prop('checked'))
            return null;
    }
    var checkboxes_seleccionados = [];
    $("input:checkbox[name=" + nombre + "]:checked").each(function (i, v) {
        if ($(v).attr('disabled') == undefined) {
            // Fix por si el checkbox no viene con value (es marca blanca, sin provincia o sin prefijo)
            if ($(v).attr('value') === undefined)
                checkboxes_seleccionados.push('');
            else
                checkboxes_seleccionados.push($(v).val());
        }
    });
    return checkboxes_seleccionados;
}

function GetFilterDataDeAsignacionInicial(filtro_aplicado) {
    var filtros = {
        'filtro_aplicado': filtro_aplicado,
        'origen': $('#id_origen').val(),
        'fecha_desde': $('#id_fecha_desde').val(),
        'fecha_hasta': $('#id_fecha_hasta').val(),
        'categorias': ObtenerSeleccionesDeCategorias(),
        'campanias': ObtenerCheckboxesSeleccionados('campanias')
    };
    var marcas_seleccionadas = ObtenerCheckboxesSeleccionados('marcas', false);
    if (marcas_seleccionadas)
        filtros['marcas'] = marcas_seleccionadas;
    var provincias_seleccionadas = ObtenerCheckboxesSeleccionados('provincias', false);
    if (provincias_seleccionadas)
        filtros['provincias'] = provincias_seleccionadas;
    var prefijos_seleccionados = ObtenerCheckboxesSeleccionados('prefijos', false);
    if (prefijos_seleccionados)
        filtros['prefijos'] = prefijos_seleccionados;

    if (para_asignacion) {
        filtros['tipo_rechazo'] = $('#id_tipo_rechazo').val();
        filtros['rechazado_por'] = $('#id_rechazado_por').val();
    }
    else {
        filtros['responsable'] = $('#id_responsable').val();
    }
    return filtros
}

function ObtenerSeleccionesDeCategorias() {
    var elem_categorias = $("#id_categorias");
    var seleccion = elem_categorias.val();
    if (seleccion == null)
        seleccion = obtenerValorDeOpciones(elem_categorias);
    return seleccion;
}

function obtenerValorDeOpciones(elem){
    var values = [];
    elem.find("option").each(function () {
        values.push(this.value);
    });
    return values;
}

function InicializarEventosDeCampanias() {
    $("input:checkbox[name='campanias']").each(function (i, v) {
        var id = $(v).val();
        if (campanias.hasOwnProperty(id)) {
            $(v).change(function () {
                ActualizarCantidadFiltrada('campanias');
            });
        }
    });
}

function ActualizarCantidadesDeCampanias(cantidades) {
    $("input:checkbox[name='campanias']").each(function (i, v) {
        var id = $(v).val();
        var cantidad = 0;

        for (var c = 0; c < cantidades.length; c++) {
            if (cantidades[c][0] == id)
                cantidad = cantidades[c][1];
        }
        var campania = campanias.filter(function (x) {
            return x.id == id
        })[0];
        var nuevo = ' ' + campania.nombre + ' (' + campania.nombre_origen + ')' + ' (' + cantidad + ')';
        var parent = $(v).parent();
        parent.html('');
        parent.append($(v));
        parent.append(nuevo);
        if (cantidad == 0) {
            $(v).parent().parent().hide();
            $(v).attr('disabled', 'disabled');
        }
        else {
            $(v).parent().parent().show();
            $(v).removeAttr('disabled');

        }
    });
    ControlSelectorAll('campanias');
    InicializarEventosDeCampanias();
}

function ActualizarCampaniasCheckboxes() {
    var origen = $('#id_origen').val();
    var categorias_elegidas = ObtenerSeleccionesDeMultiselect('categorias');
    var sin_categorias_elegidas = categorias_elegidas.length == 0;

    $("input:checkbox[name='campanias']").each(function (i, v) {
        var id = $(v).val();
        var campania = campanias.filter(function (x) {
            return x.id == id
        })[0];
        $(v).parent().parent().hide();
        $(v).attr('disabled', 'disabled');
        if (origen == campania.tipo_de_origen || permitir_multiples_origenes && origen == '') {
            if (sin_categorias_elegidas || categorias_elegidas.indexOf(String(campania.categoria)) != -1) {
                $(v).removeAttr('disabled');
                $(v).parent().parent().show();
            }
        }
    });
    ActualizarCantidadFiltrada('categorias');
}

function ActualizarCategoriasDeCampaniaAI(){
    var opciones = $('#id_categorias').children();
    var origen = $('#id_origen').val();
    for (var i=0; i<opciones.length; i++){
        var opt_id = opciones[i].value;
        var categoria = categorias.filter(function(x){return x.id==opt_id})[0];
        $(opciones[i]).hide();
        $(opciones[i]).attr('disabled', 'disabled');
        if (origen == categoria.tipo_de_origen || permitir_multiples_origenes && origen=='') {
            $(opciones[i]).removeAttr('disabled');
            $(opciones[i]).show();
        }
    }
    ActualizarCampaniasCheckboxes();
}

function ActualizarCantidadesDeSelectorPorCheckbox(cantidades, nombre) {
    $("input:checkbox[name=" + nombre + "]").each(function (i, v) {
        var id = $(v).val();
        var text = $(v).attr('text');
        var cantidad = 0;
        // Los campos vacios son los de 'Sin prefijo', 'Sin provincia' y 'Sin marca'
        // Veo si este checkbox corresponde al del campo vacio
        // (Siempre debe venir el campo vacio con id 0 aunque no se use!)
        var es_checkbox_de_vacio = $(v).attr('id') == 'id_'+ nombre +'_0';
        if (es_checkbox_de_vacio)
            $(v).attr('value', '');
        for (var c = 0; c < cantidades.length; c++) {
            // Veo si la informacion de cantidad de esta iteracion corresponde a la del campo vacio
            var es_cantidad_de_vacio = cantidades[c][0] == '';
            // Veo Si la informacion de cantidad corresponde al checkbox
            if (sonEquivalentesIgorandoAcentosYMayusculas(cantidades[c][0], id) ||
                ((es_cantidad_de_vacio) && es_checkbox_de_vacio) )
                cantidad = cantidades[c][1];
        }
        var nuevo = ' ' + text + ' (' + cantidad + ')';
        var parent = $(v).parent();
        parent.html('');
        parent.append($(v));
        parent.append(nuevo);
        if (cantidad == 0) {
            $(v).parent().parent().hide();
            $(v).attr('disabled', 'disabled');
        }
        else {
            $(v).parent().parent().show();
            $(v).removeAttr('disabled');

        }
    });
    ControlSelectorAll(nombre);
}

function DefinirValorDeSeleccionParaCheckboxes(selector, valor) {
    $('#' + selector + '_container :checkbox').each(function (i, v) {
        $(v).prop('checked', valor);
    });
}

function ToggleSelectAll(selector) {
    var checked = $('#all_' + selector).prop('checked');
    DefinirValorDeSeleccionParaCheckboxes(selector, checked);
    ActualizarCantidadFiltrada(selector);
}

function ControlSelectorAll(selector) {
    var todos = true;
    var id_todos = 'all_' + selector;
    $('#' + selector + '_container :checkbox').each(function (i, v) {
        todos = todos && (v.id == id_todos || $(v).prop('checked') || $(v).prop('disabled'));
    });
    $('#' + id_todos).prop('checked', todos);
}

function CambioSeleccionCampanias() {
    ControlSelectorAll('campanias');
    ActualizarCantidadFiltrada('campanias');
}

function CambioSeleccionProvincias() {
    ControlSelectorAll('provincias');
    ActualizarCantidadFiltrada('provincias');
}
function CambioSeleccionMarcas() {
    ControlSelectorAll('marcas');
    ActualizarCantidadFiltrada('marcas');
}

function CambioSeleccionPrefijos() {
    ControlSelectorAll('prefijos');
    ActualizarCantidadFiltrada('prefijos');
}

function LlenarCamposConSeleccionPrevia(){
    CompletarConSeleccionPrevia('marcas');
    CompletarConSeleccionPrevia('campanias');
    CompletarConSeleccionPrevia('provincias');
    CompletarConSeleccionPrevia('prefijos');
}

function CompletarConSeleccionPrevia(nombre_campo){
    if (seleccion_previa.hasOwnProperty(nombre_campo)){
        $('#' + nombre_campo + '_container :checkbox').each(function (i, v) {
            if (seleccion_previa[nombre_campo].indexOf($(v).val()) != -1){
                $(v).prop('checked', true);
            }
            else {
                $(v).prop('checked', false);
            }
        });
    }
}

function AgregarSpinnersYOcultarFiltros(filtro) {
    $("#total_filtrado").html('---');
    var posicion_filtro = orden_de_filtros.indexOf(filtro);
    for (var i = posicion_filtro + 1; i < orden_de_filtros.length; i++) {
        var nombre = orden_de_filtros[i];
        spinners_asignacion[nombre] = AgregarSpinner(nombre + '_spinner');
        $('#' + nombre + '_container').hide();
    }
}

function QuitarSpinnersYMostrarFiltros(filtro) {
    var posicion_filtro = orden_de_filtros.indexOf(filtro);
    for (var i = posicion_filtro + 1; i < orden_de_filtros.length; i++) {
        var nombre = orden_de_filtros[i];
        var spinner = spinners_asignacion[nombre];
        spinner.stop();
        $('#' + nombre + '_container').show();
    }
}


function habilitar(elemento){
    elemento.removeAttr('disabled');
}

function deshabilitar(elemento){
    elemento.attr('disabled', 'disabled');
}

function mostrarFilaDe(elemento){
    var fila = filaDe(elemento);
    fila.show();
}


function ocultarFilaDe(elemento){
    var fila = filaDe(elemento);
    fila.hide();
}


function filaDe(elemento){
    return elemento.parent().parent()
}


function sonEquivalentesIgorandoAcentosYMayusculas(palabra1, palabra2){
    return removeDiacritics(palabra1.toLowerCase()) == removeDiacritics(palabra2.toLowerCase());
}

/*
   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
    var defaultDiacriticsRemovalMap = [
        {'base':'a', 'letters':'\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250'},
        {'base':'e', 'letters':'\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD'},
        {'base':'i', 'letters':'\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131'},
        {'base':'n', 'letters':'\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5'},
        {'base':'o', 'letters':'\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275'},
        {'base':'u','letters': '\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289'}
    ];

    var diacriticsMap = {};
    for (var i=0; i < defaultDiacriticsRemovalMap .length; i++){
        var letters = defaultDiacriticsRemovalMap [i].letters;
        for (var j=0; j < letters.length ; j++){
            diacriticsMap[letters[j]] = defaultDiacriticsRemovalMap [i].base;
        }
    }

    // "what?" version ... http://jsperf.com/diacritics/12
    function removeDiacritics (str) {
        return str.replace(/[^\u0000-\u007E]/g, function(a){
           return diacriticsMap[a] || a;
        });
    }


FormularioDeAsignacion = function(equipos_de_supervisores, pedidos){
    this._equipos_de_supervisores = equipos_de_supervisores;
    this._pedidos = pedidos;
    this._ASIGNAR_A_VENDEDOR = 'asignar';
    this._PONER_A_CARGO = 'responsabilizar';
    this._ASIGNACION_VIA_SUPERVISORES = 'supervisores';
    this._MODO_DE_SELECCION_POR_VENDEDOR = 'V';
    this._MODO_DE_SELECCION_POR_EQUIPO = 'E';
    this._MODO_DE_SELECCION_A_TODOS = 'T';
    this._FACTOR_UNIFORME = 0;
    this._FACTOR_MANUAL = 1;
    this._FACTOR_MANUAL_MAS_ADMINISTRADOR = 3;
    this._elementoAccion = $('#id_accion');
    this._elementoAsignarA = $('#id_asignar_a');
    this._elementoAsignarSegun = $('#id_asignar_segun');
    this._elementoMetodo = $('#id_metodo');
    this._elementoMetodoPorProductividad = $('#id_metodo_por_productividad');
    this._elementoEquipo = $('#id_equipo');
    this._elementoVendedor = $('#id_vendedor');
    this._elementoResponsables = $('#id_responsables');
    this._elementoPedidos = $('#id_pedidos');
    this._elementoAplicarRestriccionesDePedido = $('#id_aplicar_restricciones_del_pedido');
    this._elementoRestricciones = $('#id_restricciones');
};

FormularioDeAsignacion.prototype = {
    inicializar: function () {
        this._inicializarAccion();
        this._inicializarAsignarA();
        this._inicializarAsignarSegun();
        this._inicializarResponsables();
        this._inicializarAplicarRestriccionesDePedido();
    },
    _inicializarAccion: function () {
        var self = this;
        self._actualizarAccion();
        this._elementoAccion.change(function () {
            self._actualizarAccion();
        });
    },
    _inicializarAsignarA: function(){
        var self = this;
        self._actualizarElementosSegunAsignacionDeVendedor();
        this._elementoAsignarA.change(function () {
            self._actualizarElementosSegunAsignacionDeVendedor();
        });
    },
    _inicializarAsignarSegun: function(){
        var self = this;
        this._elementoAsignarSegun.change(function () {
            self._actualizarElementosParaAsignacionSegun();
        });
    },
    _inicializarAplicarRestriccionesDePedido: function(){
        var self = this;
        this._elementoAplicarRestriccionesDePedido.change(function () {
            self._actualizarElementosParaAplicarRestriccionesDePedido();
        });
    },
    _inicializarResponsables: function(){
        var self = this;
        self._actualizarElementosSegunResponsables();
        this._elementoResponsables.change(function () {
            self._actualizarElementosSegunResponsables();
        });
    },
    _actualizarAccion: function () {
        var metodo = this._elementoMetodo;
        var metodoManual = metodo.find('input[value=\"'+ this._FACTOR_MANUAL + '\"]');
        var metodoManualMasAdmin = metodo.find('input[value=\"'+ this._FACTOR_MANUAL_MAS_ADMINISTRADOR + '\"]');
        if (this._esAsignacion()) {
            this._actualizarElementosParaAsignacion(metodoManual, metodoManualMasAdmin);
        }
        else { // responsabilizar
            this._actualizarElementosParaResponsabilizar(metodoManual, metodoManualMasAdmin);
        }
        this._actualizarElementosParaAsignacionSegun();

    },
    _actualizarElementosParaAsignacion: function (metodoManual, metodoManualMasAdmin) {
        this._actualizarRestriccionesParaAsignacion();
        this._mostrarOpcionesDeAsignacion();
        this._habilitarElemento(metodoManual);
        this._habilitarElemento(metodoManualMasAdmin);
        if (this._esViaSupervisores()){
            this._configurarMetodoManualMasAdmin();
        }

    },
    _actualizarElementosParaResponsabilizar: function (metodoManual, metodoManualMasAdmin) {
        this._actualizarRestriccionesParaResponsabilizar();
        this._ocultarFilaDe(this._elementoAsignarA);
        this._ocultarFilaDe(this._elementoVendedor);
        this._ocultarFilaDe(this._elementoEquipo);
        this._ocultarFilaDe(this._elementoMetodoPorProductividad);
        this._configurarMetodoUniforme();
        this._deshabilitarElemento(metodoManual);
        this._deshabilitarElemento(metodoManualMasAdmin);
    },
    _actualizarElementosParaAsignacionSegun: function () {
        var label_por_productividad = $("label[for='id_metodo_por_productividad']");
        if (this._esViaSupervisores()) {
            if (this._esAsignacion()) {
                this._mostrarFilaDe(label_por_productividad);
                this._mostrarFilaDe(this._elementoAsignarA);
                this._mostrarFilaDe(this._elementoVendedor);
                this._mostrarFilaDe(this._elementoEquipo);
                this._configurarMetodoManualMasAdmin();
            }
            else{
                this._ocultarFilaDe(label_por_productividad);
            }
            label_por_productividad.text("Método por productividad:");
            this._ocultarFilaDe(this._elementoAplicarRestriccionesDePedido);
            this._mostrarFilaDe(this._elementoMetodo);
        }
        else {//segun pedidos
            this._mostrarFilaDe(this._elementoAplicarRestriccionesDePedido);
            this._actualizarElementosParaAplicarRestriccionesDePedido();
            this._ocultarFilaDe(this._elementoMetodo);
            this._ocultarFilaDe(this._elementoAsignarA);
            this._ocultarFilaDe(this._elementoVendedor);
            this._ocultarFilaDe(this._elementoEquipo);
            this._ocultarFilaDe(this._elementoPedidos);
            if (this._esAsignacion()) {
                this._mostrarFilaDe(label_por_productividad);
                label_por_productividad.text("Forzar por productividad:");
            }
            else{
                this._ocultarFilaDe(label_por_productividad);
            }
        }
    },
    _actualizarRestriccionesParaAsignacion: function () {
        var datos_nuevos = $('#id_restricciones[value=\"nuevos\"]');
        this._habilitarElemento(datos_nuevos);
    },
    _actualizarRestriccionesParaResponsabilizar: function () {
        var datos_nuevos = $('#id_restricciones[value=\"nuevos\"]');
        this._deshabilitarElemento(datos_nuevos);
    },
    _actualizarElementosSegunAsignacionDeVendedor: function() {
        var modo_de_seleccion_de_vendedor = this._elementoAsignarA.val();
        var vendedor = this._elementoVendedor.find('input');

        if (modo_de_seleccion_de_vendedor == this._MODO_DE_SELECCION_POR_VENDEDOR) {
            this._deshabilitarElemento(this._elementoEquipo);
            this._habilitarElemento(vendedor);
            this._deshabilitarOpcionesDeElement(this._elementoMetodo);
            this._deshabilitarElemento(this._elementoMetodoPorProductividad);
        }
        else {
            if (modo_de_seleccion_de_vendedor == this._MODO_DE_SELECCION_POR_EQUIPO) {
                this._habilitarElemento(this._elementoEquipo);
            }
            else {
                this._deshabilitarElemento(this._elementoEquipo);
            }
            this._deshabilitarElemento(vendedor);
            this._habilitarOpcionesDeElement(this._elementoMetodo);
            this._habilitarElemento(this._elementoMetodoPorProductividad);
        }
    },
    _actualizarElementosParaUnResponsable: function (supervisores) {
        var supervisor = supervisores[0];
        this._actualizarVendedoresPara(supervisor);
        this._actualizarEquiposPara(supervisor);
        this._actualizarPedidosPara(supervisor);
        var nuevoEstado = true;
        this._actualizarSeleccionPorDefectoDeDescontarAPedidos(nuevoEstado);
        if (this._esAsignacion()) {
            this._mostrarOpcionesDeAsignacion();
        }
        if (this._esViaSupervisores()) {
            this._mostrarFilaDe(this._elementoPedidos);
        }
    },
    _actualizarElementosParaVariosResponsables: function () {
        this._mostrarFilaDe(this._elementoMetodo);
        if (this._esAsignacion()) {
            this._mostrarFilaDe(this._elementoMetodoPorProductividad);
        }
        this._ocultarOpcionesDeAsignacion();
        var nuevoEstado = false;
        this._actualizarSeleccionPorDefectoDeDescontarAPedidos(nuevoEstado);
        this._elementoAsignarA.val(this._MODO_DE_SELECCION_A_TODOS);
        this._deshabilitarElemento(this._elementoAsignarA);
        this._ocultarFilaDe(this._elementoPedidos);
    },
    _actualizarElementosSegunResponsables: function () {
        // TODO: falta tener en cuenta si es via pedidos
        var supervisores = this._responsablesSeleccionados();
        if (supervisores.length == 0) {
            this._actualizarVendedoresPara('');
            this._ocultarFilaDe(this._elementoMetodo);
        }
        else if (supervisores.length == 1) {
            this._actualizarElementosParaUnResponsable(supervisores);
        }
        else {
            this._actualizarElementosParaVariosResponsables();
        }
    },
    _actualizarElementosParaAplicarRestriccionesDePedido: function () {
        var debe_aplicar = this._elementoAplicarRestriccionesDePedido.is(':checked');
        if (debe_aplicar){
            this._deshabilitarOpcionesDeElement(this._elementoRestricciones);
        }
        else{
            this._habilitarOpcionesDeElement(this._elementoRestricciones);
        }
    },
    _mostrarOpcionesDeAsignacion: function(){
        if (this._esAsignacionViaSupervisores()) {
            this._habilitarElemento(this._elementoAsignarA);
            this._mostrarFilaDe(this._elementoVendedor);
            this._mostrarFilaDe(this._elementoEquipo);
            this._mostrarFilaDe(this._elementoMetodo);
            this._mostrarFilaDe(this._elementoMetodoPorProductividad);
        }
        this._actualizarElementosSegunAsignacionDeVendedor();
    },
    _habilitarOpcionesDeElement: function (elemento) {
        elemento.find('input').removeAttr("disabled");
    },
    _deshabilitarOpcionesDeElement: function (elemento) {
        elemento.find('input').attr('disabled', 'disabled');
    },
    _configurarMetodoManualMasAdmin: function () {
        var metodoManualMasAdmin = this._elementoMetodo.find('input[value=\"'+ this._FACTOR_MANUAL_MAS_ADMINISTRADOR + '\"]');
        metodoManualMasAdmin.prop('checked', 'checked');
    },
    _configurarMetodoUniforme: function () {
        var metodoManualMasAdmin = this._elementoMetodo.find('input[value=\"'+ this._FACTOR_UNIFORME + '\"]');
        metodoManualMasAdmin.prop('checked', 'checked');
    },
    _ocultarOpcionesDeAsignacion: function(){
        this._ocultarFilaDe(this._elementoVendedor);
        this._ocultarFilaDe(this._elementoEquipo);
    },
    _actualizarEquiposPara: function(supervisor){
        this._elementoEquipo.empty();
        var equipos = this._equipos_de_supervisores[supervisor];
         if (supervisor != '' && equipos != undefined) {
             for (var indice = 0; indice < equipos.length; indice++) {
                 var equipo = equipos[indice];
                 this._elementoEquipo.append('<option value="' + equipo.id + '">' + equipo.nombre + '</option>');
             }
         }
    },
    _actualizarVendedoresPara: function (supervisor) {
        var id_vendedor = this._elementoVendedor;
        var children = id_vendedor.children();
        var no_vendedores = $("#no_vendedores");

        if (no_vendedores.length == 0) {
            id_vendedor.parent().append('<span id="no_vendedores">Seleccione un supervisor con vendedores a cargo para listarlos</span>');
        }
        else {
            no_vendedores.show();
        }

        for (var i = 0; i < children.length; i++) {
            $(children[i]).hide();
            if (supervisor != '') {
                var opt_id = children[i].childNodes[0].childNodes[0].value;
                var vendedor = vendedores.filter(function (x) {
                    return x.id == opt_id
                })[0];
                $(children[i]).attr('disabled', 'disabled');
                if (supervisor == vendedor.supervisor || opt_id == supervisor) {
                    $(children[i]).removeAttr('disabled');
                    $(children[i]).show();
                    no_vendedores.hide();
                }
            }
        }
        id_vendedor.val(0);
    },
    _actualizarSeleccionPorDefectoDeDescontarAPedidos: function(nuevo_estado) {
        var descontar_a_pedido_element = $('#id_descontar_a_pedidos');
        if (nuevo_estado != descontar_a_pedido_element.prop('checked')) {
            descontar_a_pedido_element.prop('checked', nuevo_estado);
            var mensaje;
            if (nuevo_estado) {
                mensaje = 'Por defecto se seleccionó descontar a pedidos.';
            }
            else {
                mensaje = 'Por defecto se deseleccionó descontar a pedidos.';
            }
            notificador.notificarAdvertencia(mensaje);
        }
    },
    _actualizarPedidosPara: function (supervisor) {
        var self = this;
        this._elementoPedidos.attr('size', '3');
        this._elementoPedidos.children().each(function (i, v) {
            var id = $(v).val();
            var id_supervisor = self._pedidos[id];
            if (supervisor == id_supervisor || id == '') {
                $(v).show();
                $(v).removeAttr('disabled');
            }
            else {
                $(v).hide();
                $(v).attr('disabled', 'disabled');
            }
        });
        var ninguno = this._elementoPedidos.children()[0];
        $(ninguno).attr('selected', 'selected');
    },
    _esAsignacion: function () {
        var valor = this._elementoAccion.val();
        return valor == this._ASIGNAR_A_VENDEDOR
    },
    _esPonerACargo: function () {
        var valor = this._elementoAccion.val();
        return valor == this._PONER_A_CARGO
    },
    _esViaSupervisores: function () {
         var asignar_segun = this._elementoAsignarSegun.find('input:checked').val();
         return asignar_segun == this._ASIGNACION_VIA_SUPERVISORES
    },
    _esAsignacionViaSupervisores: function () {
        return this._esViaSupervisores() && this._esAsignacion()
    },
    _responsablesSeleccionados: function () {
        var responsables = ObtenerSeleccionesDeMultiselect('responsables');
        return responsables
    },
    _habilitarElemento: function(elemento){
        elemento.removeAttr('disabled');
    },
    _deshabilitarElemento: function(elemento){
        elemento.attr('disabled', 'disabled');
    },
    _mostrarFilaDe: function(elemento){
        var fila = this._filaDe(elemento);
        fila.show();
    },
    _ocultarFilaDe: function(elemento){
        var fila = this._filaDe(elemento);
        fila.hide();
    },
    _filaDe: function (elemento) {
        return elemento.parent().parent();
    }
};
