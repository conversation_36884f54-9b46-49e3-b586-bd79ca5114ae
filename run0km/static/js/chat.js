function ChatAlreadyOpenedException() {
    this.name = "ChatAlreadyOpenedException";
    this.message = ("El chat ya se encuentra abierto!");
}

ChatAlreadyOpenedException.prototype = Object.create(Error.prototype);
ChatAlreadyOpenedException.prototype.constructor = ChatAlreadyOpenedException;


var ChatListBarView = function (chatAreaId, customSoundUrl, propuestasParaChatUrl, envioDePropuestaUrl) {
    chatAreaId = chatAreaId || 'chatarea';
    this.$chatArea = $('#' + chatAreaId);
    this._propuestasParaChatUrl = propuestasParaChatUrl;
    this._envioDePropuestaUrl = envioDePropuestaUrl;
    this._model = new ChatListBar();
    this.activeChatViews = {};
    if (customSoundUrl !== undefined) {
        this.unreadMessageSound = new Audio(customSoundUrl);
    } else {
        this.unreadMessageSound = null;
    }
    this._storageManager = new ChatListBarViewStorageManager(this);
};

ChatListBarView.prototype.element = function () {
    return this.$chatArea;
};

ChatListBarView.prototype.storageManager = function () {
    return this._storageManager;
};

ChatListBarView.prototype.addChat = function (chatId, channel, chatIsActive, writeMessageCallback,
                                              markChatAsReadCallback) {
    var chat = this._model.addChat(chatId, channel, chatIsActive, writeMessageCallback, markChatAsReadCallback);
    var chatView = new ChatView(this, chat, this._propuestasParaChatUrl, this._envioDePropuestaUrl);
    this.addChatViewTemplateToBar(chatView);
    this.addChatViewToActiveChatViewsDict(chatView);
    return chatView;
};

ChatListBarView.prototype.addChatViewTemplateToBar = function (chatView) {
    this.$chatArea.prepend(chatView.element());
};

ChatListBarView.prototype.addChatViewToActiveChatViewsDict = function (chatView) {
    this.activeChatViews[chatView.model().id()] = chatView;
};

ChatListBarView.prototype.removeChat = function (chatView) {
    this._model.removeChat(chatView.model());
    this.removeChatElementFromBar(chatView);
    this.removeChatViewFromActiveChatViewsDict(chatView);
};

ChatListBarView.prototype.removeChatElementFromBar = function (chatView) {
    chatView.element().remove();
};

ChatListBarView.prototype.removeChatViewFromActiveChatViewsDict = function (chatView) {
    delete this.activeChatViews[chatView.model().id()];
};

ChatListBarView.prototype.chatViewWith = function (id) {
    var chat = this._model.chatWith(id);
    if (chat) {
        return this.activeChatViews[chat.id()];
    }
};

ChatListBarView.prototype.playSound = function () {
    if (this.unreadMessageSound !== null) {
        this.unreadMessageSound.play();
    }
};

ChatListBarView.prototype.includesChatWithId = function (chatId){
    return this._model.includesChatWithId(chatId)
}


var ChatListBar = function () {
    this.activeChatsInformation = [];
};

ChatListBar.prototype.chatWith = function (id) {
    for (var chatIndex = 0; chatIndex < this.activeChatsInformation.length; chatIndex++) {
        var chat = this.activeChatsInformation[chatIndex];
        if (chat.id() === id) {
            return chat;
        }
    }
};

ChatListBar.prototype.addChat = function (chatId, channel, chatIsActive, writeMessageCallback, markChatAsReadCallback) {
    var chat = new Chat(chatId, channel, chatIsActive, writeMessageCallback, markChatAsReadCallback);
    var chatsActuales = this.activeChatsInformation;
    for (var i = 0; i < chatsActuales.length; i++) {
        if (chatsActuales[i]._id === chatId) {
            throw new ChatAlreadyOpenedException;
        }
    }
    this.activeChatsInformation.push(chat);
    return chat;
};


ChatListBar.prototype.removeChat = function (chat) {
    var active_chats = this.activeChatsInformation;
    for (var i = 0; i < active_chats.length; i++) {
        var active_chat = active_chats[i];
        if (chat.id() === active_chat.id()) {
            if (active_chats.length === 1) {
                active_chats.shift();
            }
            else {
                active_chats.splice(i, 1);
            }
            this.activeChatsInformation = active_chats;
            return;
        }
    }
};

ChatListBar.prototype.includesChatWithId = function (chatId){
    return this.chatWith(chatId) != null
}


var ChatView = function (chatListBarView, chat, propuestasParaChatUrl, envioDePropuestaUrl) {
    this.ENTER_KEY_CODE = 13;
    this.DEFAULT_NAME = 'Posible Comprador';
    this.UNREAD_MESSAGE_CHATHEAD_CLASS = 'unread-message';
    this.INACTIVE_CLASS = 'inactive';
    this._model = chat;
    this._owner = chatListBarView;
    // Cacheo de elementos jQuery del DOM
    this._$element = $(this.template());
    this._proposalSendPanel = new ChatProposalSendPanel(
        this,
        (function (text) {
            this.addSentProposalToFeed(text);
             this.toggleDialog(this._proposalSendPanel);
        }).bind(this),
        propuestasParaChatUrl,
        envioDePropuestaUrl);
    this._clientDataPanel = new ChatClientDataPanel(this);

    this.$feed = this._$element.find('.feed');
    this.$settings = this._$element.find('.settings');
    this.$messagebox = this._$element.find('.messagebox');
    this.$writeMessageTextarea = this._$element.find('.writemessage');

    this.$chatHead = this._$element.find('.chathead');
    this.$toggleArea = this._$element.find('.togglearea');
    this.$clientDataButton = this._$element.find('.toggle-datos');
    this.$proposalButton = this._$element.find('.proposal-button');
    this.$closeButton = this._$element.find('#closeButton');

    this.hideCloseButton();
    this._bindFocusToMessageBoxToMarkChatAsRead();
    this._bindClickToMessageFeedToTakeFocusToMessageBox();
    this._bindEnterToWriteMessageBox();
    this._bindClickToToChatHeadToMinimize();
    this._bindClickToClientDataButton();
    this._bindClickToProposalButton();
    this._bindClickToCloseButton();



    if (this._model.hasChannelMeta()){
        this.$chatHead.addClass("meta");
        this.$proposalButton.addClass("meta-hide");
    }
};

ChatView.prototype.hideCloseButton = function () {
    var chat = this.model();
    if (chat.isActive()) {
        this._closeButtonVisible(false);
    }
};

ChatView.prototype._bindClickToProposalButton = function () {
    this.$proposalButton.click((function () {
        this._proposalSendPanel.refresh();
        this.toggleDialog(this._proposalSendPanel);
    }).bind(this));
    this._proposalSendPanel.bindCloseButton((function () {
        this.toggleDialog(this._proposalSendPanel);
    }).bind(this));
};

ChatView.prototype._closeButtonVisible = function (aBoolean) {
    if (aBoolean)
        this.$closeButton.show();
    else
        this.$closeButton.hide();
};

ChatView.prototype.model = function () {
    return this._model;
};

ChatView.prototype.element = function () {
    return this._$element;
};

ChatView.prototype.setAsInactive = function () {
    this.$chatHead.addClass(this.INACTIVE_CLASS);
    this.$writeMessageTextarea.attr('disabled', 'disabled');
    this.$messagebox.addClass('disabled');
    this._closeButtonVisible(true);
    this._unbindSubmitMessage();
};

ChatView.prototype._unbindSubmitMessage = function () {
    this.$writeMessageTextarea.off('keypress', "**");
    this.$proposalButton.off('click', "**");
};

ChatView.prototype.eraseChat = function () {
    var chatListBarView = this._owner;
    chatListBarView.removeChat(this);
    chatListBarView.storageManager().removeChatViewFromStorage(this);
};

ChatView.prototype.close = function () {
    this._owner.removeChat(this);
};

ChatView.prototype.template = function () {
    return '<div class="chat" id="' + this._element_id() + '">' +
        ChatProposalSendPanel.template() +
        ChatClientDataPanel.template() +
        this._templateChatContent() +
        '</div>'
};

ChatView.prototype._templateChatContent = function () {
    return '<div class="chathead online">' +
        '<label for>' + this.DEFAULT_NAME + '</label>' +
        '<button class="toggle" id="closeButton">x</button>' +
        '<button class="toggle">‒</button>' +
        '</div>' +
        '<div class="togglearea">' +
        '<div class="settings">' +
        '<span class="email">Email <span class="falta-dato">!</span></span><span class="separador"> | </span><span class="telefono">Teléfono <span class="falta-dato">!</span></span>' +
        '<button class="toggle-ayuda" onclick="goToHelpView()"></button>' +
        '<button class="toggle-datos"></button>' +
        '</div>' +
        '<div class="feed"></div>' +
        '<div class="messagebox">' +
        '<button class="proposal-button" title="Enviar propuesta"></button>' +
        '<textarea placeholder="Escribí un mensaje..." class="writemessage"></textarea>' +
        '</div>' +
        '</div>'
};

ChatView.prototype.whenSendMessageDo = function () {
    var message_text = this.$writeMessageTextarea.val();
    var fecha_actual = this._currentDateTimestamp();
    var message = {'texto': message_text, 'fecha': fecha_actual};
    this.addSentMessageToFeed(message);
};

ChatView.prototype.setParameters = function (parameters) {
    $.each(parameters, (function (key, value) {
        if (value !== null && value !== "") {
            this.setParameter(key, value);
        }
    }).bind(this));
};

ChatView.prototype.setParameter = function (key, value) {
    this._model.setParameter(key, value);
    var lowerCaseKey = key.toLowerCase();
    this._clearCompletedDataFromSettingsBar(lowerCaseKey);
    this._clientDataPanel.addData(lowerCaseKey, value);
    if (lowerCaseKey === 'nombre') {
        this.$chatHead.find('label').text(value);
    }
};

ChatView.prototype._clearCompletedDataFromSettingsBar = function (key) {
    var $setData = this.$settings.find('span.' + key);

    // borrar separadores si es necesario
    if ($setData.prev().hasClass('separador')) {
        $setData.prev().remove();
    } else if ($setData.next().hasClass('separador')) {
        $setData.next().remove();
    }

    $setData.remove();
};

ChatView.prototype.addReceivedMessageToFeed = function (message, isReconstructingChatFromServerData) {
    isReconstructingChatFromServerData = isReconstructingChatFromServerData || false;
    if (!isReconstructingChatFromServerData) {
        if (this.isMinimized() || this.isUnfocused()) {
            this.markAsUnread();
            this._owner.playSound();
        } else { // I'm currently on the chat and I'm reading it
            this._model.evalMarkChatAsReadCallback();
        }
    }

    var isReceivingMessage = true;
    this._addMessageToFeed(message, isReceivingMessage, isReconstructingChatFromServerData);
};

ChatView.prototype.addSentProposalToFeed = function (text) {
    var fecha_actual = this._currentDateTimestamp();
    var message = {'texto': text, 'fecha': fecha_actual};
    var isReconstructing = false;
    var isReceivingMessage = false;
    this._addMessageToFeed(message, isReceivingMessage, isReconstructing);
};

ChatView.prototype.addSentMessageToFeed = function (message, isReconstructing) {
    if ($.trim(message.texto.length) > 0) {
        var isReceivingMessage = false;
        this._addMessageToFeed(message, isReceivingMessage, isReconstructing);
        if (!isReconstructing) {
            this._model.evalWriteMessageCallback(message.texto);
        }
        this.$writeMessageTextarea.val("");
    }
};

ChatView.prototype._addMessageToFeed = function (message, isReceivingMessage, isReconstructing) {
    this._model.addMessage(message);
    this.$feed.append(this._messageTemplateFor(message, isReceivingMessage));
    this.$feed.scrollTop(this.$feed[0].scrollHeight);
    if (!isReconstructing) {
        var storageManager = this._owner.storageManager();
        storageManager.addChatViewToStorage(this, storageManager.chatInformationIndexInStorage(this.model().id()));
    }
};

ChatView.prototype.isMinimized = function () {
    return this.$toggleArea.is(':hidden');
};

ChatView.prototype.hasBeenRead = function () {
    return !this.$chatHead.hasClass(this.UNREAD_MESSAGE_CHATHEAD_CLASS);
};

ChatView.prototype.isUnfocused = function () {
    return !this.$writeMessageTextarea.is(':focus')
};

ChatView.prototype.markAsUnread = function () {
    this.$chatHead.addClass(this.UNREAD_MESSAGE_CHATHEAD_CLASS);
};

ChatView.prototype.markAsRead = function () {
    this.$chatHead.removeClass(this.UNREAD_MESSAGE_CHATHEAD_CLASS);
};

ChatView.prototype.toggleMinimize = function (boolean) {
    this.$toggleArea.toggle(!boolean);
    var storageManager = this._owner.storageManager();
    storageManager.addChatViewToStorage(this, storageManager.chatInformationIndexInStorage(this.model().id()));
};

ChatView.prototype.toggleDialog = function (panel) {
    this.$toggleArea.toggle();
    this.$chatHead.toggle();
    panel.toggle();
};

ChatView.prototype._element_id = function () {
    return this._model.id();
};

ChatView.prototype._messageTemplateFor = function (message, isReceivingMessage) {
    var messageClass = 'me', messageName = '';
    if (isReceivingMessage) {
        messageClass = 'other';
        var name = this._model.getParameter('Nombre') ? this._model.getParameter('Nombre') : this.DEFAULT_NAME;
        messageName = name + ' • ';
    }

    return '<div class="' + messageClass + '">' +
        '<div class="message">' +
        message.texto +
        '<div class="meta">' +
        messageName + message.fecha +
        '</div>' +
        '</div>' +
        '</div>';
};

ChatView.prototype._bindEnterToWriteMessageBox = function () {
    var self = this;
    this.$writeMessageTextarea.on('keypress', function (event) {
        var key = event.which || event.keyCode;
        if (key === self.ENTER_KEY_CODE) {
            self._sendMessageFromBindings(event);
        }
    });
};

ChatView.prototype._bindFocusToMessageBoxToMarkChatAsRead = function () {
    var self = this;
    this.$writeMessageTextarea.on('focus', function () {
        if (!self.hasBeenRead()) {
            self._model.evalMarkChatAsReadCallback();
            self.markAsRead();
        }
    });
};

ChatView.prototype._bindClickToMessageFeedToTakeFocusToMessageBox = function () {
    var self = this;
    this.$feed.on('click', function () {
        self.$writeMessageTextarea.focus();
    });
};

ChatView.prototype._bindClickToImageSubmitButton = function () {
    var self = this;
    this.$imageSubmitButton.click(function (event) {
        self._sendMessageFromBindings(event);
    });
};

ChatView.prototype._bindClickToToChatHeadToMinimize = function () {
    this.$chatHead.click((function () {
        this.toggleMinimize(!this.isMinimized());
    }).bind(this));
};

ChatView.prototype._bindClickToClientDataButton = function () {
    this.$clientDataButton.click((function () {
        this._clientDataPanel.refresh();
        this.toggleDialog(this._clientDataPanel);
    }).bind(this));
    this._clientDataPanel.bindCloseButton((function () {
        this.toggleDialog(this._clientDataPanel);
    }).bind(this));
};

ChatView.prototype._bindClickToToggleSettingsDialog = function () {
    var self = this;
    this.$toggleSettingsDialogButton.click(function () {
        self.toggleSettingsDialog();
    });
    this.$toggleSettingsDialogButtonBack.click(function () {
        self.toggleSettingsDialog();
    });
};

ChatView.prototype._bindClickToCloseButton = function () {
    var self = this;
    this.$closeButton.click(function () {
        self.eraseChat();
    });
};

ChatView.prototype._sendMessageFromBindings = function (event) {
    event.preventDefault();
    this.whenSendMessageDo();
};

ChatView.prototype.toggleMinimize = function (boolean) {
    this.$toggleArea.toggle(!boolean);
    var storageManager = this._owner.storageManager();
    storageManager.addChatViewToStorage(this, storageManager.chatInformationIndexInStorage(this.model().id()));
};

ChatView.prototype.toggleSettingsDialog = function () {
    this.$toggleArea.toggle();
    this.$chatHead.toggle();
    this.$datosCliente.toggle();
};

ChatView.prototype._currentDateTimestamp = function () {
    var currentDateTime = new Date();
    return moment(currentDateTime).format('D/M/YYYY, H:mm');
};

ChatView.prototype.removeAllMessages = function (){
    this._model.removeAllMessages();
    this.$feed.empty();
};

var ChatProposalSendPanel = function (chatView, successSentCallback, propuestasParaChatUrl, envioDePropuestaUrl) {
    this._owner = chatView;
    this._model = this._owner.model();
    this._propuestasParaChatUrl = propuestasParaChatUrl;
    this._successSentCallback = successSentCallback;
    this._envioDePropuestaUrl = envioDePropuestaUrl;
    this.$proposals = this._owner.element().find('.proposals');
    this.$closeButton = this.$proposals.find('.toggle-chat');
    this.$sendButton = this.$proposals.find('.send-proposal-button');

    this._bindSendButton()
};

ChatProposalSendPanel.template = function () {
    return '<div class="proposals">' +
        '<div class="datosHead">' +
        '<label>Propuestas</label>' +
        '<button class="toggle-chat"></button>' +
        '</div>' +
        '<div class="content">' +
        '<select id="select-propuestas-via-chat" size="15" style="height: 255px; width: 272px; padding: 10px">' +
        '</select>' +
        '<button type="button" id="send-proposal-button" class="send-proposal-button" role="button" aria-disabled="false">' +
        '<span class="ui-button-text">Enviar Propuesta</span></button>' +
        '</div>' +
        '</div>'
};

ChatProposalSendPanel.prototype.bindCloseButton = function (closeCallback) {
    this.$closeButton.click(function () {
        closeCallback();
    });
};

ChatProposalSendPanel.prototype._bindSendButton = function () {
    this.$sendButton.click((function () {
        this._enviar();
    }).bind(this));
};

ChatProposalSendPanel.prototype.refresh = function () {
    this._fetchProposalsDo((function (proposals){
        var element = this.$proposals.find('#select-propuestas-via-chat');
        this._renderProposalsOptions(proposals, element);
    }).bind(this));
};

ChatProposalSendPanel.prototype.toggle = function () {
    this.$proposals.toggle();
};

ChatProposalSendPanel.prototype._enviar = function () {
    // Crear url con el id del chat, obtener el id de la propuesta, falta bind del button enviar
    var url = this._envioDePropuestaUrl.replace('0000', this._model.id());
    var propuestaId = this._proposalSelected(this._model.id());
    if (propuestaId == null) {
        notificador.notificarAdvertencia('Por favor seleccione una propuesta');
        return;
    }
    var data = {propuestaId: propuestaId};
    var jqxhr = $.post(url, $.param(data, true));
    jqxhr.done((function(data){
        if (data.status === 'Exito') {
            this._successSentCallback(data.text);
        } else {
            // me quedo esta relacion de conocimiento indebida, pero deberia modelar un Fetcher...
            notificador.notificarError(data.mensaje);
        }
    }).bind(this));
    jqxhr.fail(systemUnavailable);

};

ChatProposalSendPanel.prototype._proposalSelected = function (id_chat) {
    var element = $('#' + id_chat + ' #select-propuestas-via-chat');
    return element.val()
};

ChatProposalSendPanel.prototype._fetchProposalsDo = function (successCallback) {
    var url = this._propuestasParaChatUrl.replace('0000', this._model.id());
    var jqxhr = $.get(url);
    jqxhr.done(function(data){
        if (data.status)
            successCallback(data.proposals);
        else
            // me quedo esta relacion de conocimiento indebida, pero deberia modelar un Fetcher...
            notificador.notificarError(data.mensaje);
    });
    jqxhr.fail(systemUnavailable);
};

ChatProposalSendPanel.prototype._renderProposalsOptions = function (proposals, element) {
    var option;
    var optionTemplate = '<option class="propuestas-de-chat" value="{0}" title="{1}">{1}</option>';
    element.empty();
    proposals.forEach(function callback(proposal, index, array) {
        option = optionTemplate.format(proposal.id, proposal.titulo);
        element.append(option);
    });
};


var ChatClientDataPanel = function (chatView) {
    this._owner = chatView;
    this._model = this._owner.model();

    this.$datosCliente = this._owner.element().find('.datosCliente');
    this.$infoCliente = this.$datosCliente.find('.infoCliente');
    this.$closeButton = this.$datosCliente.find('.toggle-chat');
};

ChatClientDataPanel.template = function () {
    return '<div class="datosCliente">' +
        '<div class="datosHead">' +
        '<label>Datos de Contacto</label>' +
        '<button class="toggle-chat"></button>' +
        '</div>' +
        '<div class="infoCliente">' +
        '<ul>' +
        '<li><span class="estado nombre ver"></span><span class="dato">Nombre: <span class="nombre dato-faltante">Dato faltante</span></span></li>' +
        '<li><span class="estado ver"></span><span class="dato">Email: <strong class="falta">!</strong> <span class="email dato-faltante">Dato faltante</span></span></li>' +
        '<li><span class="estado ver"></span><span class="dato">Tel: <strong class="falta">!</strong> <span class="telefono dato-faltante">Dato faltante</span></span></li>' +
        '<li><span class="estado"></span><span class="dato">Lugar: <span class="location dato-faltante">Dato faltante</span></span></li>' +
        '<li><span class="estado"></span><span class="dato">Marca: <span class="marca dato-faltante">Dato faltante</span></span></li>' +
        '<li><span class="estado"></span><span class="dato">Modelo: <span class="modelo dato-faltante">Dato faltante</span></span></li>' +
        '</ul>' +
        '</div>' +
        '</div>'
};

ChatClientDataPanel.prototype.refresh = function () {};

ChatClientDataPanel.prototype.toggle = function () {
    this.$datosCliente.toggle();
};

ChatClientDataPanel.prototype.bindCloseButton = function (closeCallback) {
    this.$closeButton.click(function () {
        closeCallback();
    });
};

ChatClientDataPanel.prototype.addData = function (key, dataValue) {
    this._removeMissingData(key);
    var $infoClienteData = this.$infoCliente.find('ul li .dato span.' + key);
    if ($infoClienteData.length > 0){
        $infoClienteData.text(dataValue).removeClass('dato-faltante');
    } else {
        this.$infoCliente.find('ul').append(this._templateDataFor(key, dataValue))
    }
};

ChatClientDataPanel.prototype._templateDataFor = function (key, dataValue) {
    return '<li><span class="estado"></span><span class="dato">' + this._asLabel(key) +
        ': <span class="'+ this._asClassname(key).toLowerCase() +' dato-faltante">'+ dataValue +
        '</span></span></li>'
};

ChatClientDataPanel.prototype._asClassname = function (aString) {
    return aString.replaceAll(/\s/g, '').toLowerCase()
}

ChatClientDataPanel.prototype._asLabel = function (aString) {
    return this._capitalizeTheFirstLetterOfEachWord(aString.replaceAll('_', ' '))
}

ChatClientDataPanel.prototype._capitalizeTheFirstLetterOfEachWord = function (aString) {
    return aString.replace(/\w\S*/g, function (eachWord) {
        return (eachWord.replace(/^\w/, function(eachChar) {
            return eachChar.toUpperCase()
        }))
    });
}

ChatClientDataPanel.prototype._removeMissingData = function (key) {
    var $infoClienteData = this.$infoCliente.find('ul li .dato span.' + key);

    // borrar marca de faltante si es necesario
    if ($infoClienteData.prev().hasClass('falta')) {
        $infoClienteData.prev().remove();
    }
};


function goToHelpView() {
    window.location.replace(ayuda_chat_url);
}


var Chat = function (id, channel, isActive, writeMessageCallback, markChatAsReadCallback) {
    /*
    * Las instancias de Chat representan un chat en delivery, este puede ser de Meta o de Compulsas
    *  */
    this._id = id;
    this._channel = channel;
    this._isActive = isActive;
    this._messages = [];
    this.writeMessageCallback = writeMessageCallback || function (chatId, message) {
    };
    this.markChatAsReadCallback = markChatAsReadCallback || function (chatId) {
    };
    this.parameters = {};
};

Chat.CHANNEL_META = "Meta";
Chat.CHANNEL_COMPULSA = "Compulsa"


Chat.prototype.isActive = function () {
    return this._isActive;
};

Chat.prototype.id = function () {
    return this._id;
};

Chat.prototype.messages = function () {
    return this._messages;
};

Chat.prototype.amountOfMessages = function () {
    return this._messages.length;
};

Chat.prototype.addMessage = function (message) {
    this._messages.push(message);
};

Chat.prototype.removeAllMessages = function () {
    this._messages = []
}

Chat.prototype.evalWriteMessageCallback = function (message) {
    return this.writeMessageCallback(this.id(), message);
};

Chat.prototype.evalMarkChatAsReadCallback = function () {
    return this.markChatAsReadCallback(this.id());
};

Chat.prototype.getParameter = function (key) {
    return this.parameters[key];
};

Chat.prototype.setParameter = function (key, value) {
    this.parameters[key] = value;
};

Chat.prototype.hasChannelMeta = function () {
    return this._channel === this.constructor.CHANNEL_META
};

Chat.prototype.getChannel = function (){
    return this._channel
}

var ChatListBarViewStorageManager = function (chatListBarView) {
    this.chatListBarView = chatListBarView;
    this.retrieveInformationFromServerFn = function (chatView) {
    };
    this.storage = localStorage;
    this.storageKey = 'activeChatsInformation';
    if (this.activeChatsInformation() === null) {
        this.storeNewActiveChats([]);
    }
};

ChatListBarViewStorageManager.prototype.eraseStorage = function () {
    this.storage.removeItem(this.storageKey);
};

ChatListBarViewStorageManager.prototype.configureServerInformationRetrieval = function (retrieveInformationFromServerFn) {
    this.retrieveInformationFromServerFn = retrieveInformationFromServerFn;
};

ChatListBarViewStorageManager.prototype.storeNewActiveChats = function (newActiveChats) {
    // no array support in local/session storages, using JSON stringify/parse
    this.storage.setItem(this.storageKey, JSON.stringify(newActiveChats));
};

ChatListBarViewStorageManager.prototype.addChatInformationFromServer = function (chatId, writeMessageCallback, markChatAsReadCallback) {
    var chatInformationWithId = this.chatInformationWithId(chatId);
    var isActive = chatInformationWithId.isActive;
    var chatChannel = chatInformationWithId.channel;
    this.chatListBarView.addChat(chatId, chatChannel,isActive, writeMessageCallback, markChatAsReadCallback);
    var chatView = this.chatListBarView.chatViewWith(chatId);

    this.retrieveInformationFromServerFn(chatView);

    if (chatInformationWithId !== null) {
        chatView.toggleMinimize(chatInformationWithId.isMinimized);
        // TODO - cuando hay un nuevo mensaje y el usuario no está con la página abierta, no funciona que se ponga en verde al iniciar
        if (chatView.model().amountOfMessages() > chatInformationWithId.amountOfMessages) {
            this.addChatViewToStorage(chatView, this.chatInformationIndexInStorage(chatId));
        }
    }
};

ChatListBarViewStorageManager.prototype.addChat = function (chatId, chatIsActive, writeMessageCallback) {
    this.chatListBarView.addChat(chatId, Chat.CHANNEL_COMPULSA ,chatIsActive, writeMessageCallback);
    var chatView = this.chatListBarView.chatViewWith(chatId);
    this.addChatViewToStorage(chatView);
    return chatView
};

ChatListBarViewStorageManager.prototype.removeChatViewFromStorage = function (chatView) {
    var activeChatsInformation = this.activeChatsInformation();
    var chat = chatView.model();
    var index = this.chatInformationIndexInStorage(chat.id());
    if (index !== -1) {
        activeChatsInformation.splice(index, 1);
    }
    this.storeNewActiveChats(activeChatsInformation);
};


ChatListBarViewStorageManager.prototype.addChatViewToStorage = function (chatView, index) {
    var activeChatsInformation = this.activeChatsInformation();
    var chat = chatView.model();
    var chatInformation = {
        id: chat.id(),
        isActive: chat.isActive(),
        isMinimized: chatView.isMinimized(),
        hasBeenRead: chatView.hasBeenRead(),
        amountOfMessages: chat.amountOfMessages(),
        channel: chat.getChannel()
    };
    if (index === undefined || index === null) {
        activeChatsInformation.push(chatInformation);
    } else {
        activeChatsInformation[index] = chatInformation;
    }
    this.storeNewActiveChats(activeChatsInformation);
};

ChatListBarViewStorageManager.prototype.activeChatsInformation = function () {
    var stringifiedActiveChatsInformation = this.storage.getItem(this.storageKey);
    if (stringifiedActiveChatsInformation === null || stringifiedActiveChatsInformation === undefined) {
        return null;
    } else {
        return JSON.parse(stringifiedActiveChatsInformation);
    }
};

ChatListBarViewStorageManager.prototype.activeCompelsChatsInformation = function () {
    return this.activeChatsInformation().filter(function (chat){
        return chat.channel === Chat.CHANNEL_COMPULSA;
    })
}

ChatListBarViewStorageManager.prototype.activeMetaChatsInformation = function () {
    return this.activeChatsInformation().filter(function (chat){
        return chat.channel === Chat.CHANNEL_META;
    })
}

ChatListBarViewStorageManager.prototype.activeSalesChatsInformation = function (channel) {
    return this.activeChatsInformation().filter(function (chat){
        return chat.channel === channel;
    })
}

ChatListBarViewStorageManager.prototype.chatInformationIndexInStorage = function (chatId) {
    var activeChatsInformation = this.activeChatsInformation();
    for (var index = 0; index < activeChatsInformation.length; index++) {
        var activeChatInformation = activeChatsInformation[index];
        if (activeChatInformation.id === chatId) {
            return index
        }
    }
    return null;
};

ChatListBarViewStorageManager.prototype.chatInformationWithId = function (chatId) {
    return this.activeChatsInformation()[this.chatInformationIndexInStorage(chatId)];
};