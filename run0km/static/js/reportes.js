function inicializarFormDeProgramacionDeReportes() {

    $('#equipos').multiSelect();
    $('#' + opcion_de_staff).multiSelect();


    // AGREGAR VARIOS MAILS
    var max_fields = 10; //maximum input boxes allowed
    var wrapper = $(".cuentasCorreo"); //Fields wrapper
    var add_button = $(".add_field_button"); //Add button ID

    var x = 1; //initlal text box count
    $(add_button).click(function (e) { //on add input button click
        e.preventDefault();
        if (x < max_fields) { //max input box allowed
            x++; //text box increment
            $(wrapper).append('<div><label>Cuentas de mails:</label><input type="text" name="mails[]"/><a href="#" class="remove_field">Remove</a></div>'); //add input box
        }
    });

    $(wrapper).on("click", ".remove_field", function (e) { //user click on remove text
        e.preventDefault();
        $(this).parent('div').remove();
        x--;
    });
    // FIN AGREGAR VARIOS MAILS

    inicializarOpcionSeleccionStaff();
}

function inicializarOpcionSeleccionStaff(){
    mostrarTipoDeStaffSeleccionado();
    $('input[type=radio][name="tipo_de_staff"]').change(function () {
        mostrarTipoDeStaffSeleccionado();
    });

}

function mostrarTipoDeStaffSeleccionado(){
    var value = $('input[type=radio][name="tipo_de_staff"]:checked').val()
    if (value == 0 ) { //selecciono equipos
        $("#multiple_equipos").show();
        $("#multiple_" + opcion_de_staff).hide();
    }
    else {
        $("#multiple_" + opcion_de_staff).show();
        $("#multiple_equipos").hide();
    }
}

function abrirPopup(reporte_pk, url) {
    var url = url + reporte_pk;
    var jqxhr = $.get(url, function (data) {
        mostrarDatos(data);
    });
}

function mostrarDatos(data) {
    if (!data['status']) {
        alert('No se pudo realizar esta accion. Intente mas tarde nuevamente por favor.');
        return false;
    }

    var dialog = $("#mails_overlay");
    dialog.html(data['informacion']);
    var titulo = data['titulo'];

    dialog.dialog({
        title: titulo,
        autoOpen: false,
        height: 300,
        width: 500,
        modal: true
    });
    dialog.dialog("open");
}

function mostrarDialogo(titulo, id_selector){
    var dialog = $("#" + id_selector);

    dialog.dialog({
        title: titulo,
        autoOpen: false,
        height: 300,
        width: 500,
        modal: true
    });
    dialog.dialog("open");
}