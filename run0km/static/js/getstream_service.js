var GetStreamService = function (apiKey, apiId, feedName, userId, userFeedToken){
    this.client = stream.connect(apiKey, null, apiId);
    this.userFeed = this.client.feed(feedName, userId, userFeedToken);
    this.methodsForVerb = {};


    function successCallback() {
        console.log('now listening to changes in realtime');
    }

    function failCallback(data) {
        console.log('something went wrong, check the console logs');
        console.log(data);
    }

    var self = this;
    this.userFeed.subscribe(function (data){
        self._handleVerbs(data);
    }).then(successCallback, failCallback);
};

GetStreamService.prototype._handleVerbs = function (data){
    var lastActivity = data.new[0];
    var verb = lastActivity.verb;
    if (this.methodsForVerb.hasOwnProperty(verb)){
        this.methodsForVerb[verb](lastActivity);
    }
};

GetStreamService.prototype.handle = function (verb, withCallback){
    this.methodsForVerb[verb] = withCallback;
};

GetStreamService.prototype.stopHandling = function (verb){
    if (this.methodsForVerb.hasOwnProperty(verb)){
        delete this.methodsForVerb[verb];
    }
};
