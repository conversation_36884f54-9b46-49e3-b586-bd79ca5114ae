function cambiarPeriodo(option) {
    var periodo = $("#periodo").val();
    // SETEAR Fecha desde y fecha hasta
}

function inicializarSelectorSupervisor(){
    $('#supervisor').change(supervisorChange);
    supervisorChange();
}

function supervisorChange() {
    actualizarSelectDeEquipos();
    actualizarCampanias();
}

function campania_no_agregada(campanias_ya_agregadas, campania_nombre){
    for(var i=0; i < campanias_ya_agregadas.length; i++){
        if(campanias_ya_agregadas[i] == campania_nombre){
            return false;
        }
    }
    return true;
}

function actualizarCampaniasDeTodosLosSupervisores(){
    var campanias_ya_agregadas = [];
        $.each(campanias_por_supervisor, function (key, value) {
           var campanias_de_un_supervisor = value;
           for(var j=0; j<campanias_de_un_supervisor.length; j++){
                var campania_id = campanias_de_un_supervisor[j];
                var campania_nombre = nombres_de_campanias[campania_id];
                if(campania_no_agregada(campanias_ya_agregadas, campania_nombre)) {
                    $('#campania').append('<option value="' + campania_id + '">' + campania_nombre + '</option>');
                    campanias_ya_agregadas.push(campania_nombre);
                }
            }

        });
}
function actualizarCampaniaDeUnSupervisor(id_sup){
    var campanias_de_supervisor = campanias_por_supervisor[id_sup];
        for (var i=0; i<campanias_de_supervisor.length; i++) {
            var id_campania = campanias_de_supervisor[i];
            var nombre_campania = nombres_de_campanias[id_campania];
            $('#campania').append('<option value="' + id_campania + '">' + nombre_campania + '</option>');
        }
}

function actualizarCampanias() {
    var id_sup = $('#supervisor').val();
    $('#campania').empty().append('<option selected="selected" value="">Todos</option>');
    if (id_sup == 'todos_los_supervisores'){
        actualizarCampaniasDeTodosLosSupervisores();
    }else{
        actualizarCampaniaDeUnSupervisor(id_sup);

    }
}

function actualizarSelectDeEquiposDeTodosLosSupervisores(){
    $.each(supervisores, function(key, cada_supervisor){
        var equipos_de_supervisor = cada_supervisor[0];
        $('#equipo').empty().append('<option selected="selected" value="">Todos</option>');
        for (var i=0; i<equipos_de_supervisor.length; i++){
            var un_equipo = equipos_de_supervisor[i];
            $('#equipo').append('<option value="'+ un_equipo[0] +'">'+ un_equipo[1] +'</option>');
        }
        inicializarSelectorEquipo();
    });
}

function actualizarSelectDeEquiposDeUnSupervisor(id_sup){
    var supervisor = supervisores[id_sup];
        var equipos = supervisor[0];
        $('#equipo').empty().append('<option selected="selected" value="">Todos</option>');
        for (var i=0; i<equipos.length; i++){
            var equipo = equipos[i];
            $('#equipo').append('<option value="'+ equipo[0] +'">'+ equipo[1] +'</option>');
        }
        inicializarSelectorEquipo();
}

function actualizarSelectDeEquipos(){
    var id_sup = $('#supervisor').val();
    if (id_sup == 'todos_los_supervisores'){
        actualizarSelectDeEquiposDeTodosLosSupervisores();
    }else{
        actualizarSelectDeEquiposDeUnSupervisor(id_sup);
    }

}

function inicializarSelectorEquipo(){
    $('#equipo').change(actualizarSelectDeVendedores);
    actualizarSelectDeVendedores();
}

function actualizarSelectDeVendedoresDeTodosLosSupervisores(){
    $.each(supervisores, function(key, value){
       var data_equipos = supervisores[key][1];
       $.each(data_equipos, function(key, value){
            var un_equipo = value;
            if (un_equipo !== undefined) {
                for (var i = 0; i < un_equipo.length; i++) {
                    var vendedor = un_equipo[i];
                    $('#vendedor').append('<option value="' + vendedor[0] + '">' + vendedor[1] + '</option>');
                }
            }
       });
    });
}

function actualizarSelectDeVendedoresDeUnSupervisor(id_sup){
    if (typeof supervisores != 'undefined') {
           var data_equ = supervisores[id_sup][1];
        }
        else {
            data_equ = equipos
        }
        var id_eq = $('#equipo').val();
        if (id_eq == '') id_eq = 0;
        var equipo = data_equ[id_eq];
        if (equipo !== undefined) {
            for (var i = 0; i < equipo.length; i++) {
                var vendedor = equipo[i];
                $('#vendedor').append('<option value="' + vendedor[0] + '">' + vendedor[1] + '</option>');
            }
        }
}

function actualizarSelectDeVendedores(){
    var id_sup = $('#supervisor').val();
    $('#vendedor').empty().append('<option selected="selected" value="">Todos</option>');
    if(id_sup != 'todos_los_supervisores'){
        actualizarSelectDeVendedoresDeUnSupervisor(id_sup);
    }else{
        actualizarSelectDeVendedoresDeTodosLosSupervisores();
    }
}


function InicializarFiltrosDeFechas(){
    var formato_fecha = "yy-mm-dd";
    var fecha_actual = new Date();
    var primer_dia_mes_anterior = new Date(fecha_actual.getFullYear(), fecha_actual.getMonth() - 1, 1);
    var ultimo_dia_mes_anterior = new Date(fecha_actual.getFullYear(), fecha_actual.getMonth(), 0);
    var fecha_desde = $('#fecha_desde');
    fecha_desde.datepicker({dateFormat: formato_fecha});
    fecha_desde.datepicker("setDate", primer_dia_mes_anterior);
    var fecha_hasta = $('#fecha_hasta');
    fecha_hasta.datepicker({dateFormat: formato_fecha});
    fecha_hasta.datepicker("setDate", ultimo_dia_mes_anterior);
}

function filtrarDataResumen() {
    /*
    *   Depende del notificador, resumen_data_url, maneja_equipos
    * */

    var tipo_de_fecha = $("#tipo-de-fecha").val();
    var desde = $("#fecha_desde").val();
    var hasta = $("#fecha_hasta").val();
    if (desde != '' && hasta != '' && desde > hasta) {
        notificador.notificarAdvertencia("La fecha 'Desde' debe ser mayor a la fecha 'Hasta'");
        return false;
    }
    var tipo_origen = $("#tipo_origen").val();
    var campania = $("#campania").val();
    var distribuidor = $("#distribuidor").val();
    var marca = $("#marcas").val();
    var query_data = {
        'fecha_desde': desde,
        'fecha_hasta': hasta,
        'tipo_de_fecha': tipo_de_fecha,
        'tipo_origen': tipo_origen,
        'campania':campania,
        'distribuidor':distribuidor,
        'marca': marca
    };
    if (maneja_equipos){
        var vendedor = $("#vendedor").val();
        var equipo = $("#equipo").val();
        var supervisor = $("#supervisor").val();
        query_data['vendedor'] = vendedor;
        query_data['equipo'] = equipo;
        query_data['supervisor'] = supervisor;
    }

    var spinner_options_prospectos = {
        lines: 7, length: 7, width: 10, radius: 10, corners: 1, rotate: 0, direction: 1,
        color: '#00A', speed: 1, trail: 60, className: 'spinner',
        zIndex: 1,
        top: '5px',
        left: '3px'
    };
    var spinner_options_ventas = jQuery.extend({}, spinner_options_prospectos);
    var spinner_options_ranking = jQuery.extend({}, spinner_options_prospectos);
    spinner_options_ventas.left = "0px";
    spinner_options_ranking.top = "20px";

    var spinner_busqueda = AgregarSpinner('spin_holder');
    var spinner_prospectos = AgregarSpinner('spinner_prospectos', spinner_options_prospectos);
    var spinner_ventas = AgregarSpinner('spinner_ventas', spinner_options_ventas);
    var spinner_ranking = AgregarSpinner('spinner_ranking', spinner_options_ranking);
    var spinners = [spinner_busqueda, spinner_prospectos, spinner_ventas, spinner_ranking];

    var $botonBuscar = $('#form_filtro .boton-default');
    $botonBuscar.attr('disabled', 'disabled');
    $('#cant_prospectos').text("");
    $('#cant_ventas').text("");
    $('#ranking_num').text("");
    $('#ranking_den').text("");

    var url = resumen_data_url;
    var jqxhr = $.get(url, $.param(query_data, true), function (data) {
        mostrarDataResumen(data);
        for (var i = 0; i < spinners.length; i++) {
            spinners[i].stop();
            $botonBuscar.removeAttr('disabled');
        }
    });
    jqxhr.fail(systemUnavailable);
}

function mostrarDataResumen(data) {
    if (!data['status']) {
        notificador.notificarError('No se pudo realizar esta accion. Intente mas tarde nuevamente por favor.');
        return false;
    }
    $("#cant_prospectos").html(data['prospectos']);
    $("#cant_ventas").html(data['ventas']);
    $("#ranking_num").html(data['ranking_num']);
    $("#ranking_den").html(data['ranking_den']);
    $("#cant_activos").html(data['prospectos_activos']);
    $("#cant_nuevos").html(data['prospectos_nuevos']);
}

function verProspectosFiltrados(solo_llamados) {
    $("#form_filtro").submit();
}

function verProspectosFiltradosActivos(solo_llamados) {
    $("#filter_estado").val('activo');
    $("#form_filtro").submit();
}

function verProspectosFiltradosVendidos(solo_llamados) {
    $("#filter_estado").val('V');
    $("#form_filtro").submit();
}

function enfocar_calendario(){
    $("html, body").animate({ scrollTop: $('#calendario-llamadas').offset().top }, 1000);
}