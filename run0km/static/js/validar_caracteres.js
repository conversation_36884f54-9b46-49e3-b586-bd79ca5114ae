var ALLOWED_CHARACTERS = "@�$������LF��CR��?_?????????ESC����SP!�%&�()*+,-./0123456789:;<=>?�ABCDEFGHIJKLMNOPQRSTUVWXYZ���ܧ�abcdefghijklmnopqrstuvwxyz����� ";
var MAX_WITH = 160;

CharacterValidator = function(text_element, correct_element, image_element, static_url){
    this._text_element = text_element;
    this._correct_element = correct_element;
    this._image_element = image_element;
    this._static_url = static_url;
    this._valid_characters = ALLOWED_CHARACTERS;
    this._max_size = MAX_WITH;
};

CharacterValidator.prototype = {
    /* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
     * Public methods
     * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
    "validate": function(){
        this._chechAndCutMaxCaracters(this._text_element);
        var new_text = this._checkAndMarkCharacteres(this._text_element.val());
        this._update_correct_element(new_text);
    },

    /* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
    * Private methods
    * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */
    "_chechAndCutMaxCaracters": function(text_element) {
        var text = text_element.text();
        if (text.length >= this._max_size) {
            text = text.substring(0, this._max_size);
            text_element.val(text);
        }
    },
    "_checkAndMarkCharacteres": function(text) {
        var has_invalid = false;
        var strTextReturn = "";
        var strReplace;
        for (var each_char of  text) {
            if (this._is_valid(each_char)) {
                strReplace = each_char;
            }
            else { //no es un caracter correcto
                strReplace = "<span class='cssTextDivMessageError'>" + each_char + "</span>";
                has_invalid = true;
            }
            strTextReturn += strReplace;
        }
        this._update_image_element(has_invalid);
        return strTextReturn;
    },
    "_is_valid": function(character) {
        return this._valid_characters.indexOf(character) !== -1;
    },
    "_update_correct_element": function(text){
          this._correct_element.html(text);
    },
    "_update_image_element": function(has_invalid_characters){
        var url;
        if (has_invalid_characters)
            url = this._failure_url();
        else
            url = this._successfull_url();
        this._image_element.attr("src", url);
    },
    "_successfull_url": function() {
        return this._static_url + "sms-successful.png";
    },
    "_failure_url": function() {
        return this._static_url + "sms-failure.png";
    }
};


