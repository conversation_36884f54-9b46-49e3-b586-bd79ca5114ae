/**
 * Created by eryx on 23/12/16.
 */

function configurarModalDeTerminosYCondiciones(){
    var modal = $('#terminos-y-condiciones-modal-id');
    var abrir_modal = $('#abrir-modal-id');
    var cerrar_modal = $('.cerrar-terminos-y-condiciones-modal');
    abrir_modal.click(function(event){
        event.preventDefault();
        modal.show();

    });

    cerrar_modal.click(function(event){
        modal.hide();
    });

    window.onclick = function(event){
        if (modal.is(event.target)){
            modal.hide();
        }
    };
}

function submitConCampanias() {
    var form_carga_de_prospecto = $('#carga_de_prospecto');
    var campanias_del_prospecto = $('#tag_handler_campanias').tagHandler("getTags");
    $('<input />').attr('type', 'hidden').attr('name', "campanias_del_prospecto").attr('value', campanias_del_prospecto).appendTo(form_carga_de_prospecto);
    form_carga_de_prospecto.submit();
}

function cargarCampaniasSeleccionables(opciones, campania_seleccionada) {
    var selector = $('input[name="nombre_de_campania"]')[0].selectize;
    selector.clearOptions();
    for(i=0; i < opciones.length; i++){
        selector.addOption({nombre: opciones[i]})
    }
    if (campania_seleccionada && !selector.options[campania_seleccionada]){
        selector.addOption({nombre: campania_seleccionada})
    }
}

function cargarCampaniaSeleccionada(campania_seleccionada) {
   var selector = $('input[name="nombre_de_campania"]')[0].selectize;
   selector.addItem(campania_seleccionada);
}

function refrescarCampania(){
    var calidad = $('#id_calidad').val();
    var data = {calidad_pk: calidad};
    var jqxhr = $.get(this.refrescar_campanias_carga_prospecto_url, $.param(data, true));
    jqxhr.done(function(data){
        var campanias = data.campanias_seleccionables;
        if (data.status)
            cargarCampaniasSeleccionables(campanias);
    });
    jqxhr.fail(systemUnavailable);
}

function actualizarCalidadSegunCampaniaSeleccionada() {
    var campania = $('input[name="nombre_de_campania"]').val();
    var data = {campania_nombre: campania};
    var jqxhr = $.get(this.calidad_para_campania_url, $.param(data, true));
    jqxhr.done(function(data){
        var calidad_seleccionada = $('.carga_prospectos_calidad').val();
        var calidad_numerica = $("#calidad-numerica");
        if(data.calidad) {
            campania_creada = false;
            $('.carga_prospectos_calidad').val(data.calidad);
            calidad_numerica.rateYo("option", "readOnly", true);
            calidad_numerica.rateYo("rating", data.calidad_numerica);
        } else {
            calidad_numerica.rateYo("option", "readOnly", false);
            calidad_numerica.rateYo("rating", 0);
            if(campania) {
                notificador.notificarAdvertencia('Se le debe asignar un puntaje de 1 a 6 a la campaña seleccionada');
                campania_creada = true;
                if (calidad_seleccionada == '') {
                    notificador.notificarAdvertencia('Se esta creando una campaña sin elegir una calidad');
                }
            }
        }
    });
    jqxhr.fail(systemUnavailable);
}
