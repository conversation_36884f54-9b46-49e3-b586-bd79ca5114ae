var white_spinner_options = {
    lines: 7, length: 25, width: 3, radius: 3, corners: 1, rotate: 0, direction: 1,
    color: '#AAA', speed: 1, trail: 60, className: 'spinner',
    zIndex: 1, // The z-index (defaults to 1)
    top: '50px', // Top position relative to parent
    left: '245px' // Left position relative to parent
};

function pagina_siguiente() {
    var input_pagina = $('#pagina');
    var max = $('#max_pag').val();
    var pagina = parseInt(input_pagina.val()) + 1;
    if (pagina < 1 || pagina > max)
        pagina = max;
    input_pagina.val(pagina);

    var criterio = $('#id_criterio').val();
    $('.criterio_submit').val(criterio);
    var busqueda = $('#id_busqueda').val();
    $('.busqueda_submit').val(busqueda);
    var estado = $('#estado').val();
    $('.estado_submit').val(estado);
    var medio = $('#medio').val();
    $('.medio_submit').val(medio);

    $('#form_pagina').submit();
}

function pagina_anterior() {
    var input_pagina = $('#pagina');
    var max = $('#max_pag').val();
    var pagina = parseInt(input_pagina.val()) - 1;
    if (pagina < 1 || pagina > max)
        pagina = 1;
    input_pagina.val(pagina);
    $('#form_pagina').submit();
}


function abrir_conversacion(id_prospecto, tipo, conversacion_url) {
    $("#chat-window").html('<div id="chat-spinner" style="position: absolute;"></div>');

    $(".capaGris").fadeIn();
    var spinner = AgregarSpinner('chat-spinner', white_spinner_options);
    var url = conversacion_url.replace('/0000/', '/' + id_prospecto + '/').replace('/TIPO', '/' + tipo);
    var jqxhr = $.get(url, {});
    jqxhr.done(function (data) {
        spinner.stop();
        if (data.status) {
            var contenedor_chat = $("#chat-window");
            contenedor_chat.html(data['conversacion']);
            contenedor_chat.animate({scrollTop: contenedor_chat.prop("scrollHeight")}, 0);
            if (data.envio_deshabilitado)
                _deshabilitar_envio_whatsapp(data.envio_deshabilitado_motivo);
            else
                _habilitar_envio_whatsapp();
        } else {
            _mostrar_fallo_al_abrir_conversacion();
        }
    });
    jqxhr.fail(function () {
        spinner.stop();
        _mostrar_fallo_al_abrir_conversacion();
    });
}

function tituloDeConversacionPresionado(conversacionId, url, tipo_conversacion) {
    marcar_conversacion_como_leida(conversacionId, url, tipo_conversacion);
}

function marcar_conversacion_como_leida(conversacion_id, url, tipo_conversacion) {
    event.preventDefault();
    var jqxhr = $.post(marcar_conversacion_como_leida_url, $.param({
        conversacion_id: conversacion_id,
        tipo_conversacion: tipo_conversacion
    }));
    jqxhr.done(function (data) {
        window.location.href = url;
    });
    jqxhr.fail(systemUnavailable);
}


function cerrar_chat() {
    $(".capaGris").fadeOut();
}


function eliminar_conversacion(id_conversacion) {
    var dialog = $("#confirmar_eliminar");
    dialog.dialog({
        autoOpen: false,
        height: 180,
        width: 450,
        modal: true,
        buttons: {
            "Aceptar": {
                'text': 'Eliminar',
                'class': 'btn-aceptar',
                'click': function () {
                    ejecutar_eliminacion(id_conversacion);
                    dialog.dialog("close");
                }
            },
            "Cancelar": {
                'text': 'Cancelar',
                'class': 'btn-cancelar',
                'click': function () {
                    dialog.dialog("close");
                }
            }
        }
    });
    dialog.dialog("open");
}


function ejecutar_eliminacion(id_conversacion) {
    var jqxhr = $.post(eliminar_conversacion_url, $.param({'pk': id_conversacion}, true), function (data) {
        if (data.status) {
            _notificar_conversacion_eliminada(id_conversacion);
        } else {
            _mostrar_fallo_al_eliminar_conversacion();
        }
    });
    jqxhr.fail(_mostrar_fallo_al_eliminar_conversacion);
}

function _deshabilitar_envio_whatsapp(mensaje_motivo) {
    var nuevo_mensaje = $('#nuevo_mensaje');
    nuevo_mensaje.attr('disabled', 'disabled');
    nuevo_mensaje.val(mensaje_motivo);
    var enviar_mensaje_de_whatsapp = $('#enviar-mensaje-wa');
    enviar_mensaje_de_whatsapp.attr('disabled', 'disabled');
    enviar_mensaje_de_whatsapp.attr('title', mensaje_motivo);
}


function _habilitar_envio_whatsapp() {
    var nuevo_mensaje = $('#nuevo_mensaje');
    nuevo_mensaje.removeAttr('disabled');
    nuevo_mensaje.val('');
    var enviar_mensaje_de_whatsapp = $('#enviar-mensaje-wa');
    enviar_mensaje_de_whatsapp.removeAttr('disabled');
    enviar_mensaje_de_whatsapp.attr('title', 'Enviar Mensaje');
}


function _mostrar_fallo_al_abrir_conversacion() {
    cerrar_chat();
    notificador.notificarError('Error al abrir la conversación. Intente nuevamente más tarde.');
}


function _mostrar_fallo_al_enviar_mensaje() {
    notificador.notificarError('Hubo un problema al intentar enviar su mensaje. Intente nuevamente mas tarde.');
}

/*
*  Deprecado
* */
function enviar_mensaje_de_whatsapp(id_prospsecto) {
    var input_mensaje = $("#nuevo_mensaje");
    var mensaje = input_mensaje.val().trim();
    if (mensaje.length < 2) {
        notificador.notificarError('El mensaje es demasiado corto. Ingrese un mensaje mas largo.');
        return;
    }
    var contenedor_chat = $("#chat-window");
    contenedor_chat.animate({scrollTop: contenedor_chat.prop("scrollHeight")}, 500);
    var query_data = {'mensaje': mensaje, 'pk': id_prospsecto};
    // Spinner
    var jqxhr = $.post(enviar_mensaje_de_whatsapp_url, $.param(query_data, true), function (data) {
        if (data.status) {
            var id_conversacion = $("#id_conversacion").val();
            _mostrar_mensaje_agregado(id_conversacion, data);
        } else {
            _mostrar_fallo_al_enviar_mensaje();
        }
    });
    jqxhr.fail(_mostrar_fallo_al_enviar_mensaje);
}


/*
*  Deprecado
* */
function enviar_mensaje_de_email(id_prospsecto) {
    var input_mensaje = $("#nuevo_mensaje");
    var mensaje = input_mensaje.val().trim();
    if (mensaje.length < 2) {
        notificador.notificarError('El mensaje es demasiado corto. Ingrese un mensaje mas largo.');
        return;
    }
    var contenedor_chat = $("#chat-window");
    contenedor_chat.animate({scrollTop: contenedor_chat.prop("scrollHeight")}, 500);
    var query_data = {'mensaje': mensaje, 'pk': id_prospsecto};
    // Spinner
    var jqxhr = $.post(enviar_mensaje_via_email_url, $.param(query_data, true), function (data) {
        if (data.status) {
            var id_conversacion = $("#id_conversacion").val();
            _mostrar_mensaje_agregado(id_conversacion, data);
        } else {
            _mostrar_fallo_al_enviar_mensaje();
        }
    });
    jqxhr.fail(_mostrar_fallo_al_enviar_mensaje);
}


function _mostrar_mensaje_agregado(id_conversacion, data) {
    var contenedor_chat = $("#chat-activo");
    var chat = contenedor_chat.html();
    contenedor_chat.html(chat + data['mensaje']);
    $("#vista_previa_" + id_conversacion).html(data.vista_previa);
    $("#nuevo_mensaje").val('');
}


function _notificar_conversacion_eliminada(id_conversacion) {
    $("#contenedor_chat_" + id_conversacion).remove();
    notificador.notificarExito('La conversacion ha sido eliminada.');
}

function _mostrar_fallo_al_eliminar_conversacion() {
    notificador.notificarError('No se pudo eliminar la conversacion. Intente nuevamente mas tarde.');
}
