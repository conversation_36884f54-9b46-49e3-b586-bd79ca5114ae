GraficoSinAsignar = function(nombre_de_categoria, total, datos, canvas_id, colorMapper){
    this._nombreDeCategoria = nombre_de_categoria;
    this._datos = datos;
    this._total = total;
    this._canvasId = canvas_id;
    this._colorMapper = colorMapper;
    this._chart = null;
};



GraficoSinAsignar.prototype.render = function() {
    var labels = this._generarLabels(this._datos);
    var datasets = this._datasetsDesde(this._datos);
    var configuracion = this._configuracion(labels, datasets);
    var context = document.getElementById(this._canvasId).getContext('2d');
    this._chart = new Chart(context, configuracion);
    return this._chart
};

GraficoSinAsignar.prototype._configuracion = function(labels, datasets) {
    var self = this;
    var defaultLegendClickHandler = Chart.defaults.global.legend.onClick;
    var configuracion = {
        type: 'doughnut',
        data: {
            datasets: datasets,
            labels: labels
        },
        options: {
            responsive: true,
            legend: {
                position: 'right',
                onClick: function (evt, legendItem) {
                    // console.log('legend onClick', evt, legendItem);
                    // defaultLegendClickHandler.call(this, evt, legendItem);
                    var index = legendItem.index;
                    var chart = this.chart;

                    var centerConfig = chart.config.options.elements.center;
                    var cantidad = parseInt(centerConfig.text);
                    var i, ilen, meta, valor;

                    for (i = 0, ilen = (chart.data.datasets || []).length; i < ilen; ++i) {
                        meta = chart.getDatasetMeta(i);
                        valor = chart.data.datasets[i].data[index];

                        // toggle visibility of index if exists
                        if (meta.data[index]) {
                            if (meta.data[index].hidden) {
                                meta.data[index].hidden = false;
                                cantidad += valor;
                            }
                            else{
                                meta.data[index].hidden = true;
                                cantidad -= valor;
                            }
                        }
                    }
                    centerConfig.text = String(cantidad);
                    chart.update();
                }
            },
            title: { display: false },
            animation: {
                animateScale: true,
                animateRotate: true
            },
            pieceLabel: {
                render: 'value',
                fontSize: 14,
                fontStyle: 'bold',
                //fontColor: '#000',
                fontColor: function (data) {
                    var rgb = self._hexToRgb(data.dataset.backgroundColor[data.index]);
                    var threshold = 140;
                    var luminance = 0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b;
                    return luminance > threshold ? 'black' : 'white';
                },
                fontFamily: '"Lucida Console", Monaco, monospace'
            },
            elements: {
				center: {
					// the longest text that could appear in the center
					maxText: '100%',
					text: String(this._total),
					fontColor: '#36A2EB',
					fontFamily: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",
					fontStyle: 'normal',
					minFontSize: 1,
					maxFontSize: 32
				}
			}
        }
    };
    return configuracion;
};


GraficoSinAsignar.prototype._hexToRgb = function(rgbString) {
    var result = rgbString.substring(4, rgbString.length-1).replace(/ /g, '').split(',');
    return result ? {
        r: parseInt(result[0]),
        g: parseInt(result[1]),
        b: parseInt(result[2])
    } : null;
};

GraficoSinAsignar.prototype._generarLabels = function() {
    // var labels = this._marcas().map(function(key){ return new LabelMarca(key).texto();});
    // return labels;
    var label, texto, labels = [], self = this;
    this._marcas().forEach(function(marca, indice) {
        cantidad = self._datos[marca];
        texto = new LabelMarca(marca).texto();
        label = texto + " (" + cantidad.toString() + ")";
        labels.push(label);
    });
    return labels;
};

GraficoSinAsignar.prototype._marcas = function() {
    return Object.keys(this._datos);
};

GraficoSinAsignar.prototype._datasetsDesde = function(datos) {
    var dataset = { data: [], backgroundColor: [], label: 'Dataset 1'};
    var color, cantidad;
    var self = this;
    this._marcas().forEach(function(marca, indice){
        cantidad = datos[marca];
        dataset.data.push(cantidad);
        color = self._colorMapper.colorPara(marca);
        dataset.backgroundColor.push(color);
    });
    return [dataset]
};
