button {
    border: none;
    background: none;
    cursor: pointer;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
img.imagenTopPOCC {
    margin: 0 auto;
    display: block;
}
nav.solapas {
    text-align: center;
    background-image: url(../img/bg-nav-occ.jpg);
    background-position: top;
    background-repeat: repeat-x;
    padding-top: 8px;
    margin-bottom: 30px;
}
nav.solapas a {
    display: inline-block;
    width: 170px;
    text-transform: uppercase;
    color: #3e3e3e;
    padding-right: 28px;
    padding-left: 28px;
    padding-top: 8px;
    padding-bottom: 8px;
    font-size: 13px;
    line-height: 29px;
    background-image: url(../img/bt-nav-occ.png);
    background-position: center top;
    background-repeat: no-repeat;
    background-color: transparent;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease
}
nav.solapas a:hover, nav.solapas a.activo {
    background-image: url(../img/bt-nav-occ-activo.png);
    color: #fff;
}
#ProspectoOCC {
    display: block;
    min-height: 200px;
    background-image: url(../img/prospecto-OCC-temp.jpg);
    background-position: left top;
    background-repeat: no-repeat;
}
ul.opcionesDisponibles {
    display: block;
}
ul.opcionesDisponibles li {
    display: block;
    height: 135px;
    margin-bottom: 28px;
    padding-left: 301px;
    padding-top: 15px;
    font-weight: bold;
    font-family: Arial;
    box-sizing: border-box;
}
ul.opcionesDisponibles li.whatsApp {
    display: block;
    height: 135px;
    margin-bottom: 28px;
    background-image: url(../img/whatsApp-logo.jpg);
    background-position: left top;
    background-repeat: no-repeat;
    background-color: #F1F1F1;
}
ul.opcionesDisponibles li.smsMasivo {
    display: block;
    height: 135px;
    margin-bottom: 28px;
    background-image: url(../img/smsMasivo.jpg);
    background-position: left top;
    background-repeat: no-repeat;
    background-color: #F1F1F1;
}
ul.opcionesDisponibles li.encuesta {
    display: block;
    height: 135px;
    margin-bottom: 28px;
    background-image: url(../img/encuesta.jpg);
    background-position: left top;
    background-repeat: no-repeat;
    background-color: #F1F1F1;
}
ul.opcionesDisponibles li h1 {
    display: block;
    color: #2565b0;
    font-weight: bold;
    font-family: arial;
    font-size: 22px;
    line-height: 29px;
    height: 58px;
    text-transform: none;
}
ul.opcionesDisponibles li button {
    display: block;
    background-image: url(../img/botones/slide-boton.png);
    background-position: left top;
    background-repeat: no-repeat;
    background-color: transparent;
    border: 0;
    padding-left: 2px;
    margin-top: 7px;
    margin-left: -7px;
    height: 38px;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
ul.opcionesDisponibles li button:focus {
    outline: 0px;
}
ul.opcionesDisponibles li button label {
    display: block;
    padding-top: 5px;
    padding-left: 82px;
    line-height: 29px;
    font-size: 17px;
    color: #666;
    height: 38px;
    cursor: pointer;
    background-image: url(../img/botones/luz-gris.png);
    background-position: left top;
    background-repeat: no-repeat;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
ul.opcionesDisponibles li button.activado label {
    background-image: url(../img/botones/luz-verde.png);
    background-position: 38px top;
    background-repeat: no-repeat;
    color: #2a9700;
}
ul.opcionesDisponibles li button.activado label samp {
    display: none;
}

/* Campañas */
.formModelo {
    width: 450px;
    display: block;
    margin: -24px auto 30px;
}
#botonesTop {
    text-align: right;
}
#botonesTop a {
    display: inline-block;
    background-color: transparent;
    border: 0;
    padding-left: 30px;
    color: #2d6fb6;
    font-weight: bold;
    line-height: 24px;
    margin-right: 18px;
    margin-bottom: 9px;
    background-image: url(../img/botones/sumar-campana.png);
    background-position: left top;
    background-repeat: no-repeat;
}
#botonesTop a.verResumenCampanas {
    background-image: url(../img/botones/ver-resumen-campana.png);
    line-height: 29px;
}
.contenedor-lista .titulo-lista {
    background-color: #0076BC;
    box-shadow: inset 0px -2px 10px -3px #000;
}
.contenedor-lista .titulo-lista h3 {
    display: inline-block;
    font-size: 15px;
    line-height: 32px;
    margin-left: 41px;
    padding-left: 36px;
    margin-top: 3px;
    margin-bottom: 3px;
    color: #FFF;
    text-shadow: 1px 1px 1px #3A7C79;
    background-image: url(../img/flecha.png);
    background-repeat: no-repeat;
    background-position: left 3px;
}
.contenedor-lista {
    padding-bottom: 50px;
}
.contenedor-lista .lista table {
    display: block;
}
.contenedor-lista .lista table tr {
    border-bottom: 1px #999 solid;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease
}
.contenedor-lista .lista table tr:hover {
    background-color: #f8f8f8;
}
.contenedor-lista .lista table tr.titulo {
    background-color: #D5ECFC;
    border-bottom: 0;
}
.contenedor-lista .lista table td {
    width: 156px;
    text-align: center;
    color: #2d6fb6;
    font-size: 12px;
    line-height: 36px;
}
.contenedor-lista .lista table tr.titulo td button {
    vertical-align: middle;
    height: 13px;
    margin-left: 4px;
    background-color: transparent;
    border: 0;
    background-repeat: no-repeat;
    background-position: left 3px;
}

.contenedor-lista .lista table tr.titulo td button.asc {
    background-image: url(../img/botones/filtro-lista-up.png);
}

.contenedor-lista .lista table tr.titulo td button.desc {
    background-image: url(../img/botones/filtro-lista-down.png);
}



.contenedor-lista .lista table td button.excel {
    display: block;
    margin: 0 auto;
    height: 41px;
    width: 34px;
    background-image: url(../img/botones/hoja-de-calculo.png);
    background-repeat: no-repeat;
    background-position: left 4px;
}
.contenedor-lista .lista table td button.excel:hover {
    background-position: left top;
}

/* OCC - GENERADOR DE PROPUESTAS */
.contBotonesPropuestas {
    padding-top: 60px;
    padding-bottom: 150px;
    display: block;
    text-align: center;
}
.contBotonesPropuestas a {
    display: inline-block;
    vertical-align: top;
    width: 200px;
    padding-top: 193px;
    color: #2565b0;
    font-size: 14px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
.contBotonesPropuestas a.bt.misPropuestas {
    background-image: url(../img/botones/btn-misPropuestas.jpg);
    background-repeat: no-repeat;
    background-position: center 5px;
}
.contBotonesPropuestas a.bt.nuevaPropuesta {
    background-image: url(../img/botones/btn-nuevaPropuesta.jpg);
    background-repeat: no-repeat;
    background-position: center 5px;
}
.contBotonesPropuestas a.bt.miFirma {
    width: 226px;
    background-image: url(../img/botones/btn-miFirma.jpg);
    background-repeat: no-repeat;
    background-position: center 5px;
}
.contBotonesPropuestas a.bt.preferencias {
    background-image: url(../img/botones/btn-preferencias.jpg);
    background-repeat: no-repeat;
    background-position: center 5px;
}
.contBotonesPropuestas a.bt.misPropuestas:hover, .contBotonesPropuestas a.bt.nuevaPropuesta:hover, .contBotonesPropuestas a.bt.miFirma:hover, .contBotonesPropuestas a.bt.preferencias:hover {
    color: #1f4674;
    background-position: center top;
}

/* Mis propuestas */
.contenedor-lista-GP {
    padding-bottom: 40px;
    text-align: center;
}
.contenedor-lista-GP .titulo-lista {
    background-color: #0076BC;
    box-shadow: inset 0px -2px 10px -3px #000;
}
.contenedor-lista-GP .titulo-lista h3 {
    display: block;
    font-size: 15px;
    line-height: 32px;
    margin-top: 3px;
    margin-bottom: 3px;
    color: #FFF;
    text-shadow: 1px 1px 1px #3A7C79;
    text-align: center
}
.contenedor-lista-GP .lista {
    display: inline-block;
}
.contenedor-lista-GP .lista table {
    margin-top: 20px;
}
.contenedor-lista-GP .lista table tr {
    border-bottom: 1px #999 solid;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease
}
.contenedor-lista-GP .lista table tr:hover {
    background-color: #e2e2e2;
}
.contenedor-lista-GP .lista table tr.titulo {
    text-transform: uppercase;
    background-color: #D5ECFC;
    border-bottom: 0;
}
.contenedor-lista-GP .lista table td {
    min-width: 120px;
    height: 36px;
    text-align: left;
    color: #2d6fb6;
    font-size: 12px;
    line-height: 36px;
}
.contenedor-lista-GP .lista table td:nth-child(1) {
    min-width: 350px;
    padding-left: 20px;
}
.contenedor-lista-GP .lista table tr.titulo td button {
    float: right;
    vertical-align: middle;
    height: 13px;
    margin-top: 11px;
    margin-right: 12px;
    background-color: transparent;
    border: 0;
    background-image: url(../img/botones/filtro-lista.png);
    background-repeat: no-repeat;
    background-position: left 3px;
}

/* nueva propuesta */
.contenedor-lista-GP .espacio {
    height: 20px
}
.contenedor-lista-GP .izq, .contenedor-lista-GP .der {
    display: inline-block;
    vertical-align: top;
    width: 50%;
    box-sizing: border-box;
    padding: 18px 18px 18px 32px;
}
.contenedor-lista-GP .der {
    padding: 18px 32px 18px 18px;
}
.contenedor-lista-GP label {
    display: block;
    margin-bottom: 0px;
    padding: 5px 10px 5px 20px;
    font-size: 14px;
    color: #2d6fb6;
    line-height: 16px;
    text-align: left;
    font-weight: bold;
}
.contenedor-lista-GP select {
    display: block;
    width: 100%;
    height: 34px;
    margin-bottom: 40px;
    padding: 5px 10px 5px 15px;
    font-size: 12px;
    color: #2d6fb6;
    text-align: left;
    border: 1px #ddd solid;
    border-radius: 10px;
    outline: none;
    box-sizing: border-box;
}
.contenedor-lista-GP input[type="text"] {
    display: block;
    width: 100%;
    height: 34px;
    margin-bottom: 40px;
    padding: 5px 10px 5px 20px;
    font-size: 12px;
    color: #2d6fb6;
    text-align: left;
    border: 1px #ddd solid;
    border-radius: 10px;
    outline: none;
    box-sizing: border-box;
}
.mce-tinymce.mce-container.mce-panel,

/* Quitar border del editor */
.mce-edit-area.mce-container.mce-panel.mce-stack-layout-item.mce-first.mce-last {
    border-style: none;
    border-top: none;
    background-color: transparent;
}
.contEditorSimple {
    display: inline-block;
    width: 100%;
    padding: 5px 10px 5px 12px;
    border: 1px #ddd solid;
    border-radius: 10px;
    box-sizing: border-box;
}
.estiloEditorSimple {
    color: #2d6fb6;
    font-size: 12px;
    line-height: 16px;
}
./* Galeria de fotos */
.contGaleria {
    padding: 18px 32px 18px 32px;
    text-align: left;
}
.contGaleria .control{
    text-align: left;
    padding-top: 20px;
    padding-left: 33px;
}
.contGaleria .bt {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}
.contGaleria .bt-examinar {
    position: relative;
    overflow: hidden;
    color: #fff;
    background-color: #5cb85c;
    border-color: #4cae4c;
}
.contGaleria .bt-examinar:hover {
  color: #fff;
  background-color: #449d44;
  border-color: #398439;
}
.contGaleria .bt-examinar input[type=file] {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    font-size: 100px;
    text-align: right;
    filter: alpha(opacity=0);
    opacity: 0;
    outline: none;
    background: white;
    cursor: inherit;
    display: block;
}


.contGaleria .bt-examinar-react {
    position: relative;
    overflow: hidden;
    color: #fff;
    background-color: #5cb85c;
    border-color: #4cae4c;
}
.contGaleria .bt-examinar-react:hover {
  color: #fff;
  background-color: #449d44;
  border-color: #398439;
}

.contGaleria .bt-subir {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4;
}
.contGaleria .bt-subir:focus,
.contGaleria .bt-subir.focus {
  color: #fff;
  background-color: #286090;
  border-color: #122b40;
}
.contGaleria .bt-subir:hover {
  color: #fff;
  background-color: #286090;
  border-color: #204d74;
}
.contGaleria .bt-cancelar {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}
.contGaleria .bt-cancelar:focus,
.contGaleria .bt-cancelar.focus {
  color: #fff;
  background-color: #ec971f;
  border-color: #985f0d;
}
.contGaleria .bt-cancelar:hover {
  color: #fff;
  background-color: #ec971f;
  border-color: #d58512;
}
@font-face {
  font-family: 'Glyphicons Halflings';
  src: url('../fonts/glyphicons-halflings-regular.eot');
  src: url('../fonts/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'), url('../fonts/glyphicons-halflings-regular.woff2') format('woff2'), url('../fonts/glyphicons-halflings-regular.woff') format('woff'), url('../fonts/glyphicons-halflings-regular.ttf') format('truetype'), url('../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon-plus:before {
  content: "\002b";
}
.glyphicon-upload:before {
  content: "\e027";
}
.glyphicon-remove:before {
  content: "\e014";
}
.contGaleria .tabla{
    display: block;
    padding-top: 20px;
    width: 93%;
    margin: 0 auto;
}
.contGaleria .tabla tr{
    border-top: 1px #ccc solid;
    background-color: #f7f7f7;
}
.contGaleria .tabla tr td{
    padding: 5px;
    padding-top: 10px;
    vertical-align: top;
    color: #272727;
    font-size: 14px;
}
.contGaleria .tabla .fila td.foto{
    width: 3%;
    text-align: center;
}
.contGaleria .tabla .fila td.nombre{
    width: 30%;
    padding-right: 3%;
    text-align: left;
}
.contGaleria .tabla .fila td.peso{
    width: 8%;
    text-align: right;
}
.contGaleria .tabla .fila td.botonesControl{
    width: 13%;
    text-align: right;
}
/* Submit */
.contenedor-lista-GP .submitForm {
    margin-top: 40px;
}
.contenedor-lista-GP .submitForm button {
    display: inline-block;
    background-color: transparent;
    border: 0;
    line-height: 58px;
    width: 197px;
    padding-right: 31px;
    font-size: 15px;
    font-weight: bold;
    text-transform: uppercase;
    color: #fff;
    background-image: url(../img/botones/aceptar-gp.png);
    background-position: center 3px;
    background-repeat: no-repeat;
}
.contenedor-lista-GP .submitForm button.rechazar {
    background-image: url(../img/botones/rechazar-gp.png);
    background-position: center 3px;
    background-repeat: no-repeat;
}
.contenedor-lista-GP .submitForm button.publicar {
    background-image: url(../img/botones/publicar-gp.png);
    background-position: center 3px;
    background-repeat: no-repeat;
}
.contenedor-lista-GP .submitForm button.publicar[disabled] {
    background-image: url(../img/botones/publicar-gp-deshabilitado.png);
}
.contenedor-lista-GP .submitForm button.siguiente {
    background-image: url(../img/botones/siguiente-gp.png);
    background-position: center 3px;
    background-repeat: no-repeat;
}
.contenedor-lista-GP .submitForm button.siguiente[disabled] {
    background-image: url(../img/botones/siguiente-gp-deshabilitado.png);
}

.contenedor-lista-GP .submitForm button.publicar-propuesta {
    width: 370px;
    background-image: url(../img/botones/publicar-propuesta-gp.png);
    background-position: center 3px;
    background-repeat: no-repeat;
}


.contenedor-lista-GP .submitForm button:hover {
    background-position: 22px 3px;
}
.contenedor-lista-GP .submitForm button.siguiente-disabled {
    background-image: url(../img/botones/siguiente-gp-deshabilitado.png);
    background-position: center 3px;
    background-repeat: no-repeat;
}


.photo-actions-enter {
  opacity: 0.01;
}

.photo-actions-enter.photo-actions-enter-active {
  opacity: 1;
  transition: opacity 500ms ease-in;
}

.photo-actions-leave {
  opacity: 1;
}

.photo-actions-leave.photo-actions-leave-active {
  opacity: 0.01;
  transition: opacity 300ms ease-in;
}

.format-section {
    fontSize: 10pt;
    color: #000000;
}

.format-section p {
    fontSize: 10pt;
    color: #000000;
}


.contEditorSimple {
    min-height: 4em;
    font-size: 12px;
}

.DraftEditor-root ul {
    padding-left: 25px;
    margin: 0px;
}

.DraftEditor-root ol {
    padding-left: 25px;
    margin: 0px;
}
