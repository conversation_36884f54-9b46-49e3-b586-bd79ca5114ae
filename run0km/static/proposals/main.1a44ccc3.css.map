{"version": 3, "sources": ["index.css", "../node_modules/draft-js-static-toolbar-plugin/lib/plugin.css", "css/email-editor.css", "css/main.css"], "names": [], "mappings": "AAAA,KACE,SACA,UACA,sBAAwB,CCH1B,sCACE,oBAAsB,CAGxB,+BACE,mBACA,WACA,eACA,SACA,gBACA,sBACA,YACA,UAAY,CAGd,mCACE,SAAW,CAGb,0EACE,mBACA,SAAW,CAGb,+BACE,mBACA,UAAY,CAGd,mCACE,SAAW,CAEb,kCACE,qBACA,4BACA,YACA,aAAgB,CAElB,gCACE,sBACA,gBACA,kBACA,uCACQ,+BACR,UACA,8BACQ,qBAAuB,CAGjC,sCACE,+BACA,sBACA,iBACA,gBAAkB,CAEpB,uCACE,8BACA,sBACA,iBACA,gBAAkB,CC3DpB,mCACE,qBAAwB,CAE1B,gCACE,4BACA,2EACQ,mEACR,WACA,sBACA,8BACQ,sBACR,YACA,eACA,gBACA,kBACA,iBACA,iBAAmB,CACnB,iEACE,kBACA,MACA,QACA,YAAc,CACd,sEACE,gBAAkB,CAExB,uCACE,eAAiB,CAEnB,gCACE,UAAY,CAEd,gCACE,sBACA,eACA,WACA,YACA,MACA,OACA,SACA,oBACA,aACA,0BACI,qBAAuB,CAE7B,6BACE,4BACA,2EACQ,mEACR,WACA,sBACA,8BACQ,sBACR,YACA,eACA,gBACA,kBACA,iBACA,iBAAmB,CACnB,2DACE,kBACA,MACA,QACA,YAAc,CACd,gEACE,gBAAkB,CAExB,oCACE,eAAiB,CAEnB,6BACE,WACA,aACI,SACJ,2BACI,iBAAmB,CC1EzB,KACE,kBACA,iBACA,eACA,gBACA,gBACA,uBACA,kBACA,mBACA,sBACA,eACA,4BAA8B,CAEhC,YACE,yBACA,qBACA,UAAa,CAEf,aACE,yBACA,qBACA,UAAa,CAEf,YACE,qBACA,qBACA,UAAa,CAEf,gBACE,UAAY,CACZ,sBACE,kBACA,gBAAkB,CAKpB,2DAHE,wBACA,eACA,gBAAkB,CAKpB,iCACE,iBAAmB,CACnB,mCACE,qBACA,mCAAsC,CAC1C,8BACE,eAAiB,CACnB,gCACE,cAAgB,CAClB,iCACE,cAAgB,CAEpB,4BACE,YAAc", "file": "static/css/main.1a44ccc3.css", "sourcesContent": ["body {\n  margin: 0;\n  padding: 0;\n  font-family: sans-serif;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./src/index.css", ".draftJsToolbar__buttonWrapper__1Dmqh {\n  display: inline-block;\n}\n\n.draftJsToolbar__button__qi1gf {\n  background: #fbfbfb;\n  color: #888;\n  font-size: 18px;\n  border: 0;\n  padding-top: 5px;\n  vertical-align: bottom;\n  height: 34px;\n  width: 36px;\n}\n\n.draftJsToolbar__button__qi1gf svg {\n  fill: #888;\n}\n\n.draftJsToolbar__button__qi1gf:hover, .draftJsToolbar__button__qi1gf:focus {\n  background: #f3f3f3;\n  outline: 0; /* reset for :focus */\n}\n\n.draftJsToolbar__active__3qcpF {\n  background: #efefef;\n  color: #444;\n}\n\n.draftJsToolbar__active__3qcpF svg {\n  fill: #444;\n}\n.draftJsToolbar__separator__3U7qt {\n  display: inline-block;\n  border-right: 1px solid #ddd;\n  height: 24px;\n  margin: 0 0.5em;\n}\n.draftJsToolbar__toolbar__dNtBH {\n  border: 1px solid #ddd;\n  background: #fff;\n  border-radius: 2px;\n  -webkit-box-shadow: 0px 1px 3px 0px rgba(220,220,220,1);\n          box-shadow: 0px 1px 3px 0px rgba(220,220,220,1);\n  z-index: 2;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n}\n\n.draftJsToolbar__toolbar__dNtBH:after {\n  border-color: rgba(255, 255, 255, 0);\n  border-top-color: #fff;\n  border-width: 4px;\n  margin-left: -4px;\n}\n.draftJsToolbar__toolbar__dNtBH:before {\n  border-color: rgba(221, 221, 221, 0);\n  border-top-color: #ddd;\n  border-width: 6px;\n  margin-left: -6px;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./node_modules/draft-js-static-toolbar-plugin/lib/plugin.css", "#proposal-editor-beefree-container {\n  background-color: white; }\n\n#proposal-editor-beefree-header {\n  position: relative !important;\n  -webkit-box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);\n          box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);\n  width: 100%;\n  background-color: white;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  height: 55px;\n  font-size: 18pt;\n  font-weight: 700;\n  text-align: center;\n  padding-top: 10px;\n  padding-left: 20px; }\n  #proposal-editor-beefree-header #proposal-editor-beefree-actions {\n    position: absolute;\n    top: 0px;\n    right: 0;\n    padding: 15px; }\n    #proposal-editor-beefree-header #proposal-editor-beefree-actions span {\n      margin-left: 20px; }\n\n#proposal-editor-beefree-header.loaded {\n  text-align: left; }\n\n#proposal-editor-beefree-editor {\n  width: 100%; }\n\n#beefree-email-editor-container {\n  background-color: white;\n  position: fixed;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-direction: column;\n      flex-direction: column; }\n\n#beefree-email-editor-header {\n  position: relative !important;\n  -webkit-box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);\n          box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);\n  width: 100%;\n  background-color: white;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  height: 55px;\n  font-size: 18pt;\n  font-weight: 700;\n  text-align: center;\n  padding-top: 10px;\n  padding-left: 20px; }\n  #beefree-email-editor-header #beefree-email-editor-actions {\n    position: absolute;\n    top: 0px;\n    right: 0;\n    padding: 15px; }\n    #beefree-email-editor-header #beefree-email-editor-actions span {\n      margin-left: 20px; }\n\n#beefree-email-editor-header.loaded {\n  text-align: left; }\n\n#beefree-email-editor-editor {\n  width: 100%;\n  -ms-flex: 1 1;\n      flex: 1 1;\n  -ms-flex-item-align: center;\n      align-self: center; }\n\n\n\n// WEBPACK FOOTER //\n// ./src/css/email-editor.css", ".btn {\n  border-radius: 4px;\n  padding: 6px 12px;\n  font-size: 14px;\n  font-weight: normal;\n  margin-bottom: 0px;\n  line-height: 1.42857143;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: 1px solid transparent; }\n\n.btn-enable {\n  background-color: #5cb85c;\n  border-color: #4cae4c;\n  color: white; }\n\n.btn-disable {\n  background-color: #f0ad4e;\n  border-color: #eea236;\n  color: white; }\n\n.btn-danger {\n  background-color: red;\n  border-color: darkred;\n  color: white; }\n\ntable.proposals {\n  width: 100%; }\n  table.proposals tr td {\n    padding-left: 10px;\n    line-height: 18px; }\n  table.proposals tr td.model {\n    min-width: 10% !important;\n    max-height: 30%;\n    line-height: 18px; }\n  table.proposals tr td.proposal {\n    min-width: 10% !important;\n    max-height: 30%;\n    line-height: 18px; }\n  table.proposals tr td.classified {\n    text-align: center; }\n    table.proposals tr td.classified a {\n      color: blue !important;\n      text-decoration: underline !important; }\n  table.proposals tr td.enabled {\n    min-width: 100px; }\n  table.proposals tr td.published {\n    min-width: 80px; }\n  table.proposals tr td.operations {\n    min-width: 60px; }\n\n#proposal-description-field {\n  height: 300px; }\n\n\n\n// WEBPACK FOOTER //\n// ./src/css/main.css"], "sourceRoot": ""}