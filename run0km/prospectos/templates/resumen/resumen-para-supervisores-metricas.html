{% load prospectos_utils %}
{% load widget_tweaks %}
{% load static from staticfiles %}

<div id="reporte" class="panel">
    <div class="cuerpo">
        <div class="container">
            <form class="row form_filtro" id="form-integrantes-equipo">
                <div class="col-5">
                    <div class="form-group row">
                        {{ integrantes_de_equipo_form.equipo|add_label_class:'col-form-label col-form-label-sm col-3' }}
                        <div class="col-9">
                            {{ integrantes_de_equipo_form.equipo|add_class:'agencia form-control form-control-sm' }}
                        </div>
                    </div>
                </div>

                <div class="col-5">
                    <div class="form-group row">
                            {{ integrantes_de_equipo_form.vendedor|add_label_class:'col-form-label col-form-label-sm col-3' }}
                        <div class="col-9">
                             {{ integrantes_de_equipo_form.vendedor|add_class:'agencia form-control form-control-sm' }}
                        </div>
                    </div>
                </div>

                <div class="col-2">
                    <input class="boton-default transparente"
                           type="button" id="filtrar-integrantes-equipo" value="filtrar"/>
                    <div id="spin-holder-filtrar-integrantes-equipo" class="spin-holder"></div>
                </div>
            </form>
        </div>

        {% include "resumen/resumen-metricas-principales.html" %}

        <div id="metricas-estado-prospectos-detalle" class="metrica-detalle" style="display: none">
            <div class="py-2 justify-content-end d-flex">
                <input class="boton-default transparente" type="button"
                       value="cerrar" onclick="$('#metricas-estado-prospectos-detalle').slideToggle('slow');">
            </div>
            <div id="metricas-estado-prospectos-detalle-contenido"></div>

        </div>
        <div class="container">
            <form class="row form_filtro" id="form-rango-de-fecha">
                <div class="col-5">
                    <div class="form-group row">
                        {{ rango_de_fechas_form.fecha_desde|add_label_class:'fecha_desde col-form-label col-form-label-sm col-3' }}
                        <div class="col-9">
                            {{ rango_de_fechas_form.fecha_desde|attr:"autocomplete:off"|add_class:'date form-control form-control-sm' }}
                        </div>
                    </div>
                </div>

                <div class="col-5">
                    <div class="form-group row">
                        {{ rango_de_fechas_form.fecha_hasta|add_label_class:'fecha_hasta col-form-label col-form-label-sm col-3' }}
                        <div class="col-9">
                            {{ rango_de_fechas_form.fecha_hasta|attr:"autocomplete:off"|add_class:'date form-control form-control-sm' }}
                        </div>
                    </div>
                </div>

                <div class="col-2">
                    <input class="boton-default transparente"
                           type="button" id="filtrar-rango-de-fechas" value="filtrar"/>
                    <div id="spin-holder-filtrar-rango-de-fechas" class="spin-holder"></div>
                </div>
            </form>
        </div>
        <div id="metricas-generales">
            <div class="container">
                <div class="metrica">
                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip" data-placement="right" title="Expandir información" id="metrica-tiempos-de-demora-en-finalizar">
                        <div class="valor text-center align-self-center col-3"
                             id="metrica-tiempos-de-demora-en-finalizar-valor">-
                            <p class="unidad"></p>
                        </div>
                        <p class="descripcion text-left align-self-center col-9">Tiempo promedio de demora
                            en finalizar un prospecto</p>
                        <div id="spin-tiempo-de-demora" class="spin-holder spin-holder-metrica"></div>
                    </div>
                    <div id="metrica-tiempos-de-demora-en-finalizar-contenido" class="metrica-detalle" style="display: none"></div>
                </div>
                <div class="metrica">
                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip" data-placement="right" title="Expandir información" id="metrica-tiempo-de-respuesta">
                        <div class="valor text-center align-self-center col-3" id="metrica-tiempos-de-respuesta-valor">-
                            <p class="unidad"></p>
                        </div>
                        <p class="descripcion text-left align-self-center col-9">Tiempo promedio de respuesta</p>
                        <div id="spin-tiempo-de-respuesta" class="spin-holder spin-holder-metrica"></div>
                    </div>
                    <div id="metrica-tiempo-de-respuesta-contenido" class="metrica-detalle" style="display: none"></div>
                </div>
{#                // Por ahora no mostramos informacion sobre la productividad de chat   #}
{#                <div class="metrica">#}
{#                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip" data-placement="right" title="Expandir información" id="metrica-conversiones-de-chat">#}
{#                        <div class="valor text-center align-self-center col-3" id="metrica-conversiones-de-chats-valor">#}
{#                            -#}
{#                        </div>#}
{#                        <p class="descripcion text-left align-self-center col-9">Productividad de Chat</p>#}
{#                        <div id="spin-conversiones-de-chat" class="spin-holder spin-holder-metrica"></div>#}
{#                    </div>#}
{#                    <div id="metrica-conversiones-de-chat-contenido" class="metrica-detalle" style="display: none"></div>#}
{#                </div>#}
                <div class="metrica">
                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip" data-placement="right" title="Expandir información" id="metrica-datos-recibidos">
                        <div class="valor text-center align-self-center col-3" id="metrica-datos-recibidos-valor">-
                        </div>
                        <p class="descripcion text-left align-self-center col-9">Datos Recibidos</p>
                        <div id="spin-datos-recibidos" class="spin-holder spin-holder-metrica"></div>
                    </div>
                     <div id="metrica-datos-recibidos-contenido" class="metrica-detalle" style="display: none"></div>
                </div>
                <div class="metrica">
                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip" data-placement="right" title="Expandir información" id="metrica-ventas">
                        <div class="valor text-center align-self-center col-3" id="metrica-ventas-valor">-</div>
                        <p class="descripcion text-left align-self-center col-9">Cantidad de Ventas</p>
                        <div id="spin-ventas" class="spin-holder spin-holder-metrica"></div>
                    </div>
                    <div id="metrica-ventas-contenido" class="metrica-detalle" style="display: none">
                        <div id="metrica-ventas-contenido-grilla"></div>
                    </div>
                </div>
                <div class="metrica">
                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip"
                         data-placement="right" title="Expandir información" id="metrica-productividad-general">
                        <div class="valor text-center align-self-center col-3" id="metrica-productividad-general-valor">
                            -
                        </div>
                        <p class="descripcion text-left align-self-center col-9">Productividad en ventas</p>
                        <div id="spin-productividad-general" class="spin-holder spin-holder-metrica"></div>
                    </div>
                    <div id="metrica-productividad-general-contenido" class="metrica-detalle" style="display: none"></div>
                </div>
                <div class="metrica">
                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip"
                         data-placement="right" title="Expandir información" id="metrica-prospectos-finalizados">
                        <div class="valor text-center align-self-center col-3" id="metrica-prospectos-finalizados-valor">
                            -
                        </div>
                        <p class="descripcion text-left align-self-center col-9">Prospectos finalizados</p>
                        <div id="spin-prospectos-finalizados" class="spin-holder spin-holder-metrica"></div>
                    </div>
                    <div id="metrica-prospectos-finalizados-contenido" class="metrica-detalle" style="display: none"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="metricas-otros" class="panel">
    <div class="titulo" data-toggle="collapse" data-target="#cuerpo-otros" aria-expanded="true"
         aria-controls="cuerpo-otros">Otros
    </div>
    <div class="cuerpo collapse show" id="cuerpo-otros">
        <div id="metricas-generales">
            <div class="container">
                <div class="metrica">
                    <div class="metrica-header metrica-header-expansible row py-2" data-toggle="tooltip" data-placement="right" title="Expandir información" id="metrica-usos-de-app-mobile">
                        <div class="valor text-center align-self-center col-3">
                            <img src="{% static 'img/resumen/logo-run0km-one.png' %}" alt="Run0km One"/>
                        </div>
                        <p class="descripcion text-left align-self-center col-9">Uso de la APP</p>
                        <div id="spin-uso-de-la-app" class="spin-holder spin-holder-metrica"></div>
                    </div>
                    <div id="metrica-usos-de-app-mobile-contenido" class="metrica-detalle" style="display: none"></div>
                </div>
                <div class="metrica ">
                    <div class="enlace-externo metrica-header row py-2" data-toggle="tooltip" data-placement="right" title="Ir a reportes">
                        <a href="{% url 'reportes-online' %}" class="row w-100">
                            <div class="valor text-center align-self-center col-3">
                                <img
                                        src="{% static 'img/resumen/metricas-reportes-flecha.png' %}"
                                        alt="Reportes"/></div>
                            <p class="descripcion text-left align-self-center col-9">Reportes</p>
                        </a>
                    </div>
                </div>
                <div class="metrica ">
                    <div class="enlace-externo metrica-header row py-2" data-toggle="tooltip" data-placement="right" title="Ir al reporte de llamados">
                        <a href="{% url 'llamadas-realizadas-anura' %}" class="row w-100">
                            <div class="valor text-center align-self-center col-3">
                                <img
                                        src="{% static 'img/resumen/icono-escuchas.png' %}"
                                        alt="Reportes"/></div>
                            <p class="descripcion text-left align-self-center col-9">Análisis de Llamados</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>