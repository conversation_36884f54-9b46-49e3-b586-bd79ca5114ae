

from django.db import migrations

from prospectos.utils.normalizador_de_marcas import NormalizadorDeMarcas


def asociar_marcas_viejas_a_nuevo_modelo(apps, schema_editor):
    Prospecto = apps.get_model("prospectos", "Prospecto")
    prospectos_con_marca_antigua = Prospecto.objects.exclude(marca='')
    normalizador = NormalizadorDeMarcas()
    prospectos = prospectos_con_marca_antigua[:100000]
    for prospecto in prospectos:
        marca_vieja = prospecto.marca
        prospecto.marca = ''
        marca = normalizador.normalizar(marca_vieja)
        prospecto._marca_id = marca.id
        prospecto.save()
    print('\nRestantes %d' % Prospecto.objects.exclude(marca='').count())


def undo_asociar_marcas_viejas_a_nuevo_modelo(apps, schema_editor):
    Marca = apps.get_model("prospectos", "Marca")
    marcas = Marca.objects.filter(_esta_normalizada=False)
    marcas.delete()


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0109_migrar_marcas_normalizadas'),
    ]

    operations = [
        migrations.RunPython(asociar_marcas_viejas_a_nuevo_modelo, undo_asociar_marcas_viejas_a_nuevo_modelo)
    ]