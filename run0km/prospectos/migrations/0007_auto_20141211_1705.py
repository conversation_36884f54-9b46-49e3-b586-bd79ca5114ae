# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0006_auto_20141020_1543'),
    ]

    operations = [
        migrations.CreateModel(
            name='Proveedor',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('source_id', models.CharField(unique=True, max_length=100, verbose_name=b'ID')),
                ('nombre', models.CharField(max_length=100, blank=True)),
            ],
            options={
                'verbose_name_plural': 'proveedores',
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='prospecto',
            name='proveedor',
            field=models.ForeignKey(related_name='prospectos', on_delete=django.db.models.deletion.SET_NULL, to='prospectos.Proveedor', null=True),
            preserve_default=True,
        ),
    ]
