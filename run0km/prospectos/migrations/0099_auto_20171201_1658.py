# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-12-01 19:58


from django.db import migrations
from django.db.models import Count


def borrar_llamadas_realizadas_repetidas(apps, schema_editor):
    LlamadaRealizada = apps.get_model("prospectos", "LlamadaRealizada")
    llamadas_repetidas = LlamadaRealizada.objects.values('fecha_comienzo', 'prospecto').annotate(
        cantidad=Count('fecha_comienzo')).filter(cantidad__gt=1)
    for data_llamados in llamadas_repetidas:
        LlamadaRealizada.objects.filter(prospecto=data_llamados['prospecto'],
                                        fecha_comienzo=data_llamados['fecha_comienzo']).order_by(
            '-duracion').first().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('prospectos', '0098_auto_20171211_1128'),
    ]

    operations = [
        migrations.RunPython(borrar_llamadas_realizadas_repetidas),
    ]
