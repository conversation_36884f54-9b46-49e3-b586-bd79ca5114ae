# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-06-15 15:55


from django.db import migrations
from django.db import models
from django.utils import timezone
from concesionarias.rango_laboral import CalendarioLaboral


def tiempo_de_respuesta_para_prospectos_de_ultimo_mes(apps, schema_editor):
    rango_laboral = CalendarioLaboral.default()
    Prospecto = apps.get_model("prospectos", "Prospecto")
    hasta = timezone.localtime(timezone.now())
    desde = hasta + timezone.timedelta(days=-60)

    for prospecto in Prospecto.objects.filter(fecha_creacion__gte=desde, fecha_creacion__lte=hasta).select_related('vendedor'):
    #for prospecto in Prospecto.objects.all().select_related('vendedor'):
        #.prefetch_related('comentarios', 'comentarios__vendedor'):
        comentarios_de_prospecto = prospecto.comentarios.filter(vendedor=prospecto.vendedor).order_by('datetime')
        primer_comentario = comentarios_de_prospecto.first()
        if primer_comentario:
            fecha_primer_comentario = primer_comentario.datetime
            if prospecto.ultima_asignacion:
                hora_inicial = prospecto.ultima_asignacion
            else:
                print("prospecto %s sin fecha de asignacion" % prospecto.pk)
                hora_inicial = prospecto.fecha_creacion

            if hora_inicial < fecha_primer_comentario:
                tiempo = rango_laboral.horas_laborales_entre(fecha_y_hora_final=fecha_primer_comentario, fecha_y_hora_inicial=hora_inicial).total_seconds()
            else:
                #print(prospecto.id)
                tiempo = 0
            prospecto._tiempo_de_respuesta = tiempo
            prospecto.save()


class Migration(migrations.Migration):
    dependencies = [
        ('prospectos', '0054_prospecto__tiempo_de_respuesta'),
    ]

    operations = [
        migrations.RunPython(tiempo_de_respuesta_para_prospectos_de_ultimo_mes),
    ]
