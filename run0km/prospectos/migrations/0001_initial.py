# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.utils.timezone
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('campanias', '0001_initial'),
        ('equipos', '0001_initial'),
        ('vendedores', '0002_vendedor_equipo'),
    ]

    operations = [
        migrations.CreateModel(
            name='CampoExtra',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('nombre', models.CharField(default=b'', max_length=64)),
                ('valor', models.TextField()),
            ],
            options={
                'verbose_name': 'campo extra',
                'verbose_name_plural': 'campos extras',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Comentario',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('comentario', models.TextField()),
            ],
            options={
                'verbose_name': 'comentario',
                'verbose_name_plural': 'comentarios',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='EmailExtra',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('email', models.EmailField(max_length=75)),
                ('activo', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'email adicional',
                'verbose_name_plural': 'emails adicionales',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='FiltroDePedido',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('accion', models.CharField(max_length=1, choices=[(b'I', b'Incluir'), (b'E', b'Excluir')])),
                ('campo', models.CharField(max_length=64)),
                ('selector', models.CharField(max_length=3, choices=[(b'pre', b'Comienza con'), (b'in', b'Contiene'), (b'fin', b'Termina con')])),
                ('valor', models.CharField(max_length=64)),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Finalizacion',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('otro_motivo', models.CharField(default=b'', max_length=64, null=True, blank=True)),
                ('comentario', models.TextField(blank=True)),
            ],
            options={
                'verbose_name_plural': 'finalizaciones',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Llamado',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('fecha', models.DateTimeField()),
            ],
            options={
                'verbose_name': 'llamado',
                'verbose_name_plural': 'llamados',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='MotivoDeFinalizacion',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('descripcion', models.CharField(default=b'', max_length=64)),
            ],
            options={
                'verbose_name_plural': 'Motivos de finalizacion',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='PedidoDeProspecto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('credito', models.PositiveIntegerField()),
                ('yapa', models.PositiveIntegerField(default=0)),
                ('consumido', models.PositiveIntegerField(default=0)),
                ('fecha', models.DateField(verbose_name=b'Fecha de comienzo')),
                ('origen', models.CharField(max_length=1, choices=[(b'S', b'SMS'), (b'W', b'Web'), (b'M', b'Mailing')])),
                ('asignar_a', models.CharField(blank=True, max_length=1, choices=[(b'T', b'Todos'), (b'E', b'Equipo'), (b'V', b'Vendedor')])),
                ('uniforme', models.BooleanField(default=True)),
                ('es_renovable', models.BooleanField(default=False)),
                ('finalizado', models.BooleanField(default=False)),
                ('campanias', models.ManyToManyField(to='campanias.Campania', blank=True)),
                ('categorias', models.ManyToManyField(to='campanias.CategoriaDeCampania')),
                ('equipo', models.ForeignKey(blank=True, to='equipos.Equipo', null=True)),
                ('supervisor', models.ForeignKey(related_name=b'Pedidos', to='vendedores.Vendedor')),
                ('vendedor', models.ForeignKey(blank=True, to='vendedores.Vendedor', null=True)),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Prospecto',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('codigo', models.CharField(max_length=100, unique=True, null=True, blank=True)),
                ('fecha', models.DateTimeField(null=True, blank=True)),
                ('nombre', models.CharField(default=b'', max_length=64, blank=True)),
                ('prefijo', models.CharField(default=b'', max_length=64, blank=True)),
                ('telefono', models.CharField(default=b'', max_length=64, blank=True)),
                ('telefono_activo', models.BooleanField(default=True)),
                ('email', models.CharField(default=b'', max_length=64, blank=True)),
                ('email_activo', models.BooleanField(default=True)),
                ('mensaje', models.CharField(default=b'', max_length=64, blank=True)),
                ('ultima_asignacion', models.DateTimeField(default=django.utils.timezone.now)),
                ('exportado', models.BooleanField(default=False)),
                ('estado', models.CharField(default=b'N', max_length=1, choices=[(b'N', b'Nuevo'), (b'P', b'En proceso'), (b'F', b'Finalizado'), (b'V', b'Vendido')])),
                ('provincia', models.CharField(max_length=64, blank=True)),
                ('localidad', models.CharField(max_length=64, blank=True)),
                ('marca', models.CharField(default=b'', max_length=64, blank=True)),
                ('modelo', models.CharField(default=b'', max_length=64, blank=True)),
                ('campania', models.ForeignKey(related_name=b'prospectos', verbose_name=b'campa\xc3\xb1a', to='campanias.Campania')),
                ('responsable', models.ForeignKey(related_name=b'prospectos_a_cargo', on_delete=django.db.models.deletion.SET_NULL, blank=True, to='vendedores.Vendedor', null=True)),
                ('vendedor', models.ForeignKey(related_name=b'prospectos', on_delete=django.db.models.deletion.SET_NULL, verbose_name=b'Vendedor Asignado', blank=True, to='vendedores.Vendedor', null=True)),
            ],
            options={
                'verbose_name': 'prospecto',
                'verbose_name_plural': 'prospectos',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Rechazo',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('prospecto', models.ForeignKey(related_name=b'rechazos', to='prospectos.Prospecto')),
                ('responsable', models.ForeignKey(related_name=b'rechazos', to='vendedores.Vendedor')),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='SubidaErronea',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('archivo', models.FileField(upload_to=b'erroneas')),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('fallidas', models.IntegerField()),
                ('exitosas', models.IntegerField()),
                ('origen', models.CharField(max_length=1, null=True, blank=True)),
                ('campania', models.CharField(max_length=110, null=True, blank=True)),
                ('responsable', models.CharField(max_length=64, null=True, blank=True)),
                ('vendedor', models.CharField(max_length=64, null=True, blank=True)),
                ('ejecutor', models.ForeignKey(related_name=b'subidas_erroneas', to='vendedores.Vendedor')),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nombre', models.CharField(max_length=32)),
                ('prospectos', models.ManyToManyField(to='prospectos.Prospecto')),
                ('vendedor', models.ForeignKey(related_name=b'tags', to='vendedores.Vendedor')),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='TelefonoExtra',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('telefono', models.TextField()),
                ('activo', models.BooleanField(default=True)),
                ('prospecto', models.ForeignKey(related_name=b'telefono_extra', to='prospectos.Prospecto')),
                ('vendedor', models.ForeignKey(related_name=b'telefonos_extra', to='vendedores.Vendedor')),
            ],
            options={
                'verbose_name': 'tel\xe9fono adicional',
                'verbose_name_plural': 'tel\xe9fonos adicionales',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Venta',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('marca', models.CharField(default=b'', max_length=64)),
                ('modelo', models.CharField(default=b'', max_length=64)),
                ('fecha', models.DateField()),
                ('precio', models.PositiveIntegerField()),
                ('prospecto', models.OneToOneField(related_name=b'venta', to='prospectos.Prospecto')),
                ('vendedor', models.ForeignKey(related_name=b'ventas', to='vendedores.Vendedor')),
            ],
            options={
                'verbose_name': 'venta',
                'verbose_name_plural': 'ventas',
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name='tag',
            unique_together=set([('vendedor', 'nombre')]),
        ),
        migrations.AddField(
            model_name='llamado',
            name='prospecto',
            field=models.OneToOneField(related_name=b'llamado', to='prospectos.Prospecto'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='llamado',
            name='vendedor',
            field=models.ForeignKey(related_name=b'llamados', to='vendedores.Vendedor'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='finalizacion',
            name='motivo',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, blank=True, to='prospectos.MotivoDeFinalizacion', null=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='finalizacion',
            name='prospecto',
            field=models.OneToOneField(related_name=b'finalizacion', to='prospectos.Prospecto'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='finalizacion',
            name='vendedor',
            field=models.ForeignKey(related_name=b'finalizaciones', to='vendedores.Vendedor'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='filtrodepedido',
            name='pedido',
            field=models.ForeignKey(related_name=b'filtros', to='prospectos.PedidoDeProspecto'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='emailextra',
            name='prospecto',
            field=models.ForeignKey(related_name=b'email_extra', to='prospectos.Prospecto'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='emailextra',
            name='vendedor',
            field=models.ForeignKey(related_name=b'emails_extra', to='vendedores.Vendedor'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='comentario',
            name='prospecto',
            field=models.ForeignKey(related_name=b'comentarios', to='prospectos.Prospecto'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='comentario',
            name='vendedor',
            field=models.ForeignKey(related_name=b'comentarios', to='vendedores.Vendedor'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='campoextra',
            name='prospecto',
            field=models.ForeignKey(related_name=b'campos_extra', to='prospectos.Prospecto'),
            preserve_default=True,
        ),
        migrations.CreateModel(
            name='VendedorFullName',
            fields=[
            ],
            options={
                'proxy': True,
            },
            bases=('vendedores.vendedor',),
        ),
    ]
