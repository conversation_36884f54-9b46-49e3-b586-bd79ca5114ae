# -*- coding: utf-8 -*-


from django.db import models, migrations


def copiar_origen_en_lista_de_origenes(apps, schema_editor):
    PedidoDeProspecto = apps.get_model("prospectos", "PedidoDeProspecto")
    for pedido in PedidoDeProspecto.objects.all():
        pedido.origenes.add(pedido.origen)
        pedido.save


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0045_pedidodeprospecto_origenes'),
    ]

    operations = [
        migrations.RunPython(copiar_origen_en_lista_de_origenes),
    ]
