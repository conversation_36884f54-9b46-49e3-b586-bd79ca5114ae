# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('log_de_errores', '0001_initial'),
        ('prospectos', '0030_auto_20150814_1708'),
    ]

    operations = [
        migrations.CreateModel(
            name='LogDeErrorChequeadorDeWhatsapp',
            fields=[
            ],
            options={
                'verbose_name': 'Error del verificador de Whatsapp',
                'proxy': True,
                'verbose_name_plural': 'Errores del verificador de Whatsapp',
            },
            bases=('log_de_errores.logdeerror',),
        ),
        migrations.AddField(
            model_name='prospecto',
            name='tiene_whatsapp',
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='telefonoextra',
            name='tiene_whatsapp',
            field=models.NullBooleanField(),
            preserve_default=True,
        ),
    ]
