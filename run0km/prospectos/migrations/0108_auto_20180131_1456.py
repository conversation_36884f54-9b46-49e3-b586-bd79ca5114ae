# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-01-31 17:56


from django.db import migrations, models
import django.db.models.deletion
import prospectos.models.base
import prospectos.models.soporte


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0107_crear_modelos_desde_servicio'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='prospecto',
            name='nuevas_marcas',
        ),
        migrations.AddField(
            model_name='prospecto',
            name='_marca',
            field=models.ForeignKey(default=prospectos.models.soporte.obtener_marca_blaca,
                                    on_delete=django.db.models.deletion.SET_DEFAULT, related_name='_prospectos',
                                    to='prospectos.Marca'),
        ),
        migrations.RenameField(
            model_name='prospecto',
            old_name='nuevos_modelos',
            new_name='_modelos',
        ),
    ]
