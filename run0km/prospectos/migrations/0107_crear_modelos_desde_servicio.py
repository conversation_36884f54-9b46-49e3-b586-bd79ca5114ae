# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-02-23 20:37


from django.conf import settings
from django.db import migrations

from client_crm import ServicioDeIntegracion


def crear_modelos_desde_servicio(apps, schema_editor):
    if not settings.ES_AMBIENTE_DE_TESTING:
        normalizador = ServicioDeIntegracion.para_modelos()
        normalizador.realizar_actualizacion()


def undo_crear_modelos_desde_servicio(apps, schema_editor):
    Modelo = apps.get_model("prospectos", "Modelo")
    modelos = Modelo.objects.filter(_esta_normalizado=True)
    modelos.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0106_crear_marcas_normalizadas_con_aliases'),
    ]

    operations = [
        migrations.RunPython(crear_modelos_desde_servicio, undo_crear_modelos_desde_servicio)
    ]
