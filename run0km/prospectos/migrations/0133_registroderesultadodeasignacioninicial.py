# -*- coding: utf-8 -*-
# Generated by Django 1.11.14 on 2024-05-10 16:15
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('prospectos', '0132_auto_20210803_2028'),
    ]

    operations = [
        migrations.CreateModel(
            name='RegistroDeResultadoDeAsignacionInicial',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_fecha', models.DateTimeField(auto_now_add=True)),
                ('_cantidad_de_datos_entregados', models.IntegerField()),
                ('_cantidad_de_datos_no_entregados', models.IntegerField()),
                ('_descripcion', models.Char<PERSON>ield(max_length=128)),
                ('_usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resultados_de_asignaciones_iniciales', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Registro de resultados de asignaci\xf3n iniciales',
            },
        ),
    ]
