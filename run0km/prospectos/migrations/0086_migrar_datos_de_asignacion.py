# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-05-15 00:17


from django.db import migrations


def migracion_asignacion_de_prospectos(apps, schema_editor):
    Prospecto = apps.get_model("prospectos", "Prospecto")
    AsignacionDeProspecto = apps.get_model("prospectos", "AsignacionDeProspecto")
    prospectos_sin_asignacion = Prospecto.objects.filter(asignacion__isnull=True)
    prospectos = prospectos_sin_asignacion.values_list('id', 'fecha_creacion', 'ultima_asignacion').order_by('fecha_creacion')
    asignaciones = []
    for prospecto in prospectos[:50000]:
        asignacion = AsignacionDeProspecto(prospecto_id=prospecto[0],
                                           fecha_de_asignacion_a_supervisor=prospecto[1],
                                           fecha_de_asignacion_a_vendedor=prospecto[2])
        asignaciones.append(asignacion)
    AsignacionDeProspecto.objects.bulk_create(asignaciones)
    print('\nRestantes %d' % Prospecto.objects.filter(asignacion__isnull=True).count())


def undo_migracion_asignacion_de_prospectos(apps, schema_editor):
    AsignacionDeProspecto = apps.get_model("prospectos", "AsignacionDeProspecto")
    asignaciones = AsignacionDeProspecto.objects.all()
    asignaciones.delete()


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0085_auto_20170514_2113'),
    ]

    operations = [
        migrations.RunPython(migracion_asignacion_de_prospectos, undo_migracion_asignacion_de_prospectos)
    ]

