# -*- coding: utf-8 -*-


from django.db import models, migrations
from django.utils.timezone import now
import datetime


def migrar_fechas(apps, _):
    prospecto_model_klass = apps.get_model("prospectos", "Prospecto")
    prospectos = prospecto_model_klass.objects.all()
    for prospecto in prospectos:
        prospecto.fecha_creacion = prospecto.ultima_asignacion
        prospecto.save()


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0004_auto_20141017_1256'),
    ]

    operations = [
        migrations.AddField(
            model_name='prospecto',
            name='fecha_creacion',
            field=models.DateTimeField(default=now(), auto_now_add=True),
            preserve_default=False,
        ),
        migrations.RunPython(migrar_fechas),
    ]
