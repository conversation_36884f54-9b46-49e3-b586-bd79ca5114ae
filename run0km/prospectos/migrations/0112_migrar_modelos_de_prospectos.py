# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-02-23 19:49


from django.db import migrations


def migrar_modelos(apps, schema_editor):
    Prospecto = apps.get_model("prospectos", "Prospecto")
    Modelo = apps.get_model("prospectos", "Modelo")
    nombes_de_modelos_y_marca = Prospecto.objects.exclude(modelo='').values('modelo', '_marca').distinct()
    for modelo_y_marca in nombes_de_modelos_y_marca[:100]:
        nombres_string = modelo_y_marca['modelo']
        marca_id = modelo_y_marca['_marca']
        print('%s: %s' % (marca_id, nombres_string))
        _actualizar_modelos(Modelo, Prospecto, marca_id, nombres_string)
    print('\nRestantes %d' % Prospecto.objects.exclude(modelo='').count())


def _actualizar_modelos(modelo_klass, Prospecto, marca_id, nombres_string):
    modelos = _modelos_con_nombres(modelo_klass, marca_id, nombres_string)
    prospectos = Prospecto.objects.filter(modelo=nombres_string, _marca_id=marca_id)
    for modelo in modelos:
        print(modelo._nombre)
        modelo._prospectos = prospectos
        modelo.save()
    prospectos.update(modelo='')


def _modelos_con_nombres(modelo_klass, marca_id, nombres_string):
    nombres = nombres_string.split('|')
    if '' in nombres:
        nombres.remove('')
    modelos = []
    for nombre in set(nombres):
        modelo, created = modelo_klass.objects.get_or_create(_nombre=nombre, _marca_id=marca_id)
        if not created:
            modelo._codigo = nombre.lower()
            modelo.save()
        modelos.append(modelo)
    return modelos


def undo_migrar_modelos(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0111_auto_20180205_2307'),
    ]

    operations = [
        migrations.RunPython(migrar_modelos, undo_migrar_modelos)
    ]
