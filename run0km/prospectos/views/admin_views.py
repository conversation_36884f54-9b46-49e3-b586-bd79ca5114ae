# coding=utf-8
import datetime
import json

from dateutil.relativedelta import relativedelta
from django.contrib import messages
from django.http import JsonResponse
from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from django.utils.dates import MONTHS
from django.utils.safestring import mark_safe
from django.utils.timezone import localtime, timedelta, now
from django.utils.translation import ugettext_lazy as _
from django.views.generic.base import TemplateView, View

from campanias.models import Campania
from core.token import TokenManager
from prospectos.configuracion import DETALLE_CAMPOS_FORMULARIO_CSV, DETALLE_CAMPOS_PROSPECTO_CSV
from prospectos.models import RegistroDeResultadoDeAsignacionInicial
from prospectos.models.base import Prospecto, PedidoDeProspecto
from prospectos.models.entrega_de_datos.opciones import AccionesDeAsignacionDeVendedorChoices
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.utils.asignacion_de_prospectos import AsignadorDeProspectos
from prospectos.utils.carga_por_csv import CargadorDeProspectosPorCSV
from prospectos.utils.exportacion_a_csv import ExportadorDeProspectosACSV
from prospectos.utils.reporte_de_compras import ReporteDeCompras
from prospectos.utils.reporte_de_datos import ReporteDeDatosEntregados
from prospectos.views.admin_forms import (AddProspectosCSVAdminForm, AsignacionInicialOldForm,
                                          FiltroDistribucionInicialForm, FiltroAsignacionInicialForm,
                                          ReporteDeComprasForm, ReporteDatosAdminForm, AsignacionInicialForm,
                                          FiltrosExportarProspectosForm, ExportarProspectosForm,
                                          DistribucionInicialForm, ReporteSimplificadoForm)
from prospectos.views.deserializadores import DeserializadorDeDistribucionDeProspectos
from reportes.reporte_de_distribucion import GeneradorDeReporteSimplificado
from vendedores.models import Vendedor


class AddProspectosCSVDoc(TemplateView):
    template_name = "add_prospectos_csv_doc.html"

    def get_context_data(self, **kwargs):
        context = super(AddProspectosCSVDoc, self).get_context_data(**kwargs)
        context['campos_formulario'] = DETALLE_CAMPOS_FORMULARIO_CSV
        context['campos_prospecto'] = DETALLE_CAMPOS_PROSPECTO_CSV
        return context


class ImportarProspectosDesdeCSVView(View):
    def get(self, request, *args, **kwargs):
        form = AddProspectosCSVAdminForm()
        return self._render_form(request, form)

    def post(self, request, *args, **kwargs):
        form = AddProspectosCSVAdminForm(request.POST, request.FILES)
        mensaje = ''
        err_filas = err_codigos = err_vendedores = log_url = ''

        if form.is_valid():
            cargador = CargadorDeProspectosPorCSV(form.cleaned_data, request.user)
            resultado = cargador.cargar_prospectos()
            mensaje = '%d prospectos cargados satisfactoriamente de %d filas leidas' % (
                resultado.cantidad_filas_exitosas(),
                resultado.cantidad_de_filas())
            if resultado.tiene_filas_erroneas():
                err_filas = 'Números de filas con errores: %d' % resultado.cantidad_filas_erroneas()
            if cargador.vendedores_erroneos:
                err_vendedores = 'Nombres de vendedores invalidos (los prospectos quedaran sin vendedor asignado) : %s' \
                                 % cargador.vendedores_erroneos
            if cargador.log:
                log_url = cargador.log.archivo.url

        return self._render_form(request, form, mensaje, err_filas, err_codigos, err_vendedores, log_url)

    def _render_form(self, request, form, mensaje='', err_filas='', err_codigos='', err_vendedores='', log_url=''):
        return render(
            request,
            'admin/prospectos/add_prospectos_csv.html',
            {
                'form': form,
                'msg': mensaje,
                'err_filas': err_filas,
                'err_codigos': err_codigos,
                'err_vendedores': err_vendedores,
                'log_url': log_url,
                'campanias': Campania.objects.all().select_related('categoria'),
                'vendedores': Vendedor.objects.filter(cargo='Vendedor'),
                'pedidos': PedidoDeProspecto.objects.activos_actuales(),
            }
        )


def cantidad_filtrada_distribucion(request):
    return cantidad_filtrada(request, FiltroDistribucionInicialForm)


def cantidad_filtrada(request, filtro_form_class=FiltroAsignacionInicialForm):
    response = {'status': False}
    filtro_aplicado = request.POST.get('filtro_aplicado', None)
    form = filtro_form_class(request.POST, filtro_aplicado=filtro_aplicado)
    response['campanias'] = list()
    response['marcas'] = list()
    response['provincias'] = list()
    response['prefijos'] = list()
    response['total_prospectos'] = 0
    if form.is_valid():
        prospectos, sin_filtrar_por_campania, sin_filtrar_por_marcas, \
        sin_filtrar_por_provincias, sin_filtrar_por_prefijos = form.prospectos_filtrados()
        response['campanias'] = Prospecto.objects.campanias_con_cantidades(sin_filtrar_por_campania)
        response['marcas'] = Prospecto.objects.marcas_con_cantidades(sin_filtrar_por_marcas)
        response['provincias'] = Prospecto.objects.provincias_con_cantidades(sin_filtrar_por_provincias)
        response['prefijos'] = Prospecto.objects.prefijos_con_cantidades(sin_filtrar_por_prefijos)
        response['total_prospectos'] = prospectos.count()
    response['status'] = True
    return JsonResponse(response)


def distribucion_inicial(request):
    return asignacion_inicial(request, gestion_form_class=DistribucionInicialForm)


def _tienen_todos_vendedores_factor_de_asignacion_cero(cleaned_data):
    if cleaned_data['accion'] == AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR:
        return False
    existen_vendedores_con_factor_cero = False
    asignar_a = cleaned_data['asignar_a']
    vendedores = []
    if asignar_a == 'E':
        equipo = cleaned_data['equipo']
        vendedores = equipo.obtener_integrantes()
    elif asignar_a == 'T':
        responsable = cleaned_data['responsable']
        vendedores = responsable.obtener_vendedores()
    elif asignar_a == 'V':
        vendedores = [cleaned_data['vendedor']]

    if asignar_a is not None and all([vendedor.obtener_factor_de_asignacion() == 0 for vendedor in vendedores]):
        existen_vendedores_con_factor_cero = True
    return existen_vendedores_con_factor_cero


# CORRESPONDE A LA VIEJA ASIGNACION INICIAL! Esta deprecada
def asignacion_inicial(request, gestion_form_class=None):
    gestion_form_class = gestion_form_class or AsignacionInicialOldForm
    seleccion_previa = {}
    if request.method == 'GET':
        form = gestion_form_class()

    if request.method == 'POST':
        reiniciar_formulario = False
        form = gestion_form_class(request.POST)
        if form.is_valid():
            prospectos, sin_filtrar_por_campania, sin_filtrar_por_marcas, \
            sin_filtrar_por_provincias, sin_filtrar_por_prefijos = form.prospectos_filtrados()
            cd = form.cleaned_data
            cantidad = cd['cantidad']
            vendedores_con_factor_de_asignacion_cero = _tienen_todos_vendedores_factor_de_asignacion_cero(
                cleaned_data=cd)
            if vendedores_con_factor_de_asignacion_cero:
                msg = 'No se puede asignar prospectos a vendedores con factor de asignacion cero.'
                messages.add_message(request, messages.ERROR, msg)
            else:
                if cantidad > prospectos.count():
                    msg = 'Debe elegir una cantidad mayor a cero, y menor al total filtrado.'
                    messages.add_message(request, messages.ERROR, msg)
                else:
                    asignador = AsignadorDeProspectos()
                    ids = prospectos.values_list('pk', flat=True)[:cantidad]
                    prospectos = Prospecto.objects.filter(pk__in=list(ids))
                    if form.para_asignacion():
                        asignador.responsabilizar_a_supervisor(request, prospectos, cd['responsable'], cd['cantidad'],
                                                               cd['pedido'])
                    if cd['accion'] == 'asignar':
                        responsable = cd['responsable']
                        if cd['asignar_a'] == 'V':
                            asignador.asignar_a_vendedor(request, prospectos, cd['vendedor'], cd['cantidad'], True)
                        elif cd['asignar_a'] == 'E':
                            equipo = cd['equipo']
                            vendedores = equipo.integrantes
                            asignador.asignar_a_vendedores(request, prospectos, responsable, vendedores, cantidad,
                                                           cd['metodo'], True)
                        elif cd['asignar_a'] == 'T':
                            vendedores = responsable.vendedores
                            asignador.asignar_a_vendedores(request, prospectos, responsable, vendedores, cantidad,
                                                           cd['metodo'], True)
                    if cd['pedido']:
                        administrador = AdministradorDePedidos()
                        consumido = prospectos.valor_total()
                        administrador.registrar_asignacion_de_prospectos(cd['pedido'], prospectos, consumido)
                    reiniciar_formulario = True
        if not reiniciar_formulario:
            if 'marcas' in list(form.cleaned_data.keys()):
                seleccion_previa['marcas'] = form.cleaned_data['marcas']
            if 'campanias' in list(form.cleaned_data.keys()):
                seleccion_previa['campanias'] = [str(x.id) for x in form.cleaned_data['campanias']]
            if 'provincias' in list(form.cleaned_data.keys()):
                seleccion_previa['provincias'] = form.cleaned_data['provincias']
            if 'prefijos' in list(form.cleaned_data.keys()):
                seleccion_previa['prefijos'] = form.cleaned_data['prefijos']
        else:
            form = gestion_form_class()

    return render(
        request,
        'admin/prospectos/asignacion_inicial_old.html',
        {
            'pedidos': PedidoDeProspecto.objects.activos_actuales(),
            'form': form,
            'para_asignacion': form.para_asignacion(),
            'usar_seleccion_previa': 'true' if len(list(seleccion_previa.keys())) > 0 else 'false',
            'seleccion_previa_json': mark_safe(json.dumps(seleccion_previa))
        }
    )


class AsignacionInicialView(View):
    def get(self, request, *args, **kwargs):
        return self._render_formulario(form=AsignacionInicialForm(), request=request, seleccion_previa={})

    def post(self, request, *args, **kwargs):
        form = AsignacionInicialForm(request.POST)
        if form.is_valid():
            cleaned_data = form.cleaned_data
            prospectos = self._prospectos_filtrados_para(form)
            cantidad = cleaned_data['cantidad']
            if cantidad > prospectos.count():
                msg = 'Debe elegir una cantidad mayor a cero, y menor al total filtrado.'
                messages.add_message(request, messages.ERROR, msg)
            else:
                ids = prospectos.values_list('pk', flat=True)[:cantidad]
                prospectos = Prospecto.objects.filter(pk__in=list(ids))
                distribuidor = self._distribuidor_para(form)
                resultado = distribuidor.evaluar_para(prospectos)
                RegistroDeResultadoDeAsignacionInicial.nuevo_para(request.user, distribuidor.descripcion(), resultado)
                return self._mostrar_resultado(request, resultado)

        return self._render_formulario_con_seleccion_previa(form, request)

    def _render_formulario_con_seleccion_previa(self, form, request):
        seleccion_previa = {}
        if 'marcas' in list(form.cleaned_data.keys()):
            seleccion_previa['marcas'] = form.cleaned_data['marcas']
        if 'campanias' in list(form.cleaned_data.keys()):
            seleccion_previa['campanias'] = [str(x.id) for x in form.cleaned_data['campanias']]
        if 'provincias' in list(form.cleaned_data.keys()):
            seleccion_previa['provincias'] = form.cleaned_data['provincias']
        if 'prefijos' in list(form.cleaned_data.keys()):
            seleccion_previa['prefijos'] = form.cleaned_data['prefijos']
        return self._render_formulario(form, request, seleccion_previa)

    def _render_formulario(self, form, request, seleccion_previa):
        return render(
            request,
            'admin/prospectos/asignacion_inicial.html',
            {
                'pedidos': PedidoDeProspecto.objects.activos_actuales(),
                'form': form,
                'para_asignacion': True,
                'usar_seleccion_previa': 'true' if len(list(seleccion_previa.keys())) > 0 else 'false',
                'seleccion_previa_json': mark_safe(json.dumps(seleccion_previa))
            }
        )

    def _prospectos_filtrados_para(self, form):
        prospectos, sin_filtrar_por_campania, sin_filtrar_por_marcas, \
        sin_filtrar_por_provincias, sin_filtrar_por_prefijos = form.prospectos_filtrados()
        return prospectos

    def _distribuidor_para(self, form):
        deserializador = DeserializadorDeDistribucionDeProspectos()
        distribuidor = deserializador.construir_desde(form.cleaned_data)
        return distribuidor

    def _mostrar_resultado(self, request, resultado):
        return render(
            request,
            'admin/prospectos/resultado_asignacion_inicial.html',
            {'resultado': resultado}
        )


class ReporteDeComprasView(View):
    template_name = 'admin/prospectos/compra/reporte_de_compras.html'

    def get(self, request, *args, **kwargs):
        return render(
            request,
            'admin/prospectos/compra/reporte_de_compras.html',
            {'form': ReporteDeComprasForm(), }
        )

    def post(self, request, *args, **kwargs):
        context = {}
        form = ReporteDeComprasForm(self.request.POST)
        if form.is_valid():
            cd = form.cleaned_data
            anio = cd.get('anio')
            context['anio'] = anio
            mes = cd.get('mes')
            context['mes'] = _(MONTHS[int(mes)]) if mes else None
            context['reporte'] = ReporteDeCompras(anio, mes)
        context['form'] = form

        return render(
            request,
            'admin/prospectos/compra/reporte_de_compras.html',
            context
        )


class ReporteDeDatosView(View):
    def get(self, request):
        hoy = datetime.datetime.today()
        mes = request.GET.get('mes', hoy.month)
        anio = request.GET.get('anio', hoy.year)
        supervisor = request.GET.get('supervisor', None)

        context = {'reporte_de_datos': ReporteDeDatosEntregados(mes, anio, supervisor),
                   'form': ReporteDatosAdminForm(initial={'mes': mes, 'anio': anio, 'supervisor': supervisor})}

        return render(request, 'admin/prospectos/reporte_de_datos.html', context)


class FiltrosExportarProspectosView(View):
    def post(self, request):
        filtro_aplicado = request.POST.get('filtro_aplicado', None)
        form = FiltrosExportarProspectosForm(request.POST, filtro_aplicado=filtro_aplicado)
        if not form.is_valid():
            return JsonResponse(data={'status': False})

        resultado_filtro = form.prospectos_filtrados()
        response = dict(status=True)
        response['cantidad'] = resultado_filtro['prospectos'].count()
        response['opciones'] = resultado_filtro['opciones']
        return JsonResponse(data=response)


class InformacionPedidoDeProspectosView(View):
    def get(self, request, pk):
        pedido = get_object_or_404(PedidoDeProspecto, pk=pk)
        datos = pedido.detalles_de_asignacion()
        context = {'datos': json.dumps(datos)}
        return render(request,
                      'admin/prospectos/pedidodeprospecto/informacion_de_pedidos_de_prospectos.html',
                      context)


class ExportarProspectosView(View):
    def get(self, request):
        primero, ultimo = self._primer_y_ultimo_dia_del_mes_pasado()
        initial = {'fecha_desde': primero.isoformat(),
                   'fecha_hasta': ultimo.isoformat()}
        return render(
            request,
            'admin/prospectos/exportar_prospectos.html',
            {'form': ExportarProspectosForm(initial=initial), }
        )

    def post(self, request):
        self._generar_token()
        form = ExportarProspectosForm(request.POST)
        if form.is_valid():
            resultado_filtro = form.prospectos_filtrados()
            prospectos = resultado_filtro['prospectos']
            if prospectos.count() > 0:
                exportador = ExportadorDeProspectosACSV()
                # Exportar los prospectos
                return exportador.exportar(prospectos, request.user)
                # Exportar en streaming tiene un Bug con los encodings
                # return exportador.exportar_en_streaming_response(prospectos, request.user)
        return render(
            request,
            'admin/prospectos/exportar_prospectos.html',
            {'form': form, }
        )

    def _primer_y_ultimo_dia_del_mes_pasado(self):
        hoy = localtime(now())
        ultimo = datetime.date(year=hoy.year, month=hoy.month, day=1) - timedelta(days=1)
        primero = datetime.date(year=ultimo.year, month=ultimo.month, day=1)
        return primero, ultimo

    def _generar_token(self):
        token_manager = TokenManager.nuevo()
        token_manager.generar_token()


class ReporteSimplificadoView(View):
    def get(self, request, *args, **kwargs):
        fecha_por_defecto = timezone.now() - relativedelta(months=1)
        datos = {
            'anio': fecha_por_defecto.year,
            'mes': fecha_por_defecto.month,
            'categorias': ReporteSimplificadoForm.categorias_por_defefcto()}
        form = ReporteSimplificadoForm(initial=datos)
        reporte = self._generar_reporte_desde(datos)
        context = {'form': form, 'reporte': reporte}
        return self._render(context, request)

    def post(self, request, *args, **kwargs):
        form = ReporteSimplificadoForm(self.request.POST)
        context = {'form': form, 'reporte': {}}
        if form.is_valid():
            context['reporte'] = self._generar_reporte_desde(form.cleaned_data)

        return self._render(context, request)

    def _render(self, context, request):
        return render(
            request,
            'admin/prospectos/reporte_simplificado.html',
            context
        )

    def _generar_reporte_desde(self, datos):
        mes = datos.get('mes')
        anio = datos.get('anio')
        categorias = datos.get('categorias')
        generador = GeneradorDeReporteSimplificado(mes, anio, categorias)
        reporte = generador.generar()
        return reporte
