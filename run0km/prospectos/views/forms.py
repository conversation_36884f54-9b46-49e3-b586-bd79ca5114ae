# -*- coding: utf-8 -*-
import re

from django import forms
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.db.models import Q
from django.utils import timezone

from campanias.models import TipoDeOrigen
from prospectos.models import <PERSON><PERSON>, Modelo
from prospectos.models.base import Prospecto, Venta, Finalizacion, Tag, InformacionDeRedesSociales, OPCIONES_DE_REDES
from vendedores.models import Vendedor


class ReasignarProspectoForm(forms.Form):
    vendedor = forms.ModelChoiceField(queryset=Vendedor.objects.all(), required=False,
                                      label='Vendedor Asignado', empty_label='--------')

    def __init__(self, supervisor, *args, **kwargs):
        self.supervisor = supervisor
        candidatos = list(supervisor.vendedores.vendedores_activos().values_list('id', flat=True)) + [supervisor.id]
        self.base_fields['vendedor'].queryset = Vendedor.objects.filter(id__in=candidatos)
        super(ReasignarProspectoForm, self).__init__(*args, **kwargs)

    def clean_vendedor(self):
        vendedor_asignado = self.cleaned_data['vendedor']
        if vendedor_asignado:
            if self.supervisor is not None and vendedor_asignado.supervisor == self.supervisor:
                return vendedor_asignado
        return None


class NuevoLlamadoForm(forms.Form):
    fecha = forms.DateTimeField(required=True, input_formats=('%Y-%m-%d %H:%M',))


class VentaForm(forms.ModelForm):
    fecha_de_realizacion = forms.DateField(
        input_formats=('%Y-%m-%d',),
        widget=forms.TextInput(attrs={'autocomplete': 'off'})
    )

    class Meta:
        model = Venta
        fields = ['marca', 'modelo', 'fecha_de_realizacion', 'numero_de_contrato', 'precio']

    def __init__(self, *args, **kwargs):
        super(VentaForm, self).__init__(*args, **kwargs)
        self.initial['fecha_de_realizacion'] = timezone.now().strftime('%Y-%m-%d')
        self.fields['fecha_de_realizacion'].widget.attrs['class'] = 'datepicker'


class FinalizacionForm(forms.ModelForm):
    class Meta:
        model = Finalizacion
        fields = ['motivo', 'otro_motivo', 'comentario']

    def __init__(self, *args, **kwargs):
        super(FinalizacionForm, self).__init__(*args, **kwargs)
        self.fields['motivo'].empty_label = 'Otro'

    def clean(self):
        cd = self.cleaned_data
        if 'motivo' in cd:
            if cd['motivo']:
                cd['otro_motivo'] = ''
            else:
                if 'otro_motivo' in cd:
                    if not cd['otro_motivo']:
                        raise ValidationError('Debe ingresar un motivo de finalizacion')
        return cd


class CancelacionDeVentaForm(forms.Form):
    EN_PROCESO = Prospecto.EN_PROCESO
    FINALIZADO = Prospecto.FINALIZADO
    ESTADO_CHOICES = ((EN_PROCESO, 'En Proceso'), (FINALIZADO, 'Finalizado'))
    comentario = forms.CharField(max_length=220, required=True)
    estado_a_pasar_el_prospecto = forms.ChoiceField(choices=ESTADO_CHOICES, required=True)


class ExtraDataForm(forms.Form):
    prospecto = forms.CharField(max_length=30, required=True)
    tipo = forms.CharField(max_length=100)
    data = forms.CharField(max_length=100, required=False)

    def clean(self):
        cd = self.cleaned_data
        try:
            cd['prospecto'] = Prospecto.objects.get(id=cd['prospecto'])
            tipo = cd['tipo']
            valor = cd['data']
        except:
            raise ValidationError('El prospecto es invalido')
        if tipo == 'mail':
            validate_email(valor)
        if valor == '' and tipo in ('mail', 'tel', 'name'):
            raise ValidationError('El valor esta vacio')
        return cd


class FiltrosResumenForm(forms.Form):
    fecha_desde = forms.DateField(input_formats=('%Y-%m-%d',), required=False)
    fecha_hasta = forms.DateField(input_formats=('%Y-%m-%d',), required=False)
    buscar = forms.CharField(required=False)


from equipos.models import Equipo

ASIGNAR_OPTIONS = (('todos', 'A todos los vendedores'), ('equipo', 'Por equipo'), ('vendedor', 'Por vendedor'),)
METODO_OPTIONS = [('uniforme', 'Uniforme'), ('factor', 'Por factor de asignación')]


class VendedorFullName(Vendedor):
    class Meta:
        proxy = True

    def __str__(self):
        return self.user.get_full_name()


class AsignacionForm(forms.Form):
    cantidad = forms.IntegerField(min_value=1, required=True)
    asignar = forms.ChoiceField(choices=ASIGNAR_OPTIONS, required=True)
    metodo = forms.ChoiceField(choices=METODO_OPTIONS, required=False)
    equipo = forms.ModelChoiceField(queryset=Equipo.objects.all(), required=False, empty_label=None)
    vendedor = forms.ModelChoiceField(queryset=Vendedor.objects.all(), required=False, empty_label=None)

    def __init__(self, supervisor, maxima_cantidad=None, *args, **kwargs):
        self.maxima_cantidad = maxima_cantidad
        self.base_fields['equipo'].queryset = supervisor.equipos.all()
        candidatos = list(supervisor.vendedores.values_list('id', flat=True)) + [supervisor.id]
        self.base_fields['vendedor'].queryset = VendedorFullName.objects.filter(id__in=candidatos).order_by(
            'cargo').select_related('user')
        super(AsignacionForm, self).__init__(*args, **kwargs)

    def clean_cantidad(self):
        if 'cantidad' in self.cleaned_data and self.cleaned_data['cantidad'] > self.maxima_cantidad:
            raise ValidationError('Debe ingresar una cantidad válida')
        return self.cleaned_data.get('cantidad', None)

    def clean_vendedor(self):
        asignar = self.cleaned_data.get('asignar', None)
        if asignar == 'vendedor':
            vendedor = self.cleaned_data.get('vendedor', None)
            if not vendedor:
                raise ValidationError('Debe elegir un vendedor de la lista si elige asignar por vendedor.')
            return vendedor
        return None

    def clean_metodo(self):
        asignar = self.cleaned_data.get('asignar', None)
        if asignar in ['todos', 'equipo']:
            metodo = self.cleaned_data.get('metodo', None)
            if not metodo:
                raise ValidationError(
                    'Debe elegir un metodo de asignacion para asignar a todos los vendedores o a un equipo.')
            return metodo
        return None

    def clean_equipo(self):
        asignar = self.cleaned_data.get('asignar', None)
        if asignar == 'equipo':
            equipo = self.cleaned_data.get('equipo', None)
            if not equipo:
                raise ValidationError('Debe elegir un equipo de la lista si elige asignar por equipo.')
            else:
                if equipo.integrantes.filter(user__is_active=True).count() == 0:
                    raise ValidationError('El equipo elegido no tiene integrantes habilitados.')
            return equipo
        return None


class TagsForm(forms.Form):
    vendedor = forms
    prospecto = forms.ModelChoiceField(queryset=Prospecto.objects.all(), required=True)

    def __init__(self, vendedor, tags, *args, **kwargs):
        self.vendedor = vendedor
        self.tags = []
        if tags:
            self.tags = list(set([x.strip().lower() for x in tags]))
            if '' in tags:
                self.tags.remove('')

        if vendedor.cargo == 'Vendedor':
            self.base_fields['prospecto'].queryset = vendedor.prospectos.all()
        elif vendedor.cargo == 'Supervisor':
            self.base_fields['prospecto'].queryset = Prospecto.objects.filter(
                Q(vendedor=vendedor) | Q(responsable=vendedor))

        super(TagsForm, self).__init__(*args, **kwargs)

    def clean_prospecto(self):
        prospecto = self.cleaned_data.get('prospecto')
        if prospecto:
            if not self.vendedor.tiene_asignado(prospecto):
                raise ValidationError('El vendedor no tiene permiso para taggear el prospecto.')
        return prospecto

    def aclean_tags(self):
        tags = self.cleaned_data.get('tags', [])
        if tags:
            tags = set([x.strip().lower() for x in tags.split(',')])
            if '' in tags:
                tags.remove('')
        return list(tags)

    def aplicar_tags(self):
        if not self.is_valid():
            raise Exception('No se pueden aplicar los tags. Datos invalidos')
        # tags = self.cleaned_data.get('tags')
        prospecto = self.cleaned_data.get('prospecto')
        # Desasigno los tags que no aparecen
        faltantes = self.vendedor.tags.exclude(nombre__in=self.tags)
        for tag in faltantes:
            prospecto.tags.remove(tag)
        # Agrego los tags que aparecen
        for nombre_tag in self.tags:
            tag, created = self.vendedor.tags.get_or_create(vendedor=self.vendedor, nombre=nombre_tag)
            tag.prospectos.add(prospecto)
        if prospecto.estado == 'N':
            prospecto.estado = 'P'
            prospecto.save()
        return self.tags


class TagForm(forms.Form):
    tag = forms.ModelChoiceField(queryset=Tag.objects.all(), required=True)
    nombre = forms.CharField(max_length=32, required=True)

    def __init__(self, vendedor, *args, **kwargs):
        self.base_fields['tag'].queryset = Tag.objects.filter(vendedor=vendedor)
        self.vendedor = vendedor
        super(TagForm, self).__init__(*args, **kwargs)

    def clean_nombre(self):
        nombre = self.cleaned_data.get('nombre')
        nombre = nombre.strip().lower()
        nombre = re.sub(r'([^\s\w]|_)+', '', nombre)  # Solo alfanumerico y espacios

        actual = self.cleaned_data.get('tag')
        if actual:
            tags = Tag.objects.filter(vendedor=self.vendedor, nombre=nombre).exclude(id=actual.id)
            if tags.count() > 0:
                raise ValidationError('Ya existe un tag con ese nombre.')
        return nombre

    def save(self):
        if not self.is_valid():
            raise Exception('No se pueden editar el tag.')
        try:
            tag = self.cleaned_data['tag']
            tag.nombre = self.cleaned_data['nombre']
            tag.save()
        except:
            raise Exception('No se puede asignar ese nombre al tag')


class ValidarInfoDeRedesSocialesForm(forms.Form):
    informacion = forms.ModelChoiceField(queryset=InformacionDeRedesSociales.objects.all(), required=True)
    estado = forms.ChoiceField(choices=OPCIONES_DE_REDES, required=True)
    comentario = forms.CharField(max_length=256, required=False)


class CargaDeProspectoForm(forms.Form):
    PROVINCIA_CHOICES = [
        ('Capital Federal y Gran Buenos Aires', 'Capital Federal y Gran Buenos Aires'),
        ('Buenos Aires Interior', 'Buenos Aires Interior'),
        ('Catamarca', 'Catamarca'),
        ('Chaco', 'Chaco'),
        ('Chubut', 'Chubut'),
        ('Cordoba', 'Cordoba'),
        ('Corrientes', 'Corrientes'),
        ('Entre Rios', 'Entre Rios'),
        ('Formosa', 'Formosa'),
        ('Santiago del Estero', 'Santiago del Estero'),
        ('Jujuy', 'Jujuy'),
        ('La Pampa', 'La Pampa'),
        ('La Rioja', 'La Rioja'),
        ('Mendoza', 'Mendoza'),
        ('Misiones', 'Misiones'),
        ('Neuquen', 'Neuquen'),
        ('Rio Negro', 'Rio Negro'),
        ('Salta', 'Salta'),
        ('San Juan', 'San Juan'),
        ('San Luis', 'San Luis'),
        ('Santa Cruz', 'Santa Cruz'),
        ('Santa Fe', 'Santa Fe')
    ]
    calidad = forms.ModelChoiceField(queryset=TipoDeOrigen.objects.all(),
                                     initial=None, empty_label='Todas', required=False)
    vendedor = forms.ModelChoiceField(required=False, queryset=None, empty_label='Ninguno')
    calidad_numerica = forms.IntegerField(initial=0)
    mensaje = forms.CharField(max_length=255, required=False)
    numero_telefono = forms.CharField(max_length=64, required=False)
    email = forms.EmailField(widget=forms.TextInput(attrs={'placeholder': 'Ejemplo: <EMAIL>'}),
                             required=False)
    provincia = forms.ChoiceField(choices=[('', 'Ubicacion')] + PROVINCIA_CHOICES, required=False,
                                  label='Ubicacion')
    marca = forms.ModelChoiceField(queryset=None, required=False, to_field_name='id')
    modelos = forms.ModelMultipleChoiceField(queryset=None, required=False)
    nombre = forms.CharField(max_length=64, required=False)
    nombre_alternativo = forms.CharField(max_length=64, required=False)
    nombre_de_campania = forms.CharField()

    def __init__(self, *args, **kwargs):
        vendedor = kwargs.pop('vendedor')
        definir_marca_por_defecto = kwargs.pop('definir_marca_por_defecto', False)
        super(CargaDeProspectoForm, self).__init__(*args, **kwargs)
        vendedores = vendedor.vendedores_a_cargo()
        self.fields['vendedor'].queryset = vendedores
        if vendedores:
            vendedor_inicial = vendedores[0]
            self.fields['vendedor'].initial = vendedor_inicial
            vendedor_del_cual_tomar_marcas = vendedor_inicial
        else:
            vendedor_del_cual_tomar_marcas = vendedor
        self._definir_marcas_elegibles(vendedor_del_cual_tomar_marcas)
        self.fields['modelos'].queryset = Modelo.objects
        self.vaciar_choices_de_modelos()
        if definir_marca_por_defecto:
            self._definir_marca_elegida_por_defecto()

    def vaciar_choices_de_modelos(self):
        self.fields['modelos'].choices = []

    def _definir_marca_elegida_por_defecto(self):
        """Define por defecto la primer marca que no sea marca blanca (es decir, la segunda opcion, si la hubiera)"""
        choice_por_defecto = None
        if len(self.fields['marca'].choices) > 1:
            choice_por_defecto = self.fields['marca'].choices[1][0]
        self.initial['marca'] = choice_por_defecto

    def definir_choices_de_modelos(self):
        "Definimos las choices validas del campo modelos segun lo que tiene elegido en marca"
        choices_validas = []
        nombre_de_marca = self.data.get('marca', '')
        for modelo in Modelo.objects.normalizados():
            if modelo.marca().nombre() == nombre_de_marca:
                choices_validas.append((modelo.pk, modelo.nombre()))
        self.fields['modelos'].choices = choices_validas

    def _definir_marcas_elegibles(self, vendedor_del_cual_tomar_marcas):
        "Define las marcas que se pueden elegir y la marca por defecto"
        marcas = [Marca.blanca()]
        marcas_pedidas_del_vendedor = self._marcas_pedidas_del_vendedor(vendedor_del_cual_tomar_marcas)
        marcas.extend(marcas_pedidas_del_vendedor)

        choices = [(marca.id, marca.nombre()) for marca in marcas]

        self.fields['marca'].choices = choices
        self.fields['marca'].queryset = Marca.objects

    def _marcas_pedidas_del_vendedor(self, vendedor_del_cual_tomar_marcas):
        marcas_strings = vendedor_del_cual_tomar_marcas.marcas_pedidas()
        marcas = list(Marca.objects.con_nombres(marcas_strings))
        return marcas

    def clean(self):
        super(CargaDeProspectoForm, self).clean()
        self._validar_datos_de_contacto()
        self._validar_calidad_numerica()
        self._validar_modelos()

    def _validar_datos_de_contacto(self):
        if self.has_error('numero_telefono'):
            return
        if self.has_error('email'):
            return
        telefono = self.cleaned_data.get('numero_telefono', '')
        email_completo = self.cleaned_data.get('email', '')

        if not telefono and not email_completo:
            from django.forms.forms import NON_FIELD_ERRORS
            self.add_error(NON_FIELD_ERRORS, self.mensaje_de_error_faltan_datos_de_contacto())

    def _validar_calidad_numerica(self):
        if self.has_error('calidad_numerica'):
            return
        calidad_numerica = self.cleaned_data.get('calidad_numerica', 0)
        if not calidad_numerica > 0 or not calidad_numerica <= 5:
            self.add_error('calidad_numerica', self.mensaje_de_error_valor_de_calidad_numerica_invalido())

    def _validar_modelos(self):
        if self.has_error('marca'):
            return
        if self.has_error('modelos'):
            return
        modelos = self.cleaned_data['modelos']
        marca = self.cleaned_data['marca']
        for modelo in modelos:
            if modelo.marca() != marca:
                self.add_error('modelos', 'El modelo seleccionado ({0}) debe ser de la marca seleccionada'.format(modelo.nombre()))
        return modelos

    @classmethod
    def mensaje_de_error_faltan_datos_de_contacto(self):
        return 'Se debe completar al menos uno de los datos de contacto (Email ó Telefono)'

    @classmethod
    def mensaje_de_error_valor_de_calidad_numerica_invalido(self):
        return 'La calidad numérica debe estar entre 1 y 5'

    @classmethod
    def mensaje_de_error_no_se_selecciono_calidad(self):
        return 'Por favor seleccione una calidad para crear la campaña'
