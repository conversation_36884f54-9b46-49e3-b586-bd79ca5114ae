from django.core.exceptions import PermissionDenied
from django.urls import reverse
from django.http.response import JsonResponse
from django.views.generic.base import View

from prospectos.models import Prospecto
from vendedores.decorators import vendedor_o_supervisor_requerido, class_view_decorator


class RedireccionarARepetidoView(View):

    def dispatch(self, request, *args, **kwargs):
        """
            No usamos @class_view_decorator(vendedor_o_supervisor_requerido) porque hace un redirect en lugar
            de lanzar la excepcion PermissionDenied
        """
        user = request.user
        if not (user.is_authenticated() and user.is_active and user.is_vendedor()):
            return PermissionDenied()
        return super(RedireccionarARepetidoView, self).dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        id_prospecto = request.GET.get('id_prospecto')
        vendedor = request.user.vendedor
        try:
            siguiente = self._siguiente_repetido_para(id_prospecto, vendedor)
        except Prospecto.DoesNotExist:
            raise PermissionDenied()

        return self._response(siguiente)

    def _siguiente_repetido_para(self, id_prospecto, vendedor):
        if vendedor.es_supervisor():
            prospecto = vendedor.prospectos_a_cargo.get(pk=id_prospecto)
            siguiente = prospecto.siguiente_repetido_para_supervisor()
        else:
            prospecto = vendedor.prospectos.get(pk=id_prospecto)
            siguiente = prospecto.siguiente_repetido_para_vendedor()
        return siguiente

    def _response(self, siguiente_repetido):
        if siguiente_repetido is None:
            # No hay cadena disponible para el Prospecto (osea no tiene repetidos).
            return JsonResponse({'status': False, 'error_message': 'El Prospecto no posee Repetidos disponibles.'})
        else:
            url_detalle_prospecto = reverse('prospecto', args=[siguiente_repetido.pk])
            return JsonResponse({'status': True, 'url_detalle_prospecto': url_detalle_prospecto})