import json
from fractions import Fraction

from django.core.exceptions import PermissionDenied
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.views.generic import View

from campanias.models import Campania, TipoDeOrigen, DistribuidorCampania
from lib.timing import timing
from occ.views.configuracion_de_cuenta import ConfiguracionDeCuenta
from permisos.templatetags.permisos import tiene_permiso_para
from prospectos.aplicacion.commands.programar_llamados import ProgramarLlamadoAutomaticamenteCommand
from prospectos.aplicacion.operaciones_oportunidades import CrearNuevaProgramacionParaSupervisor, \
    AsignarObjetivosAVendedores
from prospectos.forms import FiltroRangoDeFechaForm
from prospectos.models import Prospecto
from prospectos.services.pedir_prospecto_service import PedirProspectoService
from prospectos.utils.filtros import FiltrosParaProspectos
from vendedores.forms import IntegranteDeEquipoForm
from vendedores.gestor import GestorDeVendedores


class ResumenView(View):
    def get(self, request):
        renderer = ResumenRender.nuevo_para(request.user)
        return renderer.render(request)


class HomeRedirectView(View):
    def get(self, request):
        user = request.user
        gestor_de_vendedores = GestorDeVendedores()
        if user.is_supervisor() and not gestor_de_vendedores.tiene_pedidos_a_vendedores_en_los_ultimos_meses(
                user.vendedor, 2):
            return redirect("administracion")
        else:
            return redirect("resumen")


class ResumenRender(object):
    def __init__(self, rol):
        super(ResumenRender, self).__init__()
        self._rol = rol

    def rol(self):
        return self._rol

    def render(self, request):
        context = self._context(request)
        return render(request, template_name=self._template(), context=context)

    def _template(self):
        raise NotImplementedError('Subclass responsibility')

    def _context(self, request):
        raise NotImplementedError('Subclass responsibility')

    def _obtener_tipos_de_origen(self, supervisores):
        tipos_de_origen = TipoDeOrigen.objects.all()
        return tipos_de_origen

    @classmethod
    def nuevo_para(cls, user):
        subclases = cls.__subclasses__()
        for each in subclases:
            if each.puede_dibujar_a(user):
                return each(user.role())
        raise ValueError('Rol incorrecto: %s' % user)

    @classmethod
    def puede_dibujar_a(cls, user):
        raise NotImplementedError('subclass responsibility')


class ResumenParaGerenteRender(ResumenRender):
    def _template(self):
        return 'resumen/resumen-para-gerente.html'

    def _context(self, request):
        gerente = self.rol()

        supervisores = gerente.supervisores().select_related('user', 'equipo')
        all_campanias = data_campanias(supervisores)
        context = {'supervisores': supervisores, 'json_supervisores': data_supervisores(supervisores),
                   'tipos_de_origen': self._obtener_tipos_de_origen(supervisores),
                   'distribuidores': data_distribuidor(all_campanias[1]), 'marcas': self._data_marca(gerente)}
        context['json_campanias'], id_campanias = all_campanias
        context['json_nombres_campanias'] = data_nombres_campanias(id_campanias)
        return context

    def _datos_prospectos_para_gerente(self, gerente, supervisores):
        result = []
        for supervisor in supervisores:
            prospectos = Prospecto.objects.filter(responsable=supervisor)
            resumen_prospectos = data_resumen(prospectos)
            result.append((supervisor.id, resumen_prospectos))

        prospectos = gerente.prospectos()
        resumen_total = data_resumen(prospectos)
        result.append(('todos_los_supervisores', resumen_total))
        return json.dumps(result)

    @classmethod
    def puede_dibujar_a(cls, user):
        return user.is_gerente()

    def _data_marca(self, gerente):
        return gerente.prospectos().marcas()


class ResumenParaVendedorRender(ResumenRender):
    @classmethod
    def puede_dibujar_a(cls, user):
        return user.is_vendedor() and not user.is_supervisor()

    def _template(self):
        return 'resumen/resumen-para-vendedores.html'

    def _context(self, request):
        vendedor = self.rol()
        prospectos = prospectos_asignados(request)
        context = {}
        if tiene_permiso_para(request.user.cargo, 'administrar_equipos'):
            context['equipos'] = vendedor.equipos.all()
            context['json_equipos'] = data_equipos(vendedor)

        context['muestra_boton_pedir_prospecto'] = PedirProspectoService.cache_muestra_boton_pedir_prospecto(
            request.user)

        # TODO: Todo vendedor debe tener supervisor, no hara falta checkearlo cuando esto se cumpla.
        if vendedor.supervisor:
            context = self._agregar_informacion_de_objetivos(context=context, vendedor=vendedor)
            supervisores = {vendedor.supervisor}
        else:
            supervisores = {vendedor}
        id_campanias = prospectos.values_list('campania', flat=True).distinct()
        context['campanias'] = Campania.objects.filter(id__in=id_campanias).select_related(
            'categoria__tipo_de_origen')
        context['enfocar_calendario'] = request.GET.get('calendario', False)
        context['tipos_de_origen'] = self._obtener_tipos_de_origen(supervisores)
        context['agendar_prospectos_sin_agendar_form'] = ProgramarLlamadoAutomaticamenteCommand().get_form()
        return context


    def _agregar_informacion_de_objetivos(self, context, vendedor):
        periodo = vendedor.obtener_concesionaria().periodo_actual()
        objetivo = vendedor.objetivo_en_periodo(periodo)
        if objetivo is not None:
            context['porcentaje_objetivo'] = vendedor.porcentaje_objetivo_cumplido_en_periodo_actual()
            tooltip = objetivo.descripcion_de_progreso()
            context['tooltip_objetivo'] = tooltip
        return context

class ResumenParaSupervisorRender(ResumenRender):
    def _template(self):
        return 'resumen/resumen-para-supervisores.html'

    @classmethod
    def puede_dibujar_a(cls, user):
        return user.is_supervisor()

    def _context(self, request):
        supervisor = self.rol()
        # Viejo contexto, posiblemente se modifique o quite esta seccion de la pantalla

        prospectos = prospectos_asignados(request)
        context = {}
        if tiene_permiso_para(request.user.cargo, 'administrar_equipos'):
            context['equipos'] = supervisor.equipos.all()
            context['json_equipos'] = data_equipos(supervisor)

        supervisores = {supervisor}
        id_campanias = prospectos.values_list('campania', flat=True).distinct()
        context['campanias'] = Campania.objects.filter(id__in=id_campanias).select_related(
            'categoria__tipo_de_origen')
        context['tipos_de_origen'] = self._obtener_tipos_de_origen(supervisores)

        if supervisor.puede_acceder_a_los_proveedores_de_datos():
            context['distribuidores'] = data_distribuidor(id_campanias)


        # Nuevo contexto
        crear_nuevo_reporte = CrearNuevaProgramacionParaSupervisor({'supervisor': supervisor})
        asignar_objetivos = AsignarObjetivosAVendedores({'supervisor': supervisor})
        configuracion = ConfiguracionDeCuenta.nuevo_para(supervisor)
        context['datos_vendedores_y_equipos'] = json.dumps(self._datos_vendedores_y_equipos(supervisor))
        context['integrantes_de_equipo_form'] = IntegranteDeEquipoForm(supervisor=supervisor)
        context['rango_de_fechas_form'] = FiltroRangoDeFechaForm()
        context['oportunidades'] = {
            'esta_activado_asignar_objetivos': asignar_objetivos.tiene_vendedores_para_asignar_objetivos(),
            'explicacion_asignar_objetivos': asignar_objetivos.explicacion(),
            'esta_activado_auto_tienda': supervisor.configuracion_de_servicios().auto_tienda_habilitado(),
            'esta_activado_moto_autos': supervisor.configuracion_de_servicios().moto_autos_habilitado(),
            'esta_activado_oportunidades_reportes': not crear_nuevo_reporte.tiene_reporte_hecho(),
            'esta_activada_seccion_de_ranking_de_vendedores': not supervisor.tiene_ranking_habilitado(),
            'esta_activada_seccion_de_gmail': configuracion.puede_configurar(),
            'agendar_prospectos_sin_agendar_form': ProgramarLlamadoAutomaticamenteCommand().get_form()

        }
        context['muestra_boton_pedir_prospecto'] = PedirProspectoService.cache_muestra_boton_pedir_prospecto(
            request.user)

        return context

    def _datos_vendedores_y_equipos(self, supervisor):
        """
            Responde un json con {
                vendedores: [{id, nombre}],
                equipos:[ {id: '', 'nombre': '', 'integrantes': [{id, nombre}]}]}
        """
        result = {
            'vendedores': [],
            'equipos': {}
        }
        for vendedor in supervisor.vendedores.all().select_related('user'):
            result['vendedores'].append(self._datos_de_vendedor(vendedor))
            equipos = result['equipos']
            if vendedor.equipo_id is not None:
                equipo = vendedor.equipo
                datos_equipos = equipos.setdefault(
                    equipo.id, {'id': equipo.id, 'nombre': equipo.nombre, 'integrantes': []})
                datos_equipos['integrantes'].append(self._datos_de_vendedor(vendedor))
        return result

    def _datos_de_vendedor(self, vendedor, template=None):
        template = template or '%s'
        return {'id': vendedor.id, 'nombre': template % vendedor.full_name()}


class DatosDeResumenView(View):
    def get(self, request):
        user = request.user
        if user.is_vendedor():
            prospectos = prospectos_asignados(request)
            puede_administrar_equipos = tiene_permiso_para(user.cargo, 'administrar_equipos')
        elif user.is_gerente():
            puede_administrar_equipos = True
            supervisor = request.GET.get('supervisor')
            prospectos = self._prospectos_aigados_a_supervisor(supervisor, user.gerente)
        else:
            raise PermissionDenied()

        filtrador = FiltrosParaProspectos(filtrados=prospectos, usuario=request.user)
        filtros = filtrador.obtener_filtros_para_resumen(request, puede_administrar_equipos)
        filtrador.filtrar_por_fecha_y_origen(
            filtros['desde'], filtros['hasta'], filtros['tipo'], filtros['tipo_de_fecha'])
        campanias = set()
        if user.is_gerente() or user.is_supervisor():
            if filtros['campania']:
                campanias.add(filtros['campania'])
            if user.is_gerente():
                gerente = user.role()
                supervisores_filtrado = gerente.supervisores().select_related('user', 'equipo')
                all_campanias_filtrado = data_campanias(supervisores_filtrado)
                _, campanias = all_campanias_filtrado
            if user.is_supervisor():
                if filtros['vendedor']:
                    vendedor_a_filtrar = int(filtros['vendedor'])
                    obtencion_campanias_asociadas_a_vendedores = data_campanias_vendedor(vendedor_a_filtrar)
                else:
                    obtencion_campanias_asociadas_a_vendedores = data_campanias_vendedor(user.vendedor.id)
                campanias = obtencion_campanias_asociadas_a_vendedores
        filtrador.filtrar_por_equipo(filtros['equipo'], filtros['vendedor'])
        filtrador.filtrar_por_campania(filtros['campania'])
        filtrador.filtrar_por_distribuidor(list(campanias), filtros['distribuidor'])
        filtrador.filtrar_por_marca(filtros)
        prospectos = filtrador.prospectos_filtrados()
        response = data_resumen(prospectos)
        response['status'] = True

        return JsonResponse(response)

    def _prospectos_aigados_a_supervisor(self, supervisor_id, gerente):
        if not supervisor_id or supervisor_id == 'todos_los_supervisores':
            return gerente.prospectos()
        else:
            return Prospecto.objects.filter(responsable_id=supervisor_id)


@timing
def data_resumen(prospectos):
    result = {}
    cantidad_de_ventas = prospectos.vendidos().count()
    result['ventas'] = str(cantidad_de_ventas)
    cantidad_de_nuevos = prospectos.filter(estado=Prospecto.NUEVO).count()
    cantidad_de_en_proceso = prospectos.filter(estado=Prospecto.EN_PROCESO).count()
    result['prospectos_nuevos'] = str(cantidad_de_nuevos)
    result['prospectos_activos'] = str(cantidad_de_nuevos + cantidad_de_en_proceso)
    cantidad_de_prospectos = prospectos.count()
    result['prospectos'] = str(cantidad_de_prospectos)

    ranks = calcular_ranking(cantidad_de_prospectos, cantidad_de_ventas)
    result['ranking_num'] = ranks[0]
    result['ranking_den'] = ranks[1]
    return result


def calcular_ranking(cant_prospectos, cant_ventas):
    if cant_prospectos > 0:
        if cant_ventas > 0:
            ranking = Fraction(cant_ventas, cant_prospectos)
            return 1, (ranking.denominator // ranking.numerator)
        return 0, cant_prospectos
    return '-', '-'


def data_campanias(supervisores):
    result = {}
    all_id_campanias = set()
    for supervisor in supervisores:
        prospectos = Prospecto.objects.filter(Q(vendedor=supervisor) | Q(responsable=supervisor))
        id_campanias = list(prospectos.values_list('campania_id', flat=True).distinct())
        all_id_campanias = all_id_campanias.union(id_campanias)
        result[supervisor.id] = id_campanias
    return json.dumps(result), all_id_campanias


def data_campanias_vendedor(vendedor):
    prospectos = Prospecto.objects.filter(Q(vendedor=vendedor) | Q(responsable=vendedor))
    id_campanias = list(prospectos.values_list('campania_id', flat=True).distinct())
    return id_campanias


def data_nombres_campanias(id_campanias):
    campanias = Campania.objects.filter(id__in=id_campanias).select_related('categoria__tipo_de_origen')
    result = {}
    for campania in campanias:
        descripcion = '%s (%s)' % (campania.nombre, campania.nombre_origen())
        result[campania.id] = descripcion
    return json.dumps(result)


def data_prospectos(supervisores):
    result = []
    for supervisor in supervisores:
        prospectos = Prospecto.objects.filter(Q(vendedor=supervisor) | Q(responsable=supervisor))
        resumen_prospectos = data_resumen(prospectos)
        result.append((supervisor.id, resumen_prospectos))
    return json.dumps(result)


def equipos_del_supervisor(supervisor):
    equipos = supervisor.equipos.all()
    result = []
    for equipo in equipos:
        result.append((equipo.id, equipo.nombre))
    return result


def data_supervisores(supervisores):
    result = {}
    for supervisor in supervisores:
        info = {'0': equipos_del_supervisor(supervisor), 1: build_data_equipos(supervisor)}
        result[supervisor.id] = info
    return json.dumps(result)


def data_equipos(supervisor):
    return json.dumps(build_data_equipos(supervisor))


def build_data_equipos(supervisor):
    result = {'0': [(supervisor.id, supervisor.full_name())]}
    for vendedor in supervisor.vendedores.all().select_related('user'):
        result['0'].append((vendedor.id, vendedor.full_name()))
        if vendedor.equipo_id is not None:
            if not vendedor.equipo_id in result:
                result[vendedor.equipo_id] = []
            result[vendedor.equipo_id].append((vendedor.id, vendedor.user.get_full_name()))
    return result



def data_distribuidor(id_campanias):
    distribuidores = DistribuidorCampania.objects.filter(categoriadecampania__campanias__in=id_campanias).distinct()
    return distribuidores


def prospectos_asignados(request):
    vendedor = request.user.vendedor
    prospectos = vendedor.todos_los_prospectos()
    return prospectos
