from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import get_template


class MailPorRechazo(object):

    def __init__(self, supervisor, cantidad, origen):
        self.supervisor = supervisor
        self.cantidad = cantidad
        self.origen = origen

    def enviar(self):
        nombre = self.supervisor.user.get_full_name()
        texto = "El supervisor %s ha rechazado %d prospectos de origen %s" % (nombre, self.cantidad, self.origen)
        template = get_template('emails/mail_por_rechazo.html')
        context = {'nombre': nombre,
                   'cantidad': self.cantidad,
                   'origen': self.origen,
                   }
        html_msg = template.render(context)

        email = EmailMultiAlternatives(settings.SUBJECT_AVISO_RECHAZO % nombre,
                                       texto,
                                       settings.DEFAULT_FROM_EMAIL,
                                       [settings.EMAIL_LOGISTICA])
        email.attach_alternative(html_msg, "text/html")
        email.send()
