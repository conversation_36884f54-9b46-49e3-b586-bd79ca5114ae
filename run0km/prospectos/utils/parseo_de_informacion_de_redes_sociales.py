#-*- coding: utf-8 -*-
import json

from django.conf import settings
from django.core.exceptions import ValidationError

from lib.redescover import SocialNetworksParseResponseError


class SelectorDeDeDatosDeInformacionDeRedesSociales(object):
    def obtener_datos_de_canal(self, informacion_de_canal):
        if 'Tipo' not in informacion_de_canal:
            raise SocialNetworksParseResponseError(message='Se esperaba el campo "Tipo"',
                                                   request=None,
                                                   response=json.dumps(informacion_de_canal))
        from prospectos.models import InformacionDeRedesSociales
        tipo = informacion_de_canal['Tipo']
        if tipo == InformacionDeRedesSociales.TELEFONO:
            self._verificar_estructura_de_telefono(informacion_de_canal)
        if tipo == InformacionDeRedesSociales.DIRECCION:
            self._verificar_estructura_de_direccion(informacion_de_canal)
        if tipo == InformacionDeRedesSociales.CELULAR:
            self._verificar_estructura_de_celular(informacion_de_canal)
        if tipo == InformacionDeRedesSociales.EMAIL:
            self._verificar_estructura_de_email(informacion_de_canal)
        tipo = informacion_de_canal['Tipo']
        informacion_de_canal = informacion_de_canal['Canal']
        return tipo, json.dumps(informacion_de_canal)

    def _verificar_estructura_de_telefono(self, informacion_de_canal):
        campos_faltantes = []
        if 'Canal' not in informacion_de_canal:
            campos_faltantes.append('Canal')
        else:
            if 'Telefono' not in informacion_de_canal['Canal']:
                campos_faltantes.append('Telefono')
            else:
                if 'Numero' not in informacion_de_canal['Canal']['Telefono']:
                    campos_faltantes.append('Numero')
        if campos_faltantes:
            raise SocialNetworksParseResponseError(message='Faltan los campos: ' + ', '.join(campos_faltantes),
                                                   request=None,
                                                   response=json.dumps(informacion_de_canal))

    def _verificar_estructura_de_celular(self, informacion_de_canal):
        campos_faltantes = []
        if 'Canal' not in informacion_de_canal:
            campos_faltantes.append('Canal')
        else:
            if 'Celular' not in informacion_de_canal['Canal']:
                campos_faltantes.append('Celular')
            else:
                if 'Numero' not in informacion_de_canal['Canal']['Celular']:
                    campos_faltantes.append('Numero')
        if campos_faltantes:
            raise SocialNetworksParseResponseError(message='Faltan los campos: ' + ', '.join(campos_faltantes),
                                                   request=None,
                                                   response=json.dumps(informacion_de_canal))

    def _verificar_estructura_de_direccion(self, informacion_de_canal):
        campos_faltantes = []
        if 'Canal' not in informacion_de_canal:
            campos_faltantes.append('Canal')
        else:
            if 'Direccion' not in informacion_de_canal['Canal']:
                campos_faltantes.append('Direccion')
        if campos_faltantes:
            raise SocialNetworksParseResponseError(message='Faltan los campos: ' + ', '.join(campos_faltantes),
                                                   request=None,
                                                   response=json.dumps(informacion_de_canal))

    def _verificar_estructura_de_email(self, informacion_de_canal):
        campos_faltantes = []
        if 'Canal' not in informacion_de_canal:
            campos_faltantes.append('Canal')
        else:
            if 'Email' not in informacion_de_canal['Canal']:
                campos_faltantes.append('Email')
            else:
                if 'Direccion' not in informacion_de_canal['Canal']['Email']:
                    campos_faltantes.append('Direccion')
        if campos_faltantes:
            raise SocialNetworksParseResponseError(message='Faltan los campos: ' + ', '.join(campos_faltantes),
                                                   request=None,
                                                   response=json.dumps(informacion_de_canal))

    def obtener_datos_fisica(self, fisica):
        if 'Nombre' in fisica and 'Apellido' in fisica:
            return 'Nombre completo', '%s %s' % (fisica['Nombre'], fisica['Apellido'])
        return None, None

    def obtener_datos_de_identificaciones(self, identificaciones):
        datos = []
        for identificacion in identificaciones:
            if 'Tipo' in identificacion and 'Nombre' in identificacion['Tipo'] and 'Valor' in identificacion:
                datos.append({'tipo': 'Identificación',
                              'valor': '%s: %s' % (identificacion['Tipo']['Nombre'], identificacion['Valor'])})
        return datos

    def obtener_datos_de_vehiculos(self, vehiculos):
        datos_de_vehiculos = []
        for vehiculo in vehiculos:
            if 'Marca' in vehiculo and 'Modelo' in vehiculo:
                datos_de_vehiculos.append('%s %s' % (vehiculo['Marca'], vehiculo['Modelo']))
        return ', '.join(datos_de_vehiculos)


class MostradorDeInformacionDeRedesSociales(object):
    @classmethod
    def nuevo(cls):
        mostrador = cls()
        return mostrador

    @classmethod
    def para_foto(cls):
        return MostradorDeInformacionDeFoto.nuevo()

    @classmethod
    def para_twitter(cls):
        return MostradorDeInformacionDeTwitter.nuevo()

    @classmethod
    def para_facebook(cls):
        return MostradorDeInformacionDeFacebook.nuevo()

    @classmethod
    def para_telefono(cls):
        return MostradorDeInformacionDeTelefono.nuevo()

    @classmethod
    def para_email(cls):
        return MostradorDeInformacionDeEmail.nuevo()

    @classmethod
    def para_celular(cls):
        return MostradorDeInformacionDeCelular.nuevo()

    @classmethod
    def para_vehiculo(cls):
        return MostradorDeInformacionDeVehiculo.nuevo()

    @classmethod
    def para_nombre(cls):
        return MostradorDeInformacionDeNombre.nuevo()

    @classmethod
    def para_direccion(cls):
        return MostradorDeInformacionDeDireccion.nuevo()

    @classmethod
    def para_identificacion(cls):
        return MostradorDeInformacionDeIdentificacion.nuevo()

    @classmethod
    def _nombre_del_tipo(cls):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def puede_mostrar(cls, informacion_de_redes_sociales):
        return cls._nombre_del_tipo() == informacion_de_redes_sociales.tipo

    @classmethod
    def para_informacion(cls, informacion_de_redes_sociales):
        for mostrador in cls.__subclasses__():
            if mostrador.puede_mostrar(informacion_de_redes_sociales=informacion_de_redes_sociales):
                return mostrador.nuevo()

        raise ValidationError(message='No hay Mostrador para el tipo de informacion %s' % informacion_de_redes_sociales.tipo)

    def mostrar(self, informacion_de_redes_sociales):
        if self.__class__.puede_mostrar(informacion_de_redes_sociales=informacion_de_redes_sociales):
            return self._valor_a_mostrar(informacion=informacion_de_redes_sociales.valor)
        else:
            raise ValidationError(message='El ' + self.__class__.__name__ + ' no puede mostrar la informacion solicitada')

    def _valor_a_mostrar(self, informacion):
        raise NotImplementedError('Subclass responsibility')


class MostradorDeInformacionDeDireccion(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.DIRECCION

    def _valor_a_mostrar(self, informacion):
        info_json = json.loads(informacion)
        datos_de_direccion = [self._obtener_calle(info_json), self._obtener_localidad(info_json),
                              self._obtener_provincia(info_json)]
        datos_de_direccion = [_f for _f in datos_de_direccion if _f]
        return ', '.join(datos_de_direccion)

    def _obtener_calle(self, info_json):
        try:
            return info_json['Direccion']['Calle']
        except KeyError:
            return ''

    def _obtener_localidad(self, info_json):
        try:
            return info_json['Direccion']['Localidad']['Nombre']
        except KeyError:
            return ''

    def _obtener_provincia(self, info_json):
        try:
            return info_json['Direccion']['Localidad']['Provincia']['Nombre']
        except KeyError:
            return ''


class MostradorDeInformacionDeFoto(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.FOTO

    def _valor_a_mostrar(self, informacion):
        return informacion


class MostradorDeInformacionDeVehiculo(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.VEHICULOS

    def _valor_a_mostrar(self, informacion):
        return informacion


class MostradorDeInformacionDeIdentificacion(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.IDENTIFICACION

    def _valor_a_mostrar(self, informacion):
        return informacion


class MostradorDeInformacionDeNombre(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.NOMBRE_COMPLETO

    def _valor_a_mostrar(self, informacion):
        return informacion


class MostradorDeInformacionDeCelular(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.CELULAR

    def _valor_a_mostrar(self, informacion):
        tipo = self._nombre_del_tipo()
        return json.loads(informacion)[tipo]['Numero']


class MostradorDeInformacionDeEmail(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.EMAIL

    def _valor_a_mostrar(self, informacion):
        tipo = self._nombre_del_tipo()
        return json.loads(informacion)[tipo]['Direccion']


class MostradorDeInformacionDeTelefono(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.TELEFONO

    def _valor_a_mostrar(self, informacion):
        tipo = self._nombre_del_tipo()
        return json.loads(informacion)[tipo]['Numero']


class MostradorDeInformacionDeTwitter(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.TWITTER

    def _valor_a_mostrar(self, informacion):
        tipo = self._nombre_del_tipo()
        twitter_url = settings.TWITTER_URL + str(json.loads(informacion)[tipo]['Username'])
        return twitter_url


class MostradorDeInformacionDeFacebook(MostradorDeInformacionDeRedesSociales):
    @classmethod
    def _nombre_del_tipo(cls):
        from prospectos.models import InformacionDeRedesSociales
        return InformacionDeRedesSociales.FACEBOOK

    def _valor_a_mostrar(self, informacion):
        tipo = self._nombre_del_tipo()
        return str(json.loads(informacion)[tipo]['Url'])
