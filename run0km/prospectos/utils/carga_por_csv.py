# -*- coding: utf-8 -*-
import csv
import errno
import os
import io

from django.conf import settings
from django.core.files import File
from django.db import transaction
from django.utils.encoding import smart_str
from django.utils.timezone import now

from layers.application.commands.ingreso_de_prospectos.puesto_de_ingreso_de_prospectos import \
    PuestoDeIngresoDeProspectos
from prospectos import tasks

from prospectos.configuracion import CAMPOS_DE_CARGA_DE_PROSPECTO
from prospectos.models import SubidaErronea
from prospectos.models.carga import CargadorDeProspectos
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.models.exceptions import LoteDeProspectosRechazadoException
from prospectos.models.origen import OrigenDeProspectoCSV


class CargadorDeProspectosPorCSV(object):
    """
        Lee los datos con formato CSV, a partir de ellos crea prospectos.
        Desde los datos se puede indicar o no el vendedor, el supervisor o el pedido. En caso de indicarse el pedido se
        registra el consumo. Si no se indica supervisor ni vendedor, el prospecto queda sin asignar. Los datos vienen
        del formulario AddProspectosCSVAdminForm que expresa su validez, por ejemplo:
            - Si se selecciono vendedor y responsable: el vendedor debe tener como responsable a aquel seleccionados
            - Si se selecciono un pedido, debe pertecer al supervisor seleccionado.
    """
    columnas_a_ignorar = ['id', 'estado', 'campaña', 'campaña', 'exportado', 'ultima_asignacion', 'source',
                          'error-de-carga']

    def __init__(self, cleaned_data, ejecutor):
        self.cleaned_data = cleaned_data
        self.ejecutor = ejecutor
        self.vendedores_erroneos = []
        self.log = None
        self._puesto_de_ingreso = PuestoDeIngresoDeProspectos.nuevo()

    def cargar_prospectos(self):
        data = self.cleaned_data
        codigo_de_tipo_de_origen = self._obtener_codigo_de_tipo_de_origen(data)
        campos_generales = self._obtener_campos_generales_desde(data)

        resultado = self._pedir_acceso_e_ingresar_prospectos_desde(
            campos_generales=campos_generales, codigo_de_tipo_de_origen=codigo_de_tipo_de_origen,
            file_upload_name=data['file_upload'], pedido=data['pedido'])

        if resultado.tiene_filas_erroneas():
            self._loggear_errores(data, resultado, codigo_de_tipo_de_origen)
        return resultado

    def _obtener_codigo_de_tipo_de_origen(self, data):
        codigo_de_tipo_de_origen = data['origen'].codigo if data['origen'] else None
        return codigo_de_tipo_de_origen

    def _obtener_campos_generales_desde(self, cd):
        campos_generales = {'campania': cd.get('campania', None)}
        if cd['responsable']:
            campos_generales['responsable'] = cd['responsable']
        if cd['vendedor']:
            campos_generales['vendedor'] = cd['vendedor']
        if cd['proveedor']:
            campos_generales['proveedor'] = cd['proveedor']
        if cd['pedido']:
            campos_generales['pedido'] = cd['pedido']
        return campos_generales

    def _pedir_acceso_e_ingresar_prospectos_desde(
            self, campos_generales, codigo_de_tipo_de_origen, file_upload_name, pedido):
        resultado = self._parsear_datos_desde(file_upload_name)
        datos_de_prospectos = resultado['datos_de_prospectos']
        datos_por_fila = resultado['datos_por_fila']
        resultado_de_carga = resultado['resultado_de_carga']

        try:
            self._puesto_de_ingreso.ingresar(
                datos_de_prospectos, metodo_constructor=self._crear_prospectos_desde,
                argumentos=[campos_generales, codigo_de_tipo_de_origen, datos_por_fila, pedido, resultado_de_carga])
        except LoteDeProspectosRechazadoException as exc:
            resultado_de_carga.agregar_lote_fallido(datos_por_fila, mensaje_de_error=str(exc))

        return resultado_de_carga

    @transaction.atomic
    def _crear_prospectos_desde(
            self, campos_generales, codigo_de_tipo_de_origen, datos_por_fila, pedido, resultado):
        for fila_parseada in datos_por_fila:
            indice = fila_parseada['indice']
            fila = fila_parseada['fila']
            datos_de_prospecto = fila_parseada['datos_de_prospecto']
            datos_extra = fila_parseada['datos_extra']
            try:
                cargador = CargadorDeProspectos(origen_obligatorio=False)
                resultado_de_carga = cargador.cargar_prospecto(
                    datos_generales=campos_generales,
                    datos_de_prospecto=datos_de_prospecto,
                    datos_extra=datos_extra,
                    codigo_de_tipo_de_origen=codigo_de_tipo_de_origen,
                    origen_de_prospecto=OrigenDeProspectoCSV())

            except Exception as exc:
                resultado.registrar_falla(indice, fila, str(exc))
            else:
                if resultado_de_carga.es_erroneo():
                    resultado.registrar_falla(indice, fila, resultado_de_carga.mensaje_de_error())
                else:
                    prospecto = resultado_de_carga.prospecto()
                    self._completar_informacion_de(prospecto)
                    resultado.registrar_exito(prospecto)

        if pedido:
            administrador = AdministradorDePedidos()
            consumido = resultado.cantidad_consumida()
            prospectos = resultado.prospectos_cargados()
            administrador.registrar_asignacion_de_prospectos(pedido, prospectos, consumido)

        return resultado

    def _parsear_datos_desde(self, file_upload_name):
        """
            Interpreta file_upload_name y responde un json con el resultado de carga de prospecto (registra las fallas),
            datos de cada prospecto y datos por fila ({indice, fila, datos_de_prospecto, datos_extra})
        """

        prospectos = []
        datos_por_fila = []
        resultado = ResultadoDeCargaDeProspectosViaCSV.nuevo()
        csv_read = self.unicode_csv_read(file_upload_name)
        columnas_basicas = columnas_extra = {}
        for indice, fila in enumerate(csv_read):
            if not columnas_basicas:
                columnas_basicas, columnas_extra = self.identificar_columnas(fila)
                if columnas_basicas:
                    resultado.registrar_cabecera(fila, titulo_error='error-de-carga')
            elif len(fila) > 0:
                if len(fila) < len(columnas_extra) + len(columnas_basicas):
                    resultado.registrar_falla(indice, fila, error='Fila con datos incompletos.')
                else:
                    datos_de_prospecto, datos_extra = self.separar_datos_de_fila(fila, columnas_basicas, columnas_extra)
                    datos_por_fila.append(
                        {
                            'indice': indice, 'fila': fila,
                            'datos_de_prospecto': datos_de_prospecto, 'datos_extra': datos_extra
                        })
                    prospectos.append(datos_de_prospecto)

        return {'resultado_de_carga': resultado, 'datos_de_prospectos': prospectos, 'datos_por_fila': datos_por_fila}

    def _loggear_errores(self, datos, resultado, codigo_de_tipo_de_origen):
        nombre_de_archivo = self._nombre_de_archivo()
        path_completo = self._path_completo_para(nombre_de_archivo)
        self._generar_archivo_de_errores(resultado, path_completo)
        ejecutor = self.ejecutor
        fallidas = resultado.cantidad_filas_erroneas()
        exitosas = resultado.cantidad_filas_exitosas()
        responsable = datos['responsable'].__str__() if datos['responsable'] else ''
        campania = datos['campania'].__str__() if datos['campania'] else ''
        vendedor = datos['vendedor'].__str__() if datos['vendedor'] else ''
        with open(path_completo, 'r') as f:
            log_erroneas = File(f, nombre_de_archivo)
            self.log = SubidaErronea.guardar_log(log_erroneas, ejecutor, fallidas, exitosas,
                                                 codigo_de_tipo_de_origen, campania, responsable, vendedor)
        os.remove(path_completo)

    def unicode_csv_read(self, utf8_data, dialect=csv.excel, **kwargs):
        text_data = io.TextIOWrapper(utf8_data, encoding='utf-8')
        csv_reader = csv.reader(text_data, dialect=dialect, **kwargs)
        for row in csv_reader:
            yield row

    def identificar_columnas(self, fila):
        columnas_basicas = {}
        columnas_extra = {}
        for pos, columna in enumerate(fila):
            columna = self._limpiar_nombre_de_campo(columna)
            if columna in CAMPOS_DE_CARGA_DE_PROSPECTO:
                columnas_basicas[columna] = pos
            else:
                if not columna in self.columnas_a_ignorar:
                    columnas_extra[columna] = pos
        return columnas_basicas, columnas_extra

    def separar_datos_de_fila(self, fila, columnas_basicas, columnas_extra):
        datos_de_prospecto = {}
        datos_extra = []
        for campo in columnas_basicas:
            valor = fila[columnas_basicas[campo]].strip()
            if valor:
                datos_de_prospecto[campo] = valor
        for campo in columnas_extra:
            valor = fila[columnas_extra[campo]].strip()
            if valor:
                datos_extra.append({'nombre': campo, 'valor': valor})
        return datos_de_prospecto, datos_extra

    def _limpiar_nombre_de_campo(self, columna):
        return columna.strip().lower().encode('ascii', 'ignore').decode('ascii')

    def _generar_archivo_de_errores(self, resultado, filename):
        try:
            with open(filename, 'w') as file_erroneas:
                self._escribir_en(file_erroneas, resultado)
        except EnvironmentError:  # parent of IOError, OSError *and* WindowsError where available
            raise

    def _nombre_de_archivo(self):
        return now().strftime('subida-log-%Y-%m-%d-%H-%M-%S.csv')

    def _path_completo_para(self, nombre_de_archivo):
        path = os.path.join(settings.MEDIA_ROOT, 'erroneas')
        try:
            if not os.path.exists(path):
                os.makedirs(path)
        except OSError as exception:
            if exception.errno != errno.EEXIST:
                raise
        path_completo = os.path.join(path, nombre_de_archivo)
        return path_completo

    def _escribir_en(self, archivo, resultado):
        writer = csv.writer(archivo)
        writer.writerow([campo for campo in resultado.cabecera()])

        for fila_erronea in list(resultado.filas_erroneas().values()):
            fila = [smart_str(celda, encoding='iso-8859-15') for celda in fila_erronea]
            writer.writerow(fila)

    def _completar_informacion_de(self, prospecto):
        if prospecto:
            transaction.on_commit(
                lambda:
                tasks.completar_informacion_de_geolocalizacion_desde_ip_de_prospecto.delay(
                    prospecto_id=prospecto.id))


class ResultadoDeCargaDeProspectosViaCSV(object):
    def __init__(self):
        super(ResultadoDeCargaDeProspectosViaCSV, self).__init__()
        self._filas_erroneas = {}
        self._cabecera = []
        self._consumido = 0
        self._prospectos = []

    @classmethod
    def nuevo(cls):
        return cls()

    def agregar_lote_fallido(self, datos_por_fila, mensaje_de_error):
        for fila_parseada in datos_por_fila:
            self.registrar_falla(fila_parseada['indice'], fila_parseada['fila'], mensaje_de_error)

    def registrar_cabecera(self, fila, titulo_error):
        self._cabecera = fila + [titulo_error]

    def registrar_exito(self, prospecto):
        self._prospectos.append(prospecto)
        self._consumido += prospecto.consumido_en_pedido

    def registrar_falla(self, indice, fila, error):
        self._filas_erroneas[indice] = fila + [error]

    def tiene_filas_erroneas(self):
        return self.cantidad_filas_erroneas() > 0

    def cantidad_consumida(self):
        return self._consumido

    def cantidad_filas_erroneas(self):
        return len(self._filas_erroneas)

    def cantidad_filas_exitosas(self):
        return len(self.prospectos_cargados())

    def cantidad_de_filas(self):
        return self.cantidad_filas_exitosas() + self.cantidad_filas_erroneas()

    def cabecera(self):
        return self._cabecera

    def filas_erroneas(self):
        return self._filas_erroneas

    def prospectos_cargados(self):
        return self._prospectos
