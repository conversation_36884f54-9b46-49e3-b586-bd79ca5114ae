from core.opciones import ChoiceMapper
from prospectos.models.origen import OrigenDeProspectoJotform, OrigenDeProspectoCSV, OrigenDeProspectoWeb, \
    OrigenDeProspectoAPI


class ModoDeIngresoChoices(ChoiceMapper):
    JOTFORM = '0'
    API = '1'
    CSV = '2'
    WEB = '3'
    _MAPEO = {JOTFORM: OrigenDeProspectoJotform, API: OrigenDeProspectoAPI,
              CSV: OrigenDeProspectoCSV, WEB: OrigenDeProspectoWeb}

    @classmethod
    def choices(cls):
        return [(key, cls.element_at(key).nombre()) for key in cls.mapper()]

    @classmethod
    def mapper(cls):
        return cls._MAPEO


class ModoDeSelecionDeProspectosMapper(object):
    VENCIDOS = 'vencidos'
    SIN_AGENDAR = 'sin-agendar'
    _OPCIONES = ((VENCIDOS, 'Vencidos'), (SIN_AGENDAR, 'Sin agendar'))

    @classmethod
    def opciones(cls):
        return cls._OPCIONES

    def seleccion_para(self, nombres_de_seleccion):
        """
        :param nombres_de_seleccion: puede ser un string o una lista de string.
        :return: Responde el modo de seleccion descripto por nombres_de_seleccion
        """
        if not nombres_de_seleccion:
            return self.seleccion_por_defecto()
        # TODO: ver como hacer para que esto sea compatible con python 3
        if isinstance(nombres_de_seleccion, str):
            return self._seleccion_unica(nombres_de_seleccion)
        if len(nombres_de_seleccion) == 1:
            nombre = nombres_de_seleccion[0]
            return self._seleccion_unica(nombre)

        return self._seleccion_compuesta(nombres_de_seleccion)

    def seleccion_por_defecto(self):
        seleccion_class = self._seleccion_por_defecto_class()
        return seleccion_class()

    def _seleccion_por_defecto_class(self):
        from prospectos.utils.seleccion_de_prospectos import SeleccionDeProspectosSinAgendar
        return SeleccionDeProspectosSinAgendar

    def _seleccion_unica(self, nombre):
        modo_de_seleccion = self.mappeo().get(nombre, self._seleccion_por_defecto_class())
        return modo_de_seleccion()

    def _seleccion_compuesta(self, nombres_de_seleccion):
        selecciones = [self._seleccion_unica(nombre) for nombre in nombres_de_seleccion]
        from prospectos.utils.seleccion_de_prospectos import UnionDeSelecciones
        return UnionDeSelecciones(selecciones)

    def mappeo(self):
        from prospectos.utils.seleccion_de_prospectos import SeleccionDeProspectosVencidos
        from prospectos.utils.seleccion_de_prospectos import SeleccionDeProspectosSinAgendar
        return {self.VENCIDOS: SeleccionDeProspectosVencidos, self.SIN_AGENDAR: SeleccionDeProspectosSinAgendar}
