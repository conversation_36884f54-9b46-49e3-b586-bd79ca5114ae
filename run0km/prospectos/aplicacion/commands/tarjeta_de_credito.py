from django.core.exceptions import ValidationError

from layers.application import AppCommand
from layers.application.commands.validators.generic import NotNone, NotEmptyString
from layers.application.commands.validators.validator import Parameter
from prospectos.models import Prospecto, MarcaDeTarjetaDeCredito, TarjetaDeProspecto
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto


class HabilitacionDeTarjetaDeCreditoDeProspecto(AppCommand):
    def _execute_from_successful_result(self, result):
        try:
            tarjeta = self._modificar_habilitacion_de_la_tarjeta()
        except ValidationError as error:
            result.add_error(error.messages[0])
            return result
        else:
            result.set_object(tarjeta)
            return result

    def _initial_parameters(self):
        return [
            Parameter("id_tarjeta", [NotNone()]),
            Parameter("rol", [NotNone()]),
            Parameter("accion", [NotNone(), NotEmptyString()])
        ]

    def _initial_required_parameter_names(self):
        return {'id_tarjeta', 'rol', 'accion'}

    def _id_tarjeta(self):
        return self.get_argument_named('id_tarjeta')

    def _rol(self):
        return self.get_argument_named('rol')

    def _accion(self):
        return self.get_argument_named('accion')

    def _modificar_habilitacion_de_la_tarjeta(self):
        gestor = GestorDeProspecto.nuevo_para(rol=self._rol())
        tarjeta = TarjetaDeProspecto.objects.get(id=self._id_tarjeta())
        acciones = {'habilitar': True, 'deshabilitar': False}
        gestor.modificar_habilitacion_de_la_tarjeta_de_credito(tarjeta=tarjeta,
                                                               esta_habilitada=acciones[self._accion()])
        return tarjeta


class AgregarTarjetaDeCreditoAProspecto(AppCommand):
    def _execute_from_successful_result(self, result):
        try:
            prospecto = Prospecto.objects.get(id=self._id_prospecto())
        except Prospecto.DoesNotExist as error:
            result.add_error(str(error))
            return result
        else:
            try:
                marcas_de_las_tarjetas = self._marcas_de_las_tarjetas()
                tarjetas = self._tarjetas_de_credito_desde(
                    marcas_de_las_tarjetas=marcas_de_las_tarjetas, prospecto=prospecto)
            except ValidationError as error:
                result.add_error(error.messages[0])
                return result
            else:
                result.set_object(tarjetas)
                return result

    def _marcas_de_las_tarjetas(self):
        return self.get_argument_named('marcas_de_las_tarjetas')

    def _id_prospecto(self):
        return self.get_argument_named('id_prospecto')

    def _tarjetas_de_credito_desde(self, marcas_de_las_tarjetas, prospecto):
        marcas_de_tarjetas = MarcaDeTarjetaDeCredito.objects.con_nombres(nombres=marcas_de_las_tarjetas)
        tarjetas = []
        for marca in marcas_de_tarjetas:
            tarjeta = self._crear_tarjeta_de_credito_con(marca=marca, prospecto=prospecto)
            tarjetas.append(tarjeta)

        self._remover_marca_de_tarjeta_desconocida_de_prospecto(prospecto)
        return tarjetas

    def _crear_tarjeta_de_credito_con(self, marca, prospecto):
        tarjeta = TarjetaDeProspecto.nueva(prospecto=prospecto, marca=marca)
        return tarjeta

    def _remover_marca_de_tarjeta_desconocida_de_prospecto(self, prospecto):
        # Solo quiero remover la marca desconocida en caso de que no la este agregando y si el prospecto la tiene.
        marca_desconocida = MarcaDeTarjetaDeCredito.nombre_de_marca_desconocida()
        prospecto_tiene_tarjeta_desconocida = TarjetaDeProspecto.objects.prospecto_tiene_marca_desconocida(prospecto)
        if marca_desconocida not in self._marcas_de_las_tarjetas() and prospecto_tiene_tarjeta_desconocida:
            TarjetaDeProspecto.objects.remover_tarjeta_con_marca_del_prospecto(
                prospecto=prospecto, marca=MarcaDeTarjetaDeCredito.objects.marca_desconocida())

    def _initial_parameters(self):
        return [
            Parameter("id_prospecto", [NotNone()]),
            Parameter("marcas_de_las_tarjetas", [NotNone(), NotEmptyString()])
        ]

    def _initial_required_parameter_names(self):
        return {'id_prospecto', 'marcas_de_las_tarjetas'}
