from django.core.management.base import BaseCommand

from prospectos.models import Prospecto


class Command(BaseCommand):
    help = 'Rellena fechas vacias de prospectos con la fecha de creacion'

    def handle(self, *args, **options):
        for prospecto in Prospecto.objects.all():
            if prospecto.fecha == None:
                prospecto.fecha = prospecto.fecha_creacion
                prospecto.save()
