from django.core.management.base import BaseCommand
from django.db.models import Count

from prospectos.models import InformacionDeRedesSociales


class Command(BaseCommand):
    help = 'prueba envio de mail'

    def handle(self, *args, **options):
        consulta = InformacionDeRedesSociales.objects.values('prospecto_id').annotate(
            cantidad=Count('prospecto_id'))

        consulta = consulta.filter(cantidad__gte=50)
        for info_prospecto in consulta.all():
            prospecto_id = info_prospecto['prospecto_id']
            info_basura = InformacionDeRedesSociales.objects.filter(prospecto_id=prospecto_id)[50:]
            ids = list(info_basura.values_list('id', flat=True))
            info_basura = InformacionDeRedesSociales.objects.filter(id__in=ids)
            info_basura.delete()
