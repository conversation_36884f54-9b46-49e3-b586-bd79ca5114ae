from django.core.management.base import BaseCommand
from django.utils import timezone

from prospectos.models import PedidoDeProspecto


class Command(BaseCommand):
    help = 'Verifica los pedidos renovados '

    def handle(self, *args, **options):
        verificador = VerificadorDePedidos()
        fecha = timezone.localtime(timezone.now())
        resultado = verificador.verificar(mes=fecha.month, anio=fecha.year)
        print(fecha)
        print(resultado.as_json())


class VerificadorDePedidos(object):

    def verificar(self, mes, anio):
        pedidos = PedidoDeProspecto.objects.filter(fecha__month=mes, fecha__year=anio)
        resultados = ResultadoDeListaDeActualizaciones()
        for pedido in pedidos.all():
            resultado = self.verificar_pedido(pedido)
            resultados.agregar(resultado)
        return resultados

    def verificar_pedido(self, pedido):
        resultado = ResultadoDeActualizacion(pedido)
        try:
            pedido_anterior = self.pedido_anterior_para(pedido)
        except PedidoDeProspecto.DoesNotExist:
            resultado.advertencia('No tiene pedido anterior')
        except PedidoDeProspecto.MultipleObjectsReturned:
            resultado.error('Tiene mas de un pedido anterior')
        else:
            self._comparar_pedidos(pedido, pedido_anterior, resultado)
        return resultado

    def pedido_anterior_para(self, pedido):
        mes_pasado = pedido.fecha.replace(day=1) - timezone.timedelta(days=1)
        pedido = PedidoDeProspecto.objects.get(
            fecha__month=mes_pasado.month,
            fecha__year=mes_pasado.year,
            nombre=pedido.nombre,
            supervisor=pedido.supervisor
        )
        return pedido

    def _comparar_pedidos(self, pedido, pedido_anterior, resultado):
        if pedido.supervisor != pedido_anterior.supervisor:
            resultado.diferencia('superivor')
        if pedido.es_renovable != pedido_anterior.es_renovable:
            resultado.diferencia('es_renovable')
        if set(pedido.calidades()) != set(pedido_anterior.calidades()):
            resultado.diferencia('calidades')
        if set(pedido.categorias.all()) != set(pedido_anterior.categorias.all()):
            resultado.diferencia('categorias')
        if set(pedido.campanias.all()) != set(pedido_anterior.campanias.all()):
            resultado.diferencia('campanias')
        if pedido.excluye_campanias() != pedido_anterior.excluye_campanias():
            resultado.diferencia('excluye_campanias')
        if pedido.factor_de_distribucion != pedido_anterior.factor_de_distribucion:
            resultado.diferencia('factor_de_distribucion')
        if pedido.restringir_por_datos_diarios != pedido_anterior.restringir_por_datos_diarios:
            resultado.diferencia('restringir_por_datos_diarios')
        if pedido.restringir_por_datos_nuevos != pedido_anterior.restringir_por_datos_nuevos:
            resultado.diferencia('restringir_por_datos_nuevos')
        if pedido.restringir_por_acceso != pedido_anterior.restringir_por_acceso:
            resultado.diferencia('restringir_por_acceso')


class ResultadoDeListaDeActualizaciones(object):
    def __init__(self):
        super(ResultadoDeListaDeActualizaciones, self).__init__()
        self._resultados = {'pedidos': []}

    def agregar(self, resultado):
        reporte = self._resultados.setdefault(resultado.tipo(), [])
        reporte.append('%s: %s' % (resultado.nombre_de_pedido(), resultado.descripcion()))

    def _agregar_pedido(self, resultado):
        self._resultados['pedidos'][resultado.nombre_de_pedido()] = resultado.as_string()

    def as_json(self):
        return self._resultados

    def errores(self):
        return self._resultados.get(ResultadoDeActualizacion.ERROR, [])

    def diferencias(self):
        return self._resultados.get(ResultadoDeActualizacion.DIFERENCIA, [])

    def exitos(self):
        return self._resultados.get(ResultadoDeActualizacion.EXITO, [])


class ResultadoDeActualizacion(object):
    EXITO = 'exitoso'
    ERROR = 'error'
    DIFERENCIA = 'diferencia'

    def __init__(self, pedido):
        super(ResultadoDeActualizacion, self).__init__()
        self._pedido = pedido
        self._tipo = self.EXITO
        self._descripcion = ''
        self._diferencias = []

    def as_string(self):
        descripcion = self.descripcion()
        return '%s: %s' % (self._tipo, descripcion)

    def tipo(self):
        return self._tipo

    def nombre_de_pedido(self):
        return self._pedido.nombre

    def descripcion(self):
        if self.exitoso():
            descripcion = 'Sin diferencias'
        elif self.tipo() == self.DIFERENCIA:
            descripcion = ' - '.join(self._diferencias)
        else:
            descripcion = self._descripcion
        return descripcion

    def error(self, texto):
        self._notificar_tipo(self.ERROR, texto)

    def advertencia(self, texto):
        self._notificar_tipo('advertencia', texto)

    def diferencia(self, texto):
        self._tipo = self.DIFERENCIA
        self._diferencias.append(texto)

    def _notificar_tipo(self, tipo, texto):
        self._tipo = tipo
        self._descripcion = texto

    def exitoso(self):
        return self._tipo == self.EXITO

