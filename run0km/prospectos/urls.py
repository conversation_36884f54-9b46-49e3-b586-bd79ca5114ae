from django.conf.urls import url
from django.views.generic import TemplateView

import prospectos.views.jotform
import prospectos.views.resumen
from prospectos.views import base as views
from prospectos.views.agendado import ProgramarAutomaticamenteProspectosSinAgendarView
from prospectos.views.base import (ProspectosView, ProspectoView, AdministracionView, TagsView, ToggleYaLlamado,
                                   CantidadFiltradaView, CalendarioLlamadasEvent, VisualizarFiltradosView,
                                   ValidarInfoDeRedesSocialesView, EstadoDeProspectoView,
                                   ReasignarProspectoView, MarcasParaProspectoView, ModelosParaProspectoView,
                                   InformacionAdicionalDeProspectoView, DatosDeProspectosNuevosSinAsignarView,
                                   EditarVentaView)
from prospectos.views.carga_de_prospectos import CargaDeProspectosView, RefrescarCampaniasView, CalidadParaCampaniaView
from prospectos.views.operaciones_de_prospectos import \
    (ComentarView, NuevoLlamado, BorrarLlamadoView, FinalizacionView, ExtraDataView,
     ToggleInfoDataView, ToggleExtraDataView, comentarios, ReactivarSeguimientoView, FinalizarSeguimientoView,
     AgregarTarjetaDeCreditoView, HabilitacionDeTarjetaDeCreditoView, AgregarComentarioConSemanticaView)
from prospectos.views.oportunidades import OportunidadesView
from prospectos.views.proponer_fecha_y_horario_de_llamado import ProponerFechaYHorarioDeLlamadoView
from prospectos.views.repetidos import RedireccionarARepetidoView
from prospectos.views.transferir_prospecto import TransferirProspectoView
from prospectos.views.ventas import CancelarVentaView, VentaView, CargarVentaView
from prospectos.views.pedir_nuevo_prospecto import PedirNuevoProspectoView
urlpatterns = [

    url(r'^repetidos/$', RedireccionarARepetidoView.as_view(), name='redireccionar_a_prospecto_repetido'),

    url(r'^filtro_prospectos/$', views.FiltroDeProspectosView.as_view(), name='filtro_prospectos'),

    url(r'prospectos/', ProspectosView.as_view(), name='prospectos'),

    url(r'^resumen/$', prospectos.views.resumen.DatosDeResumenView.as_view(), name='resumen_data'),

    url(r'^reasignar/(?P<pk>\d+)/$', ReasignarProspectoView.as_view(), name='reasignar'),

    url(r'^comentar/(?P<pk>\d+)/$', ComentarView.as_view(), name='comentar'),
    url(r'^comentar/(?P<pk>\d+)/comentario-con-semantica/$',
        AgregarComentarioConSemanticaView.as_view(), name='agregar_comentario_con_semantica'),

    url(r'^comentarios/(?P<pk>\d+)/$', comentarios, name='comentarios'),
    url(r'^redes_sociales/(?P<pk>\d+)/$', views.redes_sociales, name='redes_sociales'),

    url(r'^nuevo-llamado/(?P<pk>\d+)/(?P<validar_llamados_superpuestos>[0-1])/$', NuevoLlamado.as_view(),
        name='nuevo_llamado'),
    url(r'^borrar-llamado/(?P<pk>\d+)/$', BorrarLlamadoView.as_view(), name='borrar_llamado'),
    url(r'programar-automaticamente-prospectos-sin-agendar/$',
        ProgramarAutomaticamenteProspectosSinAgendarView.as_view(),
        name='programar_automaticamente_prospectos_sin_agendar'),
    url(r'^proponer-fecha-horario-llamado/(?P<pk>\d+)/$', ProponerFechaYHorarioDeLlamadoView.as_view(),
        name='proponer_fecha_horario_llamado'),

    url(r'^cargar-venta/(?P<pk>\d+)/$', CargarVentaView.as_view(), name='cargar_venta'),
    url(r'^venta/(?P<pk>\d+)/$', VentaView.as_view(), name='venta'),

    url(r'^reactivar-seguimiento/(?P<pk>\d+)/$', ReactivarSeguimientoView.as_view(), name='reactivar_seguimiento'),
    url(r'^finalizar-seguimiento/(?P<pk>\d+)/$', FinalizarSeguimientoView.as_view(), name='finalizar_seguimiento'),
    url(r'^finalizacion/(?P<pk>\d+)/$', FinalizacionView.as_view(), name='finalizacion'),

    url(r'^toggle_ya_llamado/(?P<pk>\d+)/$', ToggleYaLlamado.as_view(), name='toggle_ya_llamado'),

    url(r'^extra_data/', ExtraDataView.as_view(), name='extra_data'),
    url(r'^toggle_extra_data/', ToggleExtraDataView.as_view(), name='toggle_extra_data'),
    url(r'^toggle_info_data/', ToggleInfoDataView.as_view(), name='toggle_info_data'),

    url(r'administracion/$', AdministracionView.as_view(), name='administracion'),
    url(r'administracion/datos-de-prospectos-nuevos-sin-asignar/$', DatosDeProspectosNuevosSinAsignarView.as_view(),
        name='datos_de_prospectos_nuevos_sin_asignar'),
    url(r'administracion/cantidad/', CantidadFiltradaView.as_view(), name='cantidad-filtrada'),

    url(r'administracion/info_inactivos/', views.info_prospectos_inactivos, name='info-prospectos-inactivos'),
    url(r'administracion/visualizar_filtrados/', VisualizarFiltradosView.as_view(), name='visualizar_filtrados'),

    url(r'taggear/', views.taggear_prospecto, name='taggear-prospecto'),
    url(r'^tags/(?P<pk>\d+)/$', views.tags_de_prospecto, name='tags-de-prospecto'),
    url(r'^tags/', TagsView.as_view(), name='administrar-tags'),
    url(r'^renombrar-tag/', views.renombrar_tag, name='renombrar-tag'),
    url(r'^borrar-tag/(?P<pk>\d+)/$', views.borrar_tag, name='borrar-tag'),

    url(r'^(?P<pk>\d+)/$', ProspectoView.as_view(), name='prospecto'),

    url(r'^jotform/doc/', TemplateView.as_view(template_name="jotform_doc.html"), name='jotform-doc'),
    url(r'^jotform/$', prospectos.views.jotform.jotform, name='jotform'),
    url(r'^calendario-llamadas-events/$', CalendarioLlamadasEvent.as_view(), name='calendario-llamadas-events'),
    url(r'^redes-sociales/cambiar-estado/', ValidarInfoDeRedesSocialesView.as_view(), name='validar-redes-sociales'),
    url(r'^estado-de-prospecto/(?P<pk>\d+)/$', EstadoDeProspectoView.as_view(), name='estado-de-prospecto'),
    url(r'^editar-venta/$', EditarVentaView.as_view(), name='editar-venta'),
    url(r'^cancelar-venta/(?P<pk>\d+)/$', CancelarVentaView.as_view(), name='cancelar-venta'),
    url(r'^cargar-prospecto/$', CargaDeProspectosView.as_view(), name='cargar-prospecto'),
    url(r'^refrescar-campanias-carga-prospecto/$', RefrescarCampaniasView.as_view(),
        name='refrescar-campanias-carga-prospecto'),
    url(r'^calidad-para-campania/$', CalidadParaCampaniaView.as_view(), name='calidad-para-campania'),
    url(r'^marcas-para-prospecto/$', MarcasParaProspectoView.as_view(), name='marcas-para-prospecto'),
    url(r'^modelos-para-prospecto/$', ModelosParaProspectoView.as_view(), name='modelos-para-prospecto'),
    url(r'^calidad-para-campania/$', CalidadParaCampaniaView.as_view(), name='calidad-para-campania'),
    url(r'^agregar-informacion-adicional-de-prospecto/(?P<pk>\d+)/$', InformacionAdicionalDeProspectoView.as_view(),
        name='informacion-adicional-prospecto'),
    url(r'^oportunidades/supervisor/(?P<accion>[A-Za-z\d]+)/$',
        OportunidadesView.as_view(), name='oportunidades_de_supervisor'),
    url(r'^agregar-tarjeta-de-credito/(?P<pk_prospecto>\d+)/$', AgregarTarjetaDeCreditoView.as_view(),
        name='agregar-tarjeta-de-credito'),
    url(r'^habilitacion-de-tarjeta-de-credito/(?P<pk_tarjeta>\d+)/$', HabilitacionDeTarjetaDeCreditoView.as_view(),
        name='habilitacion-de-tarjeta-de-credito'),
    url(r'^pedir-nuevo-prospecto/$', PedirNuevoProspectoView.as_view(), name='pedir-nuevo-prospecto'),
    url(r'^transferir-prospecto/$', TransferirProspectoView.as_view(), name='transferir-prospecto'),

]
