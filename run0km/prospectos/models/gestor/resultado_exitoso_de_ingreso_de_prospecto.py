class ResultadoExitosoDeIngresoDeProspecto(object):
    _NUEVO = 'prospecto nuevo'
    _MERGEADO = 'prospecto mergeado'
    _AGREGADO_A_GRUPO_DE_REPETIDOS = 'agregado a grupo de repetidos'

    def __init__(self, prospecto, detalle, proveniente_de_chat=False, estaba_asignado=False):
        super(ResultadoExitosoDeIngresoDeProspecto, self).__init__()
        self._prospecto = prospecto
        self._detalle = detalle
        self._proveniente_de_chat = proveniente_de_chat
        self._estaba_asignado = estaba_asignado

    @classmethod
    def mergeado(cls, prospecto, estaba_asignado):
        return cls(prospecto, cls._MERGEADO, estaba_asignado=estaba_asignado)

    @classmethod
    def agregado_a_grupo_de_repetidos(cls, prospecto):
        return cls(prospecto, cls._AGREGADO_A_GRUPO_DE_REPETIDOS)

    @classmethod
    def prospecto_nuevo(cls, prospecto):
        return cls(prospecto, cls._NUEVO)

    @classmethod
    def nuevo_desde_chat(cls, prospecto):
        return cls(prospecto, cls._NUEVO, proveniente_de_chat=True)

    @classmethod
    def agregado_a_grupo_de_repetidos_desde_chat(cls, prospecto):
        return cls(prospecto, cls._AGREGADO_A_GRUPO_DE_REPETIDOS, proveniente_de_chat=True)

    def es_erroneo(self):
        return False

    def es_proveniente_de_chat(self):
        return self._proveniente_de_chat

    def fue_mergeado(self):
        return self._detalle == self._MERGEADO

    def estaba_asignado(self):
        return self._estaba_asignado

    def fue_agregado_a_grupo_de_repetidos(self):
        return self._detalle == self._AGREGADO_A_GRUPO_DE_REPETIDOS

    def fue_repetido(self):
        return self.fue_agregado_a_grupo_de_repetidos() or self.fue_mergeado()

    def prospecto(self):
        return self._prospecto
