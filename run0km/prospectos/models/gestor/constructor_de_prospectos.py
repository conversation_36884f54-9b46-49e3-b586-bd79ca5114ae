# coding=utf-8

import logging

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.utils import timezone

from prospectos.models import Prospecto, CampoExtra, Marca, PedidoDeProspecto, Modelo, Geolocalizacion
from prospectos.models.exceptions import ProspectoRepetidoException
from prospectos.models.gestor.criterio_de_prospecto_repetido import CriterioDeProspectosRepetidos
from prospectos.models.gestor.resultado_exitoso_de_ingreso_de_prospecto import ResultadoExitosoDeIngresoDeProspecto
from prospectos.models.gestor.criterio_de_rechazo_de_prospecto import CriterioDeRechazoDeProspecto
from prospectos.models.mergeador import MergeadorDeProspectos
from prospectos.models.repetidos import GrupoDeRepetidos


class ConstructorDeProspectos(object):
    @classmethod
    def nuevo(cls):
        constructor = cls()
        return constructor

    def crear_nuevo_prospecto_desde(
            self, datos, datos_extra=None, proveniente_de_chat=False, validar_email=True, modo_de_ingreso=None):
        datos_extra = datos_extra or {}
        self._normalizar_datos(datos, datos_extra)

        criterio_de_rechazo = self._validar_condiciones_iniciales(datos, validar_email)
        ids_de_repetidos = set(self._obtener_repetidos_de(datos=datos).ids())
        if proveniente_de_chat:
            resultado = self._crear_prospecto_proveniente_de_chat(
                datos, datos_extra, ids_de_repetidos, modo_de_ingreso)
        else:
            resultado = self._crear_prospecto_de_forma_usual(
                criterio_de_rechazo, datos, datos_extra, ids_de_repetidos, modo_de_ingreso)

        return resultado

    def _crear_prospecto_de_forma_usual(
            self, criterio_de_rechazo, datos, datos_extra, ids_de_repetidos, modo_de_ingreso):
        criterio_de_rechazo.validar_repetido(datos=datos, ids_de_repetidos=ids_de_repetidos)
        if len(ids_de_repetidos) > 0:
            resultado = self._manejar_repetidos(datos, datos_extra, ids_de_repetidos, modo_de_ingreso)
        else:
            prospecto = self._crear_prospecto(datos=datos, modo_de_ingreso=modo_de_ingreso, validar_email=False)
            self.agregar_campos_extras_a(prospecto, datos_extra)
            resultado = ResultadoExitosoDeIngresoDeProspecto.prospecto_nuevo(prospecto)
        return resultado

    def _manejar_repetidos(self, datos, datos_extra, ids_de_repetidos, modo_de_ingreso):
        repetidos = Prospecto.objects.con_ids(ids_de_repetidos)
        repetido_mergeable = self._repetido_mergeable(repetidos)

        if repetido_mergeable:
            estaba_asignado = repetido_mergeable.tiene_vendedor()
            self._mergear_repetido(repetido_mergeable, datos, datos_extra)
            resultado = ResultadoExitosoDeIngresoDeProspecto.mergeado(
                repetido_mergeable, estaba_asignado=estaba_asignado)
        else:
            prospecto = self._crear_prospecto(datos=datos, modo_de_ingreso=modo_de_ingreso, validar_email=False)
            fue_agregado_a_grupo_de_repetidos = self._asignar_y_crear_relacion_de_repetido(
                prospecto=prospecto, ids_de_repetidos=ids_de_repetidos)
            self.agregar_campos_extras_a(prospecto, datos_extra)
            if fue_agregado_a_grupo_de_repetidos:
                resultado = ResultadoExitosoDeIngresoDeProspecto.agregado_a_grupo_de_repetidos(prospecto)
            else:
                resultado = ResultadoExitosoDeIngresoDeProspecto.prospecto_nuevo(prospecto)

        return resultado

    def _crear_prospecto_proveniente_de_chat(self, datos, datos_extra, ids_de_repetidos, modo_de_ingreso):
        prospecto = self._crear_prospecto(
            datos=datos, modo_de_ingreso=modo_de_ingreso, validar_email=False)
        if len(ids_de_repetidos) > 0:
            resultado = self._crear_relacion_de_repetido_de_chat(prospecto=prospecto, ids_de_repetidos=ids_de_repetidos)
        else:
            resultado = ResultadoExitosoDeIngresoDeProspecto.nuevo_desde_chat(prospecto)
        self.agregar_campos_extras_a(prospecto, datos_extra)
        return resultado

    def agregar_campos_extras_a(self, prospecto, datos_extra):
        campos_extras = []
        for nombre, valor in list(datos_extra.items()):
            try:
                if valor:
                    campo_extra = CampoExtra(prospecto=prospecto, nombre=nombre, valor=valor)
                    campo_extra.full_clean()
                    campos_extras.append(campo_extra)
            except ValidationError:
                pass
        try:
            CampoExtra.objects.bulk_create(campos_extras)
        except IntegrityError:
            pass

    def _validar_condiciones_iniciales(self, datos, validar_email):
        criterio_de_rechazo = CriterioDeRechazoDeProspecto.nuevo()
        criterio_de_rechazo.validar_relacion_vendedor_supervisor_entre(datos=datos)
        criterio_de_rechazo.validar_roles_activos(datos=datos)
        criterio_de_rechazo.validar_email(datos=datos, validar_email=validar_email)
        return criterio_de_rechazo

    def agregar_prospecto_repetido_a(self, prospecto_original, prospecto_repetido):
        grupo = GrupoDeRepetidos.obtener_o_crear_para(prospecto=prospecto_original)
        grupo.agregar_prospecto(prospecto_repetido)

    def completar_datos_geolocalizacion_desde_ip(self, prospecto, provincia, localidad, latitud, longitud):
        geolocalizacion = prospecto.obtener_geolocalizacion()
        geolocalizacion.provincia = provincia
        geolocalizacion.localidad = localidad
        geolocalizacion.latitud = latitud
        geolocalizacion.longitud = longitud
        geolocalizacion.save()
        return geolocalizacion

    def validar_y_asignar_marca(self, datos_de_relaciones, prospecto):
        nombre_de_marca = datos_de_relaciones.get('nombre_de_marca', '')
        Marca.validar_nombre_de_marca(nombre_de_marca)
        marca = Marca.obtener_or_crear_con_nombre(nombre_de_marca)
        prospecto.cambiar_marca_por(marca)

    def _crear_relacion_de_repetido_de_chat(self, prospecto, ids_de_repetidos):
        """
            Lo agrego al grupo del ultimo prospecto
        """
        try:
            prospecto_original = self._seleccionar_representante_de_repetidos(prospecto, ids_de_repetidos)
            self.agregar_prospecto_repetido_a(prospecto_original=prospecto_original, prospecto_repetido=prospecto)
            return ResultadoExitosoDeIngresoDeProspecto.agregado_a_grupo_de_repetidos_desde_chat(prospecto)
        except Exception as exc:
            # TODO: por ahora capturamos todos para no perder prospectos, quitar esta linena
            logger = logging.getLogger('sentry')
            logger.error('Error al ingrear un repetido!', exc_info=True)
            raise ProspectoRepetidoException.nuevo()

    def _seleccionar_representante_de_repetidos(self, prospecto, ids_de_repetidos):
        """
            Busca el ultimo grupo de los prospectos del vendedor. Sino tiene ninguno toma el grupo del ultimo prospecto
            que ingreso.

            Falta analizar si esto degrada mucho la performance.
        """
        repetidos = Prospecto.objects.filter(pk__in=ids_de_repetidos)
        representante = repetidos.filter(vendedor_id=prospecto.vendedor_id).order_by('-fecha_creacion').first()
        if representante is None:
            representante = repetidos.order_by('-fecha_creacion').first()
        return representante

    def _asignar_y_crear_relacion_de_repetido(self, prospecto, ids_de_repetidos):
        """
            Si hay repetidos pero no cumplen las condiciones sobre los pedidos, no lo agregamos al grupo, ya que será
            ingresado por logica automatica
        """
        try:
            prospecto_original = self._asignar_segun_repetidos(prospecto, ids_de_repetidos)
            if prospecto_original is not None:
                self.agregar_prospecto_repetido_a(prospecto_original=prospecto_original, prospecto_repetido=prospecto)
                return True
            else:
                return False
        except Exception as exc:
            # TODO: por ahora capturamos todos para no perder prospectos, quitar esta linena
            logger = logging.getLogger('sentry')
            logger.error('Error al ingrear un repetido!', exc_info=True)
            raise ProspectoRepetidoException.nuevo()

    def _asignar_segun_repetidos(self, prospecto, ids_de_repetidos):
        limite = timezone.now() - timezone.timedelta(days=7)
        pedidos = PedidoDeProspecto.objects.fecha_posterior_a(limite) | PedidoDeProspecto.objects.activos_actuales()
        repetidos = Prospecto.objects.con_ids(ids_de_repetidos).select_related('campania')
        for repetido in repetidos.order_by('-fecha_creacion'):
            pedido = self._buscar_pedido_que_satisfasca(repetido, prospecto, pedidos)
            if pedido is not None:
                self._asignar_desde_repetido(nuevo_prospecto=prospecto, prospecto_repetido=repetido, pedido=pedido)
                return repetido
        return None

    def _buscar_pedido_que_satisfasca(self, repetido, nuevo_prospecto, pedidos):
        """
            Al menos un pedido del vendedor del repetido (prospecto ya ingresado) debe satisfacer la campaña del
            nuevo prospecto
        """
        vendedor = repetido.obtener_vendedor()
        if vendedor is None:
            return None
        campania = nuevo_prospecto.campania
        for pedido in pedidos:
            if pedido.incluye_vendedor(vendedor) and pedido.satisface_campania(campania):
                return pedido
        return None

    def _asignar_desde_repetido(self, nuevo_prospecto, prospecto_repetido, pedido):
        """
            TODO: no esta manejando el caso que no este asignado, _buscar_pedido_que_satisfasca lo va a rechazar asi que
             no puede entrar nunca en el else. No lo esta contabilizando en el pedido, esto lo hara cada metodo.
        """
        repartidor = self._repartidor()
        if prospecto_repetido.tiene_vendedor():
            vendedor = prospecto_repetido.obtener_vendedor()
            repartidor.asignar_prospecto_a(prospecto=nuevo_prospecto, vendedor=vendedor)
        # else:
        #     supervisor = prospecto_repetido.obtener_responsable()
        #     repartidor.asignar_responsable_a(prospecto=nuevo_prospecto, supervisor=supervisor)

    def _repartidor(self):
        from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
        return RepartidorDeProspectos.nuevo()

    def _obtener_repetidos_de(self, datos):
        prefijo = Prospecto.dar_formato_a_prefijo(datos.get('prefijo', None))
        telefono = datos.get('telefono', '')
        email = datos.get('email', '')
        nombre_de_marca = datos.get('marca', '')
        campania = datos.get('campania', None)
        if campania:
            categoria = campania.obtener_categoria()
        else:
            categoria = None
        criterio_de_repetidos = CriterioDeProspectosRepetidos.nuevo()
        prospectos = criterio_de_repetidos.aplicar(
            prefijo=prefijo, telefono=telefono, email=email, nombre_de_marca=nombre_de_marca,
            categoria_de_campania=categoria
        )
        return prospectos

    @transaction.atomic
    def _crear_prospecto(self, datos, modo_de_ingreso=None, validar_email=True):
        datos_de_relaciones = self._obtener_datos_de_relaciones_desde(datos)
        if 'nombre' in datos and len(datos['nombre']) > Prospecto._meta.get_field('nombre').max_length:
            datos['nombre'] = datos['nombre'][:Prospecto._meta.get_field('nombre').max_length]
        puede_circular = datos.get('vendedor') is None
        prospecto = Prospecto.nuevo(omitir_validar_email=not validar_email, modo_de_ingreso=modo_de_ingreso, puede_circular=puede_circular, **datos)
        self.validar_y_asignar_marca(datos_de_relaciones=datos_de_relaciones, prospecto=prospecto)
        self._reemplazar_modelo(datos_de_relaciones=datos_de_relaciones, prospecto=prospecto)
        self._crear_geolocalizacion_para(prospecto=prospecto, datos_de_relaciones=datos_de_relaciones)
        self._repartidor().crear_asignacion_para(datos_extra=datos_de_relaciones, prospecto=prospecto)
        return prospecto

    def _obtener_datos_de_relaciones_desde(self, datos):
        """
            Responde un dict con la informacion para crear los objetos relacionados con prospecto:
                marca, modelo, asignacion y geolocalizacion
        """

        ahora = self._ahora()
        fecha_de_asignacion_a_vendedor = datos.pop('fecha_de_asignacion_a_vendedor', ahora)
        ip = datos.pop('ip', None)
        nombre_de_marca = datos.pop('marca', '')
        nombre_de_modelo = datos.pop('modelo', '')
        return {'fecha_de_asignacion_a_vendedor': fecha_de_asignacion_a_vendedor, 'ip': ip,
                'nombre_de_marca': nombre_de_marca, 'nombre_de_modelo': nombre_de_modelo}

    def _reemplazar_modelo(self, datos_de_relaciones, prospecto):
        nombre_de_modelo = datos_de_relaciones.get('nombre_de_modelo', '')
        if not nombre_de_modelo:
            return
        marca = prospecto.obtener_marca()
        modelo = Modelo.objects.de_marca_con_nombre(marca, nombre_de_modelo)
        if modelo:
            if marca.es_marca_blanca():
                prospecto.cambiar_marca_por(modelo.marca())
            # Por ahora no se valida que sea de la misma marca,
            prospecto.reemplazar_modelos([modelo])
        else:
            # TODO: potencial race condition puede generar un ValidationError
            modelo = marca.agregar_modelo_de_nombre(nombre_de_modelo)
            prospecto.reemplazar_modelos([modelo])

    def _crear_geolocalizacion_para(self, prospecto, datos_de_relaciones):
        ip = datos_de_relaciones.get('ip', '')
        Geolocalizacion.nueva(prospecto=prospecto, ip=ip, provincia='',
                              localidad='', latitud=None, longitud=None)

    def _ahora(self):
        return timezone.localtime(timezone.now())

    def _mergear_repetido(self, prospecto, datos, datos_extra):
        mergeador = MergeadorDeProspectos.nuevo()
        mergeador.evaluar_con(prospecto, datos, datos_extra)

    def _repetido_mergeable(self, prospectos):
        limite = self._gap_de_tiempo_para_mergear_prospectos()
        mergeables = prospectos.entre_fechas(fecha_desde=limite).ordenar_por_fecha_de_creacion(ascendente=True)
        prospecto = mergeables.first()
        if prospecto is not None:
            return prospecto
        else:
            return self._prospecto_si_el_ultimo_no_esta_asignado(prospectos)

    def _prospecto_si_el_ultimo_no_esta_asignado(self, prospectos):
        prospecto = prospectos.ordenar_por_fecha_de_creacion(ascendente=False).last()
        if prospecto is not None and not prospecto.tiene_vendedor():
            return prospecto
        else:
            return None

    def _gap_de_tiempo_para_mergear_prospectos(self):
        segundos = timezone.timedelta(milliseconds=settings.GAP_PARA_PROSPECTOS_MERGEABLES_EN_MILISEGUNDOS)
        limite = timezone.now() - segundos
        return limite

    def _normalizar_datos(self, datos_del_prospecto, datos_extra_del_prospecto):
        """
            Datos como la IP a veces lo recibimos como dato extra y a veces dentro de los datos del prospecto.
        """
        ip = datos_extra_del_prospecto.pop('ip', None)
        if ip:
            datos_del_prospecto['ip'] = ip
