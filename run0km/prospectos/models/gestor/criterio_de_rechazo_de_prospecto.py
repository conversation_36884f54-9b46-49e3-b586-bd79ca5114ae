# coding=utf-8
from django.core.exceptions import ValidationError

from prospectos.models.exceptions import ProspectoInvalidoException, ProspectoRepetidoException
from prospectos.utils.logger import LoggerDeProspectos


class CriterioDeRechazoDeProspecto(object):
    @classmethod
    def nuevo(cls):
        criterio = cls()
        return criterio

    def __init__(self):
        self.logger = LoggerDeProspectos().para_rechazo()

    def validar_email(self, datos, validar_email=True):
        email = datos.get('email', '')
        if validar_email and email and '@' not in email:
            raise ProspectoInvalidoException.nuevo('Introduzca una dirección de email válida.')

    def validar_relacion_vendedor_supervisor_entre(self, datos):
        vendedor = self._vendedor_de(datos)
        responsable = self._responsable_de(datos)
        vendedor_y_responsables_estan_definidos = vendedor is not None and responsable is not None
        if vendedor_y_responsables_estan_definidos:
            self._validar_relacion_vendedor_y_responsable(responsable, vendedor)

    def _validar_relacion_vendedor_y_responsable(self, responsable, vendedor):
        from vendedores.gestor import GestorDeVendedores
        gestor = GestorDeVendedores()
        try:
            gestor.validar_relacion_vendedor_supervisor_entre(vendedor, responsable)
        except ValidationError as error:
            raise ProspectoInvalidoException.nuevo(error.message)

    def validar_roles_activos(self, datos):
        from vendedores.gestor import GestorDeVendedores
        gestor = GestorDeVendedores()
        vendedor = self._vendedor_de(datos)
        responsable = self._responsable_de(datos)
        try:
            gestor.validar_rol_activo(vendedor)
            gestor.validar_rol_activo(responsable)
        except ValidationError as error:
            raise ProspectoInvalidoException.nuevo(error.message)

    def validar_repetido(self, datos, ids_de_repetidos):
        # Hay un repetido y el nuevo dato viene asignado
        vendedor = self._vendedor_de(datos)
        responsable = self._responsable_de(datos)

        if (responsable or vendedor) and len(ids_de_repetidos) > 0:
            raise ProspectoRepetidoException.nuevo()

    def _responsable_de(self, datos):
        return datos.get('responsable')

    def _vendedor_de(self, datos):
        return datos.get('vendedor')

    def _mensaje_de_debug_para_logear(self, datos, exc, origen_de_prospecto=None):
        if origen_de_prospecto is not None:
            origen = origen_de_prospecto.nombre()
        else:
            origen = 'Desconocido'
        return 'El Prospecto fue rechazado. Razon: {0} ||||| Datos: {1} ||||| Origen: {2} -------------'.format(
            str(exc).encode('utf-8'), datos, origen)
