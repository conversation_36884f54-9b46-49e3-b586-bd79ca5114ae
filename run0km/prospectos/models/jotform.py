# coding=utf-8
import re

from django.core.exceptions import ValidationError
from django.utils.timezone import now

from campanias.models import Campania
from layers.application.commands.ingreso_de_prospectos.ingreso import IngresoDeProspectoDesdeJotformComando
from prospectos.aplicacion.commands.tarjeta_de_credito import AgregarTarjetaDeCreditoAProspecto
from prospectos.configuracion import CAMPOS_DE_PROSPECTO_PARA_JOTFORM
from prospectos.models.base import Proveedor, CargaFallidaDeJotform
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos import tasks
from prospectos.utils.logger import LoggerDeProspectos


class CargadorDeProspectoDesdeJotform(object):
    """
        Mi responsabilidad es crear un prospepccto a partir de un json en formato jotform.

        Es un objeto de la capa de interfaz, funciona como un adapter. Genera los datos en el formato necesario
        para un prospecto a partir del formato jotform, y ejecuta el comando IngresoDeProspectoComando para
        construir e ingresar el prospecto al sistema.
    """

    def __init__(self):
        self.repartidor = RepartidorDeProspectos()
        self._error_message = None
        self._interprete = None

    def cargar(self, raw):
        """

        :param raw: dict
        :return: (prospecto, error) prospecto = None si no se consigue cargar, error = None en caso contrario.
        """
        self._error_message = ''
        self._interprete = InterpreteDeJotform(raw)
        nuevo_prospecto = None

        try:
            datos_de_prospecto = self._interprete.obtener_datos_de_prospecto()
            campos_extra = self._interprete.obtener_campos_extra()
            campania = datos_de_prospecto.get('campania')
            self._validar_campania(campania)
            if not self._hay_errores():
                nuevo_prospecto = self._crear_prospecto(campos_extra, datos_de_prospecto)
        except ValidationError as error:
            self._set_error_message(error)

        if self._hay_errores():
            datos_de_carga = self._interprete.obtener_datos_de_carga()
            CargaFallidaDeJotform.registrar(datos_de_carga,
                                            self._mensaje_de_error(self._error_message, self._interprete))

        return nuevo_prospecto

    def _set_error_message(self, error):
        if hasattr(error, 'message_dict'):
            self._error_message = str(error.message_dict)
        else:
            self._error_message = error.message

    def _hay_errores(self):
        return len(self._error_message) > 0 or self._interprete.errores()

    def _validar_campania(self, campania):
        if not campania:
            self._error_message = 'Debe indicarse un nombre de campaña válido.'

    def _mensaje_de_error(self, error_message, interprete):
        mensaje_de_error = ''
        if error_message:
            mensaje_de_error += error_message
        if interprete.errores():
            mensaje_de_error += ', '.join(interprete.errores())
        return mensaje_de_error

    def _crear_prospecto(self, campos_extra, datos_de_prospecto):
        marca_de_tarjeta = self._obtener_marca_de_tarjeta_y_eliminarla_de_los_datos_del_prospecto(datos_de_prospecto)
        resultado = self._ejecutar_comando_ingreso_de_prospecto_con(datos_de_prospecto, campos_extra)
        if resultado.is_successful():
            resultado_de_ingreso = resultado.get_object()
            nuevo_prospecto = resultado_de_ingreso.prospecto()

            # Definir si esto debe ser responsabilidad del comando
            tasks.completar_informacion_de_geolocalizacion_desde_ip_de_prospecto.delay(prospecto_id=nuevo_prospecto.id)
            self._agregar_tarjeta_de_credito_al_prospecto(prospecto=nuevo_prospecto,
                                                          marca_de_tarjeta=marca_de_tarjeta)
            if nuevo_prospecto.esta_asignado() and not resultado_de_ingreso.fue_mergeado():
                AdministradorDePedidos().contabilizar_carga_de_prospecto_con_responsable(prospecto=nuevo_prospecto)
            return nuevo_prospecto
        else:
            self._error_message = resultado.errors_as_string()

    def _obtener_marca_de_tarjeta_y_eliminarla_de_los_datos_del_prospecto(self, datos_de_prospecto):
        try:
            marca_de_tarjeta = datos_de_prospecto['marca_de_tarjeta']
            del datos_de_prospecto['marca_de_tarjeta']
            return marca_de_tarjeta
        except KeyError:
            return None

    def _ejecutar_comando_ingreso_de_prospecto_con(self, datos_de_prospecto, campos_extra):
        comando = IngresoDeProspectoDesdeJotformComando()
        comando.set_datos_del_prospecto(datos_de_prospecto)
        comando.set_campos_extra(campos_extra)
        comando.set_debe_validar_email(False)

        resultado = comando.execute()
        return resultado

    def _agregar_tarjeta_de_credito_al_prospecto(self, prospecto, marca_de_tarjeta):
        if marca_de_tarjeta is not None:
            comando = AgregarTarjetaDeCreditoAProspecto()
            arguments = {
                'id_prospecto': prospecto.pk,
                'marcas_de_las_tarjetas': [marca_de_tarjeta]
            }
            comando.set_arguments(arguments)
            resultado = comando.execute()
            return resultado


class InterpreteDeJotform(object):
    columnas_a_ignorar = ['id', 'estado', 'campaña', 'campaña', 'exportado',
                          'telefono_activo', 'email_activo',
                          'ultima_asignacion', 'codigo',
                          'website', 'formID', 'simple_SPC']

    def __init__(self, raw):
        self.VALORES_VALIDOS_DE_TARJETA = ['visa', 'amex', 'maestro', 'si']
        self.fecha = now()
        self.datos_de_carga = dict()
        self.datos_prospecto = dict()
        self.campos_extra = dict()
        self._errores = []
        # Estos metodos usan variables definidas anteriormente, por eso vienen al final del init
        self.separar_datos_de_carga(raw)
        self.calcular_datos_de_prospecto()

    def errores(self):
        return self._errores

    def calcular_datos_de_prospecto(self):
        for key in self.datos_de_carga:
            valor = self.datos_de_carga[key]
            if valor:
                try:
                    valor = self.datos_de_carga[key].strip()
                except AttributeError:
                    pass

            if key == 'campania':
                self.obtener_campania(valor)
            elif key == 'source':
                self.obtener_proveedor(valor)
            elif key == 'vendedor':
                self.obtener_vendedor(valor)
            elif key == 'responsable':
                self.obtener_responsable(valor)
            elif key == 'tarjeta':
                self.obtener_tarjeta(valor)
            elif key == 'ip':
                self.obtener_ip(valor)
            elif key == 'fecha':
                pass
            elif key in CAMPOS_DE_PROSPECTO_PARA_JOTFORM:
                if valor:
                    self.datos_prospecto[key] = valor
                    if key == 'mensaje':
                        self._verificar_mensaje()
            elif key not in self.columnas_a_ignorar:
                if valor:
                    self.campos_extra[key] = valor
        self.corregir_formato_de_telefono()
        self._cargar_al_supervisor_del_vendedor_si_solo_hay_datos_del_vendedor()
        self.datos_prospecto['fecha'] = self.fecha

    def _cargar_al_supervisor_del_vendedor_si_solo_hay_datos_del_vendedor(self):
        if self._hay_vendedor_pero_no_supervisor_cargado():
            self.datos_prospecto['responsable'] = self.datos_prospecto['vendedor'].responsable()

    def _hay_vendedor_pero_no_supervisor_cargado(self):
        return self.datos_prospecto.get('vendedor', None) is not None and\
               self.datos_prospecto.get('responsable', None) is None

    def obtener_nombre_de_campo(self, k):
        # JotForm manda los campos con agregados en el nombre. Ej: 'nombre' se manda como 'q4_nombre4'
        key = re.sub('^q[0-9]+_', '', k)
        key = re.sub('[0-9]+$', '', key)
        return key

    def corregir_formato_de_telefono(self):
        if 'telefono' in self.datos_prospecto:
            if self.datos_prospecto['telefono'].__class__ == dict:
                prefijo = self.datos_prospecto['telefono'].get('area')
                if prefijo:
                    self.datos_prospecto['prefijo'] = prefijo.strip()
                telefono = self.datos_prospecto['telefono'].get('phone')
                if telefono:
                    self.datos_prospecto['telefono'] = telefono.strip()
                else:
                    self.datos_prospecto.pop('telefono')

            prefijo = self.datos_prospecto.get('prefijo')
            prefijo = prefijo.strip() if prefijo else ''
            self.datos_prospecto['prefijo'] = prefijo
            telefono = self.datos_prospecto.get('telefono')
            telefono = telefono.strip() if telefono else ''
            self.datos_prospecto['telefono'] = telefono
            self.datos_prospecto['telefono_sin_normalizar'] = telefono
            if prefijo and telefono:
                if not (prefijo + ' ' in telefono and telefono.index(prefijo + ' ') == 0):
                    self.datos_prospecto['telefono'] = prefijo + ' ' + telefono
            if not prefijo and telefono:
                if len(telefono.split(' ')) > 1:
                    self.datos_prospecto['prefijo'] = telefono.split(' ')[0]

    def obtener_tarjeta(self, valor):
        marca_de_tarjeta = valor.strip()
        if marca_de_tarjeta == 'si':
            self.datos_prospecto['marca_de_tarjeta'] = 'DESCONOCIDA'
        elif marca_de_tarjeta in self.VALORES_VALIDOS_DE_TARJETA:
            self.datos_prospecto['marca_de_tarjeta'] = marca_de_tarjeta.upper()

    def obtener_campania(self, valor):
        campania = Campania.objects.campanias_genericas_de_nombre(nombre=valor)
        try:
            self.datos_prospecto['campania'] = campania[0]
        except IndexError:
            pass

    def obtener_proveedor(self, valor):
        valor = valor.strip()
        if valor:
            proveedor, creado = Proveedor.objects.get_or_create(source_id=valor)
            self.datos_prospecto['proveedor'] = proveedor

    def obtener_ip(self, valor):
        valor = valor.strip()
        if valor and len(valor) <= 16:
            self.datos_prospecto['ip'] = valor

    def obtener_vendedor(self, valor):
        if valor:
            from vendedores.models import Vendedor
            try:
                vendedor = Vendedor.objects.uno_con_nombre_de_usuario(valor)
                self.datos_prospecto['vendedor'] = vendedor
            except Vendedor.DoesNotExist:
                error_message = "No se encontro el vendedor con username {0}".format(valor)
                log = LoggerDeProspectos().para_jotform()
                log.debug(error_message)
                self.errores().append(error_message)

    def obtener_responsable(self, valor):
        if valor:
            from vendedores.models import Vendedor
            try:
                supervisor = Vendedor.objects.uno_con_nombre_de_usuario(valor)
                self.datos_prospecto['responsable'] = supervisor
            except Vendedor.DoesNotExist:
                error_message = "No se encontro el responsable/supervisor con username {0}".format(valor)
                log = LoggerDeProspectos().para_jotform()
                log.debug(error_message)
                self.errores().append(error_message)

    def obtener_datos_de_prospecto(self):
        return self.datos_prospecto

    def obtener_campos_extra(self):
        return self.campos_extra

    def separar_datos_de_carga(self, raw):
        for k in list(raw.keys()):
            key = self.obtener_nombre_de_campo(k)
            valor = raw[k]
            self.datos_de_carga[key] = valor

    def obtener_datos_de_carga(self):
        return self.datos_de_carga

    def _verificar_mensaje(self):
        """ Si el mensaje recibido es mas largo que el aceptado por el campo, truncar el testo en el campo mensaje y
            generar un campo extra de nombre 'mensaje completo' con el texto completo.
        """
        mensaje_completo = self.datos_prospecto['mensaje']
        if len(self.datos_prospecto['mensaje']) > 255:
            mensaje_corto = self._truncar_mensaje(mensaje_completo)
            self.datos_prospecto['mensaje'] = mensaje_corto
            self.campos_extra['mensaje completo'] = mensaje_completo

    def _truncar_mensaje(self, mensaje):
        return mensaje[:252] + '...'