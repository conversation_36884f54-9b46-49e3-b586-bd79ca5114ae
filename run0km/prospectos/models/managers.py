from datetime import timedelta

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.db import models
from django.db.models import Q, Count, When, Case, Value, CharField, F
from django.utils import timezone
from django.utils.timezone import datetime

import prospectos
from concesionarias.rango_laboral import CalendarioLaboral
from core.querysets import DeliveryQuerySet
from core.support import make_aware_when_is_naive
from log_de_errores.models import LogDeError
from prospectos.utils.opciones import ModoDeIngresoChoices


class FotoDeModeloManager(models.Manager):
    def con_imagen(self, imagen):
        return self.get(_imagen=imagen)


class AsignacionDeProspectoQuerySet(models.QuerySet):

    def de_categoria(self, categoria):
        return self.filter(prospecto__campania__categoria=categoria)

    def con_asignacion_a_responsable_entre_fechas(self, desde, hasta):
        queryset = self
        if desde:
            desde_datetime = datetime.combine(desde, datetime.min.time())
            queryset = queryset.filter(fecha_de_asignacion_a_supervisor__gt=desde_datetime)
        if hasta:
            hasta_datetime = datetime.combine(hasta, datetime.max.time())
            queryset = queryset.filter(fecha_de_asignacion_a_supervisor__lt=hasta_datetime)
        return queryset

    def con_asignacion_a_vendedor_entre_fechas_y_horas(self, desde, hasta):
        queryset = self
        if desde:
            queryset = queryset.filter(fecha_de_asignacion_a_vendedor__gte=make_aware_when_is_naive(desde))
        if hasta:
            queryset = queryset.filter(fecha_de_asignacion_a_vendedor__lt=make_aware_when_is_naive(hasta))
        return queryset

    def con_asignacion_a_vendedor_despues_de(self, fecha):
        return self.con_asignacion_a_vendedor_entre_fechas_y_horas(desde=fecha, hasta=None)

    def con_asignacion_a_vendedor_del_dia(self, fecha):
        desde_datetime = datetime.combine(fecha, datetime.min.time())
        hasta_datetime = datetime.combine(fecha, datetime.max.time())
        return self.con_asignacion_a_vendedor_entre_fechas_y_horas(desde=desde_datetime, hasta=hasta_datetime)

    def a_vendedor(self, vendedor):
        return self.filter(vendedor=vendedor)

    def a_vendedores(self, vendedores):
        return self.filter(vendedor__in=vendedores)

    def prospectos_ids(self):
        return self.values_list('prospecto', flat=True)

    def prospectos(self):
        from prospectos.models import Prospecto
        ids = list(self.prospectos_ids().distinct())
        return Prospecto.objects.con_ids(ids)


class ProspectoQuerySet(DeliveryQuerySet):
    """
        Ir pasando de a poco las responsabilidades del manager que son del queryset
    """
    def marcas(self):
        from prospectos.models import Marca
        ids = list(self.values_list('_marca', flat=True).distinct())
        return Marca.objects.con_ids(ids)

    def sin_modelos(self):
        return self.filter(_modelos__isnull=True)

    def modelos(self):
        from prospectos.models import Modelo
        ids = list(self.values_list('id', flat=True))
        return Modelo.objects.filter(_prospectos__in=ids).distinct()

    def proveedores_ids(self):
        return self.values_list('proveedor', flat=True).distinct()

    def campanias(self):
        from campanias.models import Campania
        id_campanias = set(self.values_list('campania', flat=True))
        campanias = Campania.objects.filter(id__in=id_campanias)
        return campanias

    def con_telefono(self, telefono):
        return self.filter(telefono=telefono)

    def con_algun_telefono(self, telefonos):
        return self.filter(telefono__in=telefonos)

    def con_telefono_finalizado_en(self, sufijo_de_telefono):
        return self.filter(telefono__endswith=sufijo_de_telefono)

    def vendidos(self):
        from prospectos.models import Venta
        return self.filter(ventas__estado=Venta.APROBADA)

    def excluir_los_de_estado_vendido(self):
        return self.exclude(estado=self.model.VENDIDO)

    def excluir_los_de_estado_finalizados(self):
        return self.exclude(estado=self.model.FINALIZADO)

    def finalizados(self):
        return self.filter(estado=self.model.FINALIZADO)

    def finalizados_por(self, motivo):
        return self.filter(finalizacion__motivo=motivo)

    def finalizados_con_otro_motivo(self):
        return self.filter(finalizacion__motivo__isnull=True)

    def nuevos(self):
        return self.filter(estado=self.model.NUEVO)

    def en_progreso(self):
        return self.filter(estado=self.model.EN_PROCESO)

    def con_llamado_vencido(self):
        return self.filter(llamado__fecha__lt=timezone.now())

    def sin_agendar(self):
        return self.en_progreso().filter(llamado__isnull=True)

    def de_vendedores(self, vendedores):
        return self.filter(vendedor__in=vendedores)

    def sin_vendedor(self):
        return self.filter(vendedor__isnull=True)

    def sin_vendedor_ni_responsable(self):
        return self.filter(vendedor__isnull=True, responsable__isnull=True)

    def asignado_a_algun_vendedor_activo(self):
        return self.filter(vendedor__isnull=False, vendedor__user__is_active=True)
    
    def asignado_a_algun_vendedor(self):
        return self.filter(vendedor__isnull=False)

    def vendedores(self):
        return self.values_list('vendedor', flat=True)

    def con_vendedor(self, vendedor):
        return self.filter(vendedor=vendedor)

    def con_algun_pedido(self):
        return self.filter(pedido__isnull=False)

    def puede_circular(self):
        return self.filter(_puede_circular=True)

    def con_permiso_para_circular_campania(self):
        return self.filter(campania__permite_circular_prospectos=True)

    def con_permiso_para_circular_vendedor(self):
        return self.filter(vendedor__configuracion_servicios___circular_prospectos_habilitado=True)

    def con_permiso_para_circular_supervisor(self):
        return self.filter(responsable__configuracion_servicios___circular_prospectos_habilitado=True)

    def con_permiso_para_circular_concesionaria(self):
        return self.filter(responsable__concesionaria__configuracion_servicios___circular_prospectos_habilitado=True)

    def con_permiso_para_circular(self):
        return self.con_permiso_para_circular_campania().con_permiso_para_circular_vendedor().con_permiso_para_circular_supervisor().con_permiso_para_circular_concesionaria()

    def con_utlimo_acceso_al_listado_de_prospectos(self):
        return self.filter(vendedor___ultimo_acceso_al_listado_de_prospectos__isnull = False)

    def con_fecha_ultimo_acceso_al_listado_de_prospectos_menor_a_fecha_de_asignacion(self):
        return self.filter(vendedor___ultimo_acceso_al_listado_de_prospectos__lt = models.F('asignacion__fecha_de_asignacion_a_vendedor'))

    def de_marca(self, marca):
        return self.filter(_marca=marca)

    def por_mes_y_anio(self, anio, mes):
        inicio = timezone.make_aware(datetime(year=int(anio), month=int(mes), day=1), timezone.get_current_timezone())
        fin = inicio + relativedelta(months=1)
        return self.entre_fechas(fecha_desde=inicio, fecha_hasta=fin)

    def con_finalizacion_entre_fechas(self, fecha_desde, fecha_hasta):
        queryset = self
        queryset = queryset.filter(finalizacion__isnull=False)
        if fecha_desde:
            queryset = queryset.filter(finalizacion__datetime__gte=fecha_desde)
        if fecha_hasta:
            queryset = queryset.filter(finalizacion__datetime__lt=fecha_hasta)
        return queryset

    def entre_fechas(self, fecha_desde=None, fecha_hasta=None):
        queryset = self
        if fecha_desde:
            queryset = queryset.filter(fecha_creacion__gte=fecha_desde)
        if fecha_hasta:
            queryset = queryset.filter(fecha_creacion__lt=fecha_hasta)
        return queryset

    def que_ingresaron_hace_minutos(self, minutos):
        return self.entre_fechas(fecha_hasta=timezone.now() - timedelta(minutes=minutos))

    def fechas_de_creacion(self):
        return self.values_list('fecha_creacion', flat=True)

    def entre_fechas_del_dato(self, fecha_desde, fecha_hasta):
        queryset = self
        if fecha_desde:
            queryset = queryset.filter(fecha__gte=fecha_desde)
        if fecha_hasta:
            queryset = queryset.filter(fecha__lt=fecha_hasta)
        return queryset

    def entre_fechas_de_asignacion_a_responsable(self, fecha_desde, fecha_hasta):
        queryset = self
        if fecha_desde:
            queryset = queryset.filter(asignacion__fecha_de_asignacion_a_supervisor__gte=fecha_desde)
        if fecha_hasta:
            queryset = queryset.filter(asignacion__fecha_de_asignacion_a_supervisor__lt=fecha_hasta)
        return queryset

    def entre_fechas_de_asignacion_a_vendedor(self, fecha_desde, fecha_hasta):
        queryset = self
        if fecha_desde:
            queryset = queryset.filter(asignacion__fecha_de_asignacion_a_vendedor__gte=fecha_desde)
        if fecha_hasta:
            queryset = queryset.filter(asignacion__fecha_de_asignacion_a_vendedor__lt=fecha_hasta)
        return queryset

    def borrar_llamados(self):
        from prospectos.models import Llamado
        llamados = Llamado.objects.para_prospectos(prospectos=self)
        llamados.delete()

    def borrar_finalizaciones(self):
        from prospectos.models import Finalizacion
        finalizaciones = Finalizacion.objects.filter(prospecto__in=self)
        finalizaciones.delete()

    def borrar_llamadas_programadas(self):
        from prospectos.models import Llamado
        from prospectos.models import LlamadoProgramadoCaduco
        llamadas = Llamado.objects.para_prospectos(self)
        LlamadoProgramadoCaduco.objects.crear_desde_llamadas(llamadas)
        llamadas.delete()

    def con_categoria_de_campania(self, categoria_de_campania):
        return self.filter(campania__categoria=categoria_de_campania)

    def con_tipo_de_origen(self, tipo_de_origen):
        return self.filter(campania__categoria__tipo_de_origen=tipo_de_origen)

    def valor_total(self):
        return self.aggregate(valor_total=models.Sum('campania__categoria__valor'))['valor_total'] or 0

    def cantidades_entregadas_por_calidad(self):
        return self.annotate(calidad=F('campania__categoria__tipo_de_origen__nombre')).values('calidad').annotate(
            cantidad=Count('calidad'))

    def tiempo_promedio_de_respuesta(self):
        cantidad_prospectos = self.count()
        if cantidad_prospectos > 0:
            suma_tiempos = self._sumar_tiempos_de_respuesta_precalculados()
            suma_tiempos += self._sumar_tiempos_de_respuesta_no_precalculados()
            return suma_tiempos / cantidad_prospectos
        else:
            return 0

    def sin_concesionaria_o_con_concesionaria_en(self, concesionarias):
        return self.filter(responsable__isnull=True) | \
               self.filter(responsable__concesionaria__in=concesionarias)

    def tiempo_promedio_de_finalizacion(self):
        prospectos_finalizados = self.finalizados()
        if not prospectos_finalizados.exists():
            return 0
        suma_tiempos = 0
        # Cuenta la cantidad que pudo calcular el tiempo de finalizacion
        total_cantidad = 0
        for prospecto in prospectos_finalizados.select_related('finalizacion', 'asignacion'):
            cantidad, tiempos = self._calcular_tiempo_de_finalizacion_de(prospecto)
            total_cantidad += cantidad
            suma_tiempos += tiempos

        if total_cantidad:
            return suma_tiempos / total_cantidad
        else:
            return 0

    def _calcular_tiempo_de_finalizacion_de(self, prospecto):
        tiempo_de_respuesta = prospecto.tiempo_de_respuesta()
        if prospecto.esta_asignado():
            fecha_de_asignacion = prospecto.fecha_de_asignacion_a_vendedor()
        else:
            fecha_de_asignacion = prospecto.fecha_de_asignacion_a_supervisor()
        try:
            tiempo_desde_asignacion_a_finalizacion = CalendarioLaboral.default().horas_laborales_entre(
                fecha_y_hora_final=prospecto.finalizacion.datetime,
                fecha_y_hora_inicial=fecha_de_asignacion).total_seconds()
        except ValueError:
            cantidad = 0
            tiempo = 0
        else:
            cantidad = 1
            tiempo = max((tiempo_desde_asignacion_a_finalizacion - tiempo_de_respuesta), 0)
        return cantidad, tiempo

    def _sumar_tiempos_de_respuesta_precalculados(self):
        prospectos_con_tiempo_calculado = self.filter(_tiempo_de_respuesta__isnull=False)
        suma = prospectos_con_tiempo_calculado.aggregate(valor_total=models.Sum("_tiempo_de_respuesta"))['valor_total']
        return suma or 0

    def _sumar_tiempos_de_respuesta_no_precalculados(self):
        prospectos_sin_tiempo_calculado = self.filter(_tiempo_de_respuesta__isnull=True)
        return sum([prospecto.tiempo_de_respuesta() for prospecto in prospectos_sin_tiempo_calculado])

    def ordenar_por_fecha_de_creacion(self, ascendente=False):
        """
        TODO: La implementación de este método está invertido el orden,
        el guión -fecha_creacion da un orden descendente.
        Revisar los usos y corregir.
        """
        if ascendente:
            orden = "-fecha_creacion"
        else:
            orden = "fecha_creacion"
        return self.order_by(orden)

    def ordenar_por_fecha(self, ascendente=False):
        if ascendente:
            orden = "fecha"
        else:
            orden = "-fecha"
        return self.order_by(orden)


class ProspectoManager(models.Manager):
    def get_queryset(self):
        return ProspectoQuerySet(self.model, using=self._db)

    def ids(self):
        return self.values_list('id', flat=True)

    def con_ids(self, ids):
        return self.get_queryset().con_ids(ids)

    def consulta_limpia(self):
        return self.get_queryset().consulta_limpia()

    def con_nombre(self, nombre):
        return self.get(nombre=nombre)

    def de_vendedores(self, vendedores):
        return self.get_queryset().de_vendedores(vendedores)

    def con_telefono(self, telefono):
        return self.get_queryset().con_telefono(telefono)

    def con_algun_telefono(self, telefonos):
        return self.get_queryset().con_algun_telefono(telefonos)

    def con_algun_telefono_extra(self, telefonos):
        return self.filter(telefono__in=telefonos) | self.filter(telefono_extra__telefono__in=telefonos)

    def entre_fechas(self, fecha_desde=None, fecha_hasta=None):
        return self.get_queryset().entre_fechas(fecha_desde, fecha_hasta)

    def vendidos(self):
        return self.get_queryset().vendidos()

    def finalizados(self):
        return self.get_queryset().finalizados()

    def borrar_finalizaciones(self):
        return self.get_queryset().borrar_finalizaciones()

    def finalizados_con_otro_motivo(self):
        return self.get_queryset().finalizados_con_otro_motivo()

    def finalizados_por(self, motivo):
        return self.get_queryset().finalizados_por(motivo)

    def nuevos(self):
        return self.get_queryset().nuevos()

    def con_llamado_vencido(self):
        return self.get_queryset().con_llamado_vencido()

    def sin_agendar(self):
        return self.get_queryset().sin_agendar()

    def marcas(self):
        return self.get_queryset().marcas()

    def modelos(self):
        return self.get_queryset().modelos()

    def proveedores_ids(self):
        return self.get_queryset().proveedores_ids()

    def campanias(self):
        return self.get_queryset().campanias()

    def por_mes_y_anio(self, anio, mes):
        return self.get_queryset().por_mes_y_anio(anio, mes)

    def sin_vendedor(self):
        return self.get_queryset().sin_vendedor()

    def con_vendedor(self, vendedor):
        return self.get_queryset().con_vendedor(vendedor)

    def valor_total(self):
        return self.get_queryset().valor_total()

    def borrar_llamadas_programadas(self):
        return self.get_queryset().borrar_llamadas_programadas()

    def con_categoria_de_campania(self, categoria_de_campania):
        return self.get_queryset().con_categoria_de_campania(categoria_de_campania)

    def con_tipo_de_origen(self, tipo_de_origen):
        return self.get_queryset().con_tipo_de_origen(tipo_de_origen)

    def de_vendedor_en_los_ultimos_dias(self, vendedor, dias):
        hace_dias = timezone.now() + timezone.timedelta(-1 * dias)
        from prospectos.models import AsignacionDeProspecto
        asignados_a_vendedor = AsignacionDeProspecto.objects.a_vendedor(vendedor=vendedor)
        asignaciones = asignados_a_vendedor.con_asignacion_a_vendedor_despues_de(hace_dias)
        return asignaciones.prospectos()

    def sin_estados_de_integracion_y_con_antiguedad_y_supervisores(
            self, fecha_cota_superior, fecha_cota_inferior, supervisores):
        return self.filter(responsable__in=supervisores, _integracion__isnull=True,
                           fecha_creacion__lt=fecha_cota_superior, fecha_creacion__gt=fecha_cota_inferior)

    def de_supervisores(self, supervisores_ids):
        return self.filter(responsable__id__in=supervisores_ids)

    def con_estados_de_integracion(self, estados_ids):
        return self.filter(_integracion__id__in=estados_ids)

    def sin_estado_de_integracion_creado(self, prospectos_ids=None):
        if prospectos_ids is not None:
            return self.filter(id__in=prospectos_ids, _integracion__isnull=True)
        return self.filter(_integracion__isnull=True)

    def campanias_con_cantidades(self, qs):
        cantidades = qs.values_list('campania').annotate(cantidad=Count('campania'))
        return list(cantidades)

    def concesionarias(self, qs):
        return qs.values_list('responsable__concesionaria', flat=True).distinct()

    def responsables(self, qs):
        return qs.values_list('responsable', flat=True).distinct()

    def categorias(self, qs):
        return qs.values_list('campania__categoria', flat=True).distinct()

    def origenes(self, qs):
        return qs.values_list('campania__categoria__tipo_de_origen', flat=True).distinct()

    def estados(self, qs):
        return qs.values_list('estado', flat=True).distinct()

    def marcas_con_cantidades(self, qs):
        cantidades = qs.values_list('_marca___codigo').annotate(cantidad=Count('_marca___codigo'))
        return list(cantidades)

    def provincias(self, qs):
        return qs.order_by('provincia').values_list('provincia', flat=True).distinct()

    def provincias_con_cantidades(self, qs):
        cantidades = qs.values_list('provincia').annotate(cantidad=Count('provincia'))
        return list(cantidades)

    def prefijos(self, qs):
        return qs.order_by('prefijo').values_list('prefijo', flat=True).distinct()

    def prefijos_con_cantidades(self, qs):
        cantidades = qs.values_list('prefijo').annotate(cantidad=Count('prefijo'))
        return list(cantidades)

    def sin_vendedor_ni_responsable(self):
        return self.get_queryset().sin_vendedor_ni_responsable()

    def sin_vendedor_y_con_responsable(self):
        return self.filter(vendedor__isnull=True, responsable__isnull=False)

    def con_vendedor_y_responsable(self):
        return self.filter(vendedor__isnull=False, responsable__isnull=False)

    def con_llamado_en_fecha(self, qs, fecha):
        return qs.filter(llamado__isnull=False, llamado__fecha=fecha)

    def mas_viejos_arriba(self, qs, ordenar_por_llamado=False):
        if ordenar_por_llamado:
            qs = qs.extra(select={'null_order': 'prospectos_llamado.id is NULL'})
            return qs.order_by("null_order", "llamado__fecha")
        else:
            return qs.order_by("fecha")

    def mas_nuevos_arriba(self, qs, ordenar_por_llamado):
        if ordenar_por_llamado:
            qs = qs.extra(select={'null_order': 'prospectos_llamado.id is NULL'})
            return qs.order_by("null_order", "-llamado__fecha")
        else:
            return qs.order_by("-fecha")

    def en_rojo(self, qs, fecha_limite):
        return qs.filter(estado='N', vendedor__isnull=False,
                         asignacion__fecha_de_asignacion_a_vendedor__lte=fecha_limite).order_by("-fecha")

    def en_rojo_primero(self, qs, ordenar_por_llamado, fecha_limite):
        if ordenar_por_llamado:
            qs = qs.extra(select={'null_order': 'prospectos_llamado.id is NULL'})
            return qs.order_by("null_order", "-llamado__fecha")
        else:
            # No tiene sentido ordenar prospectos en rojo si se filtra/ordena  por llamado, ya que los prospectos
            # que estan "sin atender hace demasiado" son Nuevos, no pueden tener llamados programados.
            qs = qs.annotate(en_rojo=Case(
                When(estado='N', vendedor__isnull=False, asignacion__fecha_de_asignacion_a_vendedor__lte=fecha_limite,
                     then=Value('1')),
                default=Value('2'),
                output_field=CharField()
            ))
            return qs.order_by("en_rojo", "-fecha")

    def prospectos_con_algun_dato_de_contacto(self, prefijo, telefono, telefono_normalizado, email, nombre_de_marca,
                                              telefono_limpiado=''):
        limite = timezone.now() - timezone.timedelta(settings.DIAS_ANTES_DE_REPETIR_DATOS_DE_CONTACTO)
        prospectos = self.filter(fecha_creacion__gte=limite)
        prospectos = prospectos.exclude(_modo_de_ingreso=ModoDeIngresoChoices.WEB)
        if nombre_de_marca is not None:
            prospectos = prospectos.filter(Q(_marca___nombre=nombre_de_marca) | Q(_marca___codigo=nombre_de_marca) |
                                           Q(_marca___aliases___nombre=nombre_de_marca))
        # Prospectos con email se revisa primero si hay coincidencia con el mail. Si no la hay se pasa a telefono.
        if self._es_email_valido(email) and prospectos.filter(email=email).count() > 0:
            return prospectos.filter(email=email)
        # Si tiene telefono limpiado, filtro por ese ya que me abstrae del servicio de normalizacion externo.
        prefijo = prefijo or ''
        if telefono_limpiado and prospectos.filter(telefono_sin_normalizar=telefono_limpiado, prefijo=prefijo).count() > 0:
            return prospectos.filter(telefono_sin_normalizar=telefono_limpiado, prefijo=prefijo)
        # Prospectos con telefono fueron cargados recientemente si hay coincidencia con el prefijo y el telefono,
        # sea normalizado o no.
        if self._es_telefono_valido(telefono):
            return prospectos.filter(
                (Q(telefono=telefono) | Q(telefono=telefono_normalizado)) & Q(prefijo=prefijo))
        # Si llegue hasta aca, no hay prospecto original, es decir los datos recibidos no son repetidos.
        return self.none()

    def buscar_con_texto(self, qs, texto):
        return qs.filter(Q(mensaje__icontains=texto) | Q(provincia__icontains=texto) |
                         Q(_marca___nombre__icontains=texto) | Q(_modelos___nombre__icontains=texto) |
                         Q(nombre__icontains=texto) |
                         Q(nombre_alternativo__icontains=texto) |
                         Q(telefono__icontains=texto) | Q(email__icontains=texto) |
                         Q(telefono_extra__telefono__contains=texto) |
                         Q(email_extra__email__contains=texto) |
                         Q(comentarios__comentario__icontains=texto)).distinct()

    def buscar_con_nombre(self, qs, texto):
        return qs.filter(Q(nombre__icontains=texto)).distinct()

    def ya_llamados_por_vendedor(self, vendedor):
        marcas = prospectos.models.MarcaYaLLamadoOtraConcesionaria.objects.filter(vendedor=vendedor)
        ids_de_prospectos = list(marcas.values_list('prospecto_id', flat=True))
        return ids_de_prospectos

    def activos(self, qs):
        return qs.filter(ventas__isnull=True, finalizacion__isnull=True)

    def prospecto_del_proximo_llamado(self, qs):
        ahora = timezone.now()
        tope_alerta = ahora + timezone.timedelta(minutes=settings.DELTA_ALERTA_LLAMADOS)
        menores_a_tope = qs.filter(llamado__fecha__lte=tope_alerta).order_by('-llamado__fecha')
        if menores_a_tope.count() > 0:
            return menores_a_tope[0]
        else:
            return qs.order_by('llamado__fecha')[0]

    def prospectos_rechazables_segun_fecha(self, qs):
        limite = timezone.now() - timezone.timedelta(days=settings.DIAS_ANTES_DE_QUE_NO_SE_PUEDA_RECHAZAR)
        return qs.filter(Q(asignacion__fecha_de_asignacion_a_supervisor__gte=limite))

    def sin_informacion_de_redes_sociales(self):
        return self.filter(estado='N', informacion_de_redes_sociales_pedida=False).order_by('-fecha')

    def nuevos_sin_mensaje_de_bienvenida_desde(self, vendedor, fecha_limite):
        return self.filter(vendedor=vendedor, fecha_creacion__gte=fecha_limite, estado='N', mensajes__isnull=True)

    def tiempo_promedio_de_respuesta(self):
        return self.get_queryset().tiempo_promedio_de_respuesta()

    def nuevos_sin_asignar(self):
        return self.filter(estado='N', vendedor__isnull=True)

    def no_nuevos_de_vendedor(self, vendedor):
        return self.filter(~Q(estado='N'), vendedor=vendedor)

    def archivados(self):
        return self.filter(archivado__isnull=False)

    def sin_datos_de_archivado(self, qs):
        if qs is not None:
            return qs.filter(archivado__isnull=True)
        return self.filter(archivado__isnull=True)

    def optimizar_consulta(self, consulta_prospectos):
        consulta_prospectos = consulta_prospectos.select_related('campania', 'llamado', 'geolocalizacion')
        consulta_prospectos = consulta_prospectos.prefetch_related(
            'comentarios', 'llamadas_realizadas', 'telefono_extra')
        return consulta_prospectos

    def nuevos_ordenados_por_fecha_de(self, vendedor):
        return self.filter(estado='N', vendedor=vendedor).order_by('-fecha')

    def con_llamados_programados_recientes_para(self, vendedor):
        return self.filter(estado='P', vendedor=vendedor, llamado__fecha__gte=timezone.now()).order_by(
            'llamado__fecha', '-fecha')

    def en_proceso_ordenados_por_fecha_de(self, vendedor):
        return self.filter(estado='P', vendedor=vendedor).order_by('-fecha')

    def no_exportados(self):
        return self.filter(exportado=False)

    def _es_email_valido(self, email):
        return email and '@' in email

    def _es_telefono_valido(self, telefono):
        return telefono and len(telefono) > 0

class TagQuerySet(models.QuerySet):

    def nombres(self):
        return self.values_list('nombre', flat=True)

    def tags_de(self, vendedor):
        tags = self.filter(vendedor=vendedor).distinct().nombres()

        if vendedor.es_supervisor():
            from vendedores.models import Vendedor
            todos_los_vendedores = Vendedor.all_objects.filter(supervisor=vendedor)
            tags_de_vendedores_de_supervisor = self.filter(vendedor__in=todos_los_vendedores)
            tags_vendedores_de_supervisor = tags_de_vendedores_de_supervisor.distinct().values_list('nombre', flat=True)
            tags = list(tags_vendedores_de_supervisor) + list(tags)
        return tags

    def nombres_de_tags_de_prospectos(self, prospectos):
        tags = []
        if prospectos.filter(tags__isnull=True).exists():
            tags += ['']
        tags += list(self.filter(prospectos__in=prospectos).nombres().distinct())
        return tags


class ComentarioManager(models.Manager):
    def ultimos_comentarios_manuales(self):
        return self.filter(automatico=False).order_by('-id')[:3]


class CompraManager(models.Manager):
    def opciones_de_anios(self):
        return sorted(list([(x, x) for x in self.values_list('anio', flat=True).distinct()]), reverse=True)

    def proveedores_con_compras_en_el_mes(self, anio, mes):
        return self.filter(anio=anio, mes=mes).values_list('proveedor', flat=True).distinct()

    def por_anio_y_mes(self, anio, mes):
        return self.filter(anio=anio, mes=mes)


class RechazoManager(models.Manager):
    def por_mes_y_anio(self, anio, mes):
        inicio = datetime(year=int(anio), month=int(mes), day=1, tzinfo=timezone.get_current_timezone())
        fin = inicio + relativedelta(months=1)
        return self.filter(datetime__gte=inicio, datetime__lt=fin)


class LogDeErrorNormalizadorManager(models.Manager):
    def get_queryset(self):
        return super(LogDeErrorNormalizadorManager, self).get_queryset().filter(
            codigo=LogDeError.CODIGO_LOG_DE_ERROR_NORMALIZADOR)


class LogDeErrorChequeadorDeWhatsappManager(models.Manager):
    def get_queryset(self):
        return super(LogDeErrorChequeadorDeWhatsappManager, self).get_queryset().filter(
            codigo=LogDeError.CODIGO_LOG_DE_ERROR_CHEQUEADOR_DE_WHATSAPP)


class LogDeErrorDeInformacionDeRedesSocialesManager(models.Manager):
    def get_queryset(self):
        return super(LogDeErrorDeInformacionDeRedesSocialesManager, self).get_queryset().filter(
            codigo=LogDeError.CODIGO_LOG_DE_ERROR_INFORMACION_DE_REDES_SOCIALES)


class LogDeErrorDeCRMManager(models.Manager):
    def get_queryset(self):
        return super(LogDeErrorDeCRMManager, self).get_queryset().filter(
            codigo=LogDeError.CODIGO_LOG_DE_ERROR_CRM)


class ProveedorQuerySet(models.QuerySet):
    def excluir_proveedor_vacio(self):
        return self.exclude(source_id=self.model.source_id_de_vacio())