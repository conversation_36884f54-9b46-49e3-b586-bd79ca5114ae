# -*- coding: utf-8 -*-
import re

from django.conf import settings
from django.core.exceptions import MultipleObjectsReturned
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from django.utils import timezone

from campanias.models import Campania, TipoDeOrigen
from core.models import Sistema
from prospectos.configuracion import CAMPOS_DE_CARGA_DE_PROSPECTO
from prospectos.models.base import Proveedor
from prospectos.models.exceptions import ProspectoInvalidoException
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from vendedores.models import Vendedor


class ObtenedorDeCampanias(object):
    def __init__(self, origen_obligatorio=True):
        self.origen_obligatorio = origen_obligatorio
        self.__sistema = None

    def obtener_campania(self, campania_default=None, origen_default=None, nombre_campania='', origen=None):
        # Si el origen es obligatorio, se usa el origen default.
        if self.origen_obligatorio:
            tipo_de_origen = self._tipo_de_origen_segun_codigo(origen_default)
            if tipo_de_origen is None:
                return None, 'Debe indicar un origen válido.'
            if nombre_campania:
                return self.campania_por_nombre_y_origen(nombre_campania, tipo_de_origen)
            else:
                if campania_default:
                    if not campania_default.origen == origen_default:
                        return None, 'El origen y la campaña no coinciden'
                    return campania_default, None
                else:
                    return self.campania_default_por_origen(origen_default)
        else:
            if origen:
                tipo_de_origen = self._tipo_de_origen_segun_codigo(origen)
                if tipo_de_origen is None:
                    return None, 'Debe indicar un origen válido.'
                if nombre_campania:
                    return self.campania_por_nombre_y_origen(nombre_campania, tipo_de_origen)
                else:
                    if campania_default:
                        if campania_default.categoria.tipo_de_origen == tipo_de_origen:
                            return campania_default, None
                    return self.campania_default_por_origen(origen)
            else:
                if nombre_campania:
                    # No importa si la campaña indicada en nombre_campania no coincide con el origen default
                    if nombre_campania:
                        return self.campania_por_nombre(nombre_campania)
                else:
                    if origen_default:
                        tipo_de_origen = self._tipo_de_origen_segun_codigo(origen_default)
                        if tipo_de_origen is None:
                            return None, 'Debe indicar un origen válido.'
                        if campania_default:
                            if not campania_default.categoria.tipo_de_origen == tipo_de_origen:
                                return None, 'El origen y la campaña no coinciden'
                            return campania_default, None
                        else:
                            return self.campania_default_por_origen(origen_default)
                    else:
                        if campania_default:
                            return campania_default, None
                        else:
                            return None, 'No se puede definir una campaña'

    def campania_por_nombre_y_origen(self, nombre, tipo_de_origen):
        try:
            campania = Campania.objects.get(categoria__tipo_de_origen=tipo_de_origen, nombre__iexact=nombre)
            return campania, None
        except ObjectDoesNotExist:
            return None, 'Nombre de campaña inexistente para ese origen.'

    def campania_por_nombre(self, nombre):
        try:
            campania = Campania.objects.get(nombre__iexact=nombre)
            return campania, None
        except ObjectDoesNotExist:
            return None, 'Nombre de campaña inexistente.'
        except MultipleObjectsReturned:
            campania = Campania.objects.get(nombre__iexact=nombre, categoria__es_externa=False)
            return campania, None

    def campania_default_por_origen(self, codigo_origen):
        try:
            return Campania.objects.get(categoria__tipo_de_origen__codigo__iexact=codigo_origen,
                                        id__in=self._sistema().ids_de_campanias_genericas()), None
        except ObjectDoesNotExist:
            return None, 'No se puede definir una campaña por defecto para el origen %s' % str(codigo_origen)

    def _tipo_de_origen_segun_codigo(self, codigo):
        try:
            return TipoDeOrigen.objects.get(codigo=codigo)
        except ObjectDoesNotExist:
            return None

    def _sistema(self):
        if not self.__sistema:
            self.__sistema = Sistema.instance()
        return self.__sistema


class CargadorDeProspectos(object):
    def __init__(self, origen_obligatorio=True):
        self.cargador_de_campanias = ObtenedorDeCampanias(origen_obligatorio)

    def obtener_fecha(self, defaults):
        fecha_hora = self._fecha_y_hora_desde(str(self.datos_de_prospecto.get('fecha', '')), default=timezone.now())
        defaults['fecha'] = fecha_hora

    def _fecha_y_hora_desde(self, fecha_string, default=None):
        fecha_string = fecha_string or ''
        fecha_string = fecha_string.strip()

        if not fecha_string:
            return default

        try:
            fecha = timezone.datetime.strptime(fecha_string, '%d/%m/%Y %H:%M')
            fecha = timezone.make_aware(fecha, timezone.get_current_timezone())
            return fecha
        except ValueError:
            try:
                fecha = timezone.datetime.strptime(fecha_string, settings.API_FORMATO_FECHA_Y_HORA)
                fecha = timezone.make_aware(fecha, timezone.get_current_timezone())
                return fecha
            except ValueError:
                return default

    def obtener_responsable(self, defaults):
        if 'responsable' not in defaults:
            if 'responsable' in self.datos_de_prospecto:
                valor = str(self.datos_de_prospecto['responsable']).strip()
                if valor:
                    try:
                        defaults['responsable'] = Vendedor.objects. \
                            supervisores_activos().get(user__username__iexact=valor)
                    except ObjectDoesNotExist:
                        return 'Username de supervisor responsable inexistente.'
        return None

    def obtener_vendedor(self, defaults):
        # Obtener vendedor
        if 'vendedor' not in defaults:
            if 'vendedor' in self.datos_de_prospecto:
                valor = str(self.datos_de_prospecto['vendedor']).strip()
                if valor:
                    try:
                        vendedor = Vendedor.objects.get(user__username__iexact=valor)
                    except ObjectDoesNotExist:
                        return 'Username de vendedor inexistente.'
                    else:
                        if 'responsable' in defaults:
                            if not vendedor.supervisor:
                                if vendedor.cargo == 'Supervisor' and vendedor == defaults['responsable']:
                                    defaults['vendedor'] = vendedor
                                else:
                                    return 'El vendedor no esta a cargo del supervisor indicado.'
                            else:
                                if vendedor.supervisor == defaults['responsable'] or \
                                        (vendedor.cargo == 'Supervisor' and vendedor == defaults['responsable']):
                                    defaults['vendedor'] = vendedor
                                else:
                                    return 'El vendedor no esta a cargo del supervisor indicado.'
                            if not vendedor.user.is_active:
                                defaults['vendedor'] = None
                        else:
                            if not vendedor.user.is_active:
                                return 'Username de vendedor inexistente.'
                            defaults['vendedor'] = vendedor

        if 'vendedor' in defaults and 'responsable' not in defaults:
            vendedor = defaults['vendedor']
            if vendedor.supervisor:
                defaults['responsable'] = vendedor.supervisor
            elif vendedor.cargo == 'Supervisor':
                defaults['responsable'] = vendedor
        return None

    def registrar_datos_inactivos(self, defaults):
        if 'telefono_activo' in self.datos_de_prospecto:
            valor = str(self.datos_de_prospecto['telefono_activo']).strip()
            if valor == '0':
                defaults['telefono_activo'] = False
        if 'email_activo' in self.datos_de_prospecto:
            valor = str(self.datos_de_prospecto['email_activo']).strip()
            if valor == '0':
                defaults['email_activo'] = False

    def obtener_proveedor(self, defaults):
        if 'source' in self.datos_de_prospecto:
            source_id = str(self.datos_de_prospecto['source']).strip()
            if source_id:
                proveedor, nuevo = Proveedor.objects.get_or_create(source_id=source_id)
                defaults['proveedor'] = proveedor
        if 'proveedor' not in defaults and 'proveedor' in self.datos_generales:
            defaults['proveedor'] = self.datos_generales['proveedor']

    def cargar_prospecto(self, datos_generales, datos_de_prospecto, datos_extra, codigo_de_tipo_de_origen=None,
                         origen_de_prospecto=None, proveniente_de_chat=False):
        """
        :param proveniente_de_chat: Instancia de si el prospecto viene desde el chat.
        :param origen_de_prospecto: Instancia de clase OrigenDeProspectoAPI o OrigenDeProspectoCSV.
                                    La opcion None se deja para testing.
        :param datos_generales: Opcionales {'campania': Campania, 'responsable': Vendedor, 'vendedor': Vendedor }
               'origen' tambien es opcional si el cargador de campanias utilizado tiene origen opcional.
        :param datos_de_prospecto: Diccionario de Strings.
        {'campania', 'origen', 'responsable', 'vendedor', 'fecha', 'nombre', 'prefijo', 'telefono', 'email', 'mensaje',
        'provincia', 'localidad', 'marca', 'modelo', 'codigo', 'telefono_activo', 'email_activo', }
        'responsable' y 'vendedor' se toman en cuenta si no estan en los generales y deben corresponder a
        usernames de responsables y vendedor existentes.
        'fecha' Debe tener el formato 'dd/mm/YY HH:MM' o 'YYYY-mm-dd HH:MM' o se utilizara la fecha actual.
        Si el origen es opcional 'campania' y 'origen' deben coincidir con el nombre de una campaña existente y el
        origen de la misma cuando ambos estan definidos.
        :param datos_extra: Campos extra del prospecto.
        :param codigo_de_tipo_de_origen: Para confirmar que la campania seleccionada para el prospecto corresponde al
         origen.
        :return: Resultado de carga
        """

        # TODO: deuda pendiendte, esto deberia responder un resultado y no esta tupla.  Eso obliga a lanzar la
        # excepcion de repetidos para arriba, porque el cliente debe manejarla de forma diferente al validation error
        self.datos_generales = datos_generales
        self.datos_de_prospecto = datos_de_prospecto
        self.datos_extra = datos_extra
        self.origen = codigo_de_tipo_de_origen

        defaults = {}
        for campo in ['campania', 'responsable', 'vendedor', 'pedido']:
            if campo in self.datos_generales:
                defaults[campo] = self.datos_generales[campo]

        self.obtener_fecha(defaults)

        campania, error = self.cargador_de_campanias.obtener_campania(
            datos_generales.get('campania', None),
            codigo_de_tipo_de_origen,
            datos_de_prospecto.get('campania', '').strip(),
            datos_de_prospecto.get('origen', '').strip().upper())
        if error:
            return ResultadoDeCargaFallida.con_error(error)
        else:
            defaults['campania'] = campania

        error = self.obtener_responsable(defaults)
        if error:
            return ResultadoDeCargaFallida.con_error(error)

        error = self.obtener_vendedor(defaults)
        if error:
            return ResultadoDeCargaFallida.con_error(error)

        self.registrar_datos_inactivos(defaults)
        self.obtener_proveedor(defaults)

        campos_ya_procesados = ['campania', 'responsable', 'vendedor', 'fecha',
                                'telefono_activo', 'email_activo', 'source', 'origen']
        for campo in self.datos_de_prospecto:
            if campo not in campos_ya_procesados \
                    and campo in CAMPOS_DE_CARGA_DE_PROSPECTO:
                valor = self.obtener_valor(self.datos_de_prospecto[campo])
                if campo == 'mensaje' and valor:
                    valor = valor[:255]
                if valor:
                    defaults[campo] = valor

        try:
            repartidor = RepartidorDeProspectos()
            resultado = repartidor.crear_nuevo_prospecto_desde(
                datos=defaults, campos_extra=self._campos_extra(),
                origen_de_prospecto=origen_de_prospecto, proveniente_de_chat=proveniente_de_chat,
                asistente_de_gestion=self.obtener_datos_de_asistente_de_gestion()
            )

        except ValidationError as exc:
            return ResultadoDeCargaFallida.con_error(str(exc))
        except ProspectoInvalidoException as exc:
            return ResultadoDeCargaFallida.con_error(str(exc))

        return resultado

    def _crear_campos_extras(self, prospecto, repartidor):
        datos_extra = self._campos_extra()
        repartidor.agregar_campos_extras_a(prospecto, datos_extra)

    def _campos_extra(self):
        datos_extra = {str(extra['nombre']): self.obtener_valor(extra['valor']) for extra in self.datos_extra}
        return datos_extra

    def obtener_valor(self, dato):
        if dato is None:
            return None
        try:
            valor = str(dato).strip()
        except UnicodeEncodeError:
            dato = dato.encode('ISO-8859-15', 'replace')
            valor = str(dato, 'ISO-8859-15').strip()
        return valor

    def obtener_datos_de_asistente_de_gestion(self):
        programar_reunion = self.datos_de_prospecto.get('programar_reunion', None) or ''
        debe_programar_reunion = programar_reunion.lower() == 'on'
        fecha_reunion = self.datos_de_prospecto.get('fecha_reunion', None) or ''
        fecha_reunion_a_programar = self._fecha_y_hora_desde(str(fecha_reunion))
        puede_programar_reunion = debe_programar_reunion and fecha_reunion_a_programar is not None
        return {
            'debe_programar_reunion': puede_programar_reunion,
            'fecha_reunion_a_programar': fecha_reunion_a_programar
        }


class ResultadoDeCargaFallida(object):
    def __init__(self, mensaje_de_error):
        super(ResultadoDeCargaFallida, self).__init__()
        self._mensaje_de_error = mensaje_de_error

    @classmethod
    def con_error(cls, mensaje_de_error):
        return cls(mensaje_de_error)

    def mensaje_de_error(self):
        return self._mensaje_de_error

    def es_erroneo(self):
        return True
