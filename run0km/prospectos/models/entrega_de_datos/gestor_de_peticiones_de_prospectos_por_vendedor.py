from django.core.exceptions import ValidationError

from core.models import Siste<PERSON>
from prospectos.models.entrega_de_datos.notificador_de_resultado_de_entrega import NotificadorDeResultadoDeEntrega
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from enum import Enum 

class EstadoBoton(Enum): 
    VISIBLE = "visible" 
    GRISADO = "grisado" 
    NO_VISIBLE = "no_visible"

class GestorDePeticionesDeProspectosPorVendedor(object):
    def __init__(self, vendedor):
        super().__init__()
        self._vendedor = vendedor
        self._tamanio_maximo_de_la_cola_de_peticiones = Sistema.instance().tamanio_maximo_de_cola_de_peticiones

    @classmethod
    def nuevo_para(cls, vendedor):
        return cls(vendedor)

    def pedir_prospecto(self):
        self._validar_que_el_vendedor_esta_habilitado_a_pedir_prospectos()
        cola_de_peticiones = self._cola_de_peticiones()
        cola_de_peticiones.agregar_peticion_de_prospecto_para(vendedor=self._vendedor)

    def entregar_prospecto(self):
        self._validar_que_el_vendedor_esta_habilitado_a_pedir_prospectos()
        administrador_de_pedidos = AdministradorDePedidos.nuevo()
        return administrador_de_pedidos.asignar_algun_prospecto_disponible_a_vendedor(vendedor=self._vendedor)
    
    
    def puede_pedir_prospecto(self)->EstadoBoton:
        """TODO: Meter un objeto que devuelva un objeto que diga si puede, o si no puede + detalle la view es quien debe manejar tema colores y demás"""
        if self._no_esta_habilitado_a_pedir_prospectos():
            return EstadoBoton.NO_VISIBLE

        if not self._vendedor.tiene_cargo_vendedor():
            return EstadoBoton.NO_VISIBLE

        administrador_de_pedidos = AdministradorDePedidos.nuevo()
        prospectos_disponibles = administrador_de_pedidos.prospectos_disponibles_para_entregar_a_vendedor()

        if len(prospectos_disponibles) < 1:
            return EstadoBoton.GRISADO

        pedidos_con_credito = administrador_de_pedidos.pedidos_con_credito_de_vendedor(vendedor=self._vendedor)

        if len(pedidos_con_credito) < 1:
            return EstadoBoton.GRISADO

        if (self._cola_de_peticiones().esta_inhabilitado_al_tener_peticiones_no_entregadas(vendedor=self._vendedor)
                or self._cola_de_peticiones().esta_inhabilitado_por_peticiones_pendientes_para(vendedor=self._vendedor)
                or self._cola_de_peticiones().esta_inhabilitado_al_tener_la_cola_llena()):
            return EstadoBoton.GRISADO
        return EstadoBoton.VISIBLE


    def _validar_que_el_vendedor_esta_habilitado_a_pedir_prospectos(self):
        if self._no_esta_habilitado_a_pedir_prospectos():
            raise ValidationError('Solo los vendedores habilitados pueden pedir prospectos')

    def _no_esta_habilitado_a_pedir_prospectos(self):
        return (not self._vendedor.habilitado or not self._vendedor.tiene_cargo_vendedor()
                or not self._tiene_servicio_habilitado_para_pedir_prospecto()
                or not self.el_vendedor_no_supero_el_limite_diario())

    def _tiene_servicio_habilitado_para_pedir_prospecto(self):
        return self._vendedor.configuracion_servicios.puede_pedir_prospecto()

    def el_vendedor_no_supero_el_limite_diario(self):
        administrador_de_pedidos = AdministradorDePedidos.nuevo()
        return administrador_de_pedidos.el_vendedor_no_supero_el_limite_diario(vendedor=self._vendedor)

    def _cola_de_peticiones(self):
        from prospectos.models.entrega_de_datos.cola_de_peticion_de_prospectos_de_vendedor import \
            ColaDePeticionDeProspectoDeVendedor
        notificador_de_resultado = NotificadorDeResultadoDeEntrega.nuevo()
        cola_de_peticiones = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo=self._tamanio_maximo_de_la_cola_de_peticiones,
            notificador_de_resultado=notificador_de_resultado)
        return cola_de_peticiones