class FuncionNormalizadoraDeTiempoDeRespuesta(object):
    _DEFAULT = 0.3
    _VALORES_PRIMEROS_PUESTOS = {2: 0.4, 1: 0.5, 0: 1.0}
    _VALORES_ULTIMOS_PUESTOS = {2: 0.2, 1: 0.1, 0: 0}

    def __init__(self, vendedores):
        super(FuncionNormalizadoraDeTiempoDeRespuesta, self).__init__()
        self._valores_mensuales = self._crear_valores(vendedores,
                                                      lambda each: each.tiempo_promedio_de_respuesta_del_mes())
        self._valores_semanales = self._crear_valores(vendedores,
                                                      lambda each: each.tiempo_promedio_de_respuesta_de_ultima_semana())

    def _crear_valores(self, vendedores, sort_block):
        ordenados_por_tiempo = sorted(vendedores, key=sort_block)
        size = len(ordenados_por_tiempo) - 1
        valores = {}
        for indice in [2, 1, 0]:
            vendedor = ordenados_por_tiempo[max(0, size - indice)]
            valores[vendedor.id] = self._VALORES_ULTIMOS_PUESTOS[indice]
            vendedor = ordenados_por_tiempo[min(size, indice)]
            valores[vendedor.id] = self._VALORES_PRIMEROS_PUESTOS[indice]
        return valores

    def factor_mensual_para(self, vendedor):
        return self._evaluar(vendedor, self._valores_mensuales)

    def factor_semanal_para(self, vendedor):
        return self._evaluar(vendedor, self._valores_semanales)

    def _evaluar(self, vendedor, distribucion):
        valor = distribucion.get(vendedor.id, self._DEFAULT)
        return valor

    @classmethod
    def nueva(cls, vendedores):
        assert (len(vendedores) > 0)
        return cls(vendedores)


class Regla(object):
    def __init__(self, minimo, maximo):
        self._minimo = minimo
        self._maximo = maximo

    def minimo(self):
        return self._minimo

    def maximo(self):
        return self._maximo

    def evaluar(self, constante, factor, factor_condicional):
        if factor_condicional == 0:
            valor = self._evaluar_minimo(constante, self.minimo())
        else:
            valor = self._evaluar_maximo(constante, self.maximo(), factor)
        return int(valor)

    def _evaluar_maximo(self, constante, factor_regla, factor_relativo):
        return constante + int(constante *
                               factor_regla * factor_relativo)

    def _evaluar_minimo(self, constante, factor_regla):
        return constante * factor_regla

    @classmethod
    def nuevo(cls, maximo, minimo):
        assert 0 < maximo <= 1
        assert 0 < minimo < 1

        return cls(minimo, maximo)