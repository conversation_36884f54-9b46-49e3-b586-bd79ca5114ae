import re

from prospectos.configuracion import CAMPOS_DE_PROSPECTO_PARA_API

from prospectos.models import Geolocalizacion


class EvaluadorDeFiltrosDeProspecto(object):
    def aplica_para(self, filtro, prospecto):
        campo = filtro.campo
        if campo in CAMPOS_DE_PROSPECTO_PARA_API:
            descriptor = DescriptorDeCampo.nuevo_para(campo)
            aplica_filtro = descriptor.aplica_filtro(filtro, prospecto)
            return aplica_filtro
        else:
            return self._aplicar_a_campos_extras(filtro, prospecto.campos_extra)

    def _aplicar_a_campos_extras(self, filtro, campos_extra):
        for campo_extra in campos_extra.filter(nombre=filtro.campo):
            if filtro.aplica(campo_extra.valor):
                return True
        return False


class DescriptorDeCampo(object):
    def __init__(self, campo):
        super(DescriptorDeCampo, self).__init__()
        self._campo = campo

    def aplica_filtro(self, filtro, prospecto):
        valores = self.valores_para(prospecto)
        return any([self._aplica_valor(filtro, valor) for valor in valores])

    def _aplica_valor(self, filtro, valor):
        return valor is not None and filtro.aplica(valor)

    def valores_para(self, prospecto):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def campos(cls):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def nuevo_para(cls, campo):
        for subclass in cls.__subclasses__():
            if subclass.acepta(campo):
                return subclass(campo)
        return DescriptorDeCampoGenerico(campo)

    @classmethod
    def acepta(cls, campo):
        return campo in cls.campos()


class DescriptorDeCampoDeGeolocalizacion(DescriptorDeCampo):
    _CONVERSOR = {'localidad_desde_ip': 'localidad', 'provincia_desde_ip': 'provincia', 'ip': 'ip'}

    def valores_para(self, prospecto):
        nuevo_campo = self._CONVERSOR.get(self._campo)
        try:
            geo = prospecto.obtener_geolocalizacion()
            return [geo.serializable_value(nuevo_campo)]
        except Geolocalizacion.DoesNotExist:
            return []

    @classmethod
    def campos(cls):
        return ['ip', 'localidad_desde_ip', 'provincia_desde_ip']


class DescriptorDeCampoMarca(DescriptorDeCampo):
    def valores_para(self, prospecto):
        marca = prospecto.obtener_marca()
        if marca is not None:
            return [marca.nombre()]
        else:
            return []

    @classmethod
    def campos(cls):
        return ['marca']


class DescriptorDeCampoModelo(DescriptorDeCampo):

    def valores_para(self, prospecto):
        return [modelo.codigo() for modelo in prospecto.obtener_modelos()]

    @classmethod
    def campos(cls):
        return ['modelo']


class DescriptorDeCampoPrefijo(DescriptorDeCampo):

    def valores_para(self, prospecto):
        valor = prospecto.serializable_value(self._campo)
        prefijo = re.sub(r'^0+', '', valor)
        return [prefijo.zfill(i) for i in range(1, 6)]

    @classmethod
    def campos(cls):
        return ['prefijo']


class DescriptorDeCampoGenerico(DescriptorDeCampo):
    def valores_para(self, prospecto):
        return [prospecto.serializable_value(self._campo)]

    @classmethod
    def campos(cls):
        return []
