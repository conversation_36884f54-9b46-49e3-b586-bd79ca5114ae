from testing.base import BaseFixturedTest
from testing.factories import ProveedoresFactory, ComprasFactory
from prospectos.models import Compra


class TestCompra(BaseFixturedTest):

    def test_compra_manager(self):
        self.assertEqual(0, len(Compra.objects.opciones_de_anios()))
        p1 = ProveedoresFactory(source_id='p1')
        p2 = ProveedoresFactory(source_id='p2')
        p3 = ProveedoresFactory(source_id='p3')
        p4 = ProveedoresFactory(source_id='p4')
        c1 = ComprasFactory(proveedor=p1, anio=2011, mes=5, monto=100)
        c2 = ComprasFactory(proveedor=p1, anio=2011, mes=6, monto=100)
        c3 = ComprasFactory(proveedor=p1, anio=2013, mes=6, monto=100)
        c4 = ComprasFactory(proveedor=p1, anio=2014, mes=6, monto=100)
        c5 = ComprasFactory(proveedor=p1, anio=2014, mes=7, monto=100)
        c6 = ComprasFactory(proveedor=p1, anio=2014, mes=8, monto=100)
        c7 = ComprasFactory(proveedor=p2, anio=2014, mes=7, monto=100)
        c8 = ComprasFactory(proveedor=p2, anio=2014, mes=8, monto=100)
        opciones = Compra.objects.opciones_de_anios()
        self.assertEqual(3, len(opciones))
        self.assertIn((2011, 2011), opciones)
        self.assertIn((2013, 2013), opciones)
        self.assertIn((2014, 2014), opciones)

        proveedores = Compra.objects.proveedores_con_compras_en_el_mes(2011, 4)
        self.assertEqual(0, len(proveedores))

        proveedores = Compra.objects.proveedores_con_compras_en_el_mes(2011, 5)
        self.assertEqual(1, len(proveedores))
        self.assertIn(p1.id, proveedores)

        proveedores = Compra.objects.proveedores_con_compras_en_el_mes(2014, 7)
        self.assertEqual(2, len(proveedores))
        self.assertIn(p1.id, proveedores)
        self.assertIn(p2.id, proveedores)
