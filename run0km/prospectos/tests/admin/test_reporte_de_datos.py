#-*- coding: utf-8 -*-

from django.test.testcases import TestCase
from django.utils import timezone
from django.utils.timezone import make_aware

from campanias.models import TipoDeOrigen
from prospectos.utils.reporte_de_datos import ReporteDeDatosEntregados
from testing.base import BaseLoggedAdminTest
from testing.factories import ProspectosFactory, CampaniasFactory, CategoriasDeCampaniaFactory, UsersFactory, \
    VendedoresFactory, ConcesionariasFactory, RechazosFactory


class AdminReporteDeComprasTest(BaseLoggedAdminTest):

    def test_links_a_reportes(self):
        response = self.client.get('/admin/prospectos/prospecto/')
        self.assertEqual(response.status_code, 200)
        contenido_de_la_respuesta = response.content.decode('utf-8')
        self.assertIn('Reporte de datos entregados', contenido_de_la_respuesta)
        self.assertIn('<a href="/admin/prospectos/prospecto/reportes/datos/"', contenido_de_la_respuesta)
        response = self.client.get('/admin/prospectos/prospecto/reportes/datos/')
        self.assertEqual(response.status_code, 200)


class ReporteDeComprasTest(TestCase):

    def test_reporte_de_ejemplo(self):
        hoy = timezone.localtime(timezone.now())

        usr1 = UsersFactory(username='sup1')
        con1 = ConcesionariasFactory(nombre='con1', dia_fin_periodos=12, dia_inicio_periodos=1)
        sup1 = VendedoresFactory(user=usr1, cargo='Supervisor', concesionaria=con1)
        usr2 = UsersFactory(username='sup2')
        sup2 = VendedoresFactory(user=usr2, cargo='Supervisor', concesionaria=con1)

        usr3 = UsersFactory(username='sup3')
        con2 = ConcesionariasFactory(nombre='con2', dia_fin_periodos=12, dia_inicio_periodos=1)
        sup3 = VendedoresFactory(user=usr3, cargo='Supervisor', concesionaria=con2)

        usr4 = UsersFactory(username='sup4')
        sup4 = VendedoresFactory(user=usr4, cargo='Supervisor', concesionaria=con2)

        tipo_s, created = TipoDeOrigen.objects.get_or_create(nombre='SMS', codigo='S')
        cat1 = CategoriasDeCampaniaFactory(nombre='cat1', tipo_de_origen=tipo_s, valor=30)
        cat2 = CategoriasDeCampaniaFactory(nombre='cat2', tipo_de_origen=tipo_s, valor=50)
        campania1 = CampaniasFactory(nombre='camp1', categoria=cat1)
        campania2 = CampaniasFactory(nombre='camp2', categoria=cat2)

        marca_uno = 'Mar1'
        ProspectosFactory(responsable=sup1, campania=campania1, marca=marca_uno, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup1, campania=campania1, marca=marca_uno, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup1, campania=campania2, marca=marca_uno, fecha_creacion=hoy)
        marca_dos = 'Mar2'
        ProspectosFactory(responsable=sup1, campania=campania1, marca=marca_dos, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup1, campania=campania2, marca=marca_dos, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup1, campania=campania2, marca=marca_dos, fecha_creacion=hoy)

        p1 = ProspectosFactory(responsable=sup4, campania=campania2, marca=marca_dos)
        p1.fecha_creacion = make_aware(timezone.datetime(10, 10, 10))
        p1.save()
        RechazosFactory(prospecto=p1, responsable=sup1, datetime=hoy)

        ProspectosFactory(responsable=sup2, campania=campania2, marca=marca_uno, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup2, campania=campania2, marca=marca_uno, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup2, campania=campania2, marca=marca_uno, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup2, campania=campania1, marca=marca_dos, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup2, campania=campania1, marca=marca_dos, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup2, campania=campania1, marca=marca_dos, fecha_creacion=hoy)

        p2 = ProspectosFactory(responsable=sup4, campania=campania2, marca=marca_dos)
        p2.fecha_creacion = make_aware(timezone.datetime(10, 10, 10))
        p2.save()
        RechazosFactory(prospecto=p2, responsable=sup2, datetime=hoy)

        ProspectosFactory(responsable=sup3, campania=campania1, marca=marca_uno, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup3, campania=campania1, marca=marca_dos, fecha_creacion=hoy)
        ProspectosFactory(responsable=sup3, campania=campania1, marca=marca_dos, fecha_creacion=hoy)

        p3 = ProspectosFactory(responsable=sup4, campania=campania1, marca=marca_dos)
        p3.fecha_creacion = make_aware(timezone.datetime(10, 10, 10))
        p3.save()
        RechazosFactory(prospecto=p3, responsable=sup3, datetime=hoy)

        reporte = ReporteDeDatosEntregados(mes=hoy.month, anio=hoy.year, supervisor=None)

        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat1']['entregados'], 2)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat1']['entregados_valor'], 60)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat1']['rechazados'], 0)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat1']['rechazados_valor'], 0)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat1']['total'], 2)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat1']['total_valor'], 60)

        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat2']['entregados'], 1)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat2']['entregados_valor'], 50)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat2']['rechazados'], 0)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat2']['rechazados_valor'], 0)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat2']['total'], 1)
        self.assertEqual(reporte.datos[con1][sup1][marca_uno]['cat2']['total_valor'], 50)

        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat1']['entregados'], 1)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat1']['entregados_valor'], 30)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat1']['rechazados'], 0)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat1']['rechazados_valor'], 0)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat1']['total'], 1)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat1']['total_valor'], 30)

        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat2']['entregados'], 2)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat2']['entregados_valor'], 100)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat2']['rechazados'], -1)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat2']['rechazados_valor'], -50)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat2']['total'], 1)
        self.assertEqual(reporte.datos[con1][sup1][marca_dos]['cat2']['total_valor'], 50)

        self.assertNotIn('cat1', reporte.datos[con1][sup2][marca_uno])

        self.assertEqual(reporte.datos[con1][sup2][marca_uno]['cat2']['entregados'], 3)
        self.assertEqual(reporte.datos[con1][sup2][marca_uno]['cat2']['entregados_valor'], 150)
        self.assertEqual(reporte.datos[con1][sup2][marca_uno]['cat2']['rechazados'], 0)
        self.assertEqual(reporte.datos[con1][sup2][marca_uno]['cat2']['rechazados_valor'], 0)
        self.assertEqual(reporte.datos[con1][sup2][marca_uno]['cat2']['total'], 3)
        self.assertEqual(reporte.datos[con1][sup2][marca_uno]['cat2']['total_valor'], 150)

        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat1']['entregados'], 3)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat1']['entregados_valor'], 90)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat1']['rechazados'], 0)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat1']['rechazados_valor'], 0)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat1']['total'], 3)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat1']['total_valor'], 90)

        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat2']['entregados'], 0)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat2']['entregados_valor'], 0)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat2']['rechazados'], -1)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat2']['rechazados_valor'], -50)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat2']['total'], -1)
        self.assertEqual(reporte.datos[con1][sup2][marca_dos]['cat2']['total_valor'], -50)

        self.assertEqual(reporte.datos[con2][sup3][marca_uno]['cat1']['entregados'], 1)
        self.assertEqual(reporte.datos[con2][sup3][marca_uno]['cat1']['entregados_valor'], 30)
        self.assertEqual(reporte.datos[con2][sup3][marca_uno]['cat1']['rechazados'], 0)
        self.assertEqual(reporte.datos[con2][sup3][marca_uno]['cat1']['rechazados_valor'], 0)
        self.assertEqual(reporte.datos[con2][sup3][marca_uno]['cat1']['total'], 1)
        self.assertEqual(reporte.datos[con2][sup3][marca_uno]['cat1']['total_valor'], 30)

        self.assertNotIn('cat2', reporte.datos[con2][sup3][marca_uno])
        self.assertNotIn('cat2', reporte.datos[con2][sup3][marca_dos])

        self.assertEqual(reporte.datos[con2][sup3][marca_dos]['cat1']['entregados'], 2)
        self.assertEqual(reporte.datos[con2][sup3][marca_dos]['cat1']['entregados_valor'], 60)
        self.assertEqual(reporte.datos[con2][sup3][marca_dos]['cat1']['rechazados'], -1)
        self.assertEqual(reporte.datos[con2][sup3][marca_dos]['cat1']['rechazados_valor'], -30)
        self.assertEqual(reporte.datos[con2][sup3][marca_dos]['cat1']['total'], 1)
        self.assertEqual(reporte.datos[con2][sup3][marca_dos]['cat1']['total_valor'], 30)

        self.assertEqual(reporte.total['total_categorias']['cat1'], (8, 240))
        self.assertEqual(reporte.total['total_categorias']['cat2'], (4, 200))
        self.assertEqual(reporte.total[sup1], (5, 190))
        self.assertEqual(reporte.total[sup2], (5, 190))
        self.assertEqual(reporte.total[sup3], (2, 60))
        self.assertEqual(reporte.total[con1]['cat1'], (6, 180))
        self.assertEqual(reporte.total[con1]['cat2'], (4, 200))
        self.assertEqual(reporte.total[con2]['cat1'], (2, 60))
        self.assertNotIn('cat2', reporte.total[con2])