#-*- coding: utf-8 -*-

from django.utils import timezone
from django.utils.dateparse import parse_datetime
from testing.base import BaseLoggedAdminTest, BaseFixturedTest
from testing.factories import ComprasFactory, ProveedoresFactory, ProspectosFactory, VentasFactory

from prospectos.utils.reporte_de_compras import ReporteDeCompras


class AdminReporteDeComprasTest(BaseLoggedAdminTest):

    def test_links_a_reportes(self):
        response = self.client.get('/admin/prospectos/compra/')
        self.assertEqual(response.status_code, 200)
        contenido_de_la_respuesta = response.content.decode('utf-8')
        self.assertIn('Ver Reportes', contenido_de_la_respuesta)
        self.assertIn('<a href="/admin/prospectos/compra/reportes/" ', contenido_de_la_respuesta)
        response = self.client.get('/admin/prospectos/compra/reportes/')
        self.assertEqual(response.status_code, 200)

    def test_formulario_de_reportes_muestra_solo_anios_desde_2014(self):
        response = self.client.get('/admin/prospectos/compra/reportes/')
        self.assertEqual(response.status_code, 200)
        contenido_de_la_respuesta = response.content.decode('utf-8')
        self.assertIn('<option value="2014">2014</option>', contenido_de_la_respuesta)
        self.assertIn('<option value="2015">2015</option>', contenido_de_la_respuesta)

    def test_errores_de_formulario(self):
        response = self.client.post('/admin/prospectos/compra/reportes/')
        contenido_de_la_respuesta = response.content.decode('utf-8')
        self.assertInHTML("<li>Este campo es obligatorio.</li>", contenido_de_la_respuesta, 2)
        prov = ProveedoresFactory(source_id='P1')
        response = self.client.post('/admin/prospectos/compra/reportes/', {'anio': 2010, 'mes': 13})
        contenido_de_la_respuesta = response.content.decode('utf-8')
        error_anio = "<li>Seleccione una opción válida. 2010 no es una de las opciones disponibles.</li>"
        self.assertInHTML(error_anio, contenido_de_la_respuesta)
        error_mes = "<li>Seleccione una opción válida. 13 no es una de las opciones disponibles.</li>"
        self.assertInHTML(error_mes, contenido_de_la_respuesta)

        response = self.client.post('/admin/prospectos/compra/reportes/', {'anio': 2014, 'mes': 10})
        contenido_de_la_respuesta = response.content.decode('utf-8')
        error_anio = "<li>Seleccione una opción válida. 2014 no es una de las opciones disponibles.</li>"
        self.assertInHTML(error_anio, contenido_de_la_respuesta, 0)
        error_mes = "<li>Seleccione una opción válida. 10 no es una de las opciones disponibles.</li>"
        self.assertInHTML(error_mes, contenido_de_la_respuesta, 0)
        

class ReporteDeComprasTest(BaseFixturedTest):

    def setUp(self):
        super(ReporteDeComprasTest, self).setUp()
        self._vendedor = self.fixture['vend_1']
        self._anio = 2011
        self._mes = 6

    def assert_valores_completos_y_vacios(self, datos):
        self.assertTrue('cantidad_de_prospectos' in datos)
        self.assertEqual(datos['cantidad_de_prospectos'], 0)
        self.assertTrue('ranking'in datos)
        self.assertEqual(datos['ranking'], '-/-')
        self.assertTrue('ventas' in datos)
        self.assertEqual(datos['ventas'], 0)
        self.assertTrue('monto' in datos)
        self.assertEqual(datos['monto'], 0)
        self.assertTrue('costo_por_dato' in datos)
        self.assertEqual(datos['costo_por_dato'], None)
        self.assertTrue('costo_por_venta' in datos)
        self.assertEqual(datos['costo_por_venta'], None)
        self.assertTrue('precio_venta_promedio' in datos)
        self.assertEqual(datos['precio_venta_promedio'], None)

    def crear_prospecto(self, campania, proveedor, vendedor=None):
        if vendedor:
            responsable = vendedor.responsable()
        else:
            responsable = None

        p = ProspectosFactory(campania=campania, proveedor=proveedor, vendedor=vendedor, responsable=responsable)
        p.fecha_creacion = parse_datetime('2011-06-15 12:00+03:00')
        p.save()
        return p

    def cambiar_valor_de_campania(self, campania, valor):
        c = self.fixture[campania]
        c.categoria.valor = valor
        c.categoria.save()
        return c

    def _crear_compra(self, anio, mes, prov):
        ComprasFactory(proveedor=prov, anio=anio, mes=mes, monto=100)

    def test_datos_totales_sin_compras_debe_indicar_total_en_cero(self):
        hoy = timezone.now()
        reporte = ReporteDeCompras(hoy.year, hoy.month)
        self.assertEqual(len(reporte.datos_por_compra()), 0)
        totales = reporte.totales()
        self.assert_valores_completos_y_vacios(totales)
        self.assertTrue('total_comprado' in totales)
        self.assertEqual(totales['total_comprado'], 0)

    def test_datos_de_compra_sin_prospectos_debe_ser_vacio(self):
        prov = ProveedoresFactory(source_id='P1')
        self._crear_compra(self._anio, self._mes, prov)
        reporte = ReporteDeCompras(self._anio, self._mes)
        datos_por_compra = reporte.datos_por_compra()
        self.assertEqual(0, len(datos_por_compra))

    def test_datos_de_compra_con_un_prospecto_sin_asignar_debe_ser_vacio(self):
        prov = ProveedoresFactory(source_id='P1')
        self._crear_compra(self._anio, self._mes, prov)
        self.crear_prospecto(campania=self.fixture['camp_1'], proveedor=prov)

        reporte = ReporteDeCompras(self._anio, self._mes)
        datos_por_compra = reporte.datos_por_compra()
        self.assertEqual(0, len(datos_por_compra))

    def test__datos_de_compra_con_un_prospecto_asignado_se_indica_la_compra(self):
        prov = ProveedoresFactory(source_id='P1')
        compra = ComprasFactory(proveedor=prov, anio=self._anio, mes=self._mes, monto=100)
        self.crear_prospecto(campania=self.fixture['camp_1'], proveedor=prov, vendedor=self._vendedor)

        reporte = ReporteDeCompras(self._anio, self._mes)
        datos_por_compra = reporte.datos_por_compra()

        self.assertEqual(1, len(datos_por_compra))
        self.assertTrue('cantidad_de_prospectos' in datos_por_compra[0])
        self.assertEqual(datos_por_compra[0]['cantidad_de_prospectos'], 1)
        self.assertTrue('total_comprado' in datos_por_compra[0])
        self.assertEqual(datos_por_compra[0]['compra'], compra)
        self.assertEqual(datos_por_compra[0]['total_comprado'], compra.monto)

    def test_reporte_con_compras(self):
        compra1, compra2 = self._crear_escenario()

        reporte = ReporteDeCompras(self._anio, self._mes)
        datos_por_compra = reporte.datos_por_compra()
        # Todos los proveedores entran al reporte ahora.
        self.assertEqual(2, len(datos_por_compra))
        if datos_por_compra[0]['compra'] == compra1:
            datos_compra_1 = datos_por_compra[0]
            datos_compra_2 = datos_por_compra[1]
        else:
            datos_compra_1 = datos_por_compra[1]
            datos_compra_2 = datos_por_compra[0]

        self._assert_datos_por_compras_para_compra_uno(datos_compra_1)
        self._assert_datos_por_compras_para_compra_dos(datos_compra_2)
        totales = reporte.totales()
        self._assert_reporte_totales(totales)

    def _assert_reporte_totales(self, totales):
        # TODO: pendiende seguir el refactor
        self.assertEqual(totales['cantidad_de_prospectos'], 9)
        self.assertEqual(totales['ranking'], '1/3')
        self.assertEqual(totales['ventas'], 3)
        self.assertEqual(totales['monto'], 30)
        self.assertEqual(totales['total_comprado'], 200)
        self.assertEqual(totales['costo_por_dato'], 200 / 9)
        self.assertEqual(totales['costo_por_venta'], 200 / 3)
        self.assertEqual(totales['precio_venta_promedio'], self._mes / 3)

    def _assert_datos_por_compras_para_compra_dos(self, datos_compra_2):
        # TODO: pendiende seguir el refactor
        self.assertEqual(datos_compra_2['cantidad_de_prospectos'], self._mes)
        self.assertEqual(datos_compra_2['ranking'], '1/3')
        self.assertEqual(datos_compra_2['ventas'], 2)
        self.assertEqual(datos_compra_2['monto'], 20)
        self.assertEqual(datos_compra_2['total_comprado'], 100)
        self.assertEqual(datos_compra_2['costo_por_dato'], 100 / self._mes)
        self.assertEqual(datos_compra_2['costo_por_venta'], 100 / 2)
        self.assertEqual(datos_compra_2['precio_venta_promedio'], 4 / 2)

    def _assert_datos_por_compras_para_compra_uno(self, datos_compra_1):
        # TODO: pendiende seguir el refactor
        self.assertEqual(datos_compra_1['cantidad_de_prospectos'], 3)
        self.assertEqual(datos_compra_1['ranking'], '1/3')
        self.assertEqual(datos_compra_1['ventas'], 1)
        self.assertEqual(datos_compra_1['monto'], 10)
        self.assertEqual(datos_compra_1['total_comprado'], 100)
        self.assertEqual(datos_compra_1['costo_por_dato'], 100 / 3)
        self.assertEqual(datos_compra_1['costo_por_venta'], 100 / 1)
        self.assertEqual(datos_compra_1['precio_venta_promedio'], 2 / 1)

    def _crear_escenario(self):
        """
            Crea tres compras para los proveedores 1, 2 y 3.

              El proveedor 1 tiene 3 prospectos asignados y 1 sin asignar
              El proveedor 2 tiene 6 prospectos asignados, dos de ellos vendidos

        """
        prov1 = ProveedoresFactory(source_id='P1')
        prov2 = ProveedoresFactory(source_id='P2')
        prov3 = ProveedoresFactory(source_id='P3')
        compra1 = ComprasFactory(proveedor=prov1, anio=self._anio, mes=self._mes, monto=100)
        compra2 = ComprasFactory(proveedor=prov2, anio=self._anio, mes=self._mes, monto=100)
        compra3 = ComprasFactory(proveedor=prov3, anio=self._anio, mes=5, monto=100)
        campania_de_valor_cinco = self.cambiar_valor_de_campania('camp_1', 5)
        campania_de_valor_tres = self.cambiar_valor_de_campania('camp_2', 3)
        campania_de_valor_dos = self.cambiar_valor_de_campania('camp_3', 2)
        # Prospecto sin asignar no es tenido en cuenta en el reporte
        self.crear_prospecto(campania=campania_de_valor_cinco, proveedor=prov1)
        p1 = self.crear_prospecto(campania=campania_de_valor_cinco, proveedor=prov1, vendedor=self._vendedor)
        p2 = self.crear_prospecto(campania=campania_de_valor_tres, proveedor=prov1, vendedor=self._vendedor)
        p3 = self.crear_prospecto(campania=campania_de_valor_dos, proveedor=prov1, vendedor=self._vendedor)
        VentasFactory(prospecto=p1, vendedor=self._vendedor, fecha_de_realizacion=timezone.now(), precio=2)
        p4 = self.crear_prospecto(campania=campania_de_valor_cinco, proveedor=prov2, vendedor=self._vendedor)
        p5 = self.crear_prospecto(campania=campania_de_valor_tres, proveedor=prov2, vendedor=self._vendedor)
        p6 = self.crear_prospecto(campania=campania_de_valor_dos, proveedor=prov2, vendedor=self._vendedor)
        p7 = self.crear_prospecto(campania=campania_de_valor_cinco, proveedor=prov2, vendedor=self._vendedor)
        p8 = self.crear_prospecto(campania=campania_de_valor_tres, proveedor=prov2, vendedor=self._vendedor)
        p9 = self.crear_prospecto(campania=campania_de_valor_dos, proveedor=prov2, vendedor=self._vendedor)
        VentasFactory(prospecto=p4, vendedor=self._vendedor, fecha_de_realizacion=timezone.now(), precio=2)
        VentasFactory(prospecto=p7, vendedor=self._vendedor, fecha_de_realizacion=timezone.now(), precio=2)
        return compra1, compra2
