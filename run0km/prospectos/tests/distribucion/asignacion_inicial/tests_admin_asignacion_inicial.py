# -*- coding: utf-8 -*-

from prospectos.models import <PERSON>o, <PERSON><PERSON>zo
from prospectos.models.entrega_de_datos.opciones import ModoSeleccionDeVendedorChoices, \
    TipoDeSeleccionParaAsignacionChoices, AccionesDeAsignacionDeVendedorChoices, MetodosDeAsignacionChoices, \
    RestriccionesParaAsignacionChoices
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest
from testing.base import BaseLoggedAdminTest, loadResponseJsonAndCheckStatus
from testing.factories import ProspectosFactory


class AdminAsignacionInicialTest(PedidosTest):
    def setUp(self):
        super(AdminAsignacionInicialTest, self).setUp()
        self.user = self.fixture['usr_admin']
        login_correct = self.client.login(username=self.user.username, password='admin')
        self.assertTrue(login_correct)
        self.asignar_url = '/admin/prospectos/prospecto/asignacion_inicial/'
        self.cantidad_url = '/admin/prospectos/prospecto/cantidad_filtrada/'

        Prospecto.objects.all().delete()
        ProspectosFactory(campania=self.fixture['camp_2'], nombre='a4', marca='a', provincia='c', prefijo='00002')
        ProspectosFactory(campania=self.fixture['camp_2'], nombre='a5', marca='a', provincia='c', prefijo='00003')
        ProspectosFactory(campania=self.fixture['camp_3'], nombre='a6', marca='b', provincia='b', prefijo='00003')

    def _prospectos_de_campania_uno(self, cantidad=3):
        faltantes = 0
        if cantidad >= 3:
            faltantes = 2
            self._prospectos_nuevos(cantidad=1, campania=self.campania_uno, marca='b', provincia='a', prefijo='00001',
                                    email='<EMAIL>')
            self._prospectos_nuevos(cantidad=1, campania=self.campania_uno, marca='c', provincia='a', prefijo='00001',
                                    email='<EMAIL>')
        self._prospectos_nuevos(cantidad=cantidad - faltantes, campania=self.campania_uno,
                                marca='a', provincia='a', prefijo='00001', email='<EMAIL>')
        return Prospecto.objects.filter(campania=self.campania_uno)

    def _post_data_asignacion_inicial(self, supervisores_ids=None, cantidad=1, campanias_pk_list=None, asignar_a=None,
                                      asignar_segun=None, vendedor_id=None, equipo_id=None, accion=None, metodo=None,
                                      restricciones=None, aplicar_restricciones_del_pedido=False,
                                      descontar_a_pedidos=False, pedidos=None,
                                      metodo_por_productividad=False):
        supervisores_ids = supervisores_ids or [self.fixture['sup_1'].id]
        asignar_a = asignar_a or ModoSeleccionDeVendedorChoices.TODOS
        asignar_segun = asignar_segun or TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES
        vendedor_id = vendedor_id or ''
        accion = accion or AccionesDeAsignacionDeVendedorChoices.ASIGNAR
        metodo = metodo or MetodosDeAsignacionChoices.UNIFORME
        pedidos = pedidos or []
        campanias_pk_list = campanias_pk_list or [self.fixture['camp_1'].pk, self.fixture['camp_2'].pk,
                                                  self.fixture['camp_3'].pk]
        equipo_id = equipo_id or ''
        restricciones = restricciones or []

        data = {
            'accion': accion,
            'responsables': supervisores_ids,
            'asignar_a': asignar_a,
            'asignar_segun': asignar_segun,
            'vendedor': vendedor_id,
            'equipo': equipo_id,
            'metodo': metodo,
            'cantidad': cantidad,
            'campanias': campanias_pk_list,
            'marcas': list(Prospecto.objects.marcas().codigos()),
            'provincias': [each.upper() for each in Prospecto.objects.provincias(Prospecto.objects.all())],
            'prefijos': Prospecto.objects.prefijos(Prospecto.objects.all()),
            'descontar_a_pedidos': descontar_a_pedidos,
            'pedidos': pedidos,
            'restricciones': restricciones,
            'aplicar_restricciones_del_pedido': aplicar_restricciones_del_pedido,
            'metodo_por_productividad': metodo_por_productividad
        }
        return data

    def test_get_asignacion_inicial(self):
        data = {}
        response = self.client.get(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)

    def test_asignacion_directa_a_vendedor_de_otro_supervisor_debe_responder_error(self):
        otro_supervisor = self._crear_supervisor(cantidad_de_vendedores=1)
        vendedor_de_otro_supervisor = otro_supervisor.vendedores.first()
        data = self._post_data_asignacion_inicial(supervisores_ids=[self.supervisor.id],
                                                  asignar_a=ModoSeleccionDeVendedorChoices.VENDEDOR,
                                                  vendedor_id=vendedor_de_otro_supervisor.pk,
                                                  cantidad=2)
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('Debe indicar un vendedor que este a cargo del Supervisor Responsable.',
                      response.content.decode('utf-8'))
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), 0)
        self.assertEqual(vendedor_de_otro_supervisor.prospectos.count(), 0)

    def test_asignacion_directa_a_vendedor_deshabilitado_debe_responder_error(self):
        self.vendedor_uno.deshabilitar()
        data = self._post_data_asignacion_inicial(supervisores_ids=[self.supervisor.id],
                                                  asignar_a=ModoSeleccionDeVendedorChoices.VENDEDOR,
                                                  vendedor_id=self.vendedor_uno.pk,
                                                  cantidad=2)
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Por favor corrija los errores detallados abajo.')
        self.assertContains(response,
                            'Seleccione una opción válida. La opción seleccionada no es una de las disponibles.')
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), 0)
        self.assertEqual(self.vendedor_uno.prospectos.count(), 0)

    def test_asignacion_directa_a_vendedor_con_factor_cero_no_ser_asignado(self):
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=self.vendedor_uno, factor_de_asignacion=0)
        data = self._post_data_asignacion_inicial(
            supervisores_ids=[self.supervisor.id],
            asignar_a=ModoSeleccionDeVendedorChoices.VENDEDOR,
            vendedor_id=self.vendedor_uno.pk,
            cantidad=2)
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), 0)
        self.assertEqual(self.vendedor_uno.prospectos.count(), 0)

    def test_asignacion_directa_a_vendedor_habilitado_debe_asignar_prospectos_y_enviar_mail_de_notificacion(self):
        prospectos = self._prospectos_de_campania_uno(cantidad=6)
        data = self._post_data_asignacion_inicial(supervisores_ids=[self.supervisor.id],
                                                  asignar_a=ModoSeleccionDeVendedorChoices.VENDEDOR,
                                                  vendedor_id=self.vendedor_uno.pk,
                                                  cantidad=2)

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=2)

        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), 2)
        self.assertEqual(self.vendedor_uno.prospectos.count(), 2)
        self._assert_prospectos_sin_descuento_de_credito(prospectos)
        # self.assertIn('Se han puesto 2 prospectos a cargo del supervisor %s' % supervisor.user.get_full_name(),
        #               response.content)
        # self.assertIn('Se reasignaron 2 prospectos al usuario %s' %
        #               str(vendedor_de_supervisor.full_name()), response.content)

        # self.assertEqual(len(mail.outbox), 2)
        # email = mail.outbox[0] if mail.outbox[0].to == [vendedor_de_supervisor.user.email] else mail.outbox[1]
        # self.assertEqual(email.subject, 'Asignación de prospectos.')
        # self.assertEqual(email.to, [])
        # self.assertEqual(email.from_email, supervisor.concesionaria.email_de_origen_para_notificaciones())
        # self.assertEqual(email.bcc, [vendedor_de_supervisor.user.email])
        # self.assertEqual(email.body, 'Usted tiene nuevos prospectos asignados.')

    # def test_cantidad(self):
    #     data = {}
    #     response = self.client.get(self.cantidad_url, data)
    #     result = loadResponseJsonAndCheckStatus(self, response)
    #     self.assertEqual(result['total_prospectos'], 0)

    def test_asignacion_directa_a_vendedor_descontando_a_pedido_debe_asignar_prospectos_y_actualizar_consumo(self):
        Prospecto.objects.all().delete()
        cantidad = 6
        prospectos = self._prospectos_de_campania_uno(cantidad=cantidad)
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=0)
        data = self._post_data_asignacion_inicial(
            supervisores_ids=[self.supervisor.id],
            asignar_a=ModoSeleccionDeVendedorChoices.VENDEDOR,
            vendedor_id=self.vendedor_uno.pk,
            cantidad=cantidad,
            descontar_a_pedidos=True,
            pedidos=[pedido_uno.pk]
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self._assert_se_informa_descuento_a(response, pedido_uno, prospectos)

        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), cantidad)
        self.assertEqual(self.vendedor_uno.prospectos.count(), cantidad)
        self._assert_fue_registrado_consumo_para(pedido_uno, prospectos)

    def test_asignacion_directa_a_equipo_de_otro_supervisor_debe_responder_error(self):
        otro_supervisor = self._crear_supervisor()
        equipo_de_otro_supervisor = self._crear_equipo_para(otro_supervisor, integrantes=[])

        data = self._post_data_asignacion_inicial(supervisores_ids=[self.supervisor.id],
                                                  asignar_a=ModoSeleccionDeVendedorChoices.EQUIPO,
                                                  cantidad=6,
                                                  equipo_id=equipo_de_otro_supervisor.id)

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Por favor corrija los errores detallados abajo.')
        self.assertContains(response,
                            'El equipo debe corresponder al Supervisor Responsable.')

    def test_asignacion_directa_a_equipo_de_supervisor_debe_asignar_prospectos(self):
        cantidad = 6
        self._prospectos_de_campania_uno(cantidad=cantidad)
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_uno, self.vendedor_dos])

        data = self._post_data_asignacion_inicial(supervisores_ids=[self.supervisor.id],
                                                  asignar_a=ModoSeleccionDeVendedorChoices.EQUIPO,
                                                  metodo=MetodosDeAsignacionChoices.UNIFORME,
                                                  cantidad=cantidad,
                                                  equipo_id=equipo.id)

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), cantidad)
        self.assertEqual(self.vendedor_uno.prospectos.count(), 3)
        self.assertEqual(self.vendedor_dos.prospectos.count(), 3)
        # self.assertIn('Se han puesto 6 prospectos a cargo del supervisor %s' % sup.user.get_full_name(),
        #               response.content)
        # self.assertIn('Se reasignaron 6 prospectos', response.content)
        #
        # self.assertEqual(len(mail.outbox), 2)
        # email = mail.outbox[0] if mail.outbox[1].to == [supervisor.user.email] else mail.outbox[1]
        # self.assertEqual(email.subject, 'Asignación de prospectos.')
        # self.assertEqual(email.to, [])
        # self.assertEqual(email.from_email, 'Run0km <%s>' % settings.DEFAULT_FROM_EMAIL)
        # self.assertEqual(set(email.bcc),  {vendedor_uno_de_supervisor.user.email, vendedor_dos_de_supervisor.user.email})
        # self.assertEqual(email.body, 'Usted tiene nuevos prospectos asignados.')

    def test_asignacion_directa_a_equipo_metodo_factor_debe_asignar_prospectos(self):
        cantidad = 6
        self._prospectos_de_campania_uno(cantidad=cantidad)
        self.vendedor_uno.cambiar_factor_de_asignacion_por(factor_de_asignacion=20)
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_uno, self.vendedor_dos])
        data = self._post_data_asignacion_inicial(
            supervisores_ids=[self.supervisor.id],
            asignar_a=ModoSeleccionDeVendedorChoices.EQUIPO,
            equipo_id=equipo.id,
            metodo=MetodosDeAsignacionChoices.FACTOR_MANUAL,
            cantidad=cantidad)

        response = self.client.post(self.asignar_url, data)

        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), cantidad)
        self.assertEqual(self.vendedor_uno.prospectos.count(), 4)
        self.assertEqual(self.vendedor_dos.prospectos.count(), 2)

        # self.assertIn('Se han puesto 6 prospectos a cargo del supervisor %s' % supervisor.user.get_full_name(),
        #               response.content)
        # self.assertIn('Se reasignaron 6 prospectos', response.content)
        #
        # self.assertEqual(len(mail.outbox), 2)
        # email = mail.outbox[0] if mail.outbox[1].to == [supervisor.user.email] else mail.outbox[1]
        # self.assertEqual(email.subject, 'Asignación de prospectos.')
        # self.assertEqual(email.to, [])
        # self.assertEqual(email.from_email, supervisor.concesionaria.email_de_origen_para_notificaciones())
        # self.assertEqual(set(email.bcc), {vendedor_uno_de_supervisor.user.email, vendedor_dos_de_supervisor.user.email})
        # self.assertEqual(email.body, 'Usted tiene nuevos prospectos asignados.')

    def test_asignacion_directa_a_todos_los_vendedores(self):
        cantidad = self.supervisor.vendedores.count()
        self._prospectos_de_campania_uno(cantidad=cantidad)

        data = self._post_data_asignacion_inicial(
            supervisores_ids=[self.supervisor.id],
            asignar_a=ModoSeleccionDeVendedorChoices.TODOS,
            cantidad=cantidad)

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), cantidad)
        self._assert_vendedores_tienen(self.supervisor, cantidad_de_vendedores=cantidad, cantidad_de_prospectos=1)
        # TODO: falta verificar que se envian las notificaciones
        # for vend in self.supervisor.vendedores.all():
        #     self.assertEqual(vend.prospectos.count(), 1)
        # self.assertIn('Se han puesto %d prospectos a cargo del supervisor %s' % (cantidad, supervisor.user.get_full_name()),
        #               response.content)
        # self.assertIn('Se reasignaron %d prospectos' % cantidad, response.content)

        # self.assertEqual(len(mail.outbox), 2)
        # email = mail.outbox[0] if mail.outbox[1].to == [supervisor.user.email] else mail.outbox[1]
        # self.assertEqual(email.subject, 'Asignación de prospectos.')
        # self.assertEqual(email.to, [])
        # self.assertEqual(email.from_email, supervisor.concesionaria.email_de_origen_para_notificaciones())
        # emails = set(supervisor.vendedores.values_list('user__email', flat=True))
        # self.assertEqual(set(email.bcc), emails)
        # self.assertEqual(email.body, 'Usted tiene nuevos prospectos asignados.')

    def test_asignacion_directa_a_todos_vendedores_de_supervisor_con_factor_cero_no_debe_ser_asignado(self):
        for vendedor in self.supervisor.vendedores.all():
            self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=vendedor,
                                                                         factor_de_asignacion=0)
        cantidad = self.supervisor.vendedores.count()
        data = self._post_data_asignacion_inicial(
            supervisores_ids=[self.supervisor.id],
            cantidad=cantidad,
            asignar_a=ModoSeleccionDeVendedorChoices.TODOS,
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), 0)
        self._assert_no_tienen_prospectos(vendedores=self.supervisor.vendedores.all())

    def test_asignacion_directa_a_equipo_con_vendedores_con_factor_cero_no_debe_ser_asignado(self):
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_uno, self.vendedor_dos])
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=self.vendedor_uno,
                                                                     factor_de_asignacion=0)
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=self.vendedor_dos,
                                                                     factor_de_asignacion=0)
        data = self._post_data_asignacion_inicial(
            supervisores_ids=[self.supervisor.id],
            asignar_a=ModoSeleccionDeVendedorChoices.EQUIPO,
            equipo_id=equipo.id,
            cantidad=6)
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=0)
        self._assert_no_tienen_prospectos(vendedores=equipo.obtener_integrantes())

    def _assert_no_tienen_prospectos(self, vendedores):
        for vendedor in vendedores:
            self.assertEqual(vendedor.prospectos.count(), 0)

    def test_poner_a_cargo_a_supervisor_deshabilitado_debe_responder_error(self):
        supervisor = self.fixture['sup_1']
        supervisor.deshabilitar()
        campanias_pk_list = [self.fixture['camp_1'].pk, self.fixture['camp_2'].pk]
        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[supervisor.id],
            cantidad=3,
            campanias_pk_list=campanias_pk_list)

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Por favor corrija los errores detallados abajo.')
        self.assertContains(response,
                            'Seleccione una opción válida. %s no es una de las opciones disponibles.' % supervisor.id)

    def test_poner_a_cargo_via_pedidos_con_pedido_finalizado_no_de_poner_a_cargo(self):
        prospectos = self._prospectos_de_campania_uno(cantidad=6)
        cantidad = prospectos.count()
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=0)
        pedido.finalizado = True
        pedido.save()
        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk]
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_prospectos_no_asignados(prospectos, cantidad=cantidad)

    def test_poner_a_cargo_via_pedidos_con_credito_superado_no_de_poner_a_cargo(self):
        prospectos = self._prospectos_de_campania_uno(cantidad=6)
        cantidad = prospectos.count()
        self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno,
            credito=100, consumido=105, yapa=5)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk]
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_prospectos_no_asignados(prospectos, cantidad=cantidad)

    def test_poner_a_cargo_via_pedidos_con_unico_pedidos_sin_restricciones_debe_poner_a_cargo_al_supervisor(self):
        prospectos = self._prospectos_de_campania_uno()
        cantidad = prospectos.count()
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk]
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=cantidad)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido, consumo_esperado=0)

    def test_poner_a_cargo_via_pedidos_con_unico_pedido_para_poner_a_cargo_sin_restricciones_debe_poner_a_cargo_al_supervisor(
            self):
        prospectos = self._prospectos_de_campania_uno()
        cantidad = prospectos.count()
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=0)

        supervisor_dos = self._crear_supervisor()
        pedido_dos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor_dos, campania=self.campania_uno, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id, supervisor_dos.id],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk]
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=0)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_uno, consumo_esperado=0)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_dos, consumo_esperado=0)

    def test_poner_a_cargo_via_pedidos_sin_restricciones_debe_priorizar_pedidos_con_menos_credito_consumidos(self):
        prospectos = self._prospectos_de_campania_uno()
        cantidad = prospectos.count()
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=100)

        supervisor_dos = self._crear_supervisor()
        self._configurar_ultima_actividad_excedida_a(supervisor_dos)

        pedido_dos = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_dos, campania=self.campania_uno, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id, supervisor_dos.id],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk],
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=0)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=cantidad)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_uno, consumo_esperado=100)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_dos, consumo_esperado=0)

    def test_poner_a_cargo_via_pedidos_con_restriccion_de_acceso_debe_poner_a_cargo_al_supervisor_que_las_cumpla(self):
        """
            Los pedidos son priorizados por la cantidad de credito que le falta consumir, en este caso
            [pedidos_dos, pedido_uno]

        """
        prospectos = self._prospectos_de_campania_uno()
        cantidad = prospectos.count()
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno,
            credito=1000, consumido=100)

        supervisor_dos = self._crear_supervisor()
        self._configurar_ultima_actividad_excedida_a(supervisor_dos)

        pedido_dos = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_dos, campania=self.campania_uno,
            credito=1000, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id, supervisor_dos.id],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk],
            restricciones=[RestriccionesParaAsignacionChoices.ACCESO]
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=0)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_uno, consumo_esperado=100)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_dos, consumo_esperado=0)

    def test_poner_a_cargo_via_pedidos_descontando_credito_debe_poner_a_cargo_al_supervisor_y_consumir_credito_del_pedido(
            self):
        prospectos = self._prospectos_de_campania_uno()
        cantidad = prospectos.count()
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=0)

        supervisor_dos = self._crear_supervisor()
        pedido_dos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor_dos, campania=self.campania_uno, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id, supervisor_dos.id],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=True
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=cantidad)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=0)
        self._assert_fue_registrado_consumo_para(pedido_uno, prospectos)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_dos, consumo_esperado=0)

    def test_poner_a_cargo_via_supervisores_sin_supervisores_no_deberia_ponerse_a_cargo(self):
        prospectos = self._prospectos_nuevos(cantidad=6)
        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            campanias_pk_list=[self.campania_uno.pk]
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_prospectos_no_asignados(prospectos, cantidad=6)

    def test_poner_a_cargo_via_supervisores_con_distribucion_uniforme_sin_restricciones(self):
        """
            Dado 2 prospectos y 2 supervisores, debe poner a cargo un prospecto para cada supervisor
        """
        prospectos = self._prospectos_de_campania_uno(cantidad=2)
        supervisor_dos = self._crear_supervisor()

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=1)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=1)

        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=1)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=1)

    def test_poner_a_cargo_via_supervisores_con_distribucion_uniforme_con_restriccion_de_acceso(self):
        """
            Dado 4 prospectos y 3 supervisores S1, S2 y S3 con restriccion de acceso que no cumple S2
              debe poner a cargo 2 prospecto a S1, 0 a S2 y 2 a S3
        """
        prospectos = self._prospectos_de_campania_uno(cantidad=4)
        supervisor_dos = self._crear_supervisor()
        supervisor_tres = self._crear_supervisor()
        self._configurar_ultima_actividad_excedida_a(supervisor_dos)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id, supervisor_dos.id, supervisor_tres.id],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            restricciones=[RestriccionesParaAsignacionChoices.ACCESO]
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=2)
        self._assert_se_informa_asignacion_de(response, supervisor_tres, cantidad=2)

        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=2)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=0)
        self._assert_tiene_prospectos_a_cargo(supervisor_tres, cantidad=2)

    def test_poner_a_cargo_via_supervisores_con_descuento_de_credito_sin_pedido_debe_poner_a_cargo_sin_descontar_credito(
            self):
        """
            Dado 2 prospectos y 2 supervisores, debe poner a cargo un prospecto para cada supervisor
        """
        prospectos = self._prospectos_de_campania_uno(cantidad=2)
        supervisor_dos = self._crear_supervisor()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor_dos, campania=self.campania_uno, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=True
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=1)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=1)

        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=1)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=1)
        self._assert_prospectos_sin_descuento_de_credito(prospectos)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido, consumo_esperado=0)

    def test_poner_a_cargo_via_supervisores_con_descuento_de_credito_debe_poner_a_cargo_y_descontar_credito(self):
        """
            Dado 2 prospectos y 2 supervisores, debe poner a cargo un prospecto para cada supervisor
        """
        prospectos = self._prospectos_de_campania_uno(cantidad=2)
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=0)

        supervisor_dos = self._crear_supervisor()
        pedido_dos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor_dos, campania=self.campania_uno, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=True
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=1)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=1)

        self._assert_tiene_prospectos_a_cargo(self.supervisor, cantidad=1)
        self._assert_tiene_prospectos_a_cargo(supervisor_dos, cantidad=1)
        self._assert_fue_registrado_consumo_para(pedido_uno, self.supervisor.prospectos_a_cargo.all())
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_dos, consumo_esperado=0)

    def test_poner_a_cargo_debe_poner_prospecto_a_cargo_de_supervisor(self):
        cantidad = 3
        self._prospectos_de_campania_uno(cantidad=cantidad)
        campanias_pk_list = [self.fixture['camp_1'].pk, self.fixture['camp_2'].pk]
        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.RESPONSABILIZAR,
            supervisores_ids=[self.supervisor.id],
            cantidad=cantidad,
            campanias_pk_list=campanias_pk_list)

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=cantidad)
        self.assertEqual(self.supervisor.prospectos_a_cargo.count(), cantidad)
        # self.assertIn('Se han puesto 3 prospectos a cargo del supervisor %s' % supervisor.user.get_full_name(),
        #               response.content)
        #
        # self.assertEqual(len(mail.outbox), 1)
        # email = mail.outbox[0]
        # self.assertEqual(email.subject, 'Designación de prospectos.')
        # self.assertEqual(email.to, [supervisor.user.email])
        # self.assertEqual(email.from_email, supervisor.concesionaria.email_de_origen_para_notificaciones())
        # self.assertEqual(email.bcc, [])
        # self.assertEqual(email.body, 'Se le han designado 3 nuevos prospectos a su cargo.')
        #
        # data = {}
        # response = self.client.get(self.cantidad_url, data)
        # result = loadResponseJsonAndCheckStatus(self, response)
        # self.assertEqual(result['total_prospectos'], 0)

    # def test_registrar_asignacion_con_pedido(self):
    #     self.fixture['camp_1'].categoria.valor = 2
    #     self.fixture['camp_1'].categoria.save()
    #     pedido = PedidosDeProspectoFactory(supervisor=self.fixture['sup_1'], credito=10, yapa=5, consumido=2,
    #                                        asignar_a='T', fecha=now().date())
    #     pedido.origenes = [self.fixture['tipo_s'], ]
    #
    #     data = {'accion': 'asignar', 'responsables': [self.fixture['sup_2'].id, ], 'asignar_a': 'T', 'vendedor': '',
    #             'equipo': '', 'metodo': 'uniforme', 'cantidad': 6, 'pedido': pedido.id,
    #             'campanias': [self.fixture['camp_1'].pk, self.fixture['camp_2'].pk,
    #                           self.fixture['camp_3'].pk],
    #             'marcas': ['A', 'B', 'C'],
    #             'provincias': ['A', 'B', 'C'], 'prefijos': ['00001', '00002', '00003']}
    #
    #     data = self._post_data_asignacion_inicial(supervisor_id=self.fixture['sup_2'].id, cantidad=6,
    #                                               asignar_a=ModoSeleccionDeVendedorChoices.TODOS,
    #                                               pedido_id=pedido.id)
    #     response = self.client.post(self.asignar_url, data)
    #     self.assertEqual(response.status_code, 200)
    #     self.assertIn('Debe elegir Pedidos asignados al Supervisor Responsable', response.content)
    #
    #     data['responsables'] = [self.fixture['sup_1'].id, ]
    #     response = self.client.post(self.asignar_url, data)
    #     self.assertEqual(response.status_code, 200)
    #     pedido = PedidoDeProspecto.objects.get(id=pedido.id)
    #     self.assertEqual(pedido.consumido, 11)
    #     self.assertEqual(pedido.prospectos_asignados.count(), 6)

    def test_asignar_vendedor_via_supervisores_con_seleccion_uniforme_de_vendedor_sin_restricciones(self):
        prospectos = self._prospectos_de_campania_uno(cantidad=4)
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=1)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=False
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=2)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=2)

        self._assert_vendedores_tienen(self.supervisor, cantidad_de_vendedores=2, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(self.supervisor, cantidad_de_vendedores=1, cantidad_de_prospectos=0)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=1, cantidad_de_prospectos=2)

    def test_asignar_vendedor_via_supervisores_con_seleccion_uniforme_de_vendedor_con_restricciones(self):
        prospectos = self._prospectos_de_campania_uno(cantidad=6)
        supervisor_dos = self._crear_supervisor()
        self._agregar_vendedor_a_(supervisor_dos, limite_de_datos_nuevos=1)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=False,
            restricciones=[RestriccionesParaAsignacionChoices.NUEVOS]
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=5)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=1)

        self._assert_vendedores_tienen(self.supervisor, cantidad_de_vendedores=2, cantidad_de_prospectos=2)
        self._assert_vendedores_tienen(self.supervisor, cantidad_de_vendedores=1, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=1, cantidad_de_prospectos=1)

    def test_asignar_vendedor_via_supervisores_con_descuento_de_credito_debe_asignar_y_descontar_credito(self):
        prospectos = self._prospectos_de_campania_uno(cantidad=2)
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor, campania=self.campania_uno, consumido=0)

        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=1)
        pedido_dos = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor_dos, campania=self.campania_uno, consumido=0)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=prospectos.count(),
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=True,
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=1)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=1)
        self._assert_se_informa_descuento_a(response, pedido_dos, supervisor_dos.prospectos_a_cargo.all())

        self._assert_vendedores_tienen(self.supervisor, cantidad_de_vendedores=1, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=1, cantidad_de_prospectos=1)
        self._assert_prospectos_no_descontados_en_pedido(prospectos, pedido_uno, consumo_esperado=0)
        self._assert_fue_registrado_consumo_para(pedido_dos, supervisor_dos.prospectos_a_cargo.all())

    def test_asignar_vendedor_via_supervisores_con_seleccion_por_productividad_de_vendedor_sin_restricciones(self):
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_uno,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=3,
                                                                       ventas_semanales=1,
                                                                       tiempo_de_respuesta_semanal=80,
                                                                       tiempo_de_respuesta_mensual=110)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_dos,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=70,
                                                                       tiempo_de_respuesta_mensual=120)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_tres,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=60,
                                                                       tiempo_de_respuesta_mensual=60)

        cantidad = 5
        self._prospectos_de_campania_uno(cantidad=cantidad)
        cantidad_de_uno = self.vendedor_uno.prospectos.count()
        cantidad_de_dos = self.vendedor_dos.prospectos.count()
        cantidad_de_tres = self.vendedor_tres.prospectos.count()

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=False,
            metodo_por_productividad=True
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad)
        self._assert_vendedor_tiene(self.vendedor_tres, cantidad_de_prospectos=2 + cantidad_de_tres)
        self._assert_vendedor_tiene(self.vendedor_dos, cantidad_de_prospectos=2 + cantidad_de_dos)
        self._assert_vendedor_tiene(self.vendedor_uno, cantidad_de_prospectos=1 + cantidad_de_uno)

    def test_asignar_vendedor_via_supervisores_con_varios_supervisores_y_seleccion_por_productividad_de_vendedor(self):
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_uno,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=3,
                                                                       ventas_semanales=1,
                                                                       tiempo_de_respuesta_semanal=80,
                                                                       tiempo_de_respuesta_mensual=110)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_dos,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=70,
                                                                       tiempo_de_respuesta_mensual=120)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_tres,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=60,
                                                                       tiempo_de_respuesta_mensual=60)

        cantidad = 4
        self._prospectos_de_campania_uno(cantidad=cantidad)
        cantidad_de_uno = self.vendedor_uno.prospectos.count()
        cantidad_de_dos = self.vendedor_dos.prospectos.count()
        cantidad_de_tres = self.vendedor_tres.prospectos.count()
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=1)
        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_SUPERVISORES,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=False,
            metodo_por_productividad=True
        )

        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=2)
        self._assert_se_informa_asignacion_de(response, supervisor_dos, cantidad=2)

        self._assert_vendedor_tiene(self.vendedor_tres, cantidad_de_prospectos=1 + cantidad_de_tres)
        self._assert_vendedor_tiene(self.vendedor_dos, cantidad_de_prospectos=1 + cantidad_de_dos)
        self._assert_vendedor_tiene(self.vendedor_uno, cantidad_de_prospectos=cantidad_de_uno)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=1, cantidad_de_prospectos=2)

    def test_asignar_vendedor_via_pedidos_y_seleccion_por_productividad_de_vendedor(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor, campania=self.campania_uno,
            consumido=0, metodo_por_productividad=True)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_uno,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=3,
                                                                       ventas_semanales=1,
                                                                       tiempo_de_respuesta_semanal=80,
                                                                       tiempo_de_respuesta_mensual=110)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_dos,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=70,
                                                                       tiempo_de_respuesta_mensual=120)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_tres,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=60,
                                                                       tiempo_de_respuesta_mensual=60)
        cantidad = 2
        self._prospectos_de_campania_uno(cantidad=cantidad)
        cantidad_de_uno = self.vendedor_uno.prospectos.count()
        cantidad_de_dos = self.vendedor_dos.prospectos.count()
        cantidad_de_tres = self.vendedor_tres.prospectos.count()
        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=False
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=2)
        self._assert_vendedor_tiene(self.vendedor_tres, cantidad_de_prospectos=1 + cantidad_de_tres)
        self._assert_vendedor_tiene(self.vendedor_dos, cantidad_de_prospectos=1 + cantidad_de_dos)
        self._assert_vendedor_tiene(self.vendedor_uno, cantidad_de_prospectos=cantidad_de_uno)

    def test_asignar_vendedor_via_pedidos_forzando_seleccion_por_productividad_de_vendedor(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor, campania=self.campania_uno,
            consumido=0, metodo_por_productividad=False)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_uno,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=3,
                                                                       ventas_semanales=1,
                                                                       tiempo_de_respuesta_semanal=80,
                                                                       tiempo_de_respuesta_mensual=110)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_dos,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=70,
                                                                       tiempo_de_respuesta_mensual=120)
        self.creador_de_contexto.asignar_ventas_y_tiempos_de_respuesta(self.vendedor_tres,
                                                                       nuevos=1,
                                                                       ventas_hace_tres_semanas=1,
                                                                       ventas_semanales=2,
                                                                       tiempo_de_respuesta_semanal=60,
                                                                       tiempo_de_respuesta_mensual=60)
        cantidad = 2
        self._prospectos_de_campania_uno(cantidad=cantidad)
        cantidad_de_uno = self.vendedor_uno.prospectos.count()
        cantidad_de_dos = self.vendedor_dos.prospectos.count()
        cantidad_de_tres = self.vendedor_tres.prospectos.count()
        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            metodo=MetodosDeAsignacionChoices.UNIFORME,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=False,
            metodo_por_productividad=True
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad=2)
        self._assert_vendedor_tiene(self.vendedor_tres, cantidad_de_prospectos=1 + cantidad_de_tres)
        self._assert_vendedor_tiene(self.vendedor_dos, cantidad_de_prospectos=1 + cantidad_de_dos)
        self._assert_vendedor_tiene(self.vendedor_uno, cantidad_de_prospectos=cantidad_de_uno)

    def test_asignar_vendedor_via_pedidos_con_restriccion_tomadas_desde_el_pedido(self):
        Prospecto.objects.all().delete()
        cantidad = 1
        prospectos = self._prospectos_de_campania_uno(cantidad=cantidad)
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor,
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            restringir_por_datos_nuevos=True,
            consumido=1)
        self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[
            self.vendedor_uno, self.vendedor_dos, self.vendedor_tres],
            cantidad=2, nombre_de_marca='')
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=1)
        self._configurar_limite_de_datos_nuevos_en_sistema(2)
        self._configurtar_limite_de_datos_nuevos_para(self.vendedor_tres, 4)

        data = self._post_data_asignacion_inicial(
            accion=AccionesDeAsignacionDeVendedorChoices.ASIGNAR,
            supervisores_ids=[self.supervisor.pk, supervisor_dos.pk],
            cantidad=cantidad,
            asignar_segun=TipoDeSeleccionParaAsignacionChoices.VIA_PEDIDOS,
            campanias_pk_list=[self.campania_uno.pk],
            descontar_a_pedidos=False,
            metodo_por_productividad=False,
            aplicar_restricciones_del_pedido=True
        )
        response = self.client.post(self.asignar_url, data)
        self.assertEqual(response.status_code, 200)
        self._assert_se_informa_asignacion_de(response, self.supervisor, cantidad)

        prospecto = prospectos[0]
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto_id=prospecto.id,
                                                                 en_pedido_id=pedido.id,
                                                                 a_vendedor=self.vendedor_tres,
                                                                 a_responsable=self.supervisor,
                                                                 credito_consumido=1,
                                                                 validar_pedido=False)
        self.assertIsNone(prospecto.pedido)

    def _assert_se_informa_asignacion_de(self, response, supervisor, cantidad):
        contenido = response.content.decode('utf-8')
        self.assertInHTML('<td>%s</td>' % supervisor.obtener_concesionaria(), contenido)
        self.assertInHTML('<td>%s</td>' % supervisor, contenido)
        self.assertInHTML('<td>%s</td>' % cantidad, contenido)

    def _assert_se_informa_descuento_a(self, response, pedido, prospectos):
        consumo = self._consumo_de(prospectos)
        contenido = response.content.decode('utf-8')
        self.assertInHTML('<td>%s</td>' % pedido.nombre, contenido)
        self.assertInHTML('<td>%s</td>' % pedido.supervisor, contenido)
        self.assertInHTML('<td>%s</td>' % consumo, contenido)

    def _assert_prospectos_no_descontados_en_pedido(self, prospectos, pedido, consumo_esperado):
        self.assertEqual(pedido.consumido, consumo_esperado)
        for prospecto in prospectos:
            self.assertNotEqual(prospecto.pedido, pedido)


class AdminCantidadFiltradaView(BaseLoggedAdminTest):
    def setUp(self):
        super(AdminCantidadFiltradaView, self).setUp()
        self.asignar_url = '/admin/prospectos/prospecto/asignacion_inicial/'
        self.cantidad_url = '/admin/prospectos/prospecto/cantidad_filtrada/'

        Prospecto.objects.all().delete()

        ProspectosFactory(campania=self.fixture['camp_1'], nombre='a1', marca='a', provincia='a', prefijo='00001')
        ProspectosFactory(campania=self.fixture['camp_1'], nombre='a2', marca='b', provincia='b', prefijo='00002')
        ProspectosFactory(campania=self.fixture['camp_1'], nombre='a3', marca='c', provincia='c', prefijo='00002')
        ProspectosFactory(campania=self.fixture['camp_2'], nombre='a4', marca='a', provincia='c', prefijo='00002')
        ProspectosFactory(campania=self.fixture['camp_2'], nombre='a5', marca='a', provincia='c', prefijo='00003')
        ProspectosFactory(campania=self.fixture['camp_3'], nombre='a6', marca='b', provincia='b', prefijo='00003')

    def test_filtrar_por_origen_de_campania_uno(self):
        data = {'origen': self.fixture['camp_1'].categoria.tipo_de_origen.id, 'filtro_aplicado': 'origen'}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 3)

    def test_filtrar_por_origen_de_campania_dos(self):
        data = {'origen': self.fixture['camp_2'].categoria.tipo_de_origen.id, 'filtro_aplicado': 'origen'}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 2)

    def test_filtrar_por_categoria(self):
        data = {'categorias': [self.fixture['camp_1'].categoria.id, self.fixture['camp_2'].categoria.id],
                'filtro_aplicado': 'categorias'}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 5)

    def test_filtrar_por_marcas(self):
        data = {'categorias': [self.fixture['camp_1'].categoria.id, self.fixture['camp_2'].categoria.id],
                'marcas': ['a', 'b'], 'filtro_aplicado': 'marcas'}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 4)

    def test_filtrar_por_provincias(self):
        data = {'categorias': [self.fixture['camp_3'].categoria.id],
                'campanias': [self.fixture['camp_3'].id],
                'marcas': ['a', 'b'], 'provincias': ['A', 'B'], 'filtro_aplicado': 'provincias'}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 1)

    def test_filtrar_por_prefijos(self):
        data = {'categorias': [self.fixture['camp_1'].categoria.id, self.fixture['camp_2'].categoria.id],
                'campanias': [self.fixture['camp_1'].pk, self.fixture['camp_2'].pk],
                'marcas': ['a', 'b', 'c'],
                'provincias': ['A', 'B', 'C'], 'prefijos': ['00001', '00002'], 'filtro_aplicado': 'prefijos'}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 4)

    def test_cantidad_campanias(self):
        data = {'categorias': [
            self.fixture['camp_1'].categoria.id,
            self.fixture['camp_2'].categoria.id,
            self.fixture['camp_3'].categoria.id],
            'marcas': ['a', 'b'],
            'campanias': [self.fixture['camp_2'].id, self.fixture['camp_3'].id],
            'filtro_aplicado': 'campanias'
        }
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 3)

    def test_por_rechazos(self):
        data = {'tipo_rechazo': 'rechazados', 'rechazado_por': '', 'filtro_aplicado': 'tipo_rechazo'}
        p1 = ProspectosFactory(campania=self.fixture['camp_3'], nombre='a7')
        r = Rechazo(prospecto=p1, responsable=self.fixture['sup_1'])
        r.save()
        p2 = ProspectosFactory(campania=self.fixture['camp_3'], nombre='a8')
        r = Rechazo(prospecto=p2, responsable=self.fixture['sup_1'])
        r.save()
        p3 = ProspectosFactory(campania=self.fixture['camp_3'], nombre='a9')
        r = Rechazo(prospecto=p3, responsable=self.fixture['sup_2'])
        r.save()
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 3)

        data['rechazado_por'] = self.fixture['sup_1'].id
        data['filtro_aplicado'] = 'rechazado_por'
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 2)

    def test_por_nuevos(self):
        data = {'tipo_rechazo': 'nuevos', 'filtro_aplicado': 'tipo_rechazo'}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 6)

    def test_sin_filtro(self):
        data = {}
        response = self.client.post(self.cantidad_url, data)
        result = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(result['total_prospectos'], 0)
        # Data de cantidades:
        # self.assertTrue(["00002", 3] in result['prefijos'])
        # self.assertTrue(["00003", 2] in result['prefijos'])
        # self.assertTrue(["00001", 1] in result['prefijos'])
        # self.assertTrue(["", 3]in result['prefijos'])
        # self.assertTrue(["a", 3]in result['marcas'])
        # self.assertTrue(["", 3]in result['marcas'])
        # self.assertTrue(["c", 3]in result['provincias'])
        # self.assertTrue(["", 3]in result['provincias'])
        # self.assertTrue([self.fixture['camp_1'].id, 3]in result['campanias'])
        # self.assertTrue([self.fixture['camp_2'].id, 2]in result['campanias'])
        # self.assertTrue([self.fixture['camp_3'].id, 4]in result['campanias'])
