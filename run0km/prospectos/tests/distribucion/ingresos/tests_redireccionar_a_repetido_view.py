# coding=utf-8
import json

from django.urls import reverse
from django.utils import timezone

from testing.base import BaseFixturedTest


class RedireccionarARepetidoViewTest(BaseFixturedTest):
    def setUp(self):
        super(RedireccionarARepetidoViewTest, self).setUp()
        self.vendedor_uno = self.fixture['vend_1']
        self.prospecto_uno = self._crear_prospecto_uno_asignado_a(self.vendedor_uno)
        self.vendedor_dos = self.fixture['vend_2']
        self.redireccionar_a_repetido_url = reverse('redireccionar_a_prospecto_repetido')
        self.supervisor_de_uno_y_dos = self.vendedor_uno.responsable()
        self.vendedor_de_otro_supervisor = self.fixture['vend_6']

    def test_para_vendedor_ultimo_repetido_redirecciona_a_su_anterior_en_la_cadena(self):
        self.login_and_assert_correct(self.vendedor_uno.user)
        prospecto_repetido = self.creador_de_contexto.crear_repetido_asignado_a(
            prospecto_original=self.prospecto_uno, asignado_a=self.vendedor_uno)
        prospecto_repetido_2 = self.creador_de_contexto.crear_repetido_asignado_a(
            prospecto_original=self.prospecto_uno, asignado_a=self.vendedor_uno)
        response = self._get_redireccionar_a_repetido(prospecto_repetido_2, rol=self.vendedor_uno)
        self._assert_respuesta_redireccionando_a(prospecto=prospecto_repetido, response=response)

    def test_para_vendedor_primer_repetido_redirecciona_al_ultimo_de_la_cadena(self):
        self.login_and_assert_correct(self.vendedor_uno.user)
        self.creador_de_contexto.crear_repetido_asignado_a(
            prospecto_original=self.prospecto_uno, asignado_a=self.vendedor_uno)
        ultimo = self.creador_de_contexto.crear_repetido_asignado_a(
            prospecto_original=self.prospecto_uno, asignado_a=self.vendedor_uno)

        response = self._get_redireccionar_a_repetido(self.prospecto_uno, rol=self.vendedor_uno)
        self._assert_respuesta_redireccionando_a(prospecto=ultimo, response=response)

    def test_para_vendedor_prospecto_sin_repetidos_no_debe_redireccionar(self):
        self.login_and_assert_correct(self.vendedor_uno.user)
        response = self._get_redireccionar_a_repetido(self.prospecto_uno, rol=self.vendedor_uno)
        self._assert_responsata_invalida(response=response)

    def test_para_vendedor_repetido_de_otro_vendedor_no_debe_redireccionar(self):
        self.login_and_assert_correct(self.vendedor_uno.user)
        self.creador_de_contexto.crear_repetido_asignado_a(
            prospecto_original=self.prospecto_uno, asignado_a=self.vendedor_dos)
        response = self._get_redireccionar_a_repetido(self.prospecto_uno, rol=self.vendedor_uno)
        self._assert_responsata_invalida(response=response)

    def test_para_supervisor_repetido_puede_acceder_a_repetidos_de_sus_vendedores(self):
        prospecto_de_dos = self.creador_de_contexto.crear_repetido_asignado_a(
            prospecto_original=self.prospecto_uno, asignado_a=self.vendedor_dos)
        response = self._get_redireccionar_a_repetido(self.prospecto_uno, rol=self.supervisor_de_uno_y_dos)
        self._assert_respuesta_redireccionando_a(prospecto=prospecto_de_dos, response=response)

    def test_para_supervisor_repetido_de_otro_supervisor_no_es_accesible(self):
        self.creador_de_contexto.crear_repetido_asignado_a(
            prospecto_original=self.prospecto_uno, asignado_a=self.vendedor_de_otro_supervisor)
        response = self._get_redireccionar_a_repetido(self.prospecto_uno, rol=self.supervisor_de_uno_y_dos)
        self._assert_responsata_invalida(response=response)

    def _get_redireccionar_a_repetido(self, prospecto, rol):
        self.login_and_assert_correct(rol.user)
        response = self.client.get(path=self.redireccionar_a_repetido_url, data={'id_prospecto': prospecto.pk})
        return response

    def _assert_responsata_invalida(self, response):
        self.assertEqual(response.status_code, 200)
        respuesta_json = json.loads(response.content)
        self.assertFalse(respuesta_json['status'])
        self.assertEqual(respuesta_json['error_message'], 'El Prospecto no posee Repetidos disponibles.')

    def _assert_respuesta_redireccionando_a(self, prospecto, response):
        self.assertEqual(response.status_code, 200)
        respuesta_json = json.loads(response.content)
        self.assertTrue(respuesta_json['status'])

        detalle_prospecto_url = self._detalle_prospecto_url_para(prospecto=prospecto)
        self.assertEqual(respuesta_json['url_detalle_prospecto'], detalle_prospecto_url)

    def _crear_prospecto_uno_asignado_a(self, vendedor, email='', nombre_de_marca=''):
        hace_dias = timezone.now()
        return self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=vendedor, supervisor=vendedor.responsable(), fecha_de_asignacion_a_vendedor=hace_dias,
            fecha=hace_dias, email=email, telefono='48764498', es_telefono_movil=False, nombre_de_marca=nombre_de_marca)

    def _detalle_prospecto_url_para(self, prospecto):
        detalle_url = reverse('prospecto', args=[prospecto.pk])
        return detalle_url
