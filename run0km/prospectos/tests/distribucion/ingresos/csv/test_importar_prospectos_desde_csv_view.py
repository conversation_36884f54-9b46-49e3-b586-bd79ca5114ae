# -*- coding: utf-8 -*-

from django.utils import timezone
from freezegun import freeze_time

from prospectos.tests.support.pages.page_importar_prospectos_via_csv import ImportarProspectosDesdeCSVViewPage
from prospectos.tests.support.validators.importar_prospectos_via_csv import ValidadorImportadorDeProspectosViaCSV
from testing.base import BaseLoggedTest
from testing.test_utils import IngresoDeProspectosViaCSVHelper


class ImportarProspectosDesdeCSVViewTest(BaseLoggedTest):
    def setUp(self):
        super(ImportarProspectosDesdeCSVViewTest, self).setUp()
        self._csv_helper = IngresoDeProspectosViaCSVHelper()
        self._csv_validador = ValidadorImportadorDeProspectosViaCSV.new_for(self)

    def test_upload_archivo_vacio_notifica_el_error(self):
        data = {'origen': self.fixture['tipo_s'].id}
        page = ImportarProspectosDesdeCSVViewPage.nuevo_para(cliente=self.client)
        archivo = self._csv_helper.archivo_vacio()
        page.importar_desde(datos=data, archivo=archivo)
        self.assertTrue(page.tiene_notificacion_de_error_por_archivo_vacio())

    def test_upload_con_vendedor_de_otro_supervisor_debe_registrar_error(self):
        data = {'origen': self.fixture['tipo_s'].id}
        page = ImportarProspectosDesdeCSVViewPage.nuevo_para(cliente=self.client)
        archivo = self._csv_helper.archivo_con_fila(
            responsable='sup2', vendedor='vend1', nombre='Josefo', telefono='4342-234', otro='', source='567')
        page.importar_desde(datos=data, archivo=archivo)

        self.assertTrue(page.tiene_notificacion_prospectos_cargados_satisfactoriamente_para(
            cantidad_prospectos_cargados=0, filas_leidas=1))
        self._csv_validador.assert_tiene_subida_erronea_con(fallidas=1, exitosas=0)
        self._csv_validador.assert_subida_erronea_para_fila(
            fila=1, mensaje_de_error='El vendedor no esta a cargo del supervisor indicado.')

    @freeze_time("2012-09-10 13:21:34")
    def test_upload_prospecto_asignado_exitosamente(self):
        data = {'origen': self.fixture['tipo_s'].id}
        page = ImportarProspectosDesdeCSVViewPage.nuevo_para(cliente=self.client)
        nombre = 'Josefo'
        archivo = self._csv_helper.archivo_con_fila(
            responsable=self.supervisor_de_2.username(), vendedor=self.vendedor2.username(),
            nombre=nombre, telefono='4342-234', otro='', source='567')
        page.importar_desde(datos=data, archivo=archivo)

        self.assertTrue(page.tiene_notificacion_prospectos_cargados_satisfactoriamente_para(
            cantidad_prospectos_cargados=1, filas_leidas=1))
        ahora = timezone.now()
        self._csv_validador.assert_no_tiene_subidas_erroneas()
        self._csv_validador.assert_prospecto_cargado(
            nombre, supervisor=self.supervisor_de_2, vendedor=self.vendedor2,
            fecha_de_asignacion_a_supervisor=ahora, fecha_de_asignacion_a_vendedor=ahora)
