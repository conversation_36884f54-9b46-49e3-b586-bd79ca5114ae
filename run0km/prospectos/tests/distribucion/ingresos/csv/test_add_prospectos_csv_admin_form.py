# coding=utf-8
import mock

from campanias.models import TipoDeOrigen, Campania
from lib.client_geolocation.tests import RequestGeolocationMock
from prospectos.models import Prospecto
from prospectos.utils.carga_por_csv import CargadorDeProspectosPorCSV
from prospectos.views.admin_forms import AddProspectosCSVAdminForm
from testing.base import BaseFixturedTest
from testing.factories import TipoDeOrigenFactory, CategoriasDeCampaniaFactory, CampaniasFactory
from testing.test_utils import IngresoDeProspectosViaCSVHelper


class AddProspectosCSVAdminFormTest(BaseFixturedTest):

    def setUp(self):
        super(AddProspectosCSVAdminFormTest, self).setUp()
        self.tipo_extra = TipoDeOrigenFactory(codigo='ABC', nombre='A B C')
        cat = CategoriasDeCampaniaFactory(tipo_de_origen=self.tipo_extra, nombre='Cat A B C', valor=1)
        CampaniasFactory(categoria=cat, nombre='Campaña A B C')
        self._csv_helper = IngresoDeProspectosViaCSVHelper()

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_campania_por_default_con_origen_form_ok(self, mocked_response_geoip):
        i = 0
        for tipo_de_origen in TipoDeOrigen.objects.exclude(id=self.tipo_extra.id):
            data = {'origen': tipo_de_origen.id}
            form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(i))
            self.assertTrue(form.is_valid())
            self.assertEqual(form.cleaned_data['campania'], None)
            cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
            resultado = cargador.cargar_prospectos()
            self.assertEqual(resultado.cantidad_filas_exitosas(), 2)
            prospecto = Prospecto.objects.get(nombre='Josefo', campania__categoria__tipo_de_origen=tipo_de_origen)
            self.assertEqual(prospecto.telefono, '4342-234%d' % i)
            self.assertEqual(prospecto.campos_extra.count(), 0)
            prospecto = Prospecto.objects.get(nombre='Pepe', campania__categoria__tipo_de_origen=tipo_de_origen)
            self.assertEqual(prospecto.campos_extra.count(), 1)
            self.assertEqual(prospecto.campos_extra.all()[0].nombre, 'otro')
            self.assertEqual(prospecto.campos_extra.all()[0].valor, 'extra')
            i += 10

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_error_por_no_indicarse_campania_default_para_origen_sin_campania_por_defecto(self, mocked_response_geoip):
        data = {'origen': self.tipo_extra.id}
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(1))
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['campania'], None)
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        resultado = cargador.cargar_prospectos()
        self.assertEqual(resultado.cantidad_filas_exitosas(), 0)
        self.assertEqual(resultado.cantidad_filas_erroneas(), 2)

    def test_campania_debe_ser_del_mismo_origen(self):
        for tipo_de_origen in TipoDeOrigen.objects.all():
            campania = Campania.objects.exclude(categoria__tipo_de_origen=tipo_de_origen)[0]
            data = {'origen': tipo_de_origen.id, 'campania': campania.id}
            form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
            self.assertEqual(form.is_valid(), False)
            self.assertEqual(form.errors.get('campania'),
                             ['El origen de la campaña elegida debe corresponder con el origen seleccionado'])

    def test_supervisor(self):
        # Error con eleccion de responsable con cargo de Vendedor
        data = {'origen': self.fixture['tipo_s'].id,
                'responsable': self.fixture['vend_2'].id,
                }
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('responsable'), ['Seleccione una opción válida. '
                                                          'La opción seleccionada no es una de las disponibles.'])
        # Se selecciona responsable de los campos generales si no

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_definicion_de_vendedor(self, mocked_geo_ip):
        # Error con vendedor no a cargo del responsable
        data = {'origen': self.fixture['tipo_s'].id,
                'responsable': self.fixture['sup_2'].id,
                'vendedor': self.fixture['vend_1'].id
                }
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('vendedor'), ['Debe indicar un vendedor que este a cargo del '
                                                       'Supervisor Responsable.'])

        # Si no elijo vendedor general, no carga prospectos con vendedores no asignados al responsable.
        data.pop('vendedor')
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(1))
        form.ejecutor = self.fixture['vend_1'].user
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        self.assertEqual(Prospecto.objects.filter(nombre='Pepe').count(), 0)
        prospecto = Prospecto.objects.get(nombre='Josefo')
        self.assertEqual(prospecto.responsable, self.fixture['sup_2'])
        self.assertEqual(prospecto.vendedor, None)

        data['responsable'] = self.fixture['sup_1'].id
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(2))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        prospecto = Prospecto.objects.get(nombre='Pepe')
        self.assertEqual(prospecto.responsable, self.fixture['sup_1'])
        self.assertEqual(prospecto.vendedor, self.fixture['vend_2'])

    def test_archivo_vacio(self):
        data = {'origen': self.fixture['tipo_s'].id}
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_vacio())
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('file_upload'), ['El archivo enviado está vacío.'])