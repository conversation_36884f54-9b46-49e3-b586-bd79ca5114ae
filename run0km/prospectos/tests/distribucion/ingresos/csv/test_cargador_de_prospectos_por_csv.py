# coding=utf-8
from unittest import skipIf

import mock
from django.utils import timezone
from freezegun import freeze_time

from campanias.models import Campania
from lib.client_geolocation.tests import RequestGeolocationMock
from core.tests.locker_helper import <PERSON><PERSON><PERSON>elper
from prospectos.models import PedidoDeP<PERSON><PERSON>o, Prospecto, Proveedor
from prospectos.tests.distribucion.pedidos.test_pedidos_core import mock_on_commit
from prospectos.tests.support.validators.importar_prospectos_via_csv import ValidadorImportadorDeProspectosViaCSV
from prospectos.utils.carga_por_csv import CargadorDeProspectosPorCSV
from prospectos.views.admin_forms import AddProspectosCSVAdminForm
from testing.base import BaseFixturedTest
from testing.factories import PedidosDeProspectoFactory, ProveedoresFactory
from testing.test_utils import IngresoDeProspectosViaCSVHelper


class CargadorDeProspectosPorCSVTest(BaseFixturedTest):
    def setUp(self):
        super(CargadorDeProspectosPorCSVTest, self).setUp()
        self._locker_helper = LockerHelper()
        self._csv_helper = IngresoDeProspectosViaCSVHelper()
        self._csv_validador = ValidadorImportadorDeProspectosViaCSV.new_for(self)

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_suma_consumo_a_pedido_seleccionado(self, mocked_geoip_response):
        camp = Campania.objects.filter(categoria__tipo_de_origen__codigo='S')[0]
        camp.categoria.valor = 2
        camp.categoria.save()
        supervisor = self.fixture['sup_1']
        pedido = PedidosDeProspectoFactory(supervisor=supervisor, credito=10, yapa=5, consumido=2,
                                           asignar_a='T', fecha=timezone.now().date())
        data = {'origen': self.fixture['tipo_s'].id, 'campania': camp.id,
                'responsable': self.fixture['sup_2'].id, 'pedido': pedido.id}
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('pedido'), ['Debe elegir un Pedido asignado al Supervisor Responsable'])

        data = {'origen': self.fixture['tipo_s'].id, 'campania': camp.id,
                'responsable': supervisor.id, 'pedido': pedido.id}

        supervisor.deshabilitar()
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('responsable'),
                         ['Seleccione una opción válida. La opción seleccionada no es una de las disponibles.'])
        self.assertEqual(form.errors.get('pedido'),
                         ['Seleccione una opción válida. La opción seleccionada no es una de las disponibles.'])

        supervisor.habilitar()
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        pedido = PedidoDeProspecto.objects.get(id=pedido.id)
        self.assertEqual(6, pedido.consumido)

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_asigna_el_pedido_utilizado_a_los_prospectos(self, mocked_geoip_response):
        camp = Campania.objects.filter(categoria__tipo_de_origen__codigo='S')[0]
        camp.categoria.valor = 2
        camp.categoria.save()
        supervisor = self.fixture['sup_1']
        pedido = PedidosDeProspectoFactory(supervisor=supervisor, credito=10, yapa=5, consumido=2,
                                           asignar_a='T', fecha=timezone.now().date())
        pedido.origenes = [self.fixture['tipo_s'], ]

        data = {'origen': self.fixture['tipo_s'].id, 'campania': camp.id,
                'responsable': supervisor.id, 'pedido': pedido.id}
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()

        p1 = Prospecto.objects.get(nombre='Josefo')
        self.assertEqual(p1.pedido, pedido)
        self.assertEqual(p1.consumido_en_pedido, p1.campania.categoria.valor)
        p2 = Prospecto.objects.get(nombre='Pepe')
        self.assertEqual(p2.pedido, pedido)
        self.assertEqual(p2.consumido_en_pedido, p2.campania.categoria.valor)

    @freeze_time("2012-09-10 13:21:34")
    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_asigna_genera_asignacion_de_responsable(self, mocked_geoip_response):
        ahora = timezone.now()
        campania = Campania.objects.filter(categoria__tipo_de_origen__codigo='S')[0]
        supervisor = self.fixture['sup_1']

        data = {'origen': campania.categoria.tipo_de_origen.id,
                'campania': campania.id,
                'responsable': supervisor.id}
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()

        self._csv_validador.assert_prospecto_cargado(
            nombre='Josefo', supervisor=supervisor, vendedor=None,
            fecha_de_asignacion_a_supervisor=ahora, fecha_de_asignacion_a_vendedor=None)

        self._csv_validador.assert_prospecto_cargado(
            nombre='Pepe', supervisor=supervisor, vendedor=self.fixture['vend_2'],
            fecha_de_asignacion_a_supervisor=ahora, fecha_de_asignacion_a_vendedor=ahora)

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_proveedor(self, mocked_geoip_response):
        proveedor = ProveedoresFactory(source_id='1234', nombre='Nombre Proveedor')
        data = {'origen': self.fixture['tipo_s'].id, 'proveedor': proveedor.id}
        form = AddProspectosCSVAdminForm(data, self._csv_helper.json_con_archivo_con_template_para(0))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        self.assertEqual(1, Proveedor.objects.filter(source_id='567').count())
        prospecto = Prospecto.objects.get(nombre='Josefo')
        self.assertIsNotNone(prospecto.proveedor)
        self.assertEqual('567', prospecto.proveedor.source_id)
        prospecto = Prospecto.objects.get(nombre='Pepe')
        self.assertIsNotNone(prospecto.proveedor)
        self.assertEqual(proveedor, prospecto.proveedor)

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_origen_no_obligatorio(self, mocked_geoip_response):
        # Agrego prospectos con datos de campaña en el archivo csv
        header = 'email,campania,origen'
        archivo = self._csv_helper.archivo_con_tabla(header, filas=['<EMAIL>,SMS,'])
        form = AddProspectosCSVAdminForm({}, self._csv_helper.json_para_archivo(archivo))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        self.assertEqual(1, Prospecto.objects.filter(email='<EMAIL>').count())
        self.assertEqual(Prospecto.objects.get(email='<EMAIL>').campania, self.fixture['camp_1'])

        archivo = self._csv_helper.archivo_con_tabla(header, filas=['<EMAIL>,,S'])
        form = AddProspectosCSVAdminForm({}, self._csv_helper.json_para_archivo(archivo))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        self.assertEqual(1, Prospecto.objects.filter(email='<EMAIL>').count())
        self.assertEqual(Prospecto.objects.get(email='<EMAIL>').campania.nombre, 'Genérica SMS')

        archivo = self._csv_helper.archivo_con_tabla(header, filas=['<EMAIL>,,'])
        form = AddProspectosCSVAdminForm({}, self._csv_helper.json_para_archivo(archivo))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        self.assertIsNotNone(cargador.log)
        self._csv_validador.assert_archivo_subida_erronea(
            archivo=cargador.log.archivo, fila=1, mensaje_de_error='No se puede definir una campaña')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    def test_csv_con_columna_ip_deberia_configurar_geolocalizacion(self, moched_on_commit, mocked_geoip):
        ip_esperada = '***********'
        fila = '<EMAIL>,SMS,%s' % ip_esperada
        archivo = self._csv_helper.archivo_con_tabla(header='email,campania,ip', filas=[fila])
        form = AddProspectosCSVAdminForm({}, self._csv_helper.json_para_archivo(archivo))
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        self.assertEqual(1, Prospecto.objects.filter(email='<EMAIL>').count())
        prospecto = Prospecto.objects.get(email='<EMAIL>')
        self.assert_geolocalizacion(prospecto=prospecto, ip='***********', latitud=-34.7203, localidad='Quilmes',
                                    longitud=-58.2694, provincia='Buenos Aires')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_error_vendedor(self, mocked_geoip_response):
        data = {'origen': self.fixture['tipo_s'].id}
        archivo = self._csv_helper.json_con_archivo_con_fila(
            responsable='sup2', vendedor='vend1', nombre='Josefo', telefono='4342-234', otro='', source='567')
        form = AddProspectosCSVAdminForm(data, archivo)
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        cargador.cargar_prospectos()
        self.assertIsNotNone(cargador.log)
        self._csv_validador.assert_archivo_subida_erronea(
            archivo=cargador.log.archivo, fila=1,
            mensaje_de_error='El vendedor no esta a cargo del supervisor indicado.')

    @skipIf(True, reason='Deploy incompleto')
    @mock.patch('core.locker.mem_locker.Locker.do_locking')
    def test_prospecto_con_telefono_y_email_bloqueados_debe_responder_error(self, mock_locker):
        # Dado
        self._locker_helper.configurar_locker_lanzar_recurso_bloqueado(mock_locker)
        campania = Campania.objects.filter(categoria__tipo_de_origen__codigo='S').first()
        supervisor = self.fixture['sup_1']
        data = {'origen': self.fixture['tipo_s'].id, 'campania': campania.id, 'responsable': supervisor.id}
        nombre = 'Josefo'
        archivo_csv = self._csv_helper.json_con_archivo_con_fila(
            responsable='sup2', vendedor='vend1', nombre=nombre, telefono='4342-234', otro='', source='567')

        # Cuando
        form = AddProspectosCSVAdminForm(data, archivo_csv)
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        resultado = cargador.cargar_prospectos()

        # Entonces
        self.assertTrue(resultado.tiene_filas_erroneas())
        self.assertFalse(Prospecto.objects.filter(nombre=nombre).exists())

        self.assertIsNotNone(cargador.log)
        self._csv_validador.assert_archivo_subida_erronea(
            archivo=cargador.log.archivo, fila=1,
            mensaje_de_error='Los datos de contacto estan momentaneamente bloqueados.')

    @mock.patch('core.locker.mem_locker.Locker.lock_resource')
    def test_al_tercer_intento_carga_desbloqueada_debe_ingresar_el_prospecto(self, mock_lock_resource):
        # Dado
        self._locker_con_tercer_reintento_exitoso(mock_lock_resource)
        campania = Campania.objects.filter(categoria__tipo_de_origen__codigo='S').first()
        supervisor = self.fixture['sup_1']
        data = {'origen': self.fixture['tipo_s'].id, 'campania': campania.id, 'responsable': supervisor.id}
        nombre = 'Josefo'
        archivo_csv = self._csv_helper.json_con_archivo_con_fila(
            responsable='sup2', vendedor='vend1', nombre=nombre, telefono='4342-234', otro='', source='567')
        self.assertFalse(Prospecto.objects.filter(nombre=nombre).exists())

        # Cuando
        form = AddProspectosCSVAdminForm(data, archivo_csv)
        self.assertTrue(form.is_valid())
        cargador = CargadorDeProspectosPorCSV(form.cleaned_data, self.fixture['usr_admin'])
        resultado = cargador.cargar_prospectos()

        # Entonces
        self.assertFalse(resultado.tiene_filas_erroneas())
        self.assertTrue(Prospecto.objects.filter(nombre=nombre).exists())

    def _locker_con_tercer_reintento_exitoso(self, mock_lock_resource):
        # - Primer Intento: telefono lockeado, sin email.
        # - Segundo intento telefono lockeado, sin email.
        # - Tercer intento: telefono deslockeado.

        # Asume que el prospecto tiene telefono y email
        mock_lock_resource.side_effect = [False, False, True]

    def assert_geolocalizacion(self, prospecto, ip, latitud, localidad, longitud, provincia):
        geolocalizacion = prospecto.obtener_geolocalizacion()
        self.assertEqual(geolocalizacion.ip, ip)
        self.assertEqual(geolocalizacion.latitud, latitud)
        self.assertEqual(geolocalizacion.localidad, localidad)
        self.assertEqual(geolocalizacion.longitud, longitud)
        self.assertEqual(geolocalizacion.provincia, provincia)