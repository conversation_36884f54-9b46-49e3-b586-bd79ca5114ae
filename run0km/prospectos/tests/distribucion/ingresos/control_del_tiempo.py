from django.conf import settings
from django.utils import timezone
from freezegun import freeze_time


class ControladorDelTiempo(object):

    def avanzar_tiempo_la_mitad_del_gap(self, tiempo_frizado):
        milisegundos = settings.GAP_PARA_PROSPECTOS_MERGEABLES_EN_MILISEGUNDOS / 2
        tiempo_frizado.tick(timezone.timedelta(milliseconds=milisegundos))

    def avanzar_tiempo_el_doble_del_gap(self, tiempo_frizado):
        tiempo_frizado.tick(self.timedelta_del_doble_del_gap())

    def timedelta_del_doble_del_gap(self):
        milisegundos = 2 * settings.GAP_PARA_PROSPECTOS_MERGEABLES_EN_MILISEGUNDOS
        return timezone.timedelta(milliseconds=milisegundos)

    def evaluar_luego_del_gap_de_tiempo(self, funcion, *args, **kwargs):
        with freeze_time(timezone.now()) as tiempo_frizado:
            ControladorDelTiempo.nuevo().avanzar_tiempo_el_doble_del_gap(tiempo_frizado)
            response = funcion(*args, **kwargs)
        return response

    @classmethod
    def nuevo(cls):
        return cls()
