from django.utils import timezone
from freezegun import freeze_time

from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest
from testing.test_utils import reload_model
from vendedores.gestor import GestorDeVendedores


class RepartidorDeProspectosTest(PedidosTest):
    def setUp(self):
        super(RepartidorDeProspectosTest, self).setUp()
        self.supervisor_uno = self._crear_supervisor()
        self.supervisor_dos = self._crear_supervisor()
        self.repartidor = RepartidorDeProspectos.nuevo()
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()

    def _assert_asinacion(self, prospectos, fecha_para_vendedor, fecha_para_supervisor):
        """
            Garantiza que las asignaciones de cada prospecto tenga el vendedor del prospecto y las fechas esperadas

        """
        for prospecto in prospectos:
            prospecto = reload_model(prospecto)
            self._assert_vendedor_asignado(prospecto)
            self.assertEqual(prospecto.fecha_de_asignacion_a_vendedor(), fecha_para_vendedor)
            self.assertEqual(prospecto.fecha_de_asignacion_a_supervisor(), fecha_para_supervisor)

    def _assert_vendedor_asignado(self, prospecto):
        if prospecto.tiene_vendedor():
            self.assertEqual(prospecto.obtener_vendedor(), prospecto.asignacion.obtener_vendedor())
        else:
            self._assert_prospecto_sin_asignacion_o_asignacion_sin_vendedor(prospecto)

    def _assert_prospecto_sin_asignacion_o_asignacion_sin_vendedor(self, prospecto):
        """
            Por ahora usamos este hook hasattr para no sobrecargar mas de metodos a Prospecto
        """

        self.assertTrue(not hasattr(prospecto, 'asignacion') or prospecto.asignacion.obtener_vendedor() is None)


class AsignacionDesdeElRepartidorDeProspectosTest(RepartidorDeProspectosTest):

    def setUp(self):
        super(AsignacionDesdeElRepartidorDeProspectosTest, self).setUp()
        self.vendedor_de_supervisor_uno = self._agregar_vendedor_a_(self.supervisor_uno)

    def test_asignacion_de_prospecto_de_otro_supervisor_debe_actualizar_fechas_de_asignacion_y_responsable(self):
        ahora = timezone.now()
        prospecto = self._crear_nuevo_prospecto(supervisor=self.supervisor_uno)
        vendedor_de_dos = self._agregar_vendedor_a_(self.supervisor_dos)
        self.repartidor.asignar_prospecto_a(vendedor=vendedor_de_dos,
                                            prospecto=prospecto,
                                            con_fecha=ahora)
        self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id, a_vendedor=vendedor_de_dos,
                                             a_responsable=self.supervisor_dos)
        self._assert_asinacion([prospecto], fecha_para_vendedor=ahora, fecha_para_supervisor=ahora)

    def test_asignacion_de_prospectos_debe_actualizar_fechas_de_asignacion_y_la_de_responsable_solo_los_que_cambiaron(
            self):
        ayer = timezone.now() - timezone.timedelta(days=1)
        ahora = timezone.now()
        prospectos_de_uno = self._prospectos_nuevos(cantidad=5, supervisor=self.supervisor_uno,
                                                    fecha_de_asignacion_a_supervisor=ayer)
        prospectos_de_dos = self._prospectos_nuevos(cantidad=3, supervisor=self.supervisor_dos,
                                                    fecha_de_asignacion_a_supervisor=ayer)
        vendedor_de_dos = self._agregar_vendedor_a_(self.supervisor_dos)
        self.repartidor.asignar_prospectos_a(vendedor=vendedor_de_dos,
                                             prospectos=prospectos_de_uno | prospectos_de_dos,
                                             con_fecha=ahora)
        self._assert_vendedores_tienen(self.supervisor_dos, cantidad_de_vendedores=1,
                                       cantidad_de_prospectos=prospectos_de_uno.count() + prospectos_de_dos.count())
        self._assert_asinacion(prospectos_de_uno, fecha_para_vendedor=ahora, fecha_para_supervisor=ahora)
        self._assert_asinacion(prospectos_de_dos, fecha_para_vendedor=ahora, fecha_para_supervisor=ayer)

    def test_poner_a_cargo_prospecto_mismo_supervisor_cambia_la_fecha_de_asignacion_y_anula_la_de_vendedor(self):
        """
            Poner a cargo actualiza la fecha del supervisor y pone en None al vendedor y a la fecha de asignacion
            del vendedor.
        """

        ayer = timezone.now() - timezone.timedelta(days=1)
        ahora = timezone.now()
        prospectos = self._prospectos_nuevos(
            cantidad=1, supervisor=self.supervisor_uno, vendedor=self.vendedor_de_supervisor_uno,
            fecha_de_asignacion_a_vendedor=ayer, fecha_de_asignacion_a_supervisor=ayer)
        prospecto = prospectos.first()
        self.repartidor.asignar_responsable_a(prospecto=prospecto, supervisor=self.supervisor_uno, con_fecha=ahora)
        self._assert_asignacion_de_prospecto(
            prospecto_id=prospecto.id, a_vendedor=None, a_responsable=self.supervisor_uno)
        self._assert_asinacion(prospectos, fecha_para_vendedor=None, fecha_para_supervisor=ahora)

    def test_poner_a_cargo_prospecto_de_otro_supervisor_cambia_la_fecha_de_asignacion_y_anula_la_de_vendedor(self):
        """
            Poner a cargo actualiza la fecha del supervisor y pone en None al vendedor y a la fecha de asignacion
            del vendedor.
        """
        ayer = timezone.now() - timezone.timedelta(days=1)
        ahora = timezone.now()
        prospectos = self._prospectos_nuevos(
            cantidad=1, supervisor=self.supervisor_dos, vendedor=self.vendedor_de_supervisor_uno,
            fecha_de_asignacion_a_vendedor=ayer, fecha_de_asignacion_a_supervisor=ayer)
        prospecto = prospectos.first()
        self.repartidor.asignar_responsable_a(prospecto=prospecto, supervisor=self.supervisor_uno,
                                              con_fecha=ahora)
        self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id, a_vendedor=None,
                                             a_responsable=self.supervisor_uno)
        self._assert_asinacion(prospectos, fecha_para_vendedor=None, fecha_para_supervisor=ahora)

    def test_poner_a_cargo_prospectos_cambia_la_fecha_de_asignacion_a_supervisor_y_anula_la_de_vendedor(self):
        """
            Poner a cargo actualiza la fecha del supervisor y pone en None al vendedor y a la fecha de asignacion
            del vendedor.
        """
        ayer = timezone.now() - timezone.timedelta(days=1)
        ahora = timezone.now()
        prospectos_de_uno = self._prospectos_nuevos(
            cantidad=5, supervisor=self.supervisor_uno, vendedor=self.vendedor_de_supervisor_uno,
            fecha_de_asignacion_a_vendedor=ayer, fecha_de_asignacion_a_supervisor=ayer)
        prospectos_de_dos = self._prospectos_nuevos(cantidad=3, supervisor=self.supervisor_dos,
                                                    fecha_de_asignacion_a_supervisor=ayer)
        prospectos = prospectos_de_uno | prospectos_de_dos
        self.repartidor.asignar_prospectos_a_responsable(
            supervisor=self.supervisor_uno,
            prospectos=prospectos,
            con_fecha=ahora)
        self._assert_tiene_prospectos_a_cargo(self.supervisor_uno, cantidad=prospectos.count())
        self._assert_asinacion(prospectos, fecha_para_vendedor=None, fecha_para_supervisor=ahora)

    def test_quitar_asignacion_de_vendedor_anula_la_fechas_de_asignacion_a_vendedor(self):
        ayer = timezone.now() - timezone.timedelta(days=1)
        prospectos = self._prospectos_nuevos(supervisor=self.supervisor_uno,
                                             vendedor=self.vendedor_de_supervisor_uno,
                                             fecha_de_asignacion_a_vendedor=ayer,
                                             fecha_de_asignacion_a_supervisor=ayer,
                                             cantidad=3)

        self.repartidor.quitar_asignacion_a_prospectos(prospectos=prospectos)
        self._assert_vendedores_tienen(self.supervisor_uno, cantidad_de_vendedores=1,
                                       cantidad_de_prospectos=0)
        self._assert_tiene_prospectos_a_cargo(self.supervisor_uno, cantidad=prospectos.count())
        self._assert_asinacion(prospectos, fecha_para_vendedor=None, fecha_para_supervisor=ayer)

    def test_quitar_responsable_a_prospectos__anula_las_fechas_de_asignacion(self):
        ayer = timezone.now() - timezone.timedelta(days=1)
        prospectos_de_uno = self._prospectos_nuevos(
            cantidad=5, supervisor=self.supervisor_uno, vendedor=self.vendedor_de_supervisor_uno,
            fecha_de_asignacion_a_vendedor=ayer, fecha_de_asignacion_a_supervisor=ayer)
        prospectos_de_dos = self._prospectos_nuevos(cantidad=3, supervisor=self.supervisor_dos,
                                                    fecha_de_asignacion_a_supervisor=ayer)
        prospectos = prospectos_de_uno | prospectos_de_dos
        self.repartidor.quitar_responsable_a_prospectos(prospectos)
        self._assert_prospectos_no_asignados(prospectos, cantidad=prospectos.count())
        self._assert_asinacion(prospectos, fecha_para_vendedor=None, fecha_para_supervisor=None)


@freeze_time("2016-07-10 13:21:34")
class AsignacionAVendedorAlCrearProspectoTest(RepartidorDeProspectosTest):
    def setUp(self):
        super(AsignacionAVendedorAlCrearProspectoTest, self).setUp()
        self.campania = self.fixture['camp_1']

    def test_crear_prospecto_con_vendedor_y_responsable_debe_configurar_la_asginacion(self):
        ahora = timezone.now()
        vendedor_de_uno = self._agregar_vendedor_a_(self.supervisor_uno)
        datos = self._obtener_datos_para_prospecto(supervisor=self.supervisor_uno, telefono='192837465',
                                                   campania=self.campania, nombre='prospecto_test_1',
                                                   vendedor=vendedor_de_uno)
        resultado = self.repartidor.crear_nuevo_prospecto_desde(datos=datos, validar_email=False)
        self.assertFalse(resultado.fue_repetido())
        prospecto = resultado.prospecto()
        self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id,
                                             a_vendedor=vendedor_de_uno,
                                             a_responsable=self.supervisor_uno)
        self._assert_asinacion([prospecto], fecha_para_vendedor=ahora, fecha_para_supervisor=ahora)

    def test_crear_prospecto_con_responsable_sin_vendedor_debe_configurar_solo_la_fecha_de_asignacion_de_responsable(
            self):
        ahora = timezone.now()
        datos = self._obtener_datos_para_prospecto(supervisor=self.supervisor_uno, telefono='192837465',
                                                   campania=self.campania, nombre='prospecto_test_1',
                                                   vendedor=None)
        resultado = self.repartidor.crear_nuevo_prospecto_desde(datos=datos, validar_email=False)
        self.assertFalse(resultado.fue_repetido())
        prospecto = resultado.prospecto()
        self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id,
                                             a_vendedor=None,
                                             a_responsable=self.supervisor_uno)
        self._assert_asinacion([prospecto], fecha_para_vendedor=None, fecha_para_supervisor=ahora)

    def test_crear_prospecto_sin_responsable_ni_vendedor_la_asingacion_debe_ser_vacia(self):
        datos = self._obtener_datos_para_prospecto(supervisor=None, telefono='192837465',
                                                   campania=self.campania, nombre='prospecto_test_1',
                                                   vendedor=None)
        resultado = self.repartidor.crear_nuevo_prospecto_desde(datos=datos, validar_email=False)
        self.assertFalse(resultado.fue_repetido())
        prospecto = resultado.prospecto()
        self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id,
                                             a_vendedor=None,
                                             a_responsable=None)
        self._assert_asinacion([prospecto], fecha_para_vendedor=None, fecha_para_supervisor=None)
