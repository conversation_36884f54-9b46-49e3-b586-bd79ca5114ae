from unittest import skipIf

from prospectos.models import PedidoDeProspecto
from prospectos.models.entrega_de_datos.opciones import MetodosDeAsignacionChoices
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest


@skipIf(True, "Jenkins tests en dev-android: se deben ignorar. Todavia no se sabe el motivo")
class MetodoViejoDeAsignacionDeProspectoAPedidoTest(PedidosTest):
    def setUp(self):
        super(MetodoViejoDeAsignacionDeProspectoAPedidoTest, self).setUp()
        self.prospecto = self._crear_nuevo_prospecto()

    def test_vieja_asignacion_a_pedido_por_vendedor_debe_asignar_y_consumir_credito(self):
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno, credito=10,
                                                                             yapa=5, consumido=self.credito_consumido,
                                                                             campania=self.campania_uno,
                                                                             forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)

        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, pedido.id,
                                                                 a_vendedor=pedido.vendedor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_pedido_para_un_vendedor_deshabilitado_deberia_asignar_responsable_y_consumir_credito(self):
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno, credito=10,
                                                                             yapa=5, consumido=self.credito_consumido,
                                                                             campania=self.campania_uno,
                                                                             forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        self.vendedor_uno.deshabilitar()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, en_pedido_id=pedido.id,
                                                                 a_vendedor=None, a_responsable=self.supervisor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_por_equipo_deberia_asignar_y_consumir_credito(self):
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_uno, self.vendedor_dos])
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_equipo(equipo, credito=10,
                                                                           yapa=5, consumido=self.credito_consumido,
                                                                           campania=self.campania_uno,
                                                                           forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)

        vendedor_priorizado = pedido.seleccionar_vendedor()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, pedido.id,
                                                                 a_vendedor=vendedor_priorizado,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_por_equipo_no_deberia_asignar_a_vendedor_deshabilitado(self):
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_uno, self.vendedor_dos])
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_equipo(equipo, credito=10,
                                                                           yapa=5, consumido=self.credito_consumido,
                                                                           campania=self.campania_uno,
                                                                           forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        vendedor_priorizado = pedido.seleccionar_vendedor()
        vendedor_priorizado.deshabilitar()
        if vendedor_priorizado == self.vendedor_uno:
            vendedor = self.vendedor_dos
        else:
            vendedor = self.vendedor_uno

        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, pedido.id,
                                                                 a_vendedor=vendedor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_por_equipo_deshabilitado_deberia_asignar_responsable_y_consumir_credito(self):
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_uno, self.vendedor_dos])
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_equipo(equipo, credito=10,
                                                                           yapa=5, consumido=self.credito_consumido,
                                                                           campania=self.campania_uno,
                                                                           forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        self.vendedor_uno.deshabilitar()
        self.vendedor_dos.deshabilitar()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, en_pedido_id=pedido.id,
                                                                 a_vendedor=None, a_responsable=self.supervisor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_para_todos_los_vendedores_deberia_asignar_y_consumir_credito(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            credito=10, yapa=5,
            consumido=self.credito_consumido,
            campania=self.campania_uno,
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)

        vendedor_priorizado = pedido.seleccionar_vendedor()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, pedido.id,
                                                                 a_vendedor=vendedor_priorizado,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_para_todos_sin_vendedores_deberia_asignar_responsable_y_consumir_credito(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            credito=10, yapa=5,
            consumido=self.credito_consumido,
            campania=self.campania_uno,
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        self.supervisor.vendedores.all().delete()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, en_pedido_id=pedido.id,
                                                                 a_vendedor=None, a_responsable=self.supervisor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_para_todos_deshabilitados_deberia_asignar_responsable_y_consumir_credito(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            credito=10, yapa=5,
            consumido=self.credito_consumido,
            campania=self.campania_uno,
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        [each.deshabilitar() for each in self.supervisor.vendedores.all()]
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, en_pedido_id=pedido.id,
                                                                 a_vendedor=None, a_responsable=self.supervisor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_para_un_vendedor_deberia_asignar_responsable_y_consumir_credito(self):
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            self.vendedor_uno,
            credito=10,
            yapa=5,
            consumido=self.credito_consumido,
            campania=self.campania_uno,
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=self.vendedor_uno,
                                                                     factor_de_asignacion=0)
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, en_pedido_id=pedido.id,
                                                                 a_vendedor=None, a_responsable=self.supervisor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)

    def test_asignacion_a_pedido_para_todos_con_factor_de_asignacion_deberia_asignar_responsable_y_consumir_credito(
            self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            metodo_de_asignacion=MetodosDeAsignacionChoices.FACTOR_MANUAL,
            credito=10, yapa=5,
            consumido=self.credito_consumido,
            campania=self.campania_uno,
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        for each in self.supervisor.vendedores.all():
            self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=each,
                                                                         factor_de_asignacion=0)
        self.admin_de_pedidos.asignar_prospecto_automaticamente(self.prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(self.prospecto.id, en_pedido_id=pedido.id,
                                                                 a_vendedor=None, a_responsable=self.supervisor,
                                                                 credito_consumido=self.credito_consumido + self.valor_de_categoria)
