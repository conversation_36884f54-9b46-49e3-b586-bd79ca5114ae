# coding=utf-8
from django.utils import timezone

from prospectos.models import FiltroDePedido
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from testing.base import BaseFixturedTest
from testing.creador_de_contexto import CreadorDeContexto
from testing.factories import ProspectosFactory, CampoExtraFactory, FiltrosDePedidoFactory


class FiltroAplicaAProspectoTest(BaseFixturedTest):
    """
        La clase no es de mi autoría, divide el archivo en un paquete.

        Refactor pendiente para mejorar legibilidad de los escenarios de tests
    """

    def setUp(self):
        super(FiltroAplicaAProspectoTest, self).setUp()
        self.supervisor = self.fixture['sup_1']
        self.campania_uno = self.fixture['camp_1']
        self.creador_de_contexto = CreadorDeContexto(supervisor=self.supervisor, fixture=self.fixture)
        self.pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(credito=5000)

    def test_deberia_aplicar_por_prefijo(self):
        filtro = FiltroDePedido.nuevo_para_exclusion(pedido=self.pedido,
                                                     campo='marca',
                                                     selector=FiltroDePedido.PREFIJO,
                                                     valor='inicio')
        campo = 'Inicio y Medio y Final'
        self.assertTrue(filtro.aplica(campo))

    def test_no_deberia_aplicar_por_prefijo(self):
        filtro = FiltroDePedido.nuevo_para_exclusion(pedido=self.pedido,
                                                     campo='marca',
                                                     selector=FiltroDePedido.PREFIJO,
                                                     valor='final')
        campo = 'Inicio y Medio y Final'
        self.assertFalse(filtro.aplica(campo))

    def test_deberia_aplicar_por_contencion(self):
        filtro = FiltroDePedido.nuevo_para_exclusion(pedido=self.pedido,
                                                     campo='marca',
                                                     selector=FiltroDePedido.CONTIENE,
                                                     valor='medio')
        campo = 'Inicio y Medio y Final'
        self.assertTrue(filtro.aplica(campo))

    def test_no_deberia_aplicar_por_contencion(self):
        filtro = FiltroDePedido.nuevo_para_exclusion(pedido=self.pedido,
                                                     campo='marca',
                                                     selector=FiltroDePedido.PREFIJO,
                                                     valor='cadena')
        campo = 'Inicio y Medio y Final'
        self.assertFalse(filtro.aplica(campo))

    def test_deberia_aplicar_por_sufijo(self):
        filtro = FiltroDePedido.nuevo_para_exclusion(pedido=self.pedido,
                                                     campo='marca',
                                                     selector=FiltroDePedido.SUFIJO,
                                                     valor='final')
        campo = 'Inicio y Medio y Final'
        self.assertTrue(filtro.aplica(campo))

    def test_no_deberia_aplicar_por_sufijo(self):
        filtro = FiltroDePedido.nuevo_para_exclusion(pedido=self.pedido,
                                                     campo='marca',
                                                     selector=FiltroDePedido.SUFIJO,
                                                     valor='inicio')
        campo = 'Inicio y Medio y Final'
        self.assertFalse(filtro.aplica(campo))

    def test_filtro_aplica_a_prospecto(self):
        admin_de_pedidos = AdministradorDePedidos()
        f = self.fixture
        supervisor = f['sup_1']
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=10, yapa=5, consumido=2,
            fecha=timezone.now().date(), calidades=[f['tipo_s'], ])
        p1 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], telefono='4789-9874', nombre='Un Nombre')
        CampoExtraFactory(prospecto=p1, nombre='un extra', valor='prefijo sufijo')
        f1 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='nombre', selector='in', valor='un n')
        f2 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='telefono', selector='pre', valor='478')
        f3 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='telefono', selector='fin', valor='874')
        f4 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='un extra', selector='pre', valor='prefijo')
        self.assertTrue(admin_de_pedidos._filtro_aplica_a_prospecto(f1, p1))
        self.assertTrue(admin_de_pedidos._filtro_aplica_a_prospecto(f2, p1))
        self.assertTrue(admin_de_pedidos._filtro_aplica_a_prospecto(f3, p1))
        self.assertTrue(admin_de_pedidos._filtro_aplica_a_prospecto(f4, p1))
        p2 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], telefono='555-555', nombre='Otro nombre')
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f1, p2))
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f2, p2))
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f3, p2))
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f4, p2))

    def test_pedido_con_filtro_por_localidad_no_aplica_a_prospecto_sin_localidad_pero_con_provincia_de_mismo_nombre(
            self):
        admin_de_pedidos = AdministradorDePedidos()
        f = self.fixture
        supervisor = f['sup_1']
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor, credito=10,
                                                                                   yapa=5, consumido=2,
                                                                                   fecha=timezone.now().date(),
                                                                                   calidades=[f['tipo_s'], ])
        p1 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], telefono='4789-9874', nombre='Un Nombre',
                               provincia='provinciatest')
        f1 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='localidad', selector='in', valor='provinciatest')
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f1, p1))

    def test_pedido_con_filtro_por_provincia_no_aplica_a_prospecto_sin_provincia_pero_con_localidad_de_mismo_nombre(
            self):
        admin_de_pedidos = AdministradorDePedidos()
        f = self.fixture
        supervisor = f['sup_1']
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor, credito=10,
                                                                                   yapa=5, consumido=2,
                                                                                   fecha=timezone.now().date(),
                                                                                   calidades=[f['tipo_s'], ])
        p1 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], telefono='4789-9874', nombre='Un Nombre',
                               localidad='localidadtest')
        f1 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='provincia', selector='in', valor='localidadtest')
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f1, p1))

    def test_pedido_con_filtro_por_localidad_y_provincia_no_aplica_a_prospecto_con_campos_de_distinto_nombre(self):
        admin_de_pedidos = AdministradorDePedidos()
        f = self.fixture
        supervisor = f['sup_1']
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor, credito=10,
                                                                                   yapa=5, consumido=2,
                                                                                   fecha=timezone.now().date(),
                                                                                   calidades=[f['tipo_s'], ])
        p1 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], telefono='4789-9874', nombre='Un Nombre',
                               localidad='localidadtest', provincia='provinciatest')
        f1 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='provincia', selector='in', valor='localidaadtest')
        f2 = FiltrosDePedidoFactory(pedido=pedido, accion='E', campo='localidad', selector='in', valor='provinciaatest')
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f1, p1))
        self.assertFalse(admin_de_pedidos._filtro_aplica_a_prospecto(f2, p1))