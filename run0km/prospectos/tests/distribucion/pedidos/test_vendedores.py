from datetime import timedelta, date
from django.test.testcases import TestCase
from django.utils.timezone import now
from testing.factories import VendedoresFactory, UsersFactory, ConcesionariasFactory, PedidosDeProspectoFactory, \
    EquiposFactory


class VendedoresPedidoTest(TestCase):
    def un_supervisor(self, concesionaria):
        usuario_supervisor = UsersFactory(username='usuario_supervisor', first_name='sup',
                                          last_name='sup', last_login=now(),
                                          password='asd', email='<EMAIL>')
        supervisor = VendedoresFactory(user=usuario_supervisor, cargo='Supervisor', alerta_diaria=False,
                                       alerta_a_supervisor=False, concesionaria=concesionaria)
        return supervisor

    def supervisor_y_vendedores(self, cantidad):
        vendedores = []
        concesionaria = ConcesionariasFactory(nombre='conce', dia_inicio_periodos=28, dia_fin_periodos=20)
        supervisor = self.un_supervisor(concesionaria)

        for i in range(0, cantidad):
            usuario_vendedor = UsersFactory(username='usuario_vendedor' + str(i), first_name='vend' + str(i),
                                            last_name='vend' + str(i), last_login=now() + timedelta(days=(-1)*i),
                                            password='asd', email='<EMAIL>')
            vendedor = VendedoresFactory(user=usuario_vendedor, supervisor_id=supervisor.id,
                                         concesionaria=concesionaria)
            vendedores.append(vendedor)
        return supervisor, vendedores

    def ordenar_por_id(self, vendedores):
        return sorted(vendedores, key=lambda x: x.id)

    def test_pedido_para_todos_los_vendedores(self):
        supervisor, vendedores = self.supervisor_y_vendedores(3)
        pedido = PedidosDeProspectoFactory(supervisor=supervisor, credito=10, yapa=5, consumido=2,
                                           asignar_a='T', fecha=date.today())

        self.assertListEqual(self.ordenar_por_id(pedido.vendedores()), self.ordenar_por_id(vendedores))

    def test_pedido_para_nadie(self):
        supervisor, vendedores = self.supervisor_y_vendedores(3)
        pedido = PedidosDeProspectoFactory(supervisor=supervisor, credito=10, yapa=5, consumido=2,
                                           asignar_a='', fecha=date.today())

        self.assertEqual(len(pedido.vendedores()), 0)

    def test_pedido_para_vendedor(self):
        supervisor, vendedores = self.supervisor_y_vendedores(3)
        pedido = PedidosDeProspectoFactory(supervisor=supervisor, credito=10, yapa=5, consumido=2,
                                           asignar_a='V', fecha=date.today(), vendedor=vendedores[0])

        self.assertEqual(len(pedido.vendedores()), 1)
        self.assertIn(vendedores[0], pedido.vendedores())

    def test_pedido_para_equipo(self):
        concesionaria = ConcesionariasFactory(nombre='conce', dia_inicio_periodos=28, dia_fin_periodos=20)
        supervisor = self.un_supervisor(concesionaria)
        equipo1 = EquiposFactory(supervisor=supervisor, nombre='equipo1')
        equipo2 = EquiposFactory(supervisor=supervisor, nombre='equipo2')

        usuario_vendedor = UsersFactory(username='usuario_vendedor', first_name='vend',
                                        last_name='vend', last_login=now() + timedelta(days=(-1)),
                                        password='asd', email='<EMAIL>')
        vendedor = VendedoresFactory(user=usuario_vendedor, supervisor_id=supervisor.id,
                                     concesionaria=concesionaria, equipo=equipo1)
        usuario_vendedor2 = UsersFactory(username='usuario_vendedor2', first_name='vend',
                                         last_name='vend', last_login=now() + timedelta(days=(-1)),
                                         password='asd', email='<EMAIL>')
        vendedor2 = VendedoresFactory(user=usuario_vendedor2, supervisor_id=supervisor.id,
                                      concesionaria=concesionaria, equipo=equipo2)

        pedido = PedidosDeProspectoFactory(supervisor=supervisor, credito=10, yapa=5, consumido=2,
                                           asignar_a='E', fecha=date.today(), equipo=equipo1)

        self.assertEqual(len(pedido.vendedores()), 1)
        self.assertIn(vendedor, pedido.vendedores())

