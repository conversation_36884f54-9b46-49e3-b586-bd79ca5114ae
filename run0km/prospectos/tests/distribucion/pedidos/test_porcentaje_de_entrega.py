# coding=utf-8
from django.utils import timezone
from mock import patch

from prospectos.models import PedidoDeProspecto
from testing.base import BaseFixturedTest


def fake_datetime_klass(date_time):
    class FakeDateTime(timezone.datetime):
        @classmethod
        def now(cls, tz=None):
            return date_time

    return FakeDateTime


class PedidoDeProspectoPorcentajeEntregadoTest(BaseFixturedTest):
    """
        La clase no es de mi autoría, divide el archivo en un paquete.

        Refactor pendiente para mejorar legibilidad de los escenarios de tests
    """
    def test_porcentaje_de_entrega(self):
        f = self.fixture
        date_time = timezone.datetime(1, 4, 1)
        supervisor = f['sup_1']
        p1 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=30,
            yapa=0, consumido=5,
            fecha=date_time,
            calidades=[f['tipo_s'], ],
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        p2 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=30,
            yapa=0, consumido=10,
            fecha=date_time,
            calidades=[f['tipo_m'], ],
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        p3 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=30,
            yapa=0, consumido=15,
            fecha=date_time,
            calidades=[f['tipo_w'], ],
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        p4 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=30,
            yapa=0, consumido=20,
            fecha=date_time,
            calidades=[f['tipo_w'], ],
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        p5 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=30,
            yapa=0, consumido=30,
            fecha=date_time,
            calidades=[f['tipo_w'], ],
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)
        p6 = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=supervisor, credito=0,
            yapa=10, consumido=3,
            fecha=date_time,
            calidades=[f['tipo_w'], ],
            forma_de_entrega=PedidoDeProspecto.VIEJA_FORMA_DE_ENTREGA)

        patcher = patch('datetime.datetime', fake_datetime_klass(timezone.datetime(1, 4, 1)))
        patcher.start()
        self.assertAlmostEqual(p1.porcentaje_entregado(), 100 * 5.0 / 1)
        self.assertAlmostEqual(p2.porcentaje_entregado(), 100 * 10.0 / 1)
        self.assertAlmostEqual(p3.porcentaje_entregado(), 100 * 15.0 / 1)
        self.assertAlmostEqual(p4.porcentaje_entregado(), 100 * 20.0 / 1)
        self.assertAlmostEqual(p5.porcentaje_entregado(), 100 * 30.0 / 1)
        self.assertAlmostEqual(p6.porcentaje_entregado(), 100)
        patcher.stop()

        patcher = patch('datetime.datetime', fake_datetime_klass(timezone.datetime(1, 4, 10)))
        patcher.start()
        self.assertAlmostEqual(p1.porcentaje_entregado(), 100 * 5.0 / 10)
        self.assertAlmostEqual(p2.porcentaje_entregado(), 100 * 10.0 / 10)
        self.assertAlmostEqual(p3.porcentaje_entregado(), 100 * 15.0 / 10)
        self.assertAlmostEqual(p4.porcentaje_entregado(), 100 * 20.0 / 10)
        self.assertAlmostEqual(p5.porcentaje_entregado(), 100 * 30.0 / 10)
        self.assertAlmostEqual(p6.porcentaje_entregado(), 100)
        patcher.stop()

        patcher = patch('datetime.datetime', fake_datetime_klass(timezone.datetime(1, 4, 20)))
        patcher.start()
        self.assertAlmostEqual(p1.porcentaje_entregado(), 100 * 5.0 / 20)
        self.assertAlmostEqual(p2.porcentaje_entregado(), 100 * 10.0 / 20)
        self.assertAlmostEqual(p3.porcentaje_entregado(), 100 * 15.0 / 20)
        self.assertAlmostEqual(p4.porcentaje_entregado(), 100 * 20.0 / 20)
        self.assertAlmostEqual(p5.porcentaje_entregado(), 100 * 30.0 / 20)
        self.assertAlmostEqual(p6.porcentaje_entregado(), 100)
        patcher.stop()

        patcher = patch('datetime.datetime', fake_datetime_klass(timezone.datetime(1, 4, 30)))
        patcher.start()
        self.assertAlmostEqual(p1.porcentaje_entregado(), 100 * 5.0 / 30)
        self.assertAlmostEqual(p2.porcentaje_entregado(), 100 * 10.0 / 30)
        self.assertAlmostEqual(p3.porcentaje_entregado(), 100 * 15.0 / 30)
        self.assertAlmostEqual(p4.porcentaje_entregado(), 100 * 20.0 / 30)
        self.assertAlmostEqual(p5.porcentaje_entregado(), 100)
        self.assertAlmostEqual(p6.porcentaje_entregado(), 100)
        patcher.stop()