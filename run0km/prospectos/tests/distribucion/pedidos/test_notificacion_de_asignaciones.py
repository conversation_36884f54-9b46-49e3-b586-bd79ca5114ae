import mock
from django.test import override_settings

from notificaciones import FormaDeEnvioEmail, FormaDeEnvioWhatsapp
from occ.sms_estrategias_de_envio import DeliverySMS
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest, mock_on_commit
from prospectos.utils.notificaciones_de_prospectos import FormateadorDeProspecto


class NotificadorDeProspectosMock(object):
    def notificar_asignacion(self, prospecto, vendedor, formas_de_envio):
        pass


@override_settings(SINCRONIZACIONES=['occ.integracion.receptor.ReceptorDeNotificacionesParaIntegraciones'])
class NotificacionDeAsignacionesTest(PedidosTest):

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch("prospectos.utils.notificaciones_de_prospectos.NotificadorDeProspectos.notificar_asignacion",
                side_effect=NotificadorDeProspectosMock().notificar_asignacion)
    def test_vendedor_con_notificacion_habilitada_ante_asignacion_a_pedido_deberia_ser_notificado(
            self, servicio_de_notificacion_mock, mock_on_commit_func):
        self.gestor_de_vendedores.configurar_habilitacion_de_notificaciones(vendedor=self.vendedor_uno,
                                                                            notificaciones_habilitadas=True)
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno, credito=10,
                                                                             yapa=5, consumido=self.credito_consumido,
                                                                             campania=self.campania_uno)

        self.creador_de_contexto.agregar_configuracion_de_notificacion_a_pedido(
            pedido, por_mail=True, por_whatsapp=True)
        self.vendedor_uno.factor_de_asignacion = 1.
        self.vendedor_uno.save()

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id, a_vendedor=self.vendedor_uno,
            credito_consumido=self.credito_consumido + self.valor_de_categoria)

        servicio_de_notificacion_mock.assert_called_once_with(prospecto=prospecto,
                                                              vendedor=self.vendedor_uno,
                                                              formas_de_envio=[FormaDeEnvioEmail, FormaDeEnvioWhatsapp])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch("lib.smscover.SMSService.send", side_effect=lambda client_name, messages, lot_generator: None)
    def test_vendedor_con_notificacion_via_sms_ante_asignacion_a_pedido_deberia_enviarse_sms(
            self, mock_sms_service, mock_on_commit_func):
        self.gestor_de_vendedores.configurar_habilitacion_de_notificaciones(vendedor=self.vendedor_uno,
                                                                            notificaciones_habilitadas=True)
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno, credito=10,
                                                                             yapa=5, consumido=self.credito_consumido,
                                                                             campania=self.campania_uno)

        self.creador_de_contexto.agregar_configuracion_de_notificacion_a_pedido(pedido, por_sms=True)
        self.vendedor_uno.celular = '1112341234'
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=self.vendedor_uno,
                                                                     factor_de_asignacion=1)

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id,
            a_vendedor=self.vendedor_uno,
            credito_consumido=self.credito_consumido + self.valor_de_categoria)

        self._assert_envio_de_sms(mock_sms_service, self.vendedor_uno, prospecto)

    @mock.patch("prospectos.utils.notificaciones_de_prospectos.NotificadorDeProspectos.notificar_asignacion",
                side_effect=NotificadorDeProspectosMock().notificar_asignacion)
    def test_vendedor_con_notificacion_deshabilitada_ante_asignacion_a_pedido_no_deberia_ser_notificado(
            self, servicio_de_notificacion_mock):
        self.gestor_de_vendedores.configurar_habilitacion_de_notificaciones(vendedor=self.vendedor_uno,
                                                                            notificaciones_habilitadas=False)
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno, credito=10,
                                                                             yapa=5, consumido=self.credito_consumido,
                                                                             campania=self.campania_uno)

        self.creador_de_contexto.agregar_configuracion_de_notificacion_a_pedido(pedido, por_mail=True,
                                                                                por_whatsapp=True)
        self.vendedor_uno.factor_de_asignacion = 1.
        self.vendedor_uno.save()

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id,
            a_vendedor=self.vendedor_uno,
            credito_consumido=self.credito_consumido + self.valor_de_categoria)

        self.assertFalse(servicio_de_notificacion_mock.called)

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch("prospectos.utils.notificaciones_de_prospectos.NotificadorDeProspectos.notificar_asignacion",
                side_effect=NotificadorDeProspectosMock().notificar_asignacion)
    def test_supervisor_con_notificacion_habilitada_ante_asignacion_como_responsable_deberia_ser_notificado(
            self, servicio_de_notificacion_mock, mock_on_commit_func):
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(campania=self.campania_uno,
                                                                            consumido=self.credito_consumido, )
        self.creador_de_contexto.agregar_configuracion_de_notificacion_a_pedido(pedido, por_mail=True,
                                                                                por_whatsapp=True)
        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id,
            a_vendedor=None, a_responsable=self.supervisor,
            credito_consumido=self.credito_consumido + self.valor_de_categoria)

        servicio_de_notificacion_mock.assert_called_once_with(prospecto=prospecto,
                                                              vendedor=self.supervisor,
                                                              formas_de_envio=[FormaDeEnvioEmail, FormaDeEnvioWhatsapp])

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @mock.patch("prospectos.utils.notificaciones_de_prospectos.NotificadorDeProspectos.notificar_asignacion",
                side_effect=NotificadorDeProspectosMock().notificar_asignacion)
    def test_supervisor_con_notificacion_deshabilitada_ante_asignacion_como_responsable_no_deberia_ser_notificado(
            self, servicio_de_notificacion_mock, mock_on_commit_func):
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(campania=self.campania_uno,
                                                                            consumido=self.credito_consumido, )
        self.creador_de_contexto.agregar_configuracion_de_notificacion_a_pedido(pedido, por_mail=True,
                                                                                por_whatsapp=True)
        self.gestor_de_vendedores.configurar_habilitacion_de_notificaciones(vendedor=self.supervisor,
                                                                            notificaciones_habilitadas=False)
        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id,
            a_vendedor=None, a_responsable=self.supervisor,
            credito_consumido=self.credito_consumido + self.valor_de_categoria)
        self.assertFalse(servicio_de_notificacion_mock.called)

    # Asserts

    def _assert_envio_de_sms(self, mock_sms_service, vendedor, prospecto):
        mock_sms_service.assert_called_once()
        arguments = self._mock_sms_service_arguments(mock_sms_service)
        self.assertEqual(DeliverySMS.POR_DEFECTO, arguments.get('client_name'))
        mensajes = arguments.get('messages')
        self.assertEqual(len(mensajes), 1)
        mensaje = mensajes[0]
        self.assertEqual(mensaje.phone_number, vendedor.celular)
        texto_esperado = self._texto_para_sms_para(prospecto, vendedor)
        self.assertEqual(mensaje.get_message(), texto_esperado)

    # Helpers

    def _texto_para_sms_para(self, prospecto, vendedor):
        formateador = FormateadorDeProspecto()
        texto = formateador.texto_para_sms(prospecto, vendedor)
        return texto

    def _mock_sms_service_arguments(self, mock_sms_service):
        return mock_sms_service.call_args[1]