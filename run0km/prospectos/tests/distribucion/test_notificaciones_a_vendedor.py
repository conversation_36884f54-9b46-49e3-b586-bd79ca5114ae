# coding=utf-8
import mock
from django.test import override_settings
from django.utils import timezone
from freezegun import freeze_time

from campanias.models import Campania
from occ.tests.servicio_de_notificacion_mock import ServicioDeComunicacionSuccessMock
from prospectos.models.carga import CargadorDeProspectos
from prospectos.tests.distribucion.ingresos.control_del_tiempo import ControladorDelTiempo
from prospectos.tests.distribucion.pedidos.test_pedidos_core import mock_on_commit
from prospectos.tests.distribucion.test_repartidor import RepartidorDeProspectosTest


@freeze_time("2016-07-10 13:21:34")
@override_settings(
    SINCRONIZACIONES=['prospectos.models.entrega_de_datos.notificaciones.ReceptorDeNotificacionesAVendedor'])
@mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
class NotificacionesDeAsignacionTest(RepartidorDeProspectosTest):
    def setUp(self):
        super(NotificacionesDeAsignacionTest, self).setUp()
        self.vendedor = self._agregar_vendedor_a_(self.supervisor_uno)
        self.generica_S = Campania.objects.get(nombre='Genérica SMS')

    @mock.patch("occ.servicio_de_chat_de_ventas.ServicioDeNotificaciones.enviar_prospectos_asignado",
                side_effect=ServicioDeComunicacionSuccessMock().enviar_prospectos_asignado)
    def test_cuando_vendedor_recibe_un_prospecto_es_enviada_la_notificacion(
            self, mock_enviar_prospecto_asignado, mock_on_commit):
        # Dado
        prospecto = self._crear_nuevo_prospecto(supervisor=self.supervisor_uno)

        # Cuando
        self.repartidor.asignar_prospecto_a(vendedor=self.vendedor, prospecto=prospecto)

        # Entonces
        self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id, a_vendedor=self.vendedor)
        mock_enviar_prospecto_asignado.assert_called_once()

    @mock.patch("occ.servicio_de_chat_de_ventas.ServicioDeNotificaciones.enviar_prospectos_asignado",
                side_effect=ServicioDeComunicacionSuccessMock().enviar_prospectos_asignado)
    def test_cuando_vendedor_recibe_varios_prospectos_es_enviada_la_notificacion(
            self, mock_enviar_prospecto_asignado, mock_on_commit):
        # Dado
        prospectos = self._prospectos_nuevos(cantidad=3)

        # Cuando
        self.repartidor.asignar_prospectos_a(vendedor=self.vendedor, prospectos=prospectos)

        # Entonces
        for prospecto in prospectos:
            self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id, a_vendedor=self.vendedor)
        mock_enviar_prospecto_asignado.assert_called_once()

    @mock.patch("occ.servicio_de_chat_de_ventas.ServicioDeNotificaciones.enviar_prospectos_asignado",
                side_effect=ServicioDeComunicacionSuccessMock().enviar_prospectos_asignado)
    def test_repetido_mergeado_con_el_original_no_es_enviada_la_notificacion(
            self, mock_enviar_prospecto_asignado, mock_on_commit):
        # Dado
        generales = {}
        telefono = '48957689'
        cargador = CargadorDeProspectos()
        with freeze_time(timezone.now()) as tiempo_frizado:
            cargador.cargar_prospecto(
                generales, {'email': '', 'telefono': telefono, 'vendedor': self.vendedor.username(),
                            'campania': self.generica_S.obtener_nombre()}, {},
                codigo_de_tipo_de_origen=self.generica_S.obtener_tipo_de_origen().codigo)
            email = '<EMAIL>'
            ip = '0.0.0.0'
            campo_extra = 'un campo extra'

            # Cuando
            ControladorDelTiempo.nuevo().avanzar_tiempo_la_mitad_del_gap(tiempo_frizado)
            resultado_duplicado = cargador.cargar_prospecto(
                generales, {'email': email, 'telefono': telefono, 'campania': self.generica_S.obtener_nombre()},
                [{'nombre': 'ip', 'valor': ip}, {'nombre': 'campo_extra', 'valor': campo_extra}],
                codigo_de_tipo_de_origen=self.generica_S.obtener_tipo_de_origen().codigo
            )

        # Entonces
        self.assertTrue(resultado_duplicado.fue_mergeado())
        self.assertEqual(mock_enviar_prospecto_asignado.call_count, 1)
