from datetime import timedelta
from freezegun.api import freeze_time
from core.models import Sistema
from prospectos.models import Prospecto
from prospectos.models.entrega_de_datos.cola_de_peticion_de_prospectos_de_vendedor import \
    ColaDePeticionDeProspectoDeVendedor
from prospectos.models.permiso_pedir_prospecto_segun_disponibilidad import PermisoPedirProspectoSegunDisponibilidad
from prospectos.tests.distribucion.peticion_de_prospectos_por_parte_de_vendedor.notificador_de_resultado_de_entrega_simulado import \
    NotificadorDeResultadoDeEntregaSimulado
from testing.base import BaseFixturedTest
from testing.test_utils import reload_model


class ColaDePeticionesDeProspectoDeVendedorTest(BaseFixturedTest):
    """
    TODO: Cosas pendientes
    1. Validar pedir_prospecto asegura que valida el vendedor.
        - Verificar caso de que pida un vendedor habilitado, esté el pedido encolado
        - Hay que validar que el vendedor esté logueado (Hablar con Nico)
    """

    def setUp(self):
        super().setUp()
        Prospecto.objects.all().delete()
        self.vendedor_uno = self.fixture['vend_1']
        self.vendedor_dos = self.fixture['vend_2']
        self.campania_uno = self.fixture['camp_1']
        self.campania_dos = self.fixture['camp_2']

        self.tamanio_de_cola = 20

        self.vendedor_uno.configuracion_servicios.habilitar_pedir_prospecto()
        self.vendedor_uno.obtener_concesionaria().configuracion_servicios.habilitar_pedir_prospecto()

        self.vendedor_dos.configuracion_servicios.habilitar_pedir_prospecto()
        self.vendedor_dos.obtener_concesionaria().configuracion_servicios.habilitar_pedir_prospecto()

        self._habilitar_permiso_pedir_prospecto_segun_disponibilidad_al_vendedor_uno()


    def test_cuando_no_hay_peticion_a_procesar_se_lanza_un_error(self):
        # Dado
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo = self.tamanio_de_cola,
            notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo())

        # Cuando y Entonces
        self.assertRaisesMessage(ValueError, 'No hay pedidos de prospecto para procesar', cola_de_peticion.procesar)
        self.assertTrue(cola_de_peticion.esta_vacia())

    def test_al_agregar_un_peticion_de_prospecto_de_vendedor_la_cola_deja_de_estar_vacia(self):
        # Dado
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo = self.tamanio_de_cola,
            notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo())

        # Cuando
        cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)

        # Entonces
        self.assertFalse(cola_de_peticion.esta_vacia())

    def test_al_procesar_un_peticion_se_decrementa_la_cola(self):
        # Dado
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo = self.tamanio_de_cola,
            notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo())
        cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno)
        self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)

        # Cuando
        cola_de_peticion.procesar()

        # Entonces
        self.assertTrue(cola_de_peticion.esta_vacia())

    def test_al_procesar_un_peticion_se_intenta_entregar_el_prospecto(self):
        # Dado
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
                tamanio_maximo = self.tamanio_de_cola,
                notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo())
            cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)
            self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno)
            prospecto_a_entregar = self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)
            tiempo_freezado.tick(self._tiempo_de_antiguedad_minimo() + timedelta(seconds=1))

            # Cuando
            cola_de_peticion.procesar()

            # Entonces
            self.assertTrue(cola_de_peticion.esta_vacia())
            prospecto_a_entregar = reload_model(prospecto_a_entregar)
            self.assertEqual(prospecto_a_entregar.obtener_vendedor(), self.vendedor_uno)

    def test_cuando_el_vendedor_ya_tiene_un_peticion_no_puede_agregar_otro(self):
        # Dado
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo=self.tamanio_de_cola,
            notificador_de_resultado=NotificadorDeResultadoDeEntregaSimulado.nuevo())
        cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)

        # Cuando y Entonces
        self.assertRaisesMessage(ValueError,
                                 f'Ya existe un pedido para el vendedor {self.vendedor_uno}',
                                 cola_de_peticion.agregar_peticion_de_prospecto_para,
                                 vendedor=self.vendedor_uno)

    def test_cuando_la_cola_supera_el_limite_maximo_de_peticiones_no_puede_agregar_una_nueva_peticion(self):
        # Dado
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(1,
                                                                        NotificadorDeResultadoDeEntregaSimulado.nuevo())
        cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)

        # Cuando y Entonces
        self.assertRaisesMessage(ValueError,
                                 'La cantidad de pedidos supera el límite máximo',
                                 cola_de_peticion.agregar_peticion_de_prospecto_para,
                                 vendedor=self.vendedor_dos)

    def test_el_orden_de_entrega_respeta_el_orden_de_la_cola(self):
        # Dado
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
                tamanio_maximo = self.tamanio_de_cola,
                notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo())
            self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno)
            self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_dos)
            prospecto_a_entregar = self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)

            cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)
            cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_dos)
            tiempo_freezado.tick(self._tiempo_de_antiguedad_minimo() + timedelta(seconds=1))

            # Cuando
            cola_de_peticion.procesar()

            # Entonces
            self.assertEqual(cola_de_peticion.cantidad_de_peticiones(), 1)
            prospecto_a_entregar = reload_model(prospecto_a_entregar)
            self.assertEqual(prospecto_a_entregar.obtener_vendedor(), self.vendedor_uno)

    def test_cuando_la_peticion_de_entrega_no_es_satisfecho_no_hace_la_entrega_y_remueve_la_peticion(self):
        # Dado
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo = self.tamanio_de_cola,
            notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo())
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            vendedor=self.vendedor_uno, campania=self.campania_dos)
        prospecto_a_entregar = self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)

        cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)

        # Cuando
        cola_de_peticion.procesar()

        # Entonces
        self.assertTrue(cola_de_peticion.esta_vacia())
        self.assertIsNone(prospecto_a_entregar.obtener_vendedor())

    def test_cuando_no_se_puede_satisfacer_la_peticion_se_notifica_el_resultado(self):
        # Dado
        notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo()
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo=self.tamanio_de_cola,
            notificador_de_resultado=notificador_de_resultado
        )
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            vendedor=self.vendedor_uno, campania=self.campania_dos)
        self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)

        cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)

        # Cuando
        cola_de_peticion.procesar()

        # Entonces
        self.assertTrue(
            notificador_de_resultado.ha_sido_notificado_de_entrega_no_realizada(self.vendedor_uno, 'No hay prospectos disponibles'),
            notificador_de_resultado.notificaciones_de_entregas_no_realizadas()
        )

    def test_cuando_satisface_la_peticion_se_notifica_el_resultado(self):
        # Dado
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo()
            cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
                tamanio_maximo=self.tamanio_de_cola,
                notificador_de_resultado=notificador_de_resultado
            )
            cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)
            self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno)
            self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)
            tiempo_freezado.tick(self._tiempo_de_antiguedad_minimo() + timedelta(seconds=1))

            # Cuando
            cola_de_peticion.procesar()

            # Entonces
            self.assertTrue(
                notificador_de_resultado.ha_sido_notificado_de_entrega_realizada_a(self.vendedor_uno),
                notificador_de_resultado.notificaciones_notificaciones_de_entregas_realizadas()
            )

    def test_todas_peticiones_consecutivas_se_remueven_de_la_cola_al_ser_procesadas(self):
        # Dado
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno)
            notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo()
            cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
                tamanio_maximo=self.tamanio_de_cola,
                notificador_de_resultado=notificador_de_resultado
            )
            cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)
            self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)
            self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)
            tiempo_freezado.tick(self._tiempo_de_antiguedad_minimo() + timedelta(seconds=1))
            cola_de_peticion.procesar()
            cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)


            # Cuando

            cola_de_peticion.procesar()

            # Entonces
            self.assertEquals(
                notificador_de_resultado.cantidad_de_notificaciones_de_entregas_realizadas_a(self.vendedor_uno),
                2)
            self.assertTrue(cola_de_peticion.esta_vacia())

    def test_cuando_se_genera_una_peticion_debe_quedar_pendiente(self):
        # Dado
        notificador_de_resultado = NotificadorDeResultadoDeEntregaSimulado.nuevo()
        cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
            tamanio_maximo=self.tamanio_de_cola,
            notificador_de_resultado=NotificadorDeResultadoDeEntregaSimulado.nuevo()
        )
        cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)

        # Cuando / Entonces
        self.assertEquals(cola_de_peticion.cantidad_de_peticiones(), 1)

    def test_despues_de_pedir_prospecto_y_no_fue_entregado_queda_inhabilitado_al_pedir_nuevamente_lanza_error(self):
        # Dado
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno)
            cola_de_peticion = ColaDePeticionDeProspectoDeVendedor.nuevo_con(
                tamanio_maximo=self.tamanio_de_cola,
                notificador_de_resultado=NotificadorDeResultadoDeEntregaSimulado.nuevo()
            )
            cola_de_peticion.agregar_peticion_de_prospecto_para(self.vendedor_uno)
            cola_de_peticion.procesar()
            tiempo_freezado.tick(self._tiempo_de_inhabilitacion_pedir_prospecto())

            # Cuando / Entonces
            self.assertRaisesMessage(ValueError,
                                     'No es posible hacer otra solicitud en este momento',
                                     cola_de_peticion.agregar_peticion_de_prospecto_para,
                                     vendedor=self.vendedor_uno)

    def _tiempo_de_antiguedad_minimo(self):
        tiempo_de_antiguedad_minimo_para_prospectos_disponibles = Sistema.instance().tiempo_de_antiguedad_minimo_para_prospectos_disponibles
        return timedelta(minutes=tiempo_de_antiguedad_minimo_para_prospectos_disponibles)

    def _tiempo_de_inhabilitacion_pedir_prospecto(self):
        tiempo_de_inhabilitacion_para_pedir_prospectos = Sistema.instance().tiempo_de_inhabilitacion_para_pedir_prospectos
        return timedelta(minutes=tiempo_de_inhabilitacion_para_pedir_prospectos)

    def _habilitar_permiso_pedir_prospecto_segun_disponibilidad_al_vendedor_uno(self):
        permiso = PermisoPedirProspectoSegunDisponibilidad(_vendedor=self.vendedor_uno)
        permiso.modificar_disponibilidad_de_prospecto(
            tiene_disponibilidad=True)
