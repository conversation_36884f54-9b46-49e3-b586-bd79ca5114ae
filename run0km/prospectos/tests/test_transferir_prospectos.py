from django.core.exceptions import ValidationError
from django.urls import reverse
from freezegun import freeze_time
from rest_framework.test import APIClient
from rest_framework import status

from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.base import BaseFixturedTest
from prospectos.models import Prospecto
from vendedores.models import Vendedor, PermisoTransferencia

class TransferenciaDeProspectosTest(BaseFixturedTest):
    def setUp(self):
        super().setUp()
        self.client = APIClient()

        # Fixtures
        self.vendedor_origen = self.fixture['vend_1']
        self.vendedor_destino = self.fixture['vend_2']
        self.prospecto = self.fixture['p_1']

        self.vendedor_origen.concesionaria.configuracion_de_servicios().permitir_transferencia_de_prospectos()

        # Usuario para autenticación
        self.user = self.vendedor_origen.user
        self.login_and_assert_correct(self.user)

        # Crear permisos de transferencia
        permiso = PermisoTransferencia.objects.create(vendedor=self.vendedor_origen)
        permiso.vendedores_permitidos.add(self.vendedor_destino)

        # Obtener la URL
        self.url_transferencia = reverse('transferir-prospecto')

    def test_transferencia_exitosa(self):
        """
        Caso positivo: Transferencia de prospecto exitosa.
        """
        data = {
            'prospecto_id': self.prospecto.id,
            'vendedor_destinatario_id': self.vendedor_destino.id,
        }

        response = self.client.post(self.url_transferencia, data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.prospecto.refresh_from_db()
        self.assertEqual(self.prospecto.vendedor, self.vendedor_destino)
        self.assertEqual(response.json()['message'], "Prospecto transferido exitosamente.")

    def test_transferencia_sin_permiso(self):
        """
        Caso negativo: El vendedor origen no tiene permiso para transferir al vendedor destino.
        """
        PermisoTransferencia.objects.filter(vendedor=self.vendedor_origen).delete()

        data = {
            'prospecto_id': self.prospecto.id,
            'vendedor_destinatario_id': self.vendedor_destino.id,
        }

        response = self.client.post(self.url_transferencia, data=data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("permission", response.json()['detail'])

    def test_transferencia_prospecto_no_existente(self):
        """
        Caso negativo: Intentar transferir un prospecto que no existe.
        """
        data = {
            'prospecto_id': 9999,
            'vendedor_destinatario_id': self.vendedor_destino.id,
        }

        response = self.client.post(self.url_transferencia, data=data)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("Prospecto no encontrado", response.json()['message'])

    def test_transferencia_vendedor_destinatario_no_existente(self):
        """
        Caso negativo: El vendedor destinatario no existe.
        """
        #TODO el vendedor destinatario id tiene un vendedor que no existe pero que tampoco esta en el permiso de
        # transferencia, habria que hacer un vendedor, añadirlo al permiso, y eliminarlo
        data = {
            'prospecto_id': self.prospecto.id,
            'vendedor_destinatario_id': 9999,
        }

        response = self.client.post(self.url_transferencia, data=data)

        #Estaria bueno usar estos mensajes en vez del 403 que largo ahora
        # self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        # self.assertIn("Vendedor destinatario no encontrado", response.json()['message'])
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("You do not have permission to perform this action", response.json()['detail'])

    def test_transferencia_datos_faltantes(self):
        """
        Caso negativo: Faltan datos en la solicitud.
        """
        data = {
            'prospecto_id': self.prospecto.id,
        }

        response = self.client.post(self.url_transferencia, data=data)

        #el CanTransferProspectPermission tiene lo siguiente
        # vendedor_destinatario_id = request.data.get('vendedor_destinatario_id')
        # if not vendedor_destinatario_id:
        #     return False

        #como no se manda vendedor_destinatario da que no tiene permiso y no se puede usar 400 bad request
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("You do not have permission to perform this action", response.json()['detail'])

    def test_transferencia_prospecto_no_asignado_al_vendedor_origen(self):
        """
        Caso negativo: Intentar transferir un prospecto que no está asignado al vendedor origen.
        """
        self.prospecto.vendedor = self.vendedor_destino
        self.prospecto.save()

        data = {
            'prospecto_id': self.prospecto.id,
            'vendedor_destinatario_id': self.vendedor_destino.id,
        }

        response = self.client.post(self.url_transferencia, data=data)

        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertIn("El vendedor o gerente no tiene a cargo el prospecto.", response.json()['message'])

    def test_transferencia_sin_autenticacion(self):
        """
        Caso negativo: Intentar transferir sin autenticación.
        """
        self.client.logout()

        data = {
            'prospecto_id': self.prospecto.id,
            'vendedor_destinatario_id': self.vendedor_destino.id,
        }

        response = self.client.post(self.url_transferencia, data=data)

        self.assertEqual(response.status_code, status.HTTP_302_FOUND)
        self.assertRedirects(response, '/login/?next=/prospectos/transferir-prospecto/', status_code=302,
        target_status_code=200, fetch_redirect_response=True)

    #TODO agregar caso de test donde apago el permitir transferencia de prospecto desde la concesionaria

    def test_concesionaria_tiene_deshabilitado_permitir_transferencia_de_prospectos(self):
        """
        Caso negativo: La concesionaria de vendedor origen no tiene permiso para transferir prospectos.
        """
        self.vendedor_origen.concesionaria.configuracion_de_servicios().deshabilitar_transferencia_de_prospectos()
        data = {
            'prospecto_id': self.prospecto.id,
            'vendedor_destinatario_id': self.vendedor_destino.id,
        }

        response = self.client.post(self.url_transferencia, data=data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("Actualmente no tiene permiso de transferir prospecto.", response.json()['message'])

    @freeze_time("2025-01-31 13:21:34")
    def test_fecha_de_asignacion_al_vendedor_se_reinicia_al_transferir(self):
        #Dado
        gestor = GestorDeProspecto.nuevo_para(self.vendedor_origen)

        #Cuando
        gestor.transferir_prospecto_entre_vendedores(self.prospecto, self.vendedor_destino)

        #Entonces
        from django.utils import timezone
        self.assertEqual(self.prospecto.fecha_de_asignacion_a_vendedor(), timezone.now())