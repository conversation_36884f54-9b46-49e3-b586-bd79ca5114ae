from django.utils import timezone
from testing.base import BaseLoggedTest, loadResponseJsonAndCheckStatus
from testing.factories import ProspectosFactory


class FiltrosProspectosTest(BaseLoggedTest):

    def setUp(self):
        super(FiltrosProspectosTest, self).setUp()
        self.vendedor.prospectos.all().delete()
        self.p3 = ProspectosFactory(nombre='3', campania=self.fixture['camp_1'], vendedor=self.vendedor)
        self.p1 = ProspectosFactory(nombre='1', campania=self.fixture['camp_1'], vendedor=self.vendedor)
        self.p2 = ProspectosFactory(nombre='2', campania=self.fixture['camp_1'], vendedor=self.vendedor)

    def assert_esta_primero(self, response_msg, id_primero, id_segundo):
        self.assertIn('prospectos/%s' % id_primero, response_msg['content'])
        self.assertIn('prospectos/%s' % id_segundo, response_msg['content'])
        primero = response_msg['content'].index('prospectos/%s' % id_primero)
        segundo = response_msg['content'].index('prospectos/%s' % id_segundo)
        self.assertLess(primero, segundo)

    def filtrar(self, filter_llamado, ordenamiento, fecha=None):
        filtro_dia = fecha.strftime('%Y-%m-%d') if fecha else None
        response = self.client.get('/prospectos/filtro_prospectos/', {"tipo_origen": "S",
                                                                      "cantidad": '10',
                                                                      "filter_llamado": filter_llamado,
                                                                      "ordenamiento": ordenamiento,
                                                                      "filtro_dia": filtro_dia})
        response_msg = loadResponseJsonAndCheckStatus(self, response)
        return response_msg

    def programar_llamado(self, id_prospecto, fecha):
        response = self.client.post('/prospectos/nuevo-llamado/%s/0/' % id_prospecto,
                                   {"fecha": fecha.strftime('%Y-%m-%d %H:%M')})
        loadResponseJsonAndCheckStatus(self, response)

    def test_filtrar_llamados_vencidos(self):
        hoy = timezone.localtime(timezone.now())
        self.programar_llamado(self.p1.id, hoy - timezone.timedelta(minutes=3))  # Viejo
        self.programar_llamado(self.p3.id, hoy - timezone.timedelta(minutes=2))  # intermedio
        self.programar_llamado(self.p2.id, hoy - timezone.timedelta(minutes=1))  # Nuevo
        response_msg = self.filtrar('vencidos', '0')  # Mas viejos arriba

        self.assertTrue('cantidad_total' in response_msg)
        self.assertEqual(response_msg['cantidad_total'], 3)
        self.assert_esta_primero(response_msg, self.p1.id, self.p3.id)
        self.assert_esta_primero(response_msg, self.p3.id, self.p2.id)

        response_msg = self.filtrar('vencidos', '1')  # Mas viejos arriba
        self.assert_esta_primero(response_msg, self.p2.id, self.p3.id)
        self.assert_esta_primero(response_msg, self.p3.id, self.p1.id)

    def test_filtrar_llamados_por_dia(self):
        hace_unas_horas = timezone.localtime(timezone.now()) - timezone.timedelta(hours=5)
        self.programar_llamado(self.p1.id, hace_unas_horas)                           # Viejo
        self.programar_llamado(self.p3.id, hace_unas_horas + timezone.timedelta(minutes=1))    # Intermedio
        self.programar_llamado(self.p2.id, hace_unas_horas + timezone.timedelta(minutes=2))    # Nuevo
        response_msg = self.filtrar('dia', '0', hace_unas_horas)  # Mas viejos arriba
        self.assertTrue('cantidad_total' in response_msg)
        self.assertEqual(response_msg['cantidad_total'], 3)
        self.assert_esta_primero(response_msg, self.p1.id, self.p3.id)
        self.assert_esta_primero(response_msg, self.p3.id, self.p2.id)

        response_msg = self.filtrar('dia', '1', hace_unas_horas)  # Mas nuevos arriba
        self.assert_esta_primero(response_msg, self.p2.id, self.p3.id)
        self.assert_esta_primero(response_msg, self.p3.id, self.p1.id)

    def test_filtrar_llamados_por_futuros(self):
        self.programar_llamado(self.p1.id, timezone.now() + timezone.timedelta(days=2))  # Viejo
        self.programar_llamado(self.p3.id, timezone.now() + timezone.timedelta(days=3))  # Intermedio
        self.programar_llamado(self.p2.id, timezone.now() + timezone.timedelta(days=4))  # Nuevo
        response_msg = self.filtrar('futuros', '0')  # Mas viejos arriba
        self.assertTrue('cantidad_total' in response_msg)
        self.assertEqual(response_msg['cantidad_total'], 3)
        self.assert_esta_primero(response_msg, self.p1.id, self.p3.id)
        self.assert_esta_primero(response_msg, self.p3.id, self.p2.id)

        response_msg = self.filtrar('futuros', '1')  # Mas nuevos arriba
        self.assert_esta_primero(response_msg, self.p2.id, self.p3.id)
        self.assert_esta_primero(response_msg, self.p3.id, self.p1.id)
