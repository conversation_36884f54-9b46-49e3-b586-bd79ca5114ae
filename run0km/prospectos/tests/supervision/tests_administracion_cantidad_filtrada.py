# coding=utf-8

from django.urls import reverse
from django.utils.timezone import now, timedelta, make_aware, get_current_timezone, datetime

from occ.models import CampaniaDeComunicacion
from prospectos.models import <PERSON>a
from testing.base import BaseLoggedSupervisorTest, loadResponseJsonAndCheckStatus
from testing.factories import ProspectosFactory, LlamadosFactory, FinalizacionesFactory, VentasFactory, \
    TagsFactory


class CantidadFiltradaTest(BaseLoggedSupervisorTest):

    def setUp(self):
        super(CantidadFiltradaTest, self).setUp()
        self.url = reverse('cantidad-filtrada')
        f = self.fixture

        hace_2_dias = now() + timedelta(days=-2)
        hace_2_dias = make_aware(datetime(hace_2_dias.year, hace_2_dias.month, hace_2_dias.day), get_current_timezone())

        hoy = now()
        hoy = make_aware(datetime(hoy.year, hoy.month, hoy.day), get_current_timezone())

        self.marca_blanca = Marca.blanca()
        self.marca_uno = Marca.obtener_or_crear_con_nombre('m1')
        self.marca_dos = Marca.obtener_or_crear_con_nombre('m2')
        self.marca_tres = Marca.obtener_or_crear_con_nombre('m3')
        p1 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['vend_2'],
                               exportado=True, estado='P', marca=self.marca_uno.nombre())
        p2 = ProspectosFactory(campania=f['camp_2'], responsable=f['sup_1'], vendedor=f['vend_1'],
                               exportado=True, estado='P', marca=self.marca_uno.nombre())
        p3 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['vend_1'], estado='V',
                               marca=self.marca_uno.nombre(), prefijo='00111')
        VentasFactory(prospecto=p3, vendedor=f['vend_1'], fecha_de_realizacion=hoy, precio=0)
        p4 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'], estado='P',
                               marca=self.marca_dos.nombre(), prefijo='00111')
        p5 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'], estado='F',
                               prefijo='00222', provincia='p2')

        tag1 = TagsFactory(vendedor=f['vend_2'], nombre='t1')
        tag1.prospectos.add(p1)

        tag2 = TagsFactory(vendedor=f['vend_1'], nombre='t1')
        tag2.prospectos.add(p2)

        FinalizacionesFactory(prospecto=p5, vendedor=f['sup_1'], motivo=f['motivo_1'])

        ProspectosFactory(campania=f['camp_1'], vendedor=f['sup_1'], marca=self.marca_tres.nombre())
        ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'])
        ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'], provincia='p1')

        LlamadosFactory(vendedor=f['vend_2'], fecha=hace_2_dias, prospecto=p1, datetime=hace_2_dias)
        LlamadosFactory(vendedor=f['vend_1'], fecha=hoy, prospecto=p2, datetime=hace_2_dias)
        LlamadosFactory(vendedor=f['sup_1'], fecha=hace_2_dias, prospecto=p4, datetime=hace_2_dias)

        # Prospectos extras con otro supervisor como responsable (no deben afectar)
        p1 = ProspectosFactory(responsable=f['sup_2'], vendedor=f['vend_2'], campania=f['camp_1'])
        p2 = ProspectosFactory(campania=f['camp_2'], responsable=f['sup_2'], vendedor=f['vend_1'])
        p3 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_2'], vendedor=f['vend_1'], estado='V')
        VentasFactory(prospecto=p3, vendedor=f['vend_1'], fecha_de_realizacion=hoy, precio=0)
        p4 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_2'], vendedor=f['sup_2'])
        p5 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_2'], vendedor=f['sup_2'], estado='F')
        FinalizacionesFactory(prospecto=p5, vendedor=f['sup_1'], motivo=f['motivo_1'])
        ProspectosFactory(campania=f['camp_1'], vendedor=f['sup_2'])
        ProspectosFactory(campania=f['camp_1'], responsable=f['sup_2'])

        LlamadosFactory(vendedor=f['vend_2'], fecha=hace_2_dias, prospecto=p1, datetime=hace_2_dias)
        LlamadosFactory(vendedor=f['vend_1'], fecha=hoy, prospecto=p2, datetime=hace_2_dias)
        LlamadosFactory(vendedor=f['sup_1'], fecha=hace_2_dias, prospecto=p4, datetime=hace_2_dias)

        self.idv = self._id_de_vendedores_a_cargo_bajo(f['sup_1'])  # [vend_1,vend_2,vend_5,sup_1]

    def _id_de_vendedores_a_cargo_bajo(self, supervisor):
        vendedores_ids = [str(x) for x in supervisor.vendedores.values_list('pk', flat=True)]
        vendedores_ids.append(str(supervisor.id))
        return vendedores_ids

    def test_por_cantidad(self):
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': ''}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 6)

    def test_por_tipo_origen(self):
        params = {'tipo_origen': 'S', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': ''}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 5)

        params = {'tipo_origen': 'M', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': ''}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

    def test_por_campania(self):
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': self.fixture['camp_1'].id}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 5)

        params = {'tipo_origen': 'M', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': self.fixture['camp_2'].id}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

    def test_por_llamado(self):
        # Cuento los anteriores al dia de maniana.
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': 'vencidos', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': ''}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('total_prospectos' in msg)
        self.assertEqual(msg['total_prospectos'], 3)

        # Cuento los del dia de hoy en adelante.
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': 'futuros', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': ''}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

    def test_por_estado(self):
        params = {'tipo_origen': '', 'filter_estado': 'activo', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 4)

        params = {'tipo_origen': '', 'filter_estado': 'P', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 3)

        params = {'tipo_origen': '', 'filter_estado': 'V', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

        params = {'tipo_origen': '', 'filter_estado': 'F', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

        params = {'tipo_origen': '', 'filter_estado': 'no_vendido', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 5)

    def test_por_estado_nuevo(self):
        f = self.fixture

        hace_2_dias = now() + timedelta(days=-2)
        hace_3_dias = now() + timedelta(days=-3)
        hace_4_dias = now() + timedelta(days=-4)
        hace_5_dias = now() + timedelta(days=-5)
        hace_10_dias = now() + timedelta(days=-10)
        hace_11_dias = now() + timedelta(days=-11)

        hace_2_dias = make_aware(datetime(hace_2_dias.year, hace_2_dias.month, hace_2_dias.day), get_current_timezone())
        hace_3_dias = make_aware(datetime(hace_3_dias.year, hace_3_dias.month, hace_3_dias.day), get_current_timezone())
        hace_4_dias = make_aware(datetime(hace_4_dias.year, hace_4_dias.month, hace_4_dias.day), get_current_timezone())
        hace_5_dias = make_aware(datetime(hace_5_dias.year, hace_5_dias.month, hace_5_dias.day), get_current_timezone())
        hace_10_dias = make_aware(datetime(hace_10_dias.year, hace_10_dias.month, hace_10_dias.day),
                                  get_current_timezone())
        hace_11_dias = make_aware(datetime(hace_11_dias.year, hace_11_dias.month, hace_11_dias.day),
                                  get_current_timezone())

        p9 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                               fecha_de_asignacion_a_vendedor=hace_2_dias)
        p10 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                                fecha_de_asignacion_a_vendedor=hace_3_dias)
        p11 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                                fecha_de_asignacion_a_vendedor=hace_4_dias)
        p12 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                                fecha_de_asignacion_a_vendedor=hace_5_dias)
        p13 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                                fecha_de_asignacion_a_vendedor=hace_10_dias)
        p14 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                                fecha_de_asignacion_a_vendedor=hace_11_dias)

        params = {'tipo_origen': '', 'filter_estado': 'N', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'inactividad': ''}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 7)

        params = {'tipo_origen': '', 'filter_estado': 'N', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'inactividad': '3'}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 5)

        params = {'tipo_origen': '', 'filter_estado': 'N', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'inactividad': '5'}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 3)

        params = {'tipo_origen': '', 'filter_estado': 'N', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'inactividad': 'mas'}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 2)

    def test_por_estado_finalizado(self):
        # TODO: Testear Con un motivo precargado, sin motivo precargado (otro motivo),
        # Todos
        f = self.fixture
        p1 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'])
        FinalizacionesFactory(prospecto=p1, vendedor=f['sup_1'], otro_motivo='otro motivo')
        p2 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'])
        FinalizacionesFactory(prospecto=p2, vendedor=f['sup_1'], motivo=f['motivo_1'])
        p3 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'])
        FinalizacionesFactory(prospecto=p3, vendedor=f['sup_1'], motivo=f['motivo_1'])
        p4 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'])
        FinalizacionesFactory(prospecto=p4, vendedor=f['sup_1'], motivo=f['motivo_2'])

        # Cuento los prospectos con cualquier motivo de finalizacion
        params = {'tipo_origen': '', 'filter_estado': 'F', 'filter_llamado': '', 'asignados': 'si',
                  'motivo_de_finalizacion': '', 'vendedores': self.idv, 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 5)

        # Cuento los prospectos con motivo de finalizacion prefijado 1
        params['motivo_de_finalizacion'] = f['motivo_1'].id
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 3)

        # Cuento los prospectos con motivo de finalizacion prefijado 2
        params['motivo_de_finalizacion'] = f['motivo_2'].id
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

        # Cuento los prospectos con otro motivo de finalizacion
        params['motivo_de_finalizacion'] = '0'
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

    def test_por_vendedor(self):
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.fixture['sup_1'].id, 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 3)

        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'no', 'campania': '', }
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 1)

        ids = [self.fixture['sup_1'].id, self.fixture['vend_1'].id]
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'vendedores': ids, 'campania': '',
                  'asignados': 'si'}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 5)

    def test_por_exportado(self):
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'exportados': 'si'}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 2)

        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'exportados': 'no'}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertIn('total_prospectos', msg)
        self.assertEqual(msg['total_prospectos'], 4)

    def test_por_marca_provincia_y_prefijo(self):
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': ''}
        url = reverse('cantidad-filtrada')
        response = self.client.post(url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('total_prospectos' in msg)
        self.assertEqual(msg['total_prospectos'], 6)
        self.assertTrue('marcas' in msg)
        self.assertTrue('provincias' in msg)
        self.assertTrue('prefijos' in msg)

        self.assertEqual(
            msg['marcas'],  [
            {'nombre': self.marca_blanca.nombre(), 'id': self.marca_blanca.id},
            {'nombre': self.marca_uno.nombre(), 'id': self.marca_uno.id},
            {'nombre': self.marca_dos.nombre(), 'id': self.marca_dos.id}
        ])
        self.assertEqual(set(msg['provincias']), {'', 'p1', 'p2'})
        self.assertEqual(set(msg['prefijos']), {'', '00111', '00222'})

        params['vendedores'] = [str(self.fixture['vend_1'].id), ]
        response = self.client.post(url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['marcas'], [{'nombre': self.marca_uno.nombre(), 'id': self.marca_uno.id}])
        self.assertEqual(set(msg['provincias']), {''})
        self.assertEqual(set(msg['prefijos']), {'', '00111'})

        params['vendedores'] = self.idv
        params['marcas'] = [self.marca_uno.id, self.marca_dos.id]
        response = self.client.post(url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 4)

        params.pop('marcas')
        params['provincias'] = ['sin-dato', 'p1', ]
        response = self.client.post(url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 5)

        params.pop('provincias')
        params['prefijos'] = ['00111', ]
        response = self.client.post(url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 2)

    def test_por_etiquetas(self):
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': ''}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('total_prospectos' in msg)
        self.assertEqual(msg['total_prospectos'], 6)
        self.assertTrue('etiquetas' in msg)
        self.assertEqual(set(msg['etiquetas']), {'', 't1'})

        params['etiquetas'] = ['t1']
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 2)

        params['vendedores'] = '%s' % self.fixture['vend_1'].pk  # vend_1
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 1)

    def test_por_rango_fechas_desde(self):
        dentro_de_2_dias = now() + timedelta(days=2)
        dentro_de_2_dias = dentro_de_2_dias.date()
        hace_2_dias = now() + timedelta(days=-2)
        hace_2_dias = hace_2_dias.date()

        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'fecha_desde': hace_2_dias}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('total_prospectos' in msg)
        self.assertEqual(msg['total_prospectos'], 6)

        params['fecha_desde'] = [dentro_de_2_dias]
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 0)

        params = {'tipo_origen': '', 'filter_estado': 'V', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'fecha_desde': hace_2_dias}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 1)

        params['fecha_desde'] = [dentro_de_2_dias]
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 0)

    def test_por_rango_fechas_hasta(self):
        dentro_de_2_dias = now() + timedelta(days=2)
        dentro_de_2_dias = dentro_de_2_dias.date()
        hace_2_dias = now() + timedelta(days=-2)
        hace_2_dias = hace_2_dias.date()

        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'fecha_hasta': hace_2_dias}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('total_prospectos' in msg)
        self.assertEqual(msg['total_prospectos'], 0)

        params['fecha_hasta'] = [dentro_de_2_dias]
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 6)

        params = {'tipo_origen': '', 'filter_estado': 'V', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': self.idv, 'campania': '', 'fecha_hasta': hace_2_dias}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 0)

        params['fecha_hasta'] = [dentro_de_2_dias]
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertEqual(msg['total_prospectos'], 1)

    def test_dias_sin_contacto_por_sms(self):
        supervisor = self.fixture['sup_1']
        CampaniaDeComunicacion.objects.all().delete()
        vendedores_ids = self._id_de_vendedores_a_cargo_bajo(supervisor)
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': 'si',
                  'vendedores': vendedores_ids, 'campania': '', 'dias_sin_sms': '7'}
        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('total_prospectos' in msg)
        self.assertEqual(msg['total_prospectos'], 6)

        vendedor = supervisor.vendedores.first()
        CampaniaDeComunicacion.nueva_campania_con(nombre='nombre', mensaje='mensaje', medio=self.fixture['medio_1'],
                                                  vendedor=vendedor, prospectos=vendedor.prospectos.all())

        response = self.client.post(self.url, params)
        msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('total_prospectos' in msg)
        cantidad_prospectos_descartados = self._cantidad_de_prospectos(vendedor, supervisor)
        self.assertEqual(msg['total_prospectos'], 6 - cantidad_prospectos_descartados)

    def _cantidad_de_prospectos(self, vendedor, supervisor):
        return vendedor.prospectos.filter(responsable=supervisor).count()


