from prospectos.views.forms import AsignacionForm
from testing.base import BaseFixturedTest


class AsignacionFormTest(BaseFixturedTest):
    """
        Nota: classe movida no es de mi autoria
    """

    def test_valida_cantidad(self):
        supervisor = self.fixture['sup_1']
        params = {'cantidad': '4', 'asignar': 'todos', 'metodo': 'uniforme', 'equipo': '', 'vendedor': ''}
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertTrue(form.is_valid())
        params['cantidad'] = '5'
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertFalse(form.is_valid())

    def test_valida_metodo(self):
        supervisor = self.fixture['sup_1']
        # Asignando a todos los vendedores
        params = {'cantidad': '2', 'asignar': 'todos', 'metodo': '', 'equipo': '', 'vendedor': ''}
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertFalse(form.is_valid())
        params['metodo'] = 'uniforme'
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertTrue(form.is_valid())
        params['metodo'] = 'factor'
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertTrue(form.is_valid())

        # Asignando a un equipo
        self.fixture['vend_1'].equipo = self.fixture['equipo_1']
        self.fixture['vend_1'].save()
        params = {'cantidad': '2', 'asignar': 'equipo', 'metodo': '', 'equipo': self.fixture['equipo_1'].id,
                  'vendedor': ''}
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertFalse(form.is_valid())
        params['metodo'] = 'uniforme'
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertTrue(form.is_valid())
        params['metodo'] = 'factor'
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertTrue(form.is_valid())

    def test_valida_equipo(self):
        self.fixture['vend_1'].equipo = self.fixture['equipo_1']
        self.fixture['vend_1'].save()
        self.fixture['vend_3'].equipo = self.fixture['equipo_2']
        self.fixture['vend_3'].save()

        supervisor = self.fixture['sup_1']
        params = {'cantidad': '2', 'asignar': 'equipo', 'metodo': 'uniforme', 'equipo': '', 'vendedor': ''}
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertFalse(form.is_valid())
        params['equipo'] = self.fixture['equipo_2'].id
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertFalse(form.is_valid())
        params['equipo'] = self.fixture['equipo_1'].id
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertTrue(form.is_valid())

        self.fixture['vend_1'].equipo = None;
        self.fixture['vend_1'].save()
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertFalse(form.is_valid())

    def test_valida_vendedor(self):
        supervisor = self.fixture['sup_1']
        params = {'cantidad': '2', 'asignar': 'vendedor', 'metodo': '', 'equipo': '',
                  'vendedor': self.fixture['vend_1'].id}
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertTrue(form.is_valid())
        params['vendedor'] = self.fixture['vend_3'].id
        form = AsignacionForm(supervisor, 4, data=params)
        self.assertFalse(form.is_valid())