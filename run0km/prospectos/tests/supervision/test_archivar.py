import json

from django.urls import reverse

from prospectos.models import Prospecto
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from testing.base import BaseLoggedSupervisorTest
from testing.test_utils import reload_model


class ArchivarProspectosDesdeAdministracionViewTest(BaseLoggedSupervisorTest):
    """
        Nota: classe movida no es de mi autoria
    """

    def setUp(self):
        super(ArchivarProspectosDesdeAdministracionViewTest, self).setUp()
        self._repartidor = RepartidorDeProspectos.nuevo()
        self.prospecto = self.fixture['p_1']
        self.prospecto_dos = self.fixture['p_2']
        self.vendedor = self.fixture['vend_1']
        self.supervisor = self.fixture['sup_1']
        prospectos = Prospecto.objects.filter(id__in=[self.prospecto.pk, self.prospecto_dos.pk])
        self._repartidor.asignar_responsable_y_mantener_vendedor(prospectos=prospectos, supervisor=self.supervisor)

    def test_archivar_prospectos_del_supervisor(self):
        response = self._post_archivar(cantidad=1, vendedores_ids=self.prospecto.obtener_vendedor().id)
        self.assertEqual(response.status_code, 200)
        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.esta_archivado())

    def test_reasignar_prospectos_luego_de_haber_archivado_elimina_archivado_asociado_al_prospecto(self):
        # Archivo prospecto.
        response = self._post_archivar(cantidad=1, vendedores_ids=self.vendedor.id)
        self.assertEqual(response.status_code, 200)
        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.esta_archivado())
        # Reasigno prospectos.
        prospectos_pks = [prospecto.pk]
        seleccion_de_prospectos = self._json_para_selecion_de('selection', prospectos_pks)
        # self.supervisor asigna prospecto a self.vendedor
        response = self._post_reasignar_a_vendedor(cantidad=1, vendedor=self.vendedor,
                                                   vendedores_ids=self.supervisor.id,
                                                   seleccion_de_prospectos=seleccion_de_prospectos,
                                                   asignados='')

        self.assertEqual(response.status_code, 200)
        prospecto = reload_model(prospecto)
        self.assertEqual(prospecto.obtener_vendedor(), self.vendedor)
        self.assertFalse(prospecto.esta_archivado())

    def _post_archivar(self, cantidad, forma_de_asignacion='vendedor', vendedores_ids='', vendedor_id='',
                       equipo_id='', metodo='', seleccion_de_prospectos='{}', asignados='si'):
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': asignados,
                  'vendedores': vendedores_ids, 'campania': '', 'accion': 'archivar',
                  'cantidad': cantidad,
                  'asignar': forma_de_asignacion,
                  'metodo': metodo,
                  'equipo': equipo_id,
                  'vendedor': vendedor_id,
                  'seleccion_de_prospectos': seleccion_de_prospectos
                  }
        url = reverse('administracion')
        response = self.client.post(url, params)
        return response

    def _post_reasignar_a(self, forma_de_asignacion, cantidad, vendedores_ids=None, vendedor_id='',
                          equipo_id='', metodo='', seleccion_de_prospectos='{}', asignados='si'):
        if not vendedores_ids:
            vendedores_ids = self.fixture['sup_1'].id
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': asignados,
                  'vendedores': vendedores_ids, 'campania': '', 'accion': 'reasignar',
                  'cantidad': cantidad,
                  'asignar': forma_de_asignacion,
                  'metodo': metodo,
                  'equipo': equipo_id,
                  'vendedor': vendedor_id,
                  'seleccion_de_prospectos': seleccion_de_prospectos
                  }
        url = reverse('administracion')
        response = self.client.post(url, params)
        return response

    def _post_reasignar_a_vendedor(self, cantidad, vendedor, vendedores_ids=None, seleccion_de_prospectos='{}',
                                   asignados='si'):
        response = self._post_reasignar_a(forma_de_asignacion='vendedor',
                                          cantidad=cantidad,
                                          vendedor_id=vendedor.id,
                                          vendedores_ids=vendedores_ids,
                                          seleccion_de_prospectos=seleccion_de_prospectos, asignados=asignados)
        return response

    def _get_prospectos_pks_de_vendedor(self, vendedor, tamanio=2):
        prospectos = vendedor.todos_los_prospectos()
        self.assertGreaterEqual(prospectos.count(), 2, 'El user.vendedor debe tener al menos dos prospectos')
        prospectos_pks = [prospecto.pk for prospecto in prospectos.all()][:tamanio]
        return prospectos_pks

    def _get_prospectos_pks_de_supervisor(self, supervisor, tamanio=2):
        prospectos = supervisor.todos_los_prospectos()
        self.assertGreaterEqual(prospectos.count(), 2, 'El user.vendedor debe tener al menos dos prospectos')
        prospectos_pks = [prospecto.pk for prospecto in prospectos.all()][:tamanio]
        return prospectos_pks

    def _json_para_selecion_de(self, modo, valores):
        json_prospectos_pks = json.dumps([str(pk) for pk in valores])
        json_prospectos_pks = json_prospectos_pks.replace('\"', '\\\"')
        json_seleccion = '{ "mode": "%s", "values": "%s"}' % (modo, json_prospectos_pks)
        return json_seleccion