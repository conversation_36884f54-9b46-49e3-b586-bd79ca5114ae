# coding=utf-8
from django.utils.timezone import now, localtime

from prospectos.configuracion import CAMPOS_DE_PROSPECTO_PARA_EXPORTAR, CAMPOS_EXTRA_QUE_NO_DEBEN_EXPORTARSE
from prospectos.configuracion import CAMPOS_PUBLICOS_QUE_NO_DEBEN_EXPORTARSE
from prospectos.models import Prospecto, LogDeExportacionDeProspecto
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from prospectos.utils.exportacion_a_csv import ExportadorDeProspectosACSV, GeneradorDeDatosDeExportacionDeProspectos
from testing.base import BaseFixturedTest
from testing.factories import ProspectosFactory, CampoExtraFactory


class ExportadorDeProspectosACSVTest(BaseFixturedTest):

    """
        Refactor pendiente: separa tests de GeneradorDeDatosDeExportacionDeProspectos y ExportadorDeProspectosACSV
    """
    def setUp(self):
        super(ExportadorDeProspectosACSVTest, self).setUp()
        factory = ProspectosFactory(campania=self.fixture['camp_1'],
                                    vendedor=self.fixture['vend_1'],
                                    nombre='Roman',
                                    telefono='15151515',
                                    email='<EMAIL>')
        self.prospecto = Prospecto.objects.get(pk=factory.pk)
        self.vendedor = self.fixture['vend_1']

    def _generar_datos(self, prospectos=None):
        if not prospectos:
            prospectos = Prospecto.objects.filter(pk=self.prospecto.pk)
        generador = GeneradorDeDatosDeExportacionDeProspectos()
        campos, filas = generador.generar_datos_para(prospectos)
        return campos, filas

    def _assert_informacion_prospecto_exportado(self, fila, prospecto, campos):
        self._assert_celda('nombre', tiene_valor=prospecto.nombre,
                           campos=campos, fila=fila)
        self._assert_celda('vendedor', tiene_valor=prospecto.vendedor.username(),
                           campos=campos, fila=fila)
        self._assert_celda('telefono', tiene_valor=prospecto.telefono,
                           campos=campos, fila=fila)
        self._assert_celda('campaña', tiene_valor=prospecto.campania.nombre,
                           campos=campos, fila=fila)
        self._assert_celda('origen', tiene_valor=prospecto.campania.origen,
                           campos=campos, fila=fila)
        self._assert_celda('estado', tiene_valor=prospecto.get_estado_display(),
                           campos=campos, fila=fila)
        self._assert_celda('email', tiene_valor=prospecto.email,
                           campos=campos, fila=fila)

    def _assert_prospecto_por_defecto_exportado_en(self, filas, campos):
        self.assertEqual(len(filas), 1)
        self._assert_informacion_prospecto_exportado(filas[0], self.prospecto, campos)

    def _assert_campos_exportados(self, campos, campos_a_exportar=CAMPOS_DE_PROSPECTO_PARA_EXPORTAR,
                                  campos_no_exportados=CAMPOS_PUBLICOS_QUE_NO_DEBEN_EXPORTARSE):
        # CHEQUEO DE CAMPOS EXPORTADOS
        for nombre in campos_a_exportar:
            if not nombre == 'campania' and not nombre == 'modelo':
                self.assertIn(nombre, campos)
        self.assertIn('campaña', campos)
        self.assertIn('origen', campos)
        self.assertIn('modelos', campos)
        for nombre in campos_no_exportados:
            self.assertNotIn(nombre, campos)

    def _assert_celda(self, nombre_celda, tiene_valor, campos, fila):
        if nombre_celda in campos:  # la celda es exportable
            self.assertIn(nombre_celda, fila)
            self.assertEqual(fila[nombre_celda], tiene_valor)

    def test_datos_del_prospecto_deberia_ser_exportado(self):
        campos, filas = self._generar_datos()
        self._assert_campos_exportados(campos)
        self._assert_prospecto_por_defecto_exportado_en(filas, campos)

    def test_campo_extra_deberia_ser_agregados_como_columna(self):
        extra_exportable = CampoExtraFactory(prospecto=self.prospecto, nombre='extra', valor='valor extra')
        campos, filas = self._generar_datos()

        campos_a_exportar = CAMPOS_DE_PROSPECTO_PARA_EXPORTAR | {extra_exportable.nombre}
        self._assert_campos_exportados(campos, campos_a_exportar=campos_a_exportar)
        self.assertEqual(len(filas), 1)
        fila = filas[0]
        self._assert_informacion_prospecto_exportado(fila, self.prospecto, campos)
        self._assert_celda('extra', tiene_valor=extra_exportable.valor,
                           campos=campos, fila=fila)

    def test_campo_extra_no_exportable_no_deberia_ser_agregado(self):
        nombre_extra_no_exportable = [campo for campo in CAMPOS_EXTRA_QUE_NO_DEBEN_EXPORTARSE if campo != 'origen'][0]
        extra_no_exportable = CampoExtraFactory(prospecto=self.prospecto, nombre=nombre_extra_no_exportable,
                                                valor='nombre no_exportable')
        campos, filas = self._generar_datos()
        campos_no_exportados = CAMPOS_PUBLICOS_QUE_NO_DEBEN_EXPORTARSE | {extra_no_exportable.nombre}
        self._assert_campos_exportados(campos, campos_no_exportados=campos_no_exportados)
        self._assert_prospecto_por_defecto_exportado_en(filas, campos)

    def test_prospectos_no_finalizados_no_deberia_contener_campos_de_finalizacion(self):
        campos, filas = self._generar_datos()
        campos_no_exportados = CAMPOS_PUBLICOS_QUE_NO_DEBEN_EXPORTARSE | {'motivo de finalizacion',
                                                                          'comentario de finalizacion'}
        self._assert_campos_exportados(campos, campos_no_exportados=campos_no_exportados)
        self._assert_prospecto_por_defecto_exportado_en(filas, campos)

    def test_prospectos_finalizados_deberia_contener_campos_de_finalizacion(self):
        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor)
        motivo = self.fixture['motivo_1']
        comentario = 'comentario'
        gestor.finalizar_prospecto(prospecto=self.prospecto, motivo=motivo, comentario=comentario)
        campos, filas = self._generar_datos()
        campos_a_exportar = CAMPOS_DE_PROSPECTO_PARA_EXPORTAR | {'motivo de finalizacion',
                                                                 'comentario de finalizacion'}
        self._assert_campos_exportados(campos, campos_a_exportar=campos_a_exportar)
        self.assertEqual(len(filas), 1)
        fila = filas[0]
        self._assert_informacion_prospecto_exportado(fila, self.prospecto, campos)
        self._assert_celda('motivo de finalizacion', tiene_valor=motivo.descripcion,
                           campos=campos, fila=fila)
        self._assert_celda('comentario de finalizacion', tiene_valor=comentario,
                           campos=campos, fila=fila)

    def test_response_para_exportar_a_csv(self):
        init = now()
        vendedor = self.fixture['vend_1']
        ProspectosFactory(campania=self.fixture['camp_1'], vendedor=vendedor)
        prospectos = Prospecto.objects.filter(fecha_creacion__gte=init)
        exportador = ExportadorDeProspectosACSV()
        response = exportador.exportar(prospectos=prospectos, user=vendedor.supervisor.user)
        self.assertTrue(response.has_header('Content-Type'))
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertTrue(response.has_header('Content-Disposition'))
        self.assertEqual(response['Content-Disposition'], 'attachment; filename=prospectos.csv')

        # CHEQUEO DE CAMPOS EXPORTADOS
        contenido_de_la_respuesta = response.content.decode('utf-8')
        for nombre in CAMPOS_DE_PROSPECTO_PARA_EXPORTAR:
            if not nombre == 'campania':
                self.assertTrue(nombre.title() in contenido_de_la_respuesta)
        self.assertIn('Origen', contenido_de_la_respuesta)
        self.assertIn('Campaña', contenido_de_la_respuesta)

        self.assertIn(self.fixture['camp_1'].nombre, contenido_de_la_respuesta)
        self.assertIn(self.fixture['camp_1'].categoria.tipo_de_origen.codigo, contenido_de_la_respuesta)
        self.assertIn(self.fixture['vend_1'].user.username, contenido_de_la_respuesta)

    def test_exportar_prospectos_genera_log_exportacion_correcto(self):
        hoy = localtime(now())
        vendedor = self.fixture['vend_1']
        responsable = vendedor.supervisor
        ProspectosFactory(campania=self.fixture['camp_1'], vendedor=vendedor,
                          responsable=responsable)
        prospectos = Prospecto.objects.filter(fecha_creacion__gte=hoy)
        exportador = ExportadorDeProspectosACSV()
        exportador.exportar(prospectos=prospectos, user=vendedor.supervisor.user)

        self.assertEqual(LogDeExportacionDeProspecto.objects.filter(user=responsable.user).count(), 1)
        log = LogDeExportacionDeProspecto.objects.get(user=responsable.user)
        self.assertEqual(log.cantidad, prospectos.count())
        self.assertEqual(log.fecha, hoy.date())

    def test_exportar_dos_veces_en_un_mismo_dia_actualiza_el_log_de_exportacion(self):
        hoy = localtime(now())
        vendedor = self.fixture['vend_1']
        responsable = vendedor.supervisor
        ProspectosFactory(campania=self.fixture['camp_1'], vendedor=vendedor,
                          responsable=responsable)
        prospectos_log = Prospecto.objects.filter(responsable=vendedor.supervisor)
        cantidad_prospectos_log = prospectos_log.count()
        exportador = ExportadorDeProspectosACSV()

        exportador.exportar(prospectos=prospectos_log, user=responsable.user)

        vendedor = self.fixture['vend_2']
        ProspectosFactory(campania=self.fixture['camp_2'], vendedor=vendedor,
                          responsable=vendedor.supervisor)
        prospectos_log_actualizado = Prospecto.objects.filter(responsable=vendedor.supervisor)
        exportador.exportar(prospectos=prospectos_log_actualizado, user=responsable.user)

        logs = LogDeExportacionDeProspecto.objects.filter(user=responsable.user)
        self.assertEqual(logs.count(), 1)
        log = logs[0]
        self.assertEqual(log.cantidad, cantidad_prospectos_log + prospectos_log_actualizado.count())
        self.assertEqual(log.fecha, hoy.date())

