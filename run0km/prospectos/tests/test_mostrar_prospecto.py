from django.test import TestCase
from django.template import Template
from django.template.defaultfilters import date as _date
from django.utils.timezone import now, timedelta, localtime
from django.template import Context
from django.template.loader import get_template

from testing.base import BaseFixturedTest
from testing.factories import ProspectosFactory, LlamadosFactory, CampoExtraFactory, \
    FinalizacionesFactory, VentasFactory, ComentariosFactory, TelefonosExtraFactory, EmailsExtraFactory, TagsFactory
from prospectos.configuracion import (CAMPOS_DE_PROSPECTO_PARA_MOSTRAR,
                                      CAMPOS_DE_PROSPECTO_PARA_EXPORTAR,
                                      CAMPOS_DE_PROSPECTO_PUBLICOS,
                                      CAMPOS_PUBLICOS_QUE_NO_DEBEN_EXPORTARSE, CAMPOS_PUBLICOS_QUE_NO_DEBEN_MOSTRARSE)

# Campos que no se deben mostrar
CAMPOS_DE_PROSPECTO_OCULTOS = ['fecha_creacion', 'asignacion__fecha_de_asignacion_a_vendedor', 'exportado', 'prefijo']


class VisibilidadDeCamposTest(TestCase):
    def test_campos_son_subconjuntos(self):
        self.assertTrue(CAMPOS_DE_PROSPECTO_PARA_MOSTRAR.issubset(CAMPOS_DE_PROSPECTO_PUBLICOS))
        self.assertTrue(CAMPOS_DE_PROSPECTO_PARA_EXPORTAR.issubset(CAMPOS_DE_PROSPECTO_PUBLICOS))
        self.assertEqual(0, len(CAMPOS_PUBLICOS_QUE_NO_DEBEN_EXPORTARSE.intersection(CAMPOS_DE_PROSPECTO_PARA_EXPORTAR)))
        self.assertEqual(0, len(CAMPOS_PUBLICOS_QUE_NO_DEBEN_MOSTRARSE.intersection(CAMPOS_DE_PROSPECTO_PARA_MOSTRAR)))


class MostrarProspectoTest(BaseFixturedTest):

    def deprecated___test_mostrar_campos_publicos(self):
        # Ya no se usa mas la funcion campos_publicos antes de renderear el prospecto.
        datos_prospecto = dict()
        vend = self.fixture['vend_1']

        # Seteo valores triviales para validar mas tarde
        for campo in CAMPOS_DE_PROSPECTO_PUBLICOS:
            datos_prospecto[campo] = 'val_' + campo

        # Saco campos que no puedo definir directamente
        campos_externos = ['campos_extra', 'tags', 'telefono_extra',
                        'email_extra', 'venta', 'finalizacion', 'llamado', 'comentarios']
        for campo in campos_externos:
            datos_prospecto.pop(campo)

        # Seteo otros valores
        datos_prospecto['id'] = 789987
        datos_prospecto['prefijo'] = 'val_prefijo'
        datos_prospecto['vendedor'] = vend
        datos_prospecto['campania'] = self.fixture['camp_1']
        ahora = now()
        datos_prospecto['fecha'] = ahora+timedelta(minutes=1)

        p1 = ProspectosFactory(**datos_prospecto)
        p1.fecha_creacion = now()-timedelta(minutes=10)
        p1.save()

        # Armo relaciones con valores que pueda validar mas tarde
        ee = EmailsExtraFactory(prospecto=p1, vendedor=vend, email='val_email_extra')
        te = TelefonosExtraFactory(prospecto=p1, vendedor=vend, telefono='val_telefono_extra')
        ll = LlamadosFactory(prospecto=p1, vendedor=vend, fecha=now() + timedelta(minutes=5))
        ce = CampoExtraFactory(prospecto=p1, nombre='val_campo', valor='val_extra')
        fin = FinalizacionesFactory(prospecto=p1, vendedor=vend, comentario='val_finalizacion')
        com = ComentariosFactory(prospecto=p1, vendedor=vend, comentario='val_comentario')
        com.datetime = now() + timedelta(minutes=11)
        com.save()
        tag = TagsFactory(vendedor=vend.supervisor, nombre='val_tag')
        tag.prospectos.add(p1)
        vta = VentasFactory(prospecto=p1, vendedor=vend, marca='val_venta',
                            fecha=now() + timedelta(minutes=10), precio=10)
        # Agrego o modifico Valores que no se validan trivialmente
        datos_prospecto['telefono_activo'] = 'Eliminar'
        datos_prospecto['email_activo'] = 'Eliminar'

        formato_fecha = "j N o H:i"

        datos_prospecto['fecha'] = _date(localtime(p1.fecha), formato_fecha)
        datos_prospecto['telefono_extra'] = te.telefono
        datos_prospecto['email_extra'] = ee.email
        datos_prospecto['llamado'] = _date(localtime(ll.fecha), formato_fecha)
        datos_prospecto['tags'] = tag.nombre
        datos_prospecto['estado'] = '"nombre-prospecto "'
        datos_prospecto['campos_extra'] = (ce.nombre, ce.valor)
        datos_prospecto['finalizado'] = 'Finalizacion'
        datos_prospecto['finalizacion'] = fin.comentario
        datos_prospecto['vendido'] = 'Datos de la Venta'
        datos_prospecto['venta'] = (vta.marca, _date(localtime(vta.fecha), formato_fecha))
        datos_prospecto['vendedor'] = vend.user.get_full_name()
        datos_prospecto['campania'] = p1.campania.nombre
        datos_prospecto['id'] = str(p1.id)
        datos_prospecto['en_proceso'] = 'botones-centrales'
        datos_prospecto['comentarios'] = (com.comentario, _date(localtime(com.datetime), formato_fecha))
        datos_prospecto['fecha_creacion'] = _date(localtime(p1.fecha_creacion), formato_fecha)
        datos_prospecto['fecha_de_asignacion_a_vendedor'] = _date(localtime(p1.fecha_de_asignacion_a_vendedor()),
                                                                  formato_fecha)

        template = get_template('prospecto.html')
        vend.supervisor.user.personificado = False
        context = {'prospecto': p1.campos_publicos(), 'user': vend.supervisor.user, 'comentarios': p1.comentarios.all()}
        html_msg = template.render(context)

        for campo in CAMPOS_DE_PROSPECTO_PARA_MOSTRAR:
            if type(datos_prospecto[campo]) is tuple:
                for dato in datos_prospecto[campo]:
                    self.assertIn(dato, html_msg)
            else:
                self.assertIn(datos_prospecto[campo], html_msg)
        ocultos = CAMPOS_DE_PROSPECTO_OCULTOS
        ocultos.remove('exportado')
        for campo in ocultos:
            self.assertTrue(datos_prospecto[campo] not in html_msg)

    def test_ocultar_campos_no_publicos(self):
        datos_prospecto = dict()
        vend = self.fixture['vend_1']
        datos_prospecto['vendedor'] = vend
        datos_prospecto['campania'] = self.fixture['camp_1']
        p1 = ProspectosFactory(**datos_prospecto)

        # Seteo valores triviales para validar mas tarde
        lleno = ''
        vacio = ''
        for campo in CAMPOS_DE_PROSPECTO_OCULTOS:
            datos_prospecto[campo] = 'val_' + campo
            lleno += campo + ": {{prospecto." + campo + "}},"
            vacio += campo + ": ,"
        template = Template(lleno)
        context = Context({'prospecto': p1.campos_publicos()})
        html_msg = template.render(context)
        self.assertEqual(vacio, html_msg)
