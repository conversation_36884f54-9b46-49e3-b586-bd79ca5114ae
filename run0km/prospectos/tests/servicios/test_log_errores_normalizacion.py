import mock
from django.test.utils import override_settings
from lib.normalizador.errors import ServicioNormalizarRespuestaInesperadaError
from prospectos.models import LogDeErrorNormalizador

from prospectos.utils.servicio_para_completar_telefonos_prospectos import NormalizadorDeTelefonosDeProspectos
from testing.base import BaseFixturedTest

from lib.normalizador.tests.mocks import ClienteNormalizarMockRespuestaPredeterminada, \
    ClienteNormalizarMockErrorComunicacion, ClienteNormalizadorMockRespondeTupla


@override_settings(REALIZAR_NORMALIZACION=True)
class LogErroresNormalizacionTest(BaseFixturedTest):
    def setUp(self):
        super(LogErroresNormalizacionTest, self).setUp()
        self.prospecto = self.fixture['p_1']

    def _normalizar(self, llamar_mock, cliente=None):
        if cliente is None:
            cliente = ClienteNormalizarMockRespuestaPredeterminada()
        llamar_mock.side_effect = cliente.llamar
        normalizador = NormalizadorDeTelefonosDeProspectos()
        normalizador.normalizar(self.prospecto)

    @mock.patch("lib.normalizador.ClienteNormalizar.llamar")
    def test_normalizador_log_error_falla_comunicacion(self, llamar_mock):
        self.assertFalse(LogDeErrorNormalizador.objects.exists())

        cliente = ClienteNormalizarMockErrorComunicacion()
        self._normalizar(llamar_mock, cliente)

        self.assertEqual(LogDeErrorNormalizador.objects.count(), 1)
        log = LogDeErrorNormalizador.objects.first()
        self.assertIsNotNone(log)
        self.assertEqual(log.tipo, cliente.default_error.error_type())
        self.assertEqual(log.descripcion, str(cliente.default_error))
        self.assertIsNotNone(log.traceback_info)

    @mock.patch("lib.normalizador.ClienteNormalizar.llamar")
    def test_normalizador_log_error_respuesta_inesperada(self, llamar_mock):
        self.assertFalse(LogDeErrorNormalizador.objects.exists())
        cliente = ClienteNormalizadorMockRespondeTupla()
        self._normalizar(llamar_mock, cliente)

        self.assertEqual(LogDeErrorNormalizador.objects.count(), 1)
        log = LogDeErrorNormalizador.objects.first()
        self.assertIsNotNone(log)
        self.assertEqual(log.tipo, ServicioNormalizarRespuestaInesperadaError.error_type())
        # self.assertEqual(log.descripcion, cliente.mensaje_de_error_esperado)
        self.assertIsNotNone(log.traceback_info)
