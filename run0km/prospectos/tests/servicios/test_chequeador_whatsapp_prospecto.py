import mock
from django.test.utils import override_settings
from prospectos.models import TelefonoExtra, LogDeErrorChequeadorDeWhatsapp
from prospectos.utils.servicio_para_completar_telefonos_prospectos import CheckeadorWhatsappDeTelefonosDeProspectos

from testing.base import BaseLoggedTest
from lib.whatsapp_checker.errors import WhatsAppCheckerParseResponseError, WhatsAppCheckerConnectionError
from lib.whatsapp_checker.tests import WhatsAppCheckerMock, WhatsAppCheckerConnectionFailMock


@override_settings(REALIZAR_CHECKEO_DE_WHATSAPP=True)
class CheckeadorWhatsappDeTelefonosDeProspectosTest(BaseLoggedTest):

    def setUp(self):
        super(CheckeadorWhatsappDeTelefonosDeProspectosTest, self).setUp()
        self.prospecto = self.fixture['p_1']
        self.chequeador = CheckeadorWhatsappDeTelefonosDeProspectos()

    def _assert_todos_telefonos_extra_tienen_whatsapp(self, prospecto, tiene_whatsapp, tiene_imagen=False):
        for telefono_extra in prospecto.telefono_extra.all():
            self.assertEqual(telefono_extra.tiene_whatsapp, tiene_whatsapp)
            self.assertEqual(bool(telefono_extra.imagen_de_perfil_de_whatsapp), tiene_imagen)
            self.assertFalse(tiene_imagen and prospecto.imagen_de_perfil_de_whatsapp.url is None)

    @mock.patch("lib.whatsapp_checker.WhatsappCheckerService.call",
                side_effect=WhatsAppCheckerConnectionFailMock().call)
    def test_chequeo_whatsapp_registra_falla_comunicacion(self, mock_service_call):
        self.assertFalse(LogDeErrorChequeadorDeWhatsapp.objects.exists())
        self.chequeador.evaluar(self.prospecto)
        self.assertEqual(LogDeErrorChequeadorDeWhatsapp.objects.count(), 1)

        error = LogDeErrorChequeadorDeWhatsapp.objects.first()
        self.assertEqual(error.tipo, WhatsAppCheckerConnectionError.error_type())

    @mock.patch("lib.whatsapp_checker.WhatsappCheckerService.call",
                side_effect=WhatsAppCheckerMock.response_format_unexpected().call)
    def test_chequeo_whatsapp_registra_respuesta_erronea(self, mock_service_call):
        self.assertFalse(LogDeErrorChequeadorDeWhatsapp.objects.exists())
        self.chequeador.evaluar(self.prospecto)
        self.assertEqual(LogDeErrorChequeadorDeWhatsapp.objects.count(), 1)

        error = LogDeErrorChequeadorDeWhatsapp.objects.first()
        self.assertEqual(error.tipo, WhatsAppCheckerParseResponseError.error_type())

    @mock.patch("lib.whatsapp_checker.WhatsappCheckerService.call",
                side_effect=WhatsAppCheckerMock.response_always(True).call)
    def test_chequea_whatsapp_correctamente(self, mock_service_call):
        self.assertIsNone(self.prospecto.tiene_whatsapp)
        self.chequeador.evaluar(self.prospecto)
        self.assertTrue(self.prospecto.tiene_whatsapp)

    @mock.patch("lib.whatsapp_checker.WhatsappCheckerService.call",
                side_effect=WhatsAppCheckerMock.response_always(False).call)
    @mock.patch("lib.whatsapp_checker.WhatsappCheckerService.get_from_url",
                side_effect=WhatsAppCheckerMock.response_always_has_whatsapp_and_image_error().get_from_url)
    def test_chequea_whatsapp_telefonos_extra_correctamente_sin_imagenes(self, mock_service_call, mock_service_get_from_url):
        vendedor = self.fixture['vend_1']
        extra = TelefonoExtra(prospecto=self.prospecto, vendedor=vendedor, telefono='45454545')
        extra.save()

        self.assertIsNone(self.prospecto.tiene_whatsapp)
        self._assert_todos_telefonos_extra_tienen_whatsapp(self.prospecto, None)
        self.chequeador.evaluar(self.prospecto)
        self.assertFalse(self.prospecto.tiene_whatsapp)
        self.assertFalse(bool(self.prospecto.imagen_de_perfil_de_whatsapp))

        self._assert_todos_telefonos_extra_tienen_whatsapp(self.prospecto, False, tiene_imagen=False)

    @mock.patch("lib.whatsapp_checker.WhatsappCheckerService.call",
                side_effect=WhatsAppCheckerMock.response_always(True).call)
    @mock.patch("lib.whatsapp_checker.WhatsappCheckerService.get_from_url",
                side_effect=WhatsAppCheckerMock.response_always(True).get_from_url)
    def test_chequea_whatsapp_telefonos_extra_correctamente_con_imagenes(self, mock_service_call, mock_service_get_from_url):
        vendedor = self.fixture['vend_1']
        extra = TelefonoExtra(prospecto=self.prospecto, vendedor=vendedor, telefono='45454545')
        extra.save()

        self.assertIsNone(self.prospecto.tiene_whatsapp)
        self._assert_todos_telefonos_extra_tienen_whatsapp(self.prospecto, None)
        self.chequeador.evaluar(self.prospecto)
        self.assertTrue(self.prospecto.tiene_whatsapp)
        self.assertTrue(bool(self.prospecto.imagen_de_perfil_de_whatsapp))

        self._assert_todos_telefonos_extra_tienen_whatsapp(self.prospecto, True, tiene_imagen=True)
