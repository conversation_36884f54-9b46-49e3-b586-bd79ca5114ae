import mock
from django.test.utils import override_settings

from prospectos.models import TelefonoEx<PERSON>, Prospecto
from prospectos.utils.servicio_para_completar_telefonos_prospectos import NormalizadorDeTelefonosDeProspectos, CAMPO_PROVINCIA, \
    CAMPO_LOCALIDAD
from testing.base import BaseLoggedTest

from lib.normalizador.tests.mocks import RespuestaNormalizadorMock, ClienteNormalizarMockRespuestaPredeterminada


@override_settings(REALIZAR_NORMALIZACION=True)
class NormalizarTelefonoDeProspectoTest(BaseLoggedTest):
    def setUp(self):
        super(NormalizarTelefonoDeProspectoTest, self).setUp()
        self.prospecto = self.fixture['p_1']

    def _normalizar(self, llamar_mock, cliente=ClienteNormalizarMockRespuestaPredeterminada()):
        llamar_mock.side_effect = cliente.llamar
        normalizador = NormalizadorDeTelefonosDeProspectos()
        normalizador.normalizar(self.prospecto)
        respuesta = cliente.respuesta_default
        return respuesta

    def assert_respuesta_normalizada(self, prospecto_o_telefono_extra, numero, respuesta):
        self.assertTrue(prospecto_o_telefono_extra.esta_telefono_normalizado, 'Deberia haber sido normalizado')
        self.assertEqual(prospecto_o_telefono_extra.telefono_bien_constituido, respuesta.is_ok)
        self.assertEqual(prospecto_o_telefono_extra.telefono, numero)
        self.assertEqual(prospecto_o_telefono_extra.prefijo,
                         Prospecto.dar_formato_a_prefijo(respuesta.prefijo))
        self.assertEqual(prospecto_o_telefono_extra.telco, respuesta.telco)
        self.assertEqual(prospecto_o_telefono_extra.es_telefono_movil, respuesta.movil)
        self.assertEqual(prospecto_o_telefono_extra.esta_spam_list, respuesta.spam)

    @mock.patch("lib.normalizador.ClienteNormalizar.llamar")
    def test_normalizar_correctamente_prospecto_sin_telefono_extra(self, llamar_mock):
        numero_telefonico = self.prospecto.telefono

        self.assertFalse(self.prospecto.esta_telefono_normalizado)
        self.assertEqual(self.prospecto.localidad, '')
        self.assertEqual(self.prospecto.provincia, '')
        self.assertEqual(self.prospecto.campos_extra.count(), 0)

        respuesta = self._normalizar(llamar_mock)

        self.assertEqual(self.prospecto.campos_extra.count(), 0, 'no agrega campos extras')
        self.assert_respuesta_normalizada(self.prospecto, numero_telefonico, respuesta)

        self.assertEqual(self.prospecto.localidad, respuesta.localidad)
        self.assertEqual(self.prospecto.provincia, respuesta.provincia)

    @mock.patch("lib.normalizador.ClienteNormalizar.llamar")
    def test_normalizar_correctamente_prospecto_con_un_telefono_extra(self, llamar_mock):
        vendedor = self.fixture['vend_1']
        numero_telefonico = self.prospecto.telefono

        self.assertFalse(self.prospecto.esta_telefono_normalizado)
        self.assertEqual(self.prospecto.localidad, '')
        self.assertEqual(self.prospecto.provincia, '')
        self.assertEqual(self.prospecto.campos_extra.count(), 0)

        extra = TelefonoExtra(prospecto=self.prospecto, vendedor=vendedor, telefono='45454545')
        extra.save()
        self.assertEqual(self.prospecto.telefono_extra.count(), 1)

        respuesta = self._normalizar(llamar_mock)
        self.assert_respuesta_normalizada(self.prospecto, numero_telefonico, respuesta)
        self.assertEqual(self.prospecto.telefono_extra.count(), 1)

        telefono_extra = self.prospecto.telefono_extra.first()
        self.assert_respuesta_normalizada(telefono_extra, '45454545', respuesta)

        self.assertEqual(self.prospecto.localidad, respuesta.localidad)
        self.assertEqual(self.prospecto.provincia, respuesta.provincia)
        self.assertEqual(self.prospecto.campos_extra.count(), 0,
                         'no agrega datos repeditos ya que fueron configurados en el prospecto')

    @mock.patch("lib.normalizador.ClienteNormalizar.llamar")
    def test_normalizar_agregando_compos_extra_cuando_existe_localidad_y_provincia(self, llamar_mock):
        self.assertFalse(self.prospecto.esta_telefono_normalizado)
        self.assertEqual(self.prospecto.campos_extra.count(), 0)
        self.prospecto.localidad = 'CABA'
        self.prospecto.provincia = 'CABA'

        numero_telefonico = self.prospecto.telefono
        respuesta = self._normalizar(llamar_mock)

        self.assert_respuesta_normalizada(self.prospecto, numero_telefonico, respuesta)
        self.assertEqual(self.prospecto.localidad, 'CABA', 'Chequea que no cambie la localidad')
        self.assertEqual(self.prospecto.provincia, 'CABA', 'Chequea que no cambie la provincia')

        self.assertEqual(self.prospecto.campos_extra.count(), 2)
        localidad = self.prospecto.campos_extra.get(nombre=CAMPO_LOCALIDAD + ' 1')
        self.assertIsNotNone(localidad)
        self.assertEqual(localidad.valor, respuesta.localidad, 'Configuro la localidad en el campo extra')
        provincia = self.prospecto.campos_extra.get(nombre=CAMPO_PROVINCIA + ' 1')
        self.assertIsNotNone(provincia)
        self.assertEqual(provincia.valor, respuesta.provincia, 'Configuro la provincia en el campo extra')

    @mock.patch("lib.normalizador.ClienteNormalizar.llamar")
    def test_normalizar_toma_localidad_y_provincia_desde_telefono_extra(self, llamar_mock):
        vendedor = self.fixture['vend_1']
        numero_telefonico = self.prospecto.telefono
        self.assertFalse(self.prospecto.esta_telefono_normalizado)

        extra = TelefonoExtra(prospecto=self.prospecto, vendedor=vendedor, telefono='45454545')
        extra.save()

        self.assertEqual(self.prospecto.telefono_extra.count(), 1)
        self.assertEqual(self.prospecto.campos_extra.count(), 0)
        self.assertEqual(self.prospecto.localidad, '')
        self.assertEqual(self.prospecto.provincia, '')

        error = RespuestaNormalizadorMock.error()
        cliente = ClienteNormalizarMockRespuestaPredeterminada.nuevo_con_respuesta(numero_telefonico, error)
        self._normalizar(llamar_mock, cliente)

        self.assertTrue(self.prospecto.esta_telefono_normalizado, 'Deberia haber sido normalizado')
        self.assertFalse(self.prospecto.telefono_bien_constituido, 'El proceso de normalizacion debe tirar error')
        self.assertEqual(self.prospecto.telefono, numero_telefonico,
                         'Al responder error el normalizador no se modifica el telefono')

        self.assertEqual(self.prospecto.telefono_extra.count(), 1)
        telefono_extra = self.prospecto.telefono_extra.first()
        self.assert_respuesta_normalizada(telefono_extra, '45454545', RespuestaNormalizadorMock())

        self.assertEqual(self.prospecto.campos_extra.count(), 0, 'no agrega datos repeditos')
        self.assertEqual(self.prospecto.localidad, RespuestaNormalizadorMock.default_localidad,
                         'Configura la localidad desde el telefono extra')
        self.assertEqual(self.prospecto.provincia, RespuestaNormalizadorMock.default_provincia,
                         'Configura la provincia desde el telefono extra')
