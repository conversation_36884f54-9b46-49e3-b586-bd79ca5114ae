# coding=utf-8
from django.urls import reverse

from core.tests.pages.page_object import PageObject


class ImportarProspectosDesdeCSVViewPage(PageObject):

    def name(self):
        return 'Importar prospectos desde CSV'

    def importar_desde(self, datos, archivo):
        parametros = datos
        parametros.update({'file_upload': archivo})
        self._last_response = self._post(parametros)

    def url(self):
        url = reverse('admin:add_prospectos')
        return url

    def tiene_notificacion_de_error_por_archivo_vacio(self):
        return self.has_content(text='El archivo enviado está vacío')

    def tiene_notificacion_prospectos_cargados_satisfactoriamente_para(
            self, cantidad_prospectos_cargados, filas_leidas):
        texto = '%d prospectos cargados satisfactoriamente de %d filas leidas' % (
            cantidad_prospectos_cargados, filas_leidas)
        return self.has_content(text=texto)

    def tiene_notificacion_filas_erroneas(self, filas_erroneas):
        texto = 'Números de filas con errores: %d' % filas_erroneas
        return self.has_content(text=texto)

    @classmethod
    def nuevo_para(cls, cliente):
        return cls(client=cliente)
