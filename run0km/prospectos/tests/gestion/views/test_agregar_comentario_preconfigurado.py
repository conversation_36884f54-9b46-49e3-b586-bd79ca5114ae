# coding=utf-8
import json

import mock
from django.utils import timezone
from freezegun import freeze_time

from conversaciones.models import MensajesWhatsapp
from notificaciones import FormaDeEnvioSMS, FormaDeEnvioWhatsapp
from notificaciones.tests.soporte import NotificacionesTestHelper
from occ.models import EnvioDeMensaje, OrigenIncontactable
from prospectos.models import Comentario
from prospectos.tests.distribucion.pedidos.test_pedidos_core import mock_on_commit
from prospectos.tests.support.pages.page_prospectos import DetalleDeProspectoPage
from testing.base import BaseFixturedTest


class AgregarComentarioPreconfiguradoTest(BaseFixturedTest):

    def setUp(self):
        super(AgregarComentarioPreconfiguradoTest, self).setUp()
        self.vendedor = self.fixture['vend_1']
        self.supervisor = self.vendedor.supervisor
        self.login_and_assert_correct(self.vendedor.user)

    @freeze_time("2016-07-10 13:21:34")
    def test_vendedor_agrega_un_comentaraio_con_semantica(self):
        # Dado
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=Comentario.LLAMAR_MAS_TARDE)

        # Entonces
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto="Llamar más tarde")
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, "Llamar más tarde",
                                                             Comentario.LLAMAR_MAS_TARDE)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_sms
    def test_vendedor_agrega_un_comentaraio_con_semantica_envia_mensaje_por_incontactable_via_sms(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_sms(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_con_forma_de_envio(mock_enviar_notificacion, FormaDeEnvioSMS)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_sms
    def test_vendedor_agrega_un_comentaraio_con_semantica_no_envia_sms_por_incontactable_ya_que_fue_enviado(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_sms(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        self._crear_sms_a_incontactable(prospecto)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_whatsapp
    def test_vendedor_agrega_un_comentaraio_con_semantica_envia_mensaje_por_incontactable_via_whatsapp(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_whatsapp(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_con_forma_de_envio(mock_enviar_notificacion, FormaDeEnvioWhatsapp)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_sms
    def test_vendedor_agrega_un_comentaraio_con_semantica_no_envia_mensaje_por_incontactable_ya_que_fue_enviado(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_sms(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        self._crear_sms_a_incontactable(prospecto)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_whatsapp
    def test_vendedor_agrega_un_comentaraio_con_semantica_no_envia_whatsapp_por_incontactable_ya_que_fue_enviado(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_whatsapp(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        self._crear_whatsapp_a_incontactable(prospecto)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_whatsapp
    def test_no_envia_whatsapp_por_incontactable_ya_que_se_envio_un_whatsapp_en_las_ultimas_24_horas(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_whatsapp(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        self._crear_whatsapp_dentro_de_las_ultimas_24_horas(prospecto)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_whatsapp
    def test_no_envia_whatsapp_por_incontactable_ya_que_se_envio_un_sms_en_las_ultimas_24_horas(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_whatsapp(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        self._crear_sms_dentro_de_las_ultimas_24_horas(prospecto)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)

    @freeze_time("2016-07-10 13:21:34")
    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar_sms
    def test_no_envia_sms_por_incontactable_ya_que_se_envio_un_whatsapp_en_las_ultimas_24_horas(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)
        self._habilitar_incontactables_solo_por_sms(notificaciones_helper)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        self._crear_whatsapp_dentro_de_las_ultimas_24_horas(prospecto)
        page = DetalleDeProspectoPage.nuevo_para(prospecto, cliente=self.client)
        semantica = Comentario.NO_CONTESTA

        # Cuando
        respuesta = page.agregar_comentario_con_semantica(etiqueta_semantica=semantica)

        # Entonces
        texto = "No contesta"
        self._assert_respuesta_comentario(respuesta, self.vendedor, texto=texto)
        self._assert_prospecto_tiene_un_comentario_con_texto(prospecto, self.vendedor, texto=texto, semantica=semantica)
        notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)
    # Asserts

    def _assert_respuesta_comentario(self, respuesta, vendedor, texto):
        self.assertEqual(respuesta.status_code, 200)
        contenido = json.loads(respuesta.content)
        self.assertEqual(
            contenido,
            {
                "status": True,
                "prospecto_estado": "estado-en-proceso",
                "comentario": {
                    "vendedor": vendedor.full_name(),
                    "fecha": "2016-07-10T13:21:34Z",
                    "texto": texto
                }
            }
        )

    def _assert_prospecto_tiene_un_comentario_con_texto(self, prospecto, vendedor, texto, semantica):
        comentarios = prospecto.obtener_comentarios()
        self.assertEqual(comentarios.count(), 1)
        comentario = comentarios[0]
        self.assertEqual(comentario.obtener_vendedor(), vendedor)
        self.assertEqual(comentario.texto(), texto)
        self.assertEqual(comentario.obtener_etiqueta_semantica(), semantica)

    # Helpers

    def _habilitar_incontactables_solo_por_sms(self, notificaciones_helper):
        self.vendedor.habilitar_mensaje_incontactables()
        notificaciones_helper.deshabilitar_whatsapp_para(self.vendedor)
        notificaciones_helper.habilitar_sms_para(self.vendedor)

    def _habilitar_incontactables_solo_por_whatsapp(self, notificaciones_helper):
        self.vendedor.habilitar_mensaje_incontactables()
        notificaciones_helper.habilitar_whatsapp_para(self.vendedor)
        notificaciones_helper.deshabilitar_sms_para(self.vendedor)

    def _crear_sms_a_incontactable(self, prospecto):
        envio_de_sms = EnvioDeMensaje.nuevo(
            prospecto, id_mensaje="123", telefono="12341234", mensaje="Hola incontactable")
        envio_de_sms.cambiar_origen(OrigenIncontactable.nuevo())
        return envio_de_sms

    def _crear_whatsapp_a_incontactable(self, prospecto):
        whatsapp = MensajesWhatsapp.nuevo_mensaje(prospecto, telefono="12341234", mensaje="Hola incontactable")
        whatsapp.cambiar_origen(OrigenIncontactable.nuevo())
        return whatsapp

    def _crear_whatsapp_dentro_de_las_ultimas_24_horas(self, prospecto):
        whatsapp = MensajesWhatsapp.nuevo_mensaje(
            prospecto, telefono="12341234", mensaje="Hola incontactable",
            fecha=timezone.now() - timezone.timedelta(hours=6))
        return whatsapp

    def _crear_sms_dentro_de_las_ultimas_24_horas(self, prospecto):
        envio_de_sms = EnvioDeMensaje.nuevo(
            prospecto, id_mensaje="123", telefono="12341234", mensaje="Hola incontactable")
        return envio_de_sms
