from testing.base import BaseLoggedTest, loadResponseJsonAndCheckStatus
from testing.factories import ProspectosFactory


class ProspectosYaLlamadosTest(BaseLoggedTest):
    def test_cambia_el_estado_de_ya_llamado(self):
        c1 = self.fixture['camp_1']
        vendedor = self.user.vendedor
        p1 = ProspectosFactory(campania=c1, vendedor=vendedor, responsable=vendedor.supervisor)
        response = self.client.post('/prospectos/toggle_ya_llamado/%s/' % p1.id)
        response_msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('marca' in response_msg)
        self.assertEqual(response_msg['marca'], '1')

        response = self.client.post('/prospectos/toggle_ya_llamado/%s/' % p1.id)
        response_msg = loadResponseJsonAndCheckStatus(self, response)
        self.assertTrue('marca' in response_msg)
        self.assertEqual(response_msg['marca'], '0')