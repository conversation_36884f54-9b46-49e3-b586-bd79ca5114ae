# coding=utf-8
from django.utils import timezone

from testing.creador_de_contexto import CreadorDeContexto
from testing.base import BaseLoggedSupervisorTest
from vendedores.gestion_de_ventas import GestorDeVenta


class ToDoCreateFactoryTest(BaseLoggedSupervisorTest):
    """
    Esta clase no tiene sentido.
    Esta solo para tener un setup que crea un escenario.
    El escenario deberia estar en un factory y usarse con composición, no herencia.
    Por eso le pusimos un nombre feo. Para que moleste.
    """

    def setUp(self):
        super(ToDoCreateFactoryTest, self).setUp()
        self.fecha_de_hoy = timezone.localtime(timezone.now()).date()
        self.creador_de_contexto = CreadorDeContexto(supervisor=self.supervisor, fixture=self.fixture)
        self.vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor()
        self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor, telefono='12345678')
        prospecto = self.vendedor.prospectos.last()
        self.gestor_de_ventas = GestorDeVenta()
        self.venta = self.gestor_de_ventas.nueva_venta(
            prospecto=prospecto, marca='Ford',
            vendedor=self.vendedor, modelo='fiesta',
            fecha_de_realizacion=self.fecha_de_hoy, precio=8765432,
            numero_de_contrato=1313)
        self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor)
        self.prospecto_sin_venta = self.vendedor.prospectos.last()


