from django.utils.timezone import now, timedelta

from prospectos.models import Comentario
from prospectos.models.logger import Logger
from testing.base import BaseFixturedTest
from testing.factories import (LlamadosFactory, VentasFactory, FinalizacionesFactory,
                               EmailsExtraFactory, TelefonosExtraFactory)


class LoggerTest(BaseFixturedTest):

    def tests_loggear_llamado(self):
        logger = Logger()
        vend = self.fixture['vend_1']
        prospecto = self.fixture['p_1']
        ahora = now()
        en_5 = ahora + timedelta(minutes=5)
        llamado = LlamadosFactory(vendedor=vend, fecha=en_5, prospecto=prospecto)
        logger.loggear_llamado(llamado)
        comentario = Comentario.objects.get(datetime__gte=ahora)
        self.assertEqual(comentario.vendedor, vend)
        self.assertEqual(comentario.prospecto, prospecto)
        texto = en_5.strftime("Llamado planificado para el %d/%m/%Y a las %H:%M")
        self.assertEqual(comentario.comentario, texto)
        self.assertTrue(comentario.automatico)

    def tests_loggear_llamado_cancelado(self):
        logger = Logger()
        vend = self.fixture['vend_1']
        prospecto = self.fixture['p_1']
        ahora = now()
        en_6 = ahora + timedelta(minutes=6)
        logger.loggear_llamado_cancelado(prospecto, prospecto.vendedor, en_6)
        comentario = Comentario.objects.get(datetime__gte=ahora)
        self.assertEqual(comentario.vendedor, vend)
        self.assertEqual(comentario.prospecto, prospecto)
        texto = en_6.strftime("Se cancelo el llamado del %d/%m/%Y a las %H:%M")
        self.assertEqual(comentario.comentario, texto)
        self.assertTrue(comentario.automatico)

    def tests_loggear_venta(self):
        logger = Logger()
        vend = self.fixture['vend_1']
        prospecto = self.fixture['p_1']
        ahora = now()
        hoy = ahora.date()
        venta = VentasFactory(prospecto=prospecto, vendedor=vend, fecha_de_realizacion=hoy, precio=1)
        logger.loggear_venta(venta)
        comentario = Comentario.objects.get(datetime__gte=ahora)
        self.assertEqual(comentario.vendedor, vend)
        self.assertEqual(comentario.prospecto, prospecto)
        texto = hoy.strftime("Venta completada el %d/%m/%Y")
        self.assertEqual(comentario.comentario, texto)
        self.assertTrue(comentario.automatico)

    def tests_loggear_finalizacion(self):
        logger = Logger()
        vend = self.fixture['vend_1']
        prospecto = self.fixture['p_1']
        ahora = now()
        hoy = ahora.date()
        finalizacion = FinalizacionesFactory(prospecto=prospecto, vendedor=vend)
        logger.loggear_finalizacion(finalizacion)
        comentario = Comentario.objects.get(datetime__gte=ahora)
        self.assertEqual(comentario.vendedor, vend)
        self.assertEqual(comentario.prospecto, prospecto)
        texto = hoy.strftime("Seguimiento finalizado el %d/%m/%Y")
        self.assertEqual(comentario.comentario, texto)
        self.assertTrue(comentario.automatico)

    def tests_loggear_telefono_extra(self):
        logger = Logger()
        vend = self.fixture['vend_1']
        prospecto = self.fixture['p_1']
        ahora = now()
        hoy = ahora.date()
        extra = TelefonosExtraFactory(prospecto=prospecto, vendedor=vend, telefono='1234')
        tipo = 'tel'
        logger.loggear_extra(extra, tipo)
        comentario = Comentario.objects.get(datetime__gte=ahora)
        self.assertEqual(comentario.vendedor, vend)
        self.assertEqual(comentario.prospecto, prospecto)
        texto = hoy.strftime("Telefono adicional agregado el %d/%m/%Y")
        self.assertEqual(comentario.comentario, texto)
        self.assertTrue(comentario.automatico)

    def tests_loggear_email_extra(self):
        logger = Logger()
        vend = self.fixture['vend_1']
        prospecto = self.fixture['p_1']
        ahora = now()
        hoy = ahora.date()
        extra = EmailsExtraFactory(prospecto=prospecto, vendedor=vend, email='1234')
        tipo = 'mail'
        logger.loggear_extra(extra, tipo)
        comentario = Comentario.objects.get(datetime__gte=ahora)
        self.assertEqual(comentario.vendedor, vend)
        self.assertEqual(comentario.prospecto, prospecto)
        texto = hoy.strftime("Email adicional agregado el %d/%m/%Y")
        self.assertEqual(comentario.comentario, texto)
        self.assertTrue(comentario.automatico)
