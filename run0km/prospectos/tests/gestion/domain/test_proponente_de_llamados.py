# coding=utf-8
import datetime
from datetime import time

from django.test import override_settings
from django.utils import timezone
from freezegun import freeze_time

from core.date_helper import DatetimeHelper
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from prospectos.models.proponente_de_llamados import ProponenteDeLlamados
from testing.base import BaseFixturedTest


@override_settings(HORARIOS_LABORALES={'ENTRADA_DIA_SEMANA': time(9, 0, 0, 0), 'SALIDA_DIA_SEMANA': time(18, 0, 0, 0),
                                       'ENTRADA_SABADO': time(9, 0, 0, 0), 'SALIDA_SABADO': time(14, 0, 0, 0)})
@freeze_time("2016-10-26 09:00:00.0-03:00")
class ProponenteDeLlamadosTest(BaseFixturedTest):

    def setUp(self):
        super(ProponenteDeLlamadosTest, self).setUp()
        self.datetime_helper = DatetimeHelper()
        self.maniana = self.datetime_helper.tomorrow()
        self.vendedor = self.fixture['vend_1']
        self.vendedor.todos_los_prospectos().delete()
        self.prospecto_uno = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor)
        self.prospecto_dos = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor)

    def test_sin_llamados_y_sin_agendados_propone_el_horario_del_prospecto(self):
        """
            Cuando el vendedor no intentó llamar al prospecto, y es el primer llamado que le agenda, entonces
            propone el dia siguiente al mismo horario del prospecto
        """
        # Dado
        proponente = ProponenteDeLlamados.nuevo()

        # Cuando
        fecha_y_hora = proponente.evaluar(self.prospecto_uno, self.vendedor)

        # Entonces
        horario_esperado = self._horario_de_ingreso_de(self.prospecto_uno)
        fecha_esperada = self.datetime_helper.combine(self.maniana.date(), horario_esperado)
        self.assertEqual(fecha_y_hora, fecha_esperada)

    @freeze_time("2016-10-26 12:00:00.0-03:00")
    @override_settings(SELECTOR_HORARIOS_MINUTOS_DE_MARGEN=10)
    def test_sin_llamados_realizados_y_con_llamados_ya_agendados_propone_el_mejor_horario_para_el_vendedor(self):
        """
            Cuando el vendedor no intento llamar al prospecto, pero ya se agendó en el pasado varios llamados, entonces
            propone el dia siguiente al horario dado por SelectorDeHorarioDeProgramacionDeLlamadas

            En este caso, la hora actual es 12:00hs de un dia laboral, el horario de cierre es a las 18, por lo que
            propone agendar a las 9:00hs del dia siguiente
        """
        # Dado
        proponente = ProponenteDeLlamados.nuevo()
        ayer = self.datetime_helper.now() - timezone.timedelta(days=1)
        self._agregar_nueva_llamada_programada_a(self.prospecto_uno, fecha=ayer, vendedor=self.vendedor)

        # Cuando
        fecha_y_hora = proponente.evaluar(self.prospecto_uno, self.vendedor)

        # Entonces
        fecha_esperada = self.datetime_helper.combine(self.maniana.date(), time(9, 0))
        self.assertEqual(fecha_y_hora, fecha_esperada)

    @freeze_time("2016-10-26 12:00:00.0-03:00")
    @override_settings(SELECTOR_HORARIOS_MINUTOS_DE_MARGEN=10)
    def test_sin_llamados_y_con_llamados_ya_agendados_propone_el_mejor_horario_para_el_vendedor_con_configuracion_intervalo_entre_llamados(self):
        """
            Cuando el vendedor no intento llamar al prospecto, pero ya se agendó en el pasado varios llamados, entonces
            propone el dia siguiente al horario dado por SelectorDeHorarioDeProgramacionDeLlamadas

            En este caso, la hora actual es 12:00hs de un dia laboral, el horario de cierre es a las 18, por lo que
            propone agendar a las 9:00hs del dia siguiente
        """
        # Dado
        proponente = ProponenteDeLlamados.nuevo()
        ayer = self.datetime_helper.now() - timezone.timedelta(days=1)
        maniana = self.datetime_helper.combine(self.maniana.date(), time(9, 0))
        self._configurar_intervalo_entre_llamados_de_una_hora()
        self._agregar_nueva_llamada_programada_a(self.prospecto_uno, fecha=ayer, vendedor=self.vendedor)
        self._agregar_nueva_llamada_programada_a(self.prospecto_dos, fecha=maniana, vendedor=self.vendedor)

        # Cuando
        fecha_y_hora = proponente.evaluar(self.prospecto_uno, self.vendedor)

        # Entonces
        fecha_esperada = self.datetime_helper.combine(self.maniana.date(), time(10, 0))
        self.assertEqual(fecha_y_hora, fecha_esperada)

    def test_vendedor_se_comunico_anteriormente_propone_mismo_horario_de_la_llamada(self):
        """
            Cuando el vendedor ya tiene una comunicacion exitosa con el prospecto, le propone el mismo horario
            de la llamada al dia siguiente
        """
        proponente = ProponenteDeLlamados.nuevo()
        ayer = self.datetime_helper.now() - timezone.timedelta(days=1)
        hora_de_ultima_llamada = time(13, 15)
        llamado_agendado_test = self.datetime_helper.combine(self.maniana.date(), hora_de_ultima_llamada)
        fecha_de_inicio = self.datetime_helper.combine(ayer, hora_de_ultima_llamada)
        self._agregar_llamada_realizada_a(
            self.prospecto_uno, vendedor=self.vendedor, fecha_de_inicio=fecha_de_inicio, duracion=60)

        self._agregar_nueva_llamada_programada_a(self.prospecto_dos, fecha=llamado_agendado_test, vendedor=self.vendedor)

        # Cuando
        fecha_y_hora = proponente.evaluar(self.prospecto_uno, self.vendedor)

        # Entonces
        fecha_esperada = self.datetime_helper.combine(self.maniana, hora_de_ultima_llamada)
        self.assertEqual(fecha_y_hora, fecha_esperada)

    def test_vendedor_con_varias_comunicaciones_anteriores_propone_mismo_horario_de_la_ultima_llamada(self):
        """
            Cuando el vendedor ya tiene dos comunicaciones exitosas con el prospecto y una comunicacion no exitosa, le
            propone el mismo horario de la ultima llamada exitosa al dia siguiente
        """
        proponente = ProponenteDeLlamados.nuevo()
        ayer = self.datetime_helper.now() - timezone.timedelta(days=1)
        hora_de_ultima_llamada = time(13, 15)
        fecha_de_inicio = self.datetime_helper.combine(ayer, hora_de_ultima_llamada)

        self._agregar_llamada_realizada_a(
            self.prospecto_uno, vendedor=self.vendedor,
            fecha_de_inicio=fecha_de_inicio - timezone.timedelta(days=3, hours=3), duracion=60)

        self._agregar_llamada_realizada_a(
            self.prospecto_uno, vendedor=self.vendedor, fecha_de_inicio=fecha_de_inicio, duracion=60)

        self._agregar_llamada_realizada_a(
            self.prospecto_uno, vendedor=self.vendedor,
            fecha_de_inicio=fecha_de_inicio + timezone.timedelta(hours=3), duracion=29)

        # Cuando
        fecha_y_hora = proponente.evaluar(self.prospecto_uno, self.vendedor)

        # Entonces
        fecha_esperada = self.datetime_helper.combine(self.maniana, hora_de_ultima_llamada)
        self.assertEqual(fecha_y_hora, fecha_esperada)

    def test_vendedor_no_se_pudo_comunicar_propone_un_horario_diferente_a_los_intentos_de_llamados(self):
        """
            Cuando el vendedor tiene intentos de llamadas pero que no pudo comunicarse, entonces
            propone el dia siguiente en un horario diferente a los intentos de llamadas anteriores


            Detalle para continuar: proponer al mediodia y luego a la tarde, luego de repetir los rangos y con 7
            intentos de llamados proponer el sabado entre las 9 y 13hs. luego a las 8 am y luego a las 19hs.
        """
        pass

    # Helpers
    def _agregar_nueva_llamada_programada_a(self, prospecto, fecha, vendedor):
        from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        llamado = gestor.programar_nuevo_llamado_para(prospecto, fecha=fecha, debe_sincronizar=False)
        return llamado

    def _agregar_llamada_realizada_a(self, prospecto, vendedor, duracion, fecha_de_inicio):
        gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
        gestor.agregar_llamada_realizada(prospecto, fecha_inicio=fecha_de_inicio, duracion=duracion)

    def _horario_de_ingreso_de(self, prospecto):
        fecha_de_ingreso = self.datetime_helper.to_localtime(prospecto.obtener_fecha())
        return fecha_de_ingreso.time()

    def _configurar_intervalo_entre_llamados_de_una_hora(self):
        concesionaria = self.vendedor.obtener_concesionaria()
        intervalo_entre_llamados = datetime.time(1, 0)
        concesionaria.configurar_intervalo_entre_llamados(intervalo_entre_llamados)
        concesionaria.save()