# coding=utf-8
import json

import mock
from django.conf import settings
from requests import HTTPError
from rest_framework import status

from lib.api_client.errors import ClientConnectionError
from lib.api_client.testcase import APIClientTestCase
from client_crm import (
    CRMIntegrationAPIClient,
)
from client_crm.tests import ALL_BRANDS, ALL_PLAN_TYPES, SOME_MODELS, SOME_PLANS


class CRMRequestMock(object):
    def __init__(self, status_code, content):
        self.status_code = status_code
        self.data = content
        self.content = json.dumps(self.data)

    @classmethod
    def brands_success(cls):
        return cls(status_code=status.HTTP_200_OK,
                   content=ALL_BRANDS)

    @classmethod
    def models_success(cls):
        return cls(status_code=status.HTTP_200_OK,
                   content=SOME_MODELS)

    @classmethod
    def plans_success(cls):
        return cls(status_code=status.HTTP_200_OK,
                   content=SOME_PLANS)

    @classmethod
    def plan_types_success(cls):
        return cls(status_code=status.HTTP_200_OK,
                   content=ALL_PLAN_TYPES)

    @classmethod
    def fail(cls):
        return cls(status_code=status.HTTP_401_UNAUTHORIZED,
                   content={'detail': 'Token inválido.'})

    def raise_for_status(self):
        http_error_msg = ''
        if 400 <= self.status_code < 500:
            http_error_msg = '%s Client Error' % self.status_code
        elif 500 <= self.status_code < 600:
            http_error_msg = '%s Server Error' % self.status_code
        if http_error_msg:
            raise HTTPError(http_error_msg, response=self)

    def json(self):
        return json.loads(self.content)


class TestClientCRM(APIClientTestCase):
    @mock.patch('requests.get', return_value=CRMRequestMock.brands_success())
    def test_brands_endpoint_gets_all_brands(self, _):
        # TODO: verificar porque en jenkins tira error
        pass
        # client = CRMIntegrationAPIClient.brands_api_client(settings.CRM_INTEGRATION_API_TOKEN)
        # response = client.call()
        # self.assertEqual(client.url, 'http://www.run0km.com/integrations/api/brands/')
        # self.assertEqual(response, ALL_BRANDS)

    @mock.patch('requests.get', return_value=CRMRequestMock.models_success())
    def test_models_endpoint_gets_all_models(self, _):
        client = CRMIntegrationAPIClient.models_api_client(settings.CRM_INTEGRATION_API_TOKEN)
        response = client.call()
        self.assertEqual(client.url, 'http://www.run0km.com/integrations/api/models/?allow-finalized=1')
        self.assertEqual(response, SOME_MODELS)

    @mock.patch('requests.get', return_value=CRMRequestMock.plans_success())
    def test_plans_endpoint_gets_all_plans(self, _):
        client = CRMIntegrationAPIClient.plans_api_client(settings.CRM_INTEGRATION_API_TOKEN)
        response = client.call()
        self.assertEqual(client.url, 'http://www.run0km.com/integrations/api/plans/?allow-finalized=1')
        self.assertEqual(response, SOME_PLANS)

    @mock.patch('requests.get', return_value=CRMRequestMock.plan_types_success())
    def test_plan_types_endpoint_gets_all_plan_types(self, _):
        client = CRMIntegrationAPIClient.plan_types_api_client(settings.CRM_INTEGRATION_API_TOKEN)
        response = client.call()
        self.assertEqual(client.url, 'http://www.run0km.com/integrations/api/plan-types/?allow-finalized=1')
        self.assertEqual(response, ALL_PLAN_TYPES)

    @mock.patch('requests.get', return_value=CRMRequestMock.fail())
    def test_invalid_token_raises_authorization_error(self, _):
        client = CRMIntegrationAPIClient.plan_types_api_client('TOKEN_FRUTA')
        self.assertRaisesWithResponse(ClientConnectionError, {'detail': 'Token inválido.'}, client.call)
