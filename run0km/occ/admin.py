# coding=utf-8
from django.contrib import admin
from django.urls import reverse
from django.utils.safestring import mark_safe

from log_de_errores.admin import LogDeErrorAdmin
from occ.admin_forms import CampaniaDeComunicacionAdminForm, EnvioDeMensajeAdminForm, ConfiguracionDeIntegracionForm
from occ.models import Medio, LogDeErrorDeSMS, CreditoDeSMS, CampaniaDeComunicacion, \
    LogDeErrorActualizacionDeEstadoSMS, RespuestaDeMensaje, EnvioDeMensaje, Compulsa, ParticipacionDeCompulsa
from occ.models.anura import IntentoDeLlamado, LlamadaRealizadaDeAnura
from occ.models.eavisos import PreguntaInvalidaDePublicacionEAvisos
from occ.models.integracion_crms import ConfiguracionDeIntegracion
from occ.models.log_de_servicios import LogDeIntegracion
from occ.models.novedad import Novedad
from occ.novedades.servicio_de_novedades import ServicioDeNovedades


class PreguntaInvalidaDePublicacionEAvisosAdmin(admin.ModelAdmin):
    list_filter = ('_lugar',)
    list_display = ('_lugar', '_link_de_la_publicacion', '_created_at')

    def has_add_permission(self, request):
        return False

    # def get_lugar(self, envio):
    #   pass
    #
    # get_lugar.short_description = 'lugar'
    # get_lugar.admin_order_field = '_lugar'

    def get_readonly_fields(self, request, obj=None):
        return '_lugar', '_link_de_la_publicacion', '_created_at', '_content', '_id_externo', \
               '_id_lugar_de_publicacion', '_id_de_la_publicacion_en_lugar_de_publicacion'


class LogDeErrorSmsAdmin(admin.ModelAdmin):
    list_display = ('fecha', 'campania', 'tipo', 'descripcion')
    list_filter = ('tipo',)
    date_hierarchy = 'fecha'

    def has_add_permission(self, request):
        return False

    def get_readonly_fields(self, request, obj=None):
        return 'fecha', 'campania', 'tipo', 'descripcion', 'request', 'response'


class CreditoDeSmsAdmin(admin.ModelAdmin):
    list_display = ('vendedor', 'credito_base', 'credito_extra')
    search_fields = ['vendedor__user__first_name', 'vendedor__user__last_name']

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + ('credito_base', 'vendedor')
        return self.readonly_fields

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class EnvioDeMensajeAdmin(admin.ModelAdmin):
    form = EnvioDeMensajeAdminForm
    list_display = ('telefono', 'fecha', 'get_campania', 'mensaje', 'estado', 'id_mensaje')
    date_hierarchy = 'fecha'
    list_filter = ('estado',)
    search_fields = ['id_mensaje', 'telefono']

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return 'telefono', 'fecha', 'get_campania', 'mensaje', 'estado'

    def get_campania(self, envio):
        if hasattr(envio, 'envio_de_campania'):
            return envio.envio_de_campania.campania
        else:
            return None

    get_campania.short_description = 'Campania'
    get_campania.admin_order_field = 'envio__envio_de_campania__campania'


class RespuestaDeMensajeAdmin(admin.ModelAdmin):
    list_display = ('get_nombre_prospecto', 'fecha', 'mensaje', 'get_vendedor', 'get_id_mensaje')
    date_hierarchy = 'fecha'

    def get_nombre_prospecto(self, respuesta):
        return respuesta.envio.prospecto.nombre

    get_nombre_prospecto.short_description = 'Prospecto'
    get_nombre_prospecto.admin_order_field = 'envio__prospecto__nombre'

    def get_id_mensaje(self, respuesta):
        return respuesta.envio.id_mensaje

    get_id_mensaje.short_description = 'Id mensaje'
    get_id_mensaje.admin_order_field = 'envio__id_mensaje'

    def get_vendedor(self, respuesta):
        return respuesta.envio.envio_de_campania.campania.vendedor

    get_vendedor.short_description = 'Vendedor'
    get_vendedor.admin_order_field = 'envio__envio_de_campania__campania__vendedor'

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return 'envio', 'fecha', 'mensaje', 'leida'


class CampaniaDeComunicacionAdmin(admin.ModelAdmin):
    form = CampaniaDeComunicacionAdminForm
    list_display = ('vendedor', 'envio_mensajes_correcto', 'fecha_de_creacion', 'envio_mensajes_correcto_info',
                    'get_count_prospectos', 'get_count_envios', 'mensaje')
    list_filter = ('envio_mensajes_correcto',)

    def get_count_prospectos(self, campania):
        return campania.prospectos.count()

    get_count_prospectos.short_description = 'Cantidad de prospectos'
    get_count_prospectos.admin_order_field = 'prospectos'

    def get_count_envios(self, campania):
        return campania.envios.count()

    get_count_envios.short_description = 'Cantidad de envios'
    get_count_envios.admin_order_field = 'envios'

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class LogDeErrorActualizacionDeEstadoSMSAdmin(LogDeErrorAdmin):
    pass


class ConfiguracionDeIntegracionAdmin(admin.ModelAdmin):
    form = ConfiguracionDeIntegracionForm


class LogDeIntegracionAdmin(admin.ModelAdmin):
    list_display = ['configuracion', 'fecha', 'descripcion', 'fue_exitoso']
    list_filter = ['configuracion', 'fue_exitoso']
    date_hierarchy = 'fecha'
    exclude = ('prospectos_id',)

    def has_add_permission(self, request):
        return False

    def get_readonly_fields(self, request, obj=None):
        return 'configuracion', 'fecha', 'descripcion', 'fue_exitoso'


class ParticipacionDeCompulsaInlineAdmin(admin.TabularInline):
    model = ParticipacionDeCompulsa
    readonly_fields = ['participante', 'marca', 'intento', 'orden', 'rechazada']
    extra = 0
    can_delete = False


class CompulsaAdmin(admin.ModelAdmin):
    list_display = ['fecha', 'token', 'campania', 'marca_actual', 'get_ganador']
    date_hierarchy = 'fecha'
    list_filter = ['campania']
    search_fields = ['participaciones__participante__user__first_name',
                     'participaciones__participante__user__last_name']
    inlines = [ParticipacionDeCompulsaInlineAdmin]

    def has_add_permission(self, request):
        return False

    def get_queryset(self, request):
        qs = super(CompulsaAdmin, self).get_queryset(request)
        qs.prefetch_related('campania', 'finalizacion')
        return qs

    def get_readonly_fields(self, request, obj=None):
        return ['fecha', 'token', 'campania', 'marcas', 'marca_actual', 'cantidad_de_reintentos',
                'tamanio_ventana_vendedores', '_pedido']

    def get_ganador(self, compulsa):
        if not compulsa.esta_vigente() and compulsa.obtener_finalizacion().es_con_ganador():
            return compulsa.obtener_finalizacion().ganador
        else:
            return None

    get_ganador.short_description = 'Ganador'
    get_ganador.admin_order_field = 'finalizacion__finalizacion_finalizacion_exitosa__ganador'


class NovedadAdmin(admin.ModelAdmin):
    list_display = ['_nombre', '_activa', '_para_vendedores', '_para_supervisores', '_para_gerentes',
                    'preview_link']
    list_filter = ['_activa', '_para_vendedores', '_para_supervisores', '_para_gerentes']
    list_display_links = ['_nombre']

    def preview_link(self, novedad):
        return mark_safe('<a href="%s" target="_blank">' % reverse("preview_novedad",
                                                                   args=[
                                                                       novedad.pk]) + "Ver previsualización" + "</a>")

    def save_model(self, request, obj, form, change):
        super(NovedadAdmin, self).save_model(request, obj, form, change)
        servicio_de_novedades = ServicioDeNovedades()
        se_activo_la_novedad = '_activa' in form.changed_data and form.cleaned_data['_activa']
        if se_activo_la_novedad:
            servicio_de_novedades.novedad_activada(obj)


class IntentoDeLlamadoAdmin(admin.ModelAdmin):
    date_hierarchy = '_fecha'

    list_display = ['id', '_telefono', '_vendedor', '_prospecto', '_fecha']
    list_filter = ['_fecha']
    list_display_links = ['id', '_telefono']

    readonly_fields = ['id', '_telefono', '_vendedor', '_prospecto', '_fecha']

    def has_add_permission(self, request):
        return False


class LlamadaRealizadaDeAnuraAdmin(admin.ModelAdmin):
    date_hierarchy = '_fecha_comienzo'

    list_display = ['id', '_intento_de_llamado', '_fecha_comienzo', '_id_externo', '_duracion',
                    'tiene_archivo_de_audio']
    list_filter = ['_fecha_comienzo', '_intento_de_llamado___vendedor__concesionaria']
    list_display_links = ['id', '_intento_de_llamado']

    readonly_fields = ['id', '_intento_de_llamado', '_fecha_comienzo', '_id_externo', '_duracion',
                       'tiene_archivo_de_audio']

    def tiene_archivo_de_audio(self, obj):
        return obj.tiene_audio()

    tiene_archivo_de_audio.is_boolean = True

    def has_add_permission(self, request):
        return False

admin.site.register(Medio)
admin.site.register(Novedad, NovedadAdmin)
admin.site.register(LogDeErrorDeSMS, LogDeErrorSmsAdmin)
admin.site.register(CreditoDeSMS, CreditoDeSmsAdmin)
admin.site.register(EnvioDeMensaje, EnvioDeMensajeAdmin)
admin.site.register(CampaniaDeComunicacion, CampaniaDeComunicacionAdmin)
admin.site.register(LogDeErrorActualizacionDeEstadoSMS, LogDeErrorActualizacionDeEstadoSMSAdmin)
admin.site.register(RespuestaDeMensaje, RespuestaDeMensajeAdmin)
admin.site.register(ConfiguracionDeIntegracion, ConfiguracionDeIntegracionAdmin)
admin.site.register(LogDeIntegracion, LogDeIntegracionAdmin)
admin.site.register(Compulsa, CompulsaAdmin)
admin.site.register(PreguntaInvalidaDePublicacionEAvisos, PreguntaInvalidaDePublicacionEAvisosAdmin)
admin.site.register(IntentoDeLlamado, IntentoDeLlamadoAdmin)
admin.site.register(LlamadaRealizadaDeAnura, LlamadaRealizadaDeAnuraAdmin)
