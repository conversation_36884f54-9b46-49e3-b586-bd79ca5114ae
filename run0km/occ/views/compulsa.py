from django.core.exceptions import PermissionDenied
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.views.generic import View

from occ.errores_de_chat import ErrorDeServicioDeChat
from occ.models import Compulsa
from occ.servicio_de_chat_de_ventas import ServicioDeChatDeVentas


class OCCResponderCompulsa(View):
    def __init__(self, **kwargs):
        super(OCCResponderCompulsa, self).__init__(**kwargs)
        self.vendedor = None

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_vendedor() or not self._tiene_servicio_habilitado(request.user.vendedor):
            raise PermissionDenied
        else:
            self.vendedor = request.user.vendedor
            return super(OCCResponderCompulsa, self).dispatch(request, *args, **kwargs)

    def _tiene_servicio_habilitado(self, vendedor):
        return ServicioDeChatDeVentas.esta_habilitado(vendedor)

    def _responder(self, compulsa):
        raise NotImplementedError('subclass responsibility')

    def post(self, request, compulsa_pk):
        compulsa = get_object_or_404(Compulsa, pk=compulsa_pk)
        response = {'status': True}
        try:
            self._responder(compulsa)
        except ErrorDeServicioDeChat as exc:
            response = {'status': False, 'message': str(exc)}

        return JsonResponse(response)


class OCCAceptarCompulsaView(OCCResponderCompulsa):
    def _responder(self, compulsa):
        ServicioDeChatDeVentas().aceptar_compulsa(self.vendedor, compulsa)


class OCCRechazarCompulsaView(OCCResponderCompulsa):
    def _responder(self, compulsa):
        ServicioDeChatDeVentas().rechazar_compulsa(self.vendedor, compulsa)