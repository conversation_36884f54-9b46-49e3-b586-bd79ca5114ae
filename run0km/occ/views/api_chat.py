import logging

from django.shortcuts import get_object_or_404
from django.utils.cache import add_never_cache_headers
from rest_framework import status
from rest_framework.exceptions import ParseError
from rest_framework.response import Response
from rest_framework.views import APIView

from occ import parsers
from occ.errores_de_chat import ErrorDePedido
from occ.models import ChatDeVentas
from occ.servicio_de_chat_de_ventas import ServicioDeChatDeVentas
from occ.soporte_chat import DispatcherDePedidoDeChat


class OCCChatDeVentasView(APIView):
    logger = logging.getLogger('sales_chat_service')

    def _responder_request_invalido(self, mensaje):
        self.logger.debug('Response request invalid: %s' % mensaje)
        return Response(
            mensaje,
            status=status.HTTP_400_BAD_REQUEST
        )

    def _responder_200_OK(self):
        self.logger.debug('Response Ok')
        response = Response(status=status.HTTP_200_OK)
        add_never_cache_headers(response)
        return response

    def get_parsers(self):
        raise NotImplementedError('subclass responsibility')


class OCCRecibirEventoDeChatDeVentaView(OCCChatDeVentasView):
    def get_parsers(self):
        return [parsers.OCCParser.recibir_mensaje()]

    def post(self, request, chat_pk):
        try:
            # implicitly calls parser_classes
            pedido_json = request.data
        except ParseError as error:
            return self._responder_request_invalido('Invalid JSON - %s' % error)
        if not pedido_json:
            return self._responder_request_invalido('Invalid JSON - JSON vacio')
        try:
            self.logger.debug("Mensaje recibido!")
            self.logger.debug(pedido_json)
            chat = get_object_or_404(ChatDeVentas, pk=chat_pk)
            dispatcher_de_pedido_de_chat = DispatcherDePedidoDeChat.nuevo_para(pedido_json)
            dispatcher_de_pedido_de_chat.ejecutar_pedido_en(ServicioDeChatDeVentas(), parameters={'chat': chat})
        except ErrorDePedido as exc:
            self._responder_request_invalido(str(exc))
        return self._responder_200_OK()


class OCCPedidoDeOperadorParaChatDeVentaView(OCCChatDeVentasView):
    def get_parsers(self):
        return [parsers.OCCParser.pedido_de_operador()]

    def post(self, request):
        try:
            # implicitly calls parser_classes
            pedido_json = request.data
        except ParseError as error:
            return self._responder_request_invalido('Invalid JSON - %s' % error)
        if not pedido_json:
            return self._responder_request_invalido('Invalid JSON - JSON vacio')
        try:
            self.logger.debug("Pedido De Operador!")
            self.logger.debug(pedido_json)
            self._atender_pedido(pedido_json)
        except ErrorDePedido as exc:
            return self._responder_request_invalido(str(exc))
        return self._responder_200_OK()

    def _atender_pedido(self, pedido_json):
        dispatcher_de_pedido_de_chat = DispatcherDePedidoDeChat.nuevo_para(pedido_json)
        dispatcher_de_pedido_de_chat.ejecutar_pedido_en(ServicioDeChatDeVentas())