# coding=utf-8
import logging

from django.conf import settings
from django.contrib.sites.models import Site
from django.urls import reverse
from django.db import IntegrityError
from django.utils.module_loading import import_string

from campanias.models import Campania
from core.models import Sistema
from occ import tasks
from occ.errores_de_chat import ErrorDeServicioDeChat, ErrorDePedido, ErrorDeCompulsa, CantidadDeReintentosSuperadaError
from occ.models import Compulsa, ChatDeVentas, ChatDeVentasConvertido, OrigenPropuesta
from occ.servicio_de_notificaciones import ServicioDeNotificaciones
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto

logger = logging.getLogger('sales_chat_service')


class ServicioDeChatDeVentas(object):
    @classmethod
    def esta_habilitado(cls, vendedor):
        return vendedor.configuracion_de_servicios().habilitado_para_chatear()

    def __init__(self):
        self.servicio_de_notificacion = ServicioDeNotificaciones()
        try:
            chat_service_class = import_string(settings.CHAT_PROVIDER)
        except ImportError:
            raise ValueError('Debe configurar el servicio de chat')

        self.proveedor_de_chat = chat_service_class(assign_operator_url=settings.CHAT_ASSIGN_OPERATOR_URL,
                                                    send_message_url=settings.CHAT_SEND_MESSAGE_URL)

    def generar_chat_convertido_para_prospecto(self, prospecto, token_de_chat):
        vendedor = prospecto.vendedor
        logger.debug("CHAT: convirtiendo chat de prospecto %s" % prospecto.pk)
        if vendedor:
            try:
                chat = vendedor.chats_de_ventas.no_convertidos().con_token(token_de_chat)
            except ChatDeVentas.DoesNotExist:
                return
            else:
                ChatDeVentasConvertido.nuevo(prospecto=prospecto, chat=chat)
                self._cambiar_marca_de_prospecto_por_interseccion_marcas_supervisor_y_chat(
                    prospecto=prospecto, chat=chat)

    def _cambiar_marca_de_prospecto_por_interseccion_marcas_supervisor_y_chat(self, prospecto, chat):
        supervisor = prospecto.obtener_responsable()
        marcas_del_chat = chat.marcas()
        marcas_del_supervisor = supervisor.marcas_pedidas()
        interseccion_de_marcas = list(set(marcas_del_chat) & set(marcas_del_supervisor))
        if interseccion_de_marcas:
            gestor = GestorDeProspecto.nuevo_para(rol=supervisor)
            nombre = interseccion_de_marcas[0]
            gestor.cambiar_marca_de_nombre(prospecto=prospecto, nombre_de_marca=nombre)

    def atender_pedido_de_operador(self, pedido):
        compulsa = self._crear_compulsa_para_pedido(pedido)
        try:
            participaciones = compulsa.proximos_vendedores()
        except ErrorDeCompulsa as exc:
            raise ErrorDeServicioDeChat.nuevo(str(exc))
        except CantidadDeReintentosSuperadaError:
            raise ErrorDePedido.no_hay_vendedores_que_cumplan_los_requisitos()
        else:
            participaciones_ids = [participante.pk for participante in participaciones]
            tasks.enviar_participacion_a_compulsa.delay(compulsa.pk, participaciones_ids, self.servicio_de_notificacion)

    def reintentar_compulsa(self, compulsa):
        try:
            participaciones = compulsa.proximos_vendedores()
        except ErrorDeCompulsa as exc:
            raise ErrorDeServicioDeChat.nuevo(str(exc))
        except CantidadDeReintentosSuperadaError:
            pass
        else:
            participaciones_ids = [participante.pk for participante in participaciones]
            tasks.enviar_participacion_a_compulsa.delay(compulsa.pk, participaciones_ids, self.servicio_de_notificacion)

    def rechazar_compulsa(self, vendedor, compulsa):
        self._asegurar_participacion_en(compulsa, vendedor)
        try:
            compulsa.eliminar_participante(vendedor)
        except IntegrityError:
            return

    def aceptar_compulsa(self, vendedor, compulsa):
        self._asegurar_participacion_en(compulsa, vendedor)
        try:
            self._finalizar_compulsa_con_ganador(compulsa, ganador=vendedor)
        except IntegrityError:
            return
        chat = self._iniciar_chat(vendedor, compulsa)
        url = self._url_para(chat)
        tasks.asignar_operador.delay(
            chat.pk, url, compulsa.marca().title(), self.proveedor_de_chat, self.servicio_de_notificacion)

    def recibir_mensaje(self, chat, pedido):
        texto = pedido.texto()
        variables_de_contacto = pedido.variables()
        mensaje = chat.agregar_respuesta_y_variables_de_contacto(texto, variables_de_contacto)
        logger.debug(
            "CHAT: mensaje recibido a chat (pk=%s), fecha: %s, texto: %s" % (chat.pk, mensaje.fecha, mensaje.texto))
        tasks.recibir_mensaje.delay(chat.pk, mensaje.pk, variables_de_contacto, self.servicio_de_notificacion)

    def finalizar_chat(self, chat):
        chat.finalizar()
        tasks.finalizar_chat.delay(chat.pk, self.servicio_de_notificacion)

    def enviar_propuesta(self, propuesta, chat):
        # TODO: coordinar con Karel para ver cómo nos envia esta info si la imagen esta o no incluida en el texto
        texto = propuesta.texto_chat() or ''
        imagen = propuesta.imagen_chat() or ''
        texto_a_enviar = texto or imagen

        if texto_a_enviar:
            mensaje = self.enviar_mensaje(texto_a_enviar.strip(), chat)
            OrigenPropuesta.nuevo_mensaje(mensaje, propuesta)

    def enviar_mensaje(self, texto, chat):
        mensaje = chat.agregar_envio_con(texto)
        logger.debug(
            "CHAT: mensaje enviado, chat (pk=%s), fecha: %s, texto: %s" % (chat.pk, mensaje.fecha, mensaje.texto))
        tasks.enviar_mensaje.delay(texto, chat.pk, self.proveedor_de_chat)
        return mensaje

    def timeout_de_compulsa(self, compulsa):
        if compulsa.esta_vigente():
            self.reintentar_compulsa(compulsa)

    def marcar_mensajes_de_chat_como_leidos(self, chat):
        chat.marcar_mensajes_como_leidos()

    def _crear_compulsa_para_pedido(self, pedido):
        campania = self._campania_de(pedido)
        cantidad_de_vendedores_por_intento = Sistema.instance().cantidad_de_participantes_compulsa
        compulsa = Compulsa.nueva(campania=campania, pedido=pedido,
                                  tamanio_ventana_vendedores=cantidad_de_vendedores_por_intento)
        return compulsa

    def _asegurar_participacion_en(self, compulsa, vendedor):
        if not compulsa.esta_vigente():
            raise ErrorDeServicioDeChat.compulsa_no_esta_vigente()
        if not compulsa.es_participante(vendedor):
            raise ErrorDeServicioDeChat.vendedor_no_participa()

    def _url_para(self, chat):
        dominio = Site.objects.get_current().domain
        view = reverse('recibir-evento-visitante', kwargs={'chat_pk': chat.pk})
        url = '%s://%s%s' % (settings.SCHEMA, dominio, view)
        return url

    def _finalizar_compulsa_con_ganador(self, compulsa, ganador):
        finalizacion_de_compulsa_exitosa = compulsa.finalizar_con_ganador(ganador=ganador)
        tasks.enviar_cierre_de_compulsa.delay(finalizacion_de_compulsa_exitosa.pk, self.servicio_de_notificacion)

    def _finalizar_compulsa_por_time_out(self, compulsa):
        compulsa.finalizar_por_timeout()

    def _iniciar_chat(self, ganador, compulsa):
        pedido = compulsa.pedido()
        chat = ChatDeVentas.nuevo_para_pedido(vendedor=ganador, token=compulsa.token, pedido=pedido)
        return chat

    def _campania_de(self, pedido):
        campania_nombre = pedido.nombre_de_campania()
        try:
            campania = Campania.objects.campanias_no_externas_con_nombre(nombre=campania_nombre)
            return campania
        except Campania.DoesNotExist:
            raise ErrorDePedido.nombre_de_campania_inexistente(nombre=campania_nombre)

    def cantidad_de_mensajes_no_leidos_para(self, vendedor):
        return ChatDeVentas.objects.cantidad_de_mensajes_no_leidos_en_ultimos_chats_de(vendedor)

    def _nombre_campo_extra_token(self):
        return 'Operator.Token'
