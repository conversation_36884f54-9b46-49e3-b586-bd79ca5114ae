# coding=utf-8
from io import BytesIO

from django.core import files
from django.core.validators import URLValidator
from django.db import models
from django.utils import timezone

from occ.models.querysets import LlamadaRealizadaDeAnuraQuerySet


class IntentoDeLlamado(models.Model):
    _vendedor = models.ForeignKey('vendedores.Vendedor', related_name='intentos_de_llamado')
    _prospecto = models.ForeignKey('prospectos.Prospecto', related_name='intentos_de_llamado')
    _telefono = models.CharField(max_length=64)
    _fecha = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Intento de llamado"
        verbose_name_plural = "Intentos de llamado"

    def fecha(self):
        return self._fecha

    def prospecto(self):
        return self._prospecto

    def vendedor(self):
        return self._vendedor

    def telefono(self):
        return self._telefono

    @classmethod
    def nuevo(cls, vendedor, prospecto, telefono):
        nuevo_intento_de_llamado = cls(_vendedor=vendedor, _prospecto=prospecto, _telefono=telefono)
        nuevo_intento_de_llamado.full_clean()
        nuevo_intento_de_llamado.save()
        return nuevo_intento_de_llamado


class LlamadaRealizadaDeAnura(models.Model):
    _intento_de_llamado = models.OneToOneField(IntentoDeLlamado, related_name="llamada_realizada")
    _fecha_comienzo = models.DateTimeField(db_index=True)
    _id_externo = models.CharField(max_length=255)
    _duracion = models.PositiveIntegerField(default=0)
    _audio_url = models.TextField(validators=[URLValidator()], max_length=2000)
    _audio_file = models.FileField(upload_to="audio-llamadas/", blank=True, null=True)

    objects = LlamadaRealizadaDeAnuraQuerySet.as_manager()

    class Meta:
        verbose_name = "Llamada realizada de Anura"
        verbose_name_plural = "Llamadas realizadas de Anura"

    @classmethod
    def nueva(cls, intento_de_llamado, fecha_comienzo, id_externo, duracion, audio_url):
        llamada = cls.objects.create(_intento_de_llamado=intento_de_llamado, _fecha_comienzo=fecha_comienzo,
                                     _id_externo=id_externo, _duracion=duracion, _audio_url=audio_url)
        return llamada

    def intento_de_llamado(self):
        return self._intento_de_llamado

    def fecha_comienzo(self):
        return self._fecha_comienzo

    def id_externo(self):
        return self._id_externo

    def duracion(self):
        return self._duracion

    def audio_url(self):
        return self._audio_url

    def audio_file(self):
        return self._audio_file

    def vendedor(self):
        return self._intento_de_llamado.vendedor()

    def prospecto(self):
        return self._intento_de_llamado.prospecto()

    def set_audio_url(self, audio_url):
        self._audio_url = audio_url

    def set_audio_file(self, audio_file):
        audio_file_name = self._nombre_de_archivo_de_audio()
        self._audio_file.save(audio_file_name, audio_file)

    def tiene_audio(self):
        return bool(self._audio_file)

    def set_audio_file_from(self, a_string):
        archivo = BytesIO()
        archivo.write(a_string)
        archivo_de_audio = files.File(archivo)
        self.set_audio_file(archivo_de_audio)

    def _nombre_de_archivo_de_audio(self):
        audio_file_name = "llamada-anura-id-%s.mp3" % self.id
        return audio_file_name

    def __str__(self):
        texto = self.fecha_comienzo().strftime("realizada el %d/%m/%Y a las %H:%M")
        detalle = "%s (%s)" % (texto, timezone.timedelta(seconds=self.duracion()))
        return detalle


