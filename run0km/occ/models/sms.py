# -*- coding: utf-8 -*-

from django.conf import settings
from django.db import models
from django.utils import timezone

from conversaciones.models import Conversacion
from core.support import make_aware_when_is_naive
from log_de_errores.models import LogDeError
from occ.managers import RespuestaDeMensajeManager, EnvioDeMensajeManager, LogDeErrorActualizacionDeEstadoSMSManager
from occ.querysets.sms import CampaniaDeComunicacionQuerySet
from occ.models.origen_de_mensajes import OrigenDesconocido


class Medio(models.Model):
    nombre = models.CharField(max_length=255)

    class Meta:
        verbose_name = 'medio'
        verbose_name_plural = 'medios'

    def __str__(self):
        return self.nombre


class CampaniaDeComunicacion(models.Model):
    nombre = models.CharField(max_length=255)
    medio = models.ForeignKey('Medio')
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='campanias_de_comunicacion')
    mensaje = models.Char<PERSON><PERSON>(max_length=145)  # 145 characters long in order not to exceed sms limit
    prospectos = models.ManyToManyField('prospectos.Prospecto', related_name='campanias_de_comunicaciones')
    fecha_de_creacion = models.DateTimeField(auto_now_add=True)
    envio_mensajes_correcto = models.BooleanField(default=False)
    envio_mensajes_correcto_info = models.TextField(default='', blank=True, verbose_name='Resumen de envío')

    objects = CampaniaDeComunicacionQuerySet.as_manager()

    @classmethod
    def nueva_campania_con(cls, medio, vendedor, mensaje, prospectos, nombre=None):
        nombre = nombre or (str(vendedor.id) + "_" + str(timezone.now()))
        nueva_campania = cls(nombre=nombre, medio=medio, vendedor=vendedor, mensaje=mensaje)
        nueva_campania.full_clean()
        nueva_campania.save()

        for prospecto in prospectos:
            nueva_campania.prospectos.add(prospecto)

        return nueva_campania

    def resultado_de_envio(self, resultado, vendedor):
        if resultado.fue_exitoso():
            vendedor.credito.restar_credito(self.envios.count())
        self.envio_mensajes_correcto = resultado.fue_exitoso()
        self.envio_mensajes_correcto_info = resultado.detalle()
        self.full_clean()
        self.save()

    class Meta:
        verbose_name = 'campaña de comunicación'
        verbose_name_plural = 'campañas de comunicación'

    def __str__(self):
        return self.nombre

    def cantidad_de_envios(self):
        return self.envios.count()

    def mensajes_enviados(self):
        return EnvioDeMensaje.objects.filter(envio_de_campania__in=self.envios.all()).all()


class EnvioDeCampaniaDeSMS(models.Model):
    campania = models.ForeignKey('CampaniaDeComunicacion', related_name='envios', verbose_name='Campaña')
    envio = models.OneToOneField('EnvioDeMensaje', related_name='envio_de_campania')

    @classmethod
    def nuevo(cls, campania, prospecto, id_mensaje, telefono, mensaje):
        envio = EnvioDeMensaje.nuevo(prospecto, id_mensaje, telefono, mensaje)
        envio_campania = cls(campania=campania, envio=envio)
        envio_campania.full_clean()
        envio_campania.save()
        return envio_campania

    def realizado(self, fecha_envio):
        self.envio.realizado(fecha_envio)

    def fallido(self):
        self.envio.fallido()

    def texto(self):
        return self.envio.texto()

    def fue_exitoso(self):
        return self.envio.fue_exitoso()

    @property
    def telefono(self):
        return self.envio.telefono

    @property
    def estado(self):
        return self.envio.estado

    @property
    def id_mensaje(self):
        return self.envio.id_mensaje

    @property
    def fecha(self):
        return self.envio.fecha

    @property
    def respuestas(self):
        return self.envio.respuestas

    @property
    def prospecto(self):
        return self.envio.prospecto

    def asociar_respuesta_con(self, texto, fecha):
        return self.envio.asociar_respuesta_con(texto, fecha)


class EnvioDeMensaje(models.Model):
    """Modela el envio de un mensaje de SMS asociado a un prospecto.
    Como tal, tiene informacion del estado de envio del mensaje.

    Notas:
    Otro objeto es encargado de relizar el envio real (ver DeliverySMS).
    Un proceso Celery actualiza de forma periodica el estado de envio de estos objetos.
    """
    EN_PROCESO = 'P'
    REALIZADO = 'R'
    FALLIDO = 'F'

    TIPO_DE_CHAT = Conversacion.TIPO_SMS

    _ESTADOS_DE_PROSPECTO = (
        (EN_PROCESO, 'En proceso'),
        (REALIZADO, 'Realizado'),
        (FALLIDO, 'Envio Fallido'),
    )

    prospecto = models.ForeignKey('prospectos.Prospecto', related_name='mensajes_de_campanias')
    id_mensaje = models.CharField(max_length=255)
    mensaje = models.CharField(max_length=255, blank=True, default='')
    telefono = models.CharField(max_length=64, blank=True, default='')
    fecha = models.DateTimeField(blank=True, null=True)
    fecha_creacion = models.DateTimeField(auto_now_add=True)
    estado = models.CharField(max_length=1, choices=_ESTADOS_DE_PROSPECTO, default=EN_PROCESO)

    _origen = models.ForeignKey('occ.OrigenDeMensaje', related_name='_mensajes_sms')

    objects = EnvioDeMensajeManager()

    class Meta:
        verbose_name = 'Envio de Mensaje'
        verbose_name_plural = 'Envio de Mensajes'

    def __str__(self):
        return self.telefono

    def origen(self):
        return self._origen

    def cambiar_origen(self, origen):
        self._origen = origen
        self.full_clean()
        self.save()

    def obtener_fecha(self):
        return self.fecha or self.fecha_creacion

    def obtener_estado(self):
        return self.estado

    def texto(self):
        return self.mensaje

    # Mensaje de conveniencia
    def obtener_texto(self):
        return self.texto()

    def realizado(self, fecha_envio=None):
        fecha_envio = fecha_envio or timezone.now()
        self.fecha = make_aware_when_is_naive(fecha_envio)
        self._set_estado(self.REALIZADO)
        Conversacion.nuevo_mensaje(self)

    def fallido(self):
        self._set_estado(self.FALLIDO)

    def _set_estado(self, estado):
        self.estado = estado
        self.full_clean()
        self.save()

    def fue_exitoso(self):
        return self.estado == self.REALIZADO

    def fue_fallido(self):
        return self.estado == self.FALLIDO

    def nombre_de_estado(self):
        return dict(self._ESTADOS_DE_PROSPECTO)[self.estado]

    def asociar_respuesta_con(self, texto, fecha):
        respuesta = RespuestaDeMensaje.nuevo(envio=self, fecha=fecha, mensaje=texto)
        return respuesta

    @classmethod
    def nuevo(cls, prospecto, id_mensaje, telefono, mensaje):
        envio = cls(prospecto=prospecto, id_mensaje=id_mensaje, telefono=telefono,
                    mensaje=mensaje, _origen=OrigenDesconocido.nuevo())
        envio.full_clean()
        envio.save()
        Conversacion.nuevo_mensaje(envio)
        return envio

    def proveniente_de_cliente(self):
        return False

    def nombre_de_emisor(self):
        if self.prospecto.vendedor:
            return self.prospecto.vendedor.full_name()
        elif self.prospecto.responsable:
            return self.prospecto.responsable.full_name()
        else:
            return ''

    def fue_leido(self):
        return True

    def tipo_de_chat(self):
        return self.TIPO_DE_CHAT

    # TODO: arreglar bien
    @property
    def respuesta(self):
        if self.respuestas:
            return self.respuestas.first()
        else:
            return None

    def id_de_prospecto(self):
        return self.prospecto_id


class RespuestaDeMensaje(models.Model):
    """Respuesta de mensaje SMS. Modelo la respuesta a otro mensaje SMS (self.envio).

    Nota:
    Esta forma de modelado esta viciada por el servicio con el cual se interactúa.
    El que sea una respuesta no deberia estar en este objeto, sino en un objeto de contexto (Chat, Conversacion, etc)
    """
    REALIZADO = 'R'

    envio = models.ForeignKey('occ.EnvioDeMensaje', related_name='respuestas')
    fecha = models.DateTimeField()
    mensaje = models.CharField(max_length=255)
    leida = models.BooleanField(default=False)

    objects = RespuestaDeMensajeManager()

    @classmethod
    def nuevo(cls, envio, fecha, mensaje):
        fecha = fecha or timezone.now()
        respuesta = cls(envio=envio, fecha=fecha, mensaje=mensaje[:255])
        respuesta.full_clean()
        respuesta.save()
        Conversacion.nuevo_mensaje(respuesta)
        return respuesta

    # Metodo de conveniencia
    def origen(self):
        return OrigenDesconocido()

    def obtener_estado(self):
        return self.REALIZADO

    def texto(self):
        return self.mensaje

    # Mensaje de conveniencia
    def obtener_texto(self):
        return self.texto()

    def proveniente_de_cliente(self):
        return True

    def nombre_de_emisor(self):
        return self.prospecto.nombre

    def obtener_fecha(self):
        return self.fecha

    @property
    def prospecto(self):
        return self.envio.prospecto

    def id_de_prospecto(self):
        return self.envio.prospecto_id

    def tipo_de_chat(self):
        return EnvioDeMensaje.TIPO_DE_CHAT

    def fue_leido(self):
        return self.leida


class LogDeErrorDeSMS(models.Model):
    # refactorizar con el log de errores?
    fecha = models.DateTimeField(auto_now_add=True)
    tipo = models.TextField(default='')
    descripcion = models.TextField(max_length=1024, blank=True)
    request = models.TextField(blank=True, null=True)
    response = models.TextField(blank=True, null=True)

    campania = models.ForeignKey('CampaniaDeComunicacion')

    class Meta:
        verbose_name = 'Log de error de SMS'
        verbose_name_plural = 'Logs de errores de SMS'

    def __str__(self):
        return "%s - %s" % (self.campania.nombre, self.tipo)

    @classmethod
    def nuevo(cls, campania, tipo, descripcion, request='', response=''):
        error_de_sms = cls(campania=campania, tipo=tipo, descripcion=descripcion, request=request, response=response)
        error_de_sms.full_clean()
        error_de_sms.save()
        return error_de_sms

    @classmethod
    def nuevo_desde_api_error(cls, campania, sms_api_error):
        return cls.nuevo(campania, sms_api_error.error_type(), sms_api_error.descripcion(),
                         sms_api_error.str_request, sms_api_error.str_response)


class CreditoDeSMS(models.Model):
    vendedor = models.OneToOneField('vendedores.Vendedor', null=True, related_name='credito')
    credito_base = models.PositiveIntegerField(default=0)
    credito_extra = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = 'Listado de credito'
        verbose_name_plural = 'Listado de creditos'

    def __str__(self):
        return self.vendedor.user.__str__()

    @classmethod
    def nuevo(cls, vendedor):
        credito_de_sms = cls(vendedor=vendedor)
        credito_de_sms.full_clean()
        credito_de_sms.save()
        return credito_de_sms

    def cantidad(self):
        return self.credito_base + self.credito_extra

    def restar_credito(self, credito_usado):
        if self.credito_base > credito_usado:
            self.credito_base -= credito_usado
        else:
            self.credito_extra -= (credito_usado - self.credito_base)
            self.credito_base = 0

        self.full_clean()
        self.save()

    def asignar_credito_inicial(self):
        self.credito_base = settings.CREDITO_INICIAL_PARA_CAMPANIA
        self.full_clean()
        self.save()

    def tiene_credito_para_enviar(self, cantidad_mensajes):
        return self.cantidad() >= cantidad_mensajes


class LogDeErrorActualizacionDeEstadoSMS(LogDeError):
    objects = LogDeErrorActualizacionDeEstadoSMSManager()

    class Meta:
        proxy = True
        verbose_name = 'Error al actualizar los envios de SMS'
        verbose_name_plural = 'Errores al actualizar de los envios de SMS'

    @classmethod
    def guardar_notificacion(cls, tipo, descripcion, response):
        log = cls.nuevo(tipo, descripcion, '', '', response)
        return log

    @classmethod
    def codigo_de_log(cls):
        return cls.CODIGO_LOG_DE_ERROR_ACTUALIZACION_DE_ESTADO_SMS

