# coding=utf-8
from django.core.exceptions import ValidationError
from django.db import models

from conversaciones.medios import MedioDeConversacion, MedioEAviso
from occ.models.occ_conversaciones import Conversation, PublicationMessage, PublicationReply, UserContactData
from occ.models.querysets import DatosDeContactoDelInteresadoQuerySet
from occ.querysets.eavisos import PreguntaDePublicacionEAvisosQueryset, ConversacionDeEAvisosQueryset, \
    RespuestaAMensajeDePublicacionEAvisosQueryset


class DatosDeContactoDelInteresado(UserContactData):

    _whatsapp = models.CharField(max_length=64, blank=True, null=True, default='')

    objects = DatosDeContactoDelInteresadoQuerySet.as_manager()

    def __str__(self):
        return self.name()

    def whatsapp(self):
        return self._whatsapp

    def telefono(self):
        return self._phone

    def email(self):
        return self._email

    def nombre(self):
        return self._name

    class Meta:
        abstract = False


class PublicacionEAvisos(models.Model):
    """
        Esto modela una publicacion o avisos de Propuestas en los diferentes canales: MercadoLibre, OLX, etc.
    """
    MERCADO_LIBRE = '0'
    OLX = '1'
    MOTOAUTOS = '2'

    _NOMBRE_DE_LUGAR = {MERCADO_LIBRE: 'Mercado Libre', OLX: 'OLX', MOTOAUTOS: 'MOTOAUTOS.com'}

    _OPCIONES_LUGAR = ((MERCADO_LIBRE, _NOMBRE_DE_LUGAR[MERCADO_LIBRE]), (OLX, _NOMBRE_DE_LUGAR[OLX]))

    _id_lugar_de_publicacion = models.CharField(max_length=300)  # El id de la public. dentro del lugar de publicación.
    _lugar = models.CharField(max_length=1, choices=_OPCIONES_LUGAR, default=MERCADO_LIBRE, db_index=True)
    _url = models.URLField(max_length=256, blank=True, null=True)

    # TODO: a definir por lo que le entendi a Karel una publicacion esta asociada a un plan y ese caso tenemos que
    # _plan = models.ForeignKey(to='propuestas.Plan', related_name='_mensajes_de_eavisos')

    @classmethod
    def para(cls, lugar, id_lugar_de_publicacion):
        return cls.objects.get(_lugar=lugar, _id_lugar_de_publicacion=id_lugar_de_publicacion)

    @classmethod
    def nueva(cls, id_lugar_de_publicacion, lugar, url):
        publicacion = cls.objects.create(_id_lugar_de_publicacion=id_lugar_de_publicacion, _lugar=lugar, _url=url)
        return publicacion

    def url(self):
        return self._url

    def lugar(self):
        return self._lugar

    def id_lugar_de_publicacion(self):
        return self._id_lugar_de_publicacion

    def nombre_de_lugar_de_publicacion(self):
        return self._NOMBRE_DE_LUGAR.get(self.lugar())

    def conversaciones(self):
        return self._conversaciones.all()

    def agregar_conversacion_con(self, id_de_mensaje_externo, contenido, id_lugar_de_publicacion,
                                 cantidad_de_respuestas_permitidas, vendedor):
        conversacion = ConversacionDeEAvisos.nueva_con(
            self, id_de_mensaje_externo=id_de_mensaje_externo,
            contenido=contenido,
            id_lugar_de_publicacion=id_lugar_de_publicacion,
            cantidad_de_respuestas_permitidas=cantidad_de_respuestas_permitidas,
            vendedor=vendedor)
        return conversacion


class PreguntaInvalidaDePublicacionEAvisos(PublicationMessage):
    """

    Una Pregunta es inválida cuando, por ejemplo, no tiene datos de la Propuesta asociada
    o el Usuario asociado (del que hizo la Publicacion).

    """
    _lugar = models.CharField(max_length=40, blank=True, null=True)
    _id_externo = models.PositiveIntegerField()
    _id_lugar_de_publicacion = models.CharField(max_length=300, blank=True,
                                                null=True)  # El id del mensaje en el lugar de publicación.
    _link_de_la_publicacion = models.URLField(max_length=400, blank=True, null=True)
    _id_de_la_publicacion_en_lugar_de_publicacion = models.CharField(max_length=300, blank=True, null=True)

    @classmethod
    def nueva(cls, contenido, lugar, id_externo, id_lugar_de_publicacion, link_de_la_publicacion,
              id_de_la_publicacion_en_lugar_de_publicacion):
        pregunta = cls.objects.create(
            _content=contenido, _lugar=lugar, _id_externo=id_externo,
            _id_lugar_de_publicacion=id_lugar_de_publicacion,
            _link_de_la_publicacion=link_de_la_publicacion,
            _id_de_la_publicacion_en_lugar_de_publicacion=id_de_la_publicacion_en_lugar_de_publicacion)
        return pregunta

    def lugar(self):
        return self._lugar

    def id_externo(self):
        return self._id_externo

    def id_lugar_de_publicacion(self):
        return self._id_lugar_de_publicacion

    def id_de_la_publicacion_en_lugar_de_publicacion(self):
        return self._id_de_la_publicacion_en_lugar_de_publicacion

    def link_de_la_publicacion(self):
        return self._link_de_la_publicacion

    class Meta:
        verbose_name_plural = "Preguntas Inválidas de E-Avisos"


class PreguntaDePublicacionEAvisos(PublicationMessage):
    """
        Esto modela mensajes publicados en LugaresDePublicacion (MercadoLibre, OLX..).
        Las publicaciones en los lugares son de alguna Propuesta en particular, por lo cual también se agrega el id.
    """

    _id_externo = models.PositiveIntegerField(unique=True)
    _id_lugar_de_publicacion = models.CharField(max_length=300)  # El id del mensaje en el lugar de publicación.
    _conversacion = models.ForeignKey('ConversacionDeEAvisos', related_name='_preguntas')
    _fue_leida = models.BooleanField(default=False)
    _cantidad_de_respuestas_permitidas = models.PositiveSmallIntegerField()

    objects = PreguntaDePublicacionEAvisosQueryset.as_manager()

    @classmethod
    def nuevo(cls, contenido, id_externo, id_lugar_de_publicacion, conversacion, cantidad_de_respuestas_permitidas):
        mensaje = cls.objects.create(_id_externo=id_externo, _content=contenido, _conversacion=conversacion,
                                     _id_lugar_de_publicacion=id_lugar_de_publicacion,
                                     _cantidad_de_respuestas_permitidas=cantidad_de_respuestas_permitidas)
        return mensaje

    def tipo_de_chat(self):
        """Devuelvo el tipo de medio por el cual fui enviado"""
        return MedioEAviso.tipo()

    def fue_exitoso(self):
        """Como ya lo recibi de la API, la recepcion de este mensaje es siempre exitosa"""
        return True

    def id_externo(self):
        return self._id_externo

    def id_lugar_de_publicacion(self):
        return self._id_lugar_de_publicacion

    def cantidad_de_respuestas_permitidas(self):
        return self._cantidad_de_respuestas_permitidas

    def cantidad_de_respuestas(self):
        return self.respuestas().count()

    def cantidad_de_respuestas_disponibles(self):
        return self.cantidad_de_respuestas_permitidas() - self.cantidad_de_respuestas()

    def se_puede_responder(self):
        return self.cantidad_de_respuestas_disponibles() > 0

    def conversacion(self):
        return self._conversacion

    def contenido(self):
        return self._content

    def respuestas(self):
        return self._respuestas

    def esta_leida(self):
        return self._fue_leida

    def marcar_como_leida(self):
        self._fue_leida = True
        self.full_clean()
        self.save()

    def agregar_respuesta(self, contenido):
        respuesta = RespuestaAMensajeDePublicacionEAvisos.nueva(contenido=contenido, mensaje=self)
        return respuesta

    # --- metodos para compatibilidad con mensajes de otros medios ---

    def obtener_fecha(self):
        return self.created_at()

    def origen(self):
        from occ.models import OrigenDesconocido
        return OrigenDesconocido.nuevo()

    def obtener_texto(self):
        return self.contenido()

    def proveniente_de_cliente(self):
        return False


class RespuestaAMensajeDePublicacionEAvisos(PublicationReply):
    EN_PROCESO = 'P'
    REALIZADO = 'R'
    FALLIDO = 'F'

    _ESTADOS = (
        (EN_PROCESO, 'En proceso'),
        (REALIZADO, 'Realizado'),
        (FALLIDO, 'Envio Fallido'),
    )

    _mensaje_original = models.ForeignKey('PreguntaDePublicacionEAvisos', related_name='_respuestas')

    _id_externo = models.PositiveIntegerField(blank=True, null=True)
    _estado_del_envio = models.CharField(max_length=1, choices=_ESTADOS, default=EN_PROCESO)

    objects = RespuestaAMensajeDePublicacionEAvisosQueryset.as_manager()

    @classmethod
    def nueva(cls, contenido, mensaje):
        respuesta = cls(_content=contenido, _mensaje_original=mensaje)
        respuesta.full_clean()
        respuesta.save()
        return respuesta

    def clean(self):
        super(RespuestaAMensajeDePublicacionEAvisos, self).clean()
        if not self.mensaje_original().se_puede_responder():
            raise ValidationError(self.mensaje_de_error_pregunta_no_acepta_mas_respuestas())

    @classmethod
    def mensaje_de_error_pregunta_no_acepta_mas_respuestas(cls):
        return "La pregunta no acepta más respuestas"

    def tipo_de_chat(self):
        """Devuelvo el tipo de medio por el cual fui enviado"""
        return MedioEAviso.tipo()

    def contenido(self):
        return self._content

    def mensaje_original(self):
        return self._mensaje_original

    def conversacion(self):
        return self.mensaje_original().conversacion()

    def esta_en_proceso(self):
        return self._estado_del_envio == self.EN_PROCESO

    def fue_exitoso(self):
        return self._estado_del_envio == self.REALIZADO

    def fue_fallido(self):
        return self._estado_del_envio == self.FALLIDO

    def fallido(self):
        self._set_estado(self.FALLIDO)

    def realizado_con(self, id_externo):
        self._id_externo = id_externo
        self._set_estado(self.REALIZADO)

    def _set_estado(self, estado):
        self._estado_del_envio = estado
        self.full_clean()
        self.save()

    # --- metodos para compatibilidad con mensajes de otros medios ---

    def obtener_fecha(self):
        return self.created_at()

    def origen(self):
        from occ.models import OrigenDesconocido
        return OrigenDesconocido.nuevo()

    def obtener_texto(self):
        return self.contenido()

    def proveniente_de_cliente(self):
        return True


class ConversacionDeEAvisos(Conversation):
    """
        Esto modela la conversación entre un vendedor y un usuario sobre una publicación en algún sitio (OLX p ej.)
    """
    TIPO = 'EAVISO'

    _fecha_de_inicio = models.DateTimeField(auto_now_add=True)
    _publicacion = models.ForeignKey('PublicacionEAvisos', related_name='_conversaciones')
    _fue_leida = models.BooleanField(default=False)
    _datos_de_contacto_del_interesado = models.ForeignKey('DatosDeContactoDelInteresado', null=True, blank=True)
    _vendedor = models.ForeignKey('vendedores.Vendedor', related_name='conversaciones_eavisos')

    objects = ConversacionDeEAvisosQueryset.as_manager()

    @classmethod
    def nueva_con(cls, publicacion, id_de_mensaje_externo, contenido, id_lugar_de_publicacion,
                  cantidad_de_respuestas_permitidas, vendedor):
        conversacion = cls.objects.create(_publicacion=publicacion, _vendedor=vendedor)
        conversacion.agregar_mensaje_con(id_de_mensaje_externo=id_de_mensaje_externo, contenido=contenido,
                                         id_lugar_de_publicacion=id_lugar_de_publicacion,
                                         cantidad_de_respuestas_permitidas=cantidad_de_respuestas_permitidas)
        return conversacion

    def agregar_datos_de_contacto_del_interesado(self, telefono, whatsapp, nombre, email):
        datos_de_contacto_del_interesado = DatosDeContactoDelInteresado.objects.create(
            _phone=telefono, _whatsapp=whatsapp, _name=nombre, _email=email)
        self._datos_de_contacto_del_interesado = datos_de_contacto_del_interesado
        self.save()
        return self

    def marcar_como_leida(self):
        # TODO: refactor. Asumiendo medio como `MedioEAviso` siempre y por lo tanto paso una conversacion
        self.medio().marcar_como_leidos_mensajes_de(self)
        self._fue_leida = True
        self.full_clean()
        self.save()

    def esta_leida(self):
        return self._fue_leida

    def mensajes(self, cantidad=None):
        medio = self.medio()
        mensajes = medio.mensajes_para(self, cantidad)
        return mensajes

    def id_mensaje_a_responder(self):
        """
        Devuelve el id del mensaje al cual responder en caso de publicar una respuesta
        Contexto: este mensaje se usa cuando a nivel uso el usuario responde "a la conversacion"
        pero internamente se maneja la semantica de responder "a un mensaje específico"
        Nota: Si en un momento a nivel interfaz cambiamos el modelo de "responder al final del chat"
        a un modelo de "responder a un mensaje en particular" entonces este método se puede hacer obsoleto
        """
        preguntas = self.preguntas().ordenar_por_fecha()
        pregunta_mas_reciente = preguntas.first()
        if pregunta_mas_reciente is None:
            return None
        else:
            return pregunta_mas_reciente.id

    def mensaje_a_responder(self):
        """
        Devuelve el mensaje al cual responder en caso de publicar una respuesta
        Contexto: este mensaje se usa cuando a nivel uso el usuario responde "a la conversacion"
        pero internamente se maneja la semantica de responder "a un mensaje específico"
        Nota: Si en un momento a nivel interfaz cambiamos el modelo de "responder al final del chat"
        a un modelo de "responder a un mensaje en particular" entonces este método se puede hacer obsoleto
        """
        id_mensaje_a_responder = self.id_mensaje_a_responder()
        if id_mensaje_a_responder is not None:
            mensaje_a_responder = self.preguntas().get(id=id_mensaje_a_responder)
            return mensaje_a_responder

    def preguntas(self):
        return self._preguntas

    def medio(self):
        medio = MedioDeConversacion.nuevo_para(self.tipo())
        return medio

    def nombre_de_medio(self):
        return self.medio().nombre_para(self)

    def publicacion(self):
        return self._publicacion

    def vendedor(self):
        return self._vendedor

    def user(self):
        return self.vendedor().usuario()

    def datos_de_contacto_del_interesado(self):
        return self._datos_de_contacto_del_interesado

    def agregar_mensaje_con(self, id_de_mensaje_externo, contenido, id_lugar_de_publicacion,
                            cantidad_de_respuestas_permitidas):
        mensaje = PreguntaDePublicacionEAvisos.nuevo(
            contenido=contenido, id_externo=id_de_mensaje_externo, conversacion=self,
            id_lugar_de_publicacion=id_lugar_de_publicacion,
            cantidad_de_respuestas_permitidas=cantidad_de_respuestas_permitidas)
        return mensaje

    def tipo(self):
        return self.TIPO

    def es_de_tipo(self, tipo):
        return self.tipo() == tipo

    @classmethod
    def tipos(cls):
        return [cls.TIPO]
