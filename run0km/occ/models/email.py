
from django.db import models

from conversaciones.models import Conversacion
from occ.querysets.email import EnvioDeEmailQuerySet
from occ.models.origen_de_mensajes import OrigenDesconocido


class EnvioDeEmail(models.Model):
    VENDEDOR = 'V'
    CLIENTE = 'C'

    _ESTADO_COMPLETO = 'C'

    OPCIONES_EMISOR = ((VENDEDOR, 'Vendedor'), (CLIENTE, 'Cliente'))

    _contenido = models.TextField(null=True, blank=True)
    _prospecto = models.ForeignKey('prospectos.Prospecto', related_name='mensajes_de_email')
    _emisor = models.CharField(max_length=1, choices=OPCIONES_EMISOR, default=VENDEDOR)
    _identificador_externo = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    _identificador_cadena = models.CharField(max_length=255, blank=True, null=True)
    _fecha = models.DateTimeField(auto_now_add=True)
    _leido = models.BooleanField(default=False)
    _origen = models.ForeignKey('occ.OrigenDeMensaje', related_name='_mensajes_email')

    objects = EnvioDeEmailQuerySet.as_manager()

    def origen(self):
        return self._origen

    def cambiar_origen(self, origen):
        self._origen = origen
        self.full_clean()
        self.save()

    @property
    def fecha(self):
        return self._fecha

    def emisor(self):
        return self._emisor

    def prospecto(self):
        return self._prospecto

    def id_de_prospecto(self):
        return self._prospecto.pk

    def tipo_de_chat(self):
        return Conversacion.TIPO_EMAIL

    def obtener_texto(self):
        #Convenience method
        return self.texto()

    def obtener_fecha(self):
        #Convenience method
        return self.fecha

    def obtener_estado(self):
        return self._ESTADO_COMPLETO

    def fue_leido(self):
        return self._leido

    def texto(self):
        return self._contenido

    def mensaje(self):
        return self._contenido

    def proveniente_de_cliente(self):
        return self._emisor == self.CLIENTE

    def nombre_de_emisor(self):
        if self.proveniente_de_cliente():
            return self._prospecto.nombre
        else:
            return self._prospecto.vendedor.full_name()

    @classmethod
    def nuevo(cls, contenido, prospecto, emisor, identificador_externo=None, identificador_cadena=None):
        mensaje = cls(_contenido=contenido, _identificador_externo=identificador_externo,
                      _identificador_cadena=identificador_cadena, _prospecto=prospecto,
                      _emisor=emisor, _origen=OrigenDesconocido.nuevo())
        mensaje.full_clean()
        mensaje.save()

        #TODO Deuda Tecnica: quitar esta relacion, modelar correctamente la creacion de conversaciones
        Conversacion.nuevo_mensaje(mensaje)
        return mensaje

    @classmethod
    def nuevo_envio(cls, contenido, prospecto, identificador_externo, identificador_cadena):
        return cls.nuevo(contenido, prospecto, cls.VENDEDOR, identificador_externo, identificador_cadena)
