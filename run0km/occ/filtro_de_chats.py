# La idea es que el Filtrador recorre la lista de criterios y va creando un filtro que tiene filtros adentro.
# A la hora de filtrar, filtra la capa exterior, y forwardea lo filtrado a la capa siguiente, hasta que termina.
# ----------------------------------------------------------------------------------------------------------------------
#                                           DECORATORS
# ----------------------------------------------------------------------------------------------------------------------
from occ.errores import CriterioFiltradorDeChatsInexistenteException
from occ.models import ChatDeVentas


class FiltradorDeChatsDecorator(object):
    @classmethod
    def nuevo(cls, filtro):
        filtrador = cls(filtro=filtro)
        return filtrador

    def __init__(self, filtro):
        self.filtro = filtro

    def filtrar(self, chats):
        filtrados = self._aplicar_filtro_a(chats)
        return filtrados | self.filtro.filtrar(chats)

    def _aplicar_filtro_a(self, chats):
        raise NotImplementedError("Subclass Responsibility")


class FiltradorDeChatsConvertidos(FiltradorDeChatsDecorator):
    def _aplicar_filtro_a(self, chats):
        return chats.filter(chat_de_ventas_convertido__isnull=False)


class FiltradorDeChatsPerdidos(FiltradorDeChatsDecorator):
    def _aplicar_filtro_a(self, chats):
        return chats.filter(activo=False, chat_de_ventas_convertido__isnull=True)


class FiltradorDeChatsVigentes(FiltradorDeChatsDecorator):
    def _aplicar_filtro_a(self, chats):
        return chats.filter(activo=True)


# ----------------------------------------------------------------------------------------------------------------------
#                                           MANAGER Y COMPONENTES
# ----------------------------------------------------------------------------------------------------------------------
class FiltradorDeChats(object):
    PERDIDOS = 'filtrar_perdidos'
    CONVERTIDOS = 'filtrar_convertidos'
    VIGENTES = 'filtrar_vigentes'
    _CRITERIOS = {CONVERTIDOS: FiltradorDeChatsConvertidos, PERDIDOS: FiltradorDeChatsPerdidos,
                  VIGENTES: FiltradorDeChatsVigentes}

    def __init__(self, lista_de_criterios):
        self.filtro = FiltradorDeChatsVacio.nuevo()
        for criterio in lista_de_criterios:
            if criterio not in FiltradorDeChats._CRITERIOS:
                raise CriterioFiltradorDeChatsInexistenteException
            self.filtro = FiltradorDeChats._CRITERIOS[criterio].nuevo(self.filtro)

    def filtrar(self, chats):
        chats = self.filtro.filtrar(chats)
        return chats


# Representa Concrete Component del Decorator.
class FiltradorDeChatsVacio(object):
    @classmethod
    def nuevo(cls):
        filtrador_concreto = cls()
        return filtrador_concreto

    def filtrar(self, chats):
        return ChatDeVentas.objects.none()
