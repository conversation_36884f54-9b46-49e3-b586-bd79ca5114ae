from django.conf import settings

from occ.servicio_de_chat_de_ventas import ServicioDeChatDeVentas
from occ.servicio_de_notificaciones import ServicioDeNotificaciones


def token_del_feed_del_usuario_registrado(request):
    user = request.user
    if user.is_authenticated() and user.is_vendedor() and (ServicioDeChatDeVentas.esta_habilitado(user.vendedor) or user.vendedor.puede_pedir_prospecto()):
        return {
            'PUSHER_APP_KEY': settings.PUSHER_KEY,
            'PUSHER_APP_CLUSTER': settings.PUSHER_CLUSTER,
            'user_pusher_channel_name': ServicioDeNotificaciones().nombre_de_canal_para(user.vendedor)
        }
    else:
        return {}
