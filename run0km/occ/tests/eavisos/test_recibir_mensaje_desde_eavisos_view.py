from django.utils import timezone
from rest_framework import status

from core.tests.clients.eavisos import ProveedorEAvisos
from occ.models.eavisos import ConversacionDeEAvisos, PublicacionEAvisos, PreguntaInvalidaDePublicacionEAvisos
from testing.validadores_de_contexto import ValidadorEAvisos
from propuestas.models import Pro<PERSON>esta
from testing.base import BaseFixturedTest


class AgregarMensajeDePublicacionEAvisosViewTest(BaseFixturedTest):

    def setUp(self):
        super(AgregarMensajeDePublicacionEAvisosViewTest, self).setUp()
        self._proveedor_eavisos = ProveedorEAvisos.nuevo_con_usuario_por_defecto(
            creador_de_contexto=self.creador_de_contexto, http_client=self.client)
        self.vendedor = self.fixture['vend_1']
        self._validador = ValidadorEAvisos.new_for(self)

    def test_request_exitoso_crea_conversacion_eavisos(self):
        # Dado
        user = self.vendedor.usuario()
        propuesta = self._crear_propuesta(user)
        texto = "Otra pregunta"
        id_de_mensaje_externo = 17
        lugar = 'mercadolibre'
        id_aviso = 'MLA757499971'
        id_pregunta = '5912144576'
        link = 'http://vehiculo.mercadolibre.com.ar/MLA-757499971-aaaaaaa345-_JM'
        fecha = timezone.now()

        # Cuando
        response = self._proveedor_eavisos.nuevo_mensaje(
            id_propuesta=propuesta.identificador_externo(), user_crm=str(user.pk), id_canal=lugar,
            id_tran=id_de_mensaje_externo, id_pregunta=id_pregunta, fecha=fecha,
            id_aviso=id_aviso, link=link, msg=texto)

        # Entonces
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        conversacion = self._assert_existe_conversacion_para_vendedor(self.vendedor)
        self._validador.assert_publicacion_con(
            conversacion.publicacion(), lugar=PublicacionEAvisos.MERCADO_LIBRE, id_externo=id_aviso)
        self._validador.assert_conversacion_con_mensaje(
            id_de_mensaje_externo=id_de_mensaje_externo, contenido=texto, conversacion=conversacion)

    def test_request_sin_parametros_debe_responder_error(self):
        mensaje = {}
        response = self._proveedor_eavisos.nuevo_mensaje_para_datos(datos=mensaje)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_request_con_mensaje_sin_datos_de_vendedor_crea_pregunta_por_separado(self):
        # Dado
        texto = "Otra pregunta"
        id_de_mensaje_externo = 17
        lugar = 'mercadolibre'
        id_aviso = 'MLA757499971'
        id_pregunta = '5912144576'
        link = 'http://vehiculo.mercadolibre.com.ar/MLA-757499971-aaaaaaa345-_JM'
        fecha = timezone.now()

        # Cuando
        response = self._proveedor_eavisos.nuevo_mensaje(
            id_propuesta=None, user_crm=None, id_canal=lugar,
            id_tran=id_de_mensaje_externo, id_pregunta=id_pregunta, fecha=fecha,
            id_aviso=id_aviso, link=link, msg=texto)

        # Entonces
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(PreguntaInvalidaDePublicacionEAvisos.objects.count(), 1)

    def _assert_existe_conversacion_para_vendedor(self, vendedor):
        conversaciones = ConversacionDeEAvisos.objects.para_vendedor(vendedor)
        self.assertEqual(conversaciones.count(), 1)
        conversacion = conversaciones.first()
        return conversacion

    def _crear_propuesta(self, user):
        propuesta = Propuesta.nueva(identificador=3, user=user, titulo="propuesta one")
        return propuesta
