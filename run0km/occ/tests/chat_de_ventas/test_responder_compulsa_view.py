import mock
from django.urls import reverse
from django.test import override_settings
from rest_framework import status

from occ.tests.chat_de_ventas.contexto import CreadorDeContextoParaChat
from occ.tests.chat_de_ventas.servicio_chat_ventas_mocks import ServicioDeChatDeVentasMock
from occ.tests.servicio_de_notificacion_mock import ServicioDeComunicacionSuccessMock
from testing.base import BaseLoggedTest
from testing.test_utils import reload_model
from vendedores.gestor import GestorDeVendedores


@override_settings(CHAT_SELECTOR_PARTICIPANTES_CLASS_NAME=
                   'occ.chat_seleccion_de_participantes.SelectorParticipantesCompulsa')
class OCCRespuestaACompulsaViewTest(BaseLoggedTest):
    def setUp(self):
        super(OCCRespuestaACompulsaViewTest, self).setUp()
        self.campania = self.fixture['camp_1']
        self.supervisor = self.fixture['sup_1']
        self.supervisor.vendedores.clear()
        self.creador_de_contexto = CreadorDeContextoParaChat(supervisor=self.supervisor,
                                                             fixture=self.fixture)
        self.vendedor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor)
        self.login_and_assert_correct(self.vendedor_uno.user)
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()

    def _post_responder(self, compulsa, view_name):
        url = reverse(view_name, kwargs={'compulsa_pk': compulsa.pk})
        response = self.client.post(url)
        return response

    def _post_aceptar(self, compulsa):
        return self._post_responder(compulsa=compulsa, view_name='aceptar_compulsa')

    def _post_rechazar(self, compulsa):
        return self._post_responder(compulsa=compulsa, view_name='rechazar_compulsa')

    def test_rechazar_una_compulsa_el_vendedor_debe_ser_removido_de_los_participantes(self):
        compulsa = self.creador_de_contexto.crear_compulsa_para_ford_con_todos_los_vendedores_de(self.supervisor)
        participantes = compulsa.proximos_vendedores()
        self.assertIn(self.vendedor_uno, participantes)
        response = self._post_rechazar(compulsa)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertNotIn(self.vendedor_uno, compulsa.participantes())

    def test_aceptar_compulsa_sin_servicio_habilitado_debe_respoder_permiso_denegado(self):
        self.gestor_de_vendedores.configurar_habilitacion_de_chat(vendedor=self.vendedor_uno, chat_habilitado=False)
        compulsa = self.creador_de_contexto.crear_compulsa_para_ford_con_todos_los_vendedores_de(self.supervisor)
        response = self._post_aceptar(compulsa)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        compulsa.refresh_from_db()
        self.assertTrue(compulsa.esta_vigente())

    def test_aceptar_compulsa_vendedor_no_participante_no_debe_generar_ganador(self):
        vendedor_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor)
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor('Ford', vendedor=vendedor_dos, campania=self.campania)
        pedido_ford = self.creador_de_contexto.crear_pedido_de_operador_para(marca='Ford', campania=self.campania)
        compulsa = self.creador_de_contexto.crear_compulsa(pedido_ford, tamanio_ventana_vendedores=5)
        # Generar participaciones
        compulsa.proximos_vendedores()

        # Login vendedor uno
        response = self._post_aceptar(compulsa)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        compulsa.refresh_from_db()
        self.assertTrue(compulsa.esta_vigente())

    @mock.patch("occ.servicio_de_chat_de_ventas.ServicioDeNotificaciones.enviar_inicio_de_chat",
                side_effect=ServicioDeComunicacionSuccessMock().enviar_inicio_de_chat)
    @mock.patch("occ.servicio_de_chat_de_ventas.ServicioDeChatDeVentas._url_para",
                side_effect=ServicioDeChatDeVentasMock()._url_para)
    def test_aceptar_compulsa_debe_registrarse_al_vendedor_como_ganador(self, mock_iniciar_chat, mock_url_para):
        compulsa = self.creador_de_contexto.crear_compulsa_para_ford_con_todos_los_vendedores_de(self.supervisor)
        # Generar participaciones
        compulsa.proximos_vendedores()

        response = self._post_aceptar(compulsa)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        compulsa = reload_model(compulsa)
        self.assertFalse(compulsa.esta_vigente())
        self.assertEqual(compulsa.obtener_finalizacion().ganador, self.vendedor_uno)
        mock_iniciar_chat.assert_called_once()
