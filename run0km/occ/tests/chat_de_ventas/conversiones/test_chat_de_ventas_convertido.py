from conversaciones.models import Conversacion
from occ.models import ChatDeVentas, ChatDeVentasConvertido
from testing.base import BaseFixturedTest


class ChatDeVentasConvertidoTest(BaseFixturedTest):
    def setUp(self):
        super(ChatDeVentasConvertidoTest, self).setUp()
        self.vendedor = self.fixture['vend_1']
        self.prospecto = self.fixture['p_1']

    def test_chat_sin_mensajes_debe_lanzar_excepcion(self):
        chat = ChatDeVentas.nuevo_vacio(vendedor=self.vendedor, token='1')
        self.assertRaises(ValueError, ChatDeVentasConvertido.nuevo, prospecto=self.prospecto, chat=chat)

    def test_chat_con_mensajes_debe_generar_la_conversion(self):
        chat = ChatDeVentas.nuevo_con_envio_con(vendedor=self.vendedor, token='1', texto='HelloAConvertir')
        conversion = ChatDeVentasConvertido.nuevo(prospecto=self.prospecto, chat=chat)
        conversacion = conversion.conversacion_de_chat
        self.assertEqual(conversacion.prospecto, self.prospecto)
        self.assertEqual(conversacion.tipo, Conversacion.TIPO_CHAT)
        self.assertEqual(conversacion.fecha_ultima_respuesta, None)
        self.assertEqual(conversacion.fecha_ultimo_mensaje, chat.mensajes.last().fecha)
        self.assertEqual(conversacion.fue_leida, True)
        self.assertEqual(conversacion.eliminada, False)