# coding=utf-8
from django.test import TestCase

from occ.errores_de_chat import ErrorDePedido
from occ.soporte_chat import PedidoRecibirMensaje, PedidoDeOperador


class PedidoBuilder(object):
    @classmethod
    def pedido_de_operador(cls, token, nombre_campania, nombre='', telefono='', celular='',
                           email='', marca='', modelo='', provincia='', location='', prefijo='', localidad='', transcript=None):
        transcript = transcript or PedidoBuilder._default_transcript()
        return {
            "Event": "SessionStart",
            "Token": token,
            "Campaign": nombre_campania,
            "Transcript": transcript,
            "Variables": cls.variables(nombre, telefono, celular, email, marca, modelo, provincia, location, prefijo,
                                       localidad)
        }

    @classmethod
    def _default_transcript(cls):
        return [
            "10:26:50",
            "B: (+5s) <PERSON><PERSON>, te puedo ayudar en algo?",
            "U: (+5s) ford 123456789",
            "B: (+10s) Mi nombre es Mariela, cómo es el tuyo?"
        ]

    @classmethod
    def recibir_mensaje(cls, texto='Buenos días.', token='1', nombre='', telefono='', celular='',
                        email='', marca='Ford', modelo='', provincia=''):
        return {
            "Body": texto,
            "SlotId": 40,
            "From": "<EMAIL>",
            "Event": "VisitorMessage",
            "Token": token,
            "Variables": cls.variables(nombre, telefono, celular, email, marca, modelo, provincia)
        }

    @classmethod
    def finalizar_chat(cls, texto='Buenos días.', token='1', nombre='', telefono='', celular='',
                        email='', marca='Ford', modelo='', provincia=''):
        return {
            "Body": texto,
            "SlotId": 40,
            "From": "<EMAIL>",
            "Event": "SessionEnd",
            "Token": token,
            "Variables": cls.variables(nombre, telefono, celular, email, marca, modelo, provincia)
        }

    @classmethod
    def variables(cls, nombre='', telefono='', celular='', email='', marca='', modelo='', provincia='', location='',
                  prefijo='', localidad=''):
        return {
            "Telefono": telefono,
            "Celular": celular,
            "Email": email,
            "Nombre": nombre,
            "Modelo": modelo,
            "Marca": marca,
            "Provincia": provincia,
            "Location": location,
            "Prefijo": prefijo,
            "Localidad": localidad
        }


class CreacionDePedidosTest(TestCase):
    def test_pedido_de_operador_sin_token_debe_lanzar_excepcion(self):
        mensaje_json = PedidoBuilder.pedido_de_operador(token='1', nombre_campania='c')
        del (mensaje_json['Token'])
        pedido = PedidoDeOperador.nuevo_para(mensaje_json)
        self.assertRaises(ErrorDePedido, pedido.token)

    def test_pedido_de_operador_sin_nombre_de_campania_debe_lanzar_excepcion(self):
        mensaje_json = PedidoBuilder.pedido_de_operador(token='1', nombre_campania='c')
        del (mensaje_json['Campaign'])
        pedido = PedidoDeOperador.nuevo_para(mensaje_json)
        self.assertRaises(ErrorDePedido, pedido.nombre_de_campania)

    def test_pedido_de_operador_debe_crearlo_correctamente(self):
        mensaje_json = PedidoBuilder.pedido_de_operador(token='abc', nombre_campania='campania', telefono='13133131', nombre='Roman', celular='15151515',
                                             marca='Ford', modelo='Fiesta', provincia='La Pampa')
        pedido = PedidoDeOperador.nuevo_para(mensaje_json)

        self.assertEqual(pedido.evento(), mensaje_json['Event'])
        self.assertEqual(pedido.token(), mensaje_json['Token'])
        self.assertEqual(pedido.nombre_de_campania(), mensaje_json['Campaign'])
        variables = pedido.variables()
        self.assertEqual(variables.telefono(), mensaje_json['Variables']['Telefono'])
        self.assertEqual(variables.celular(), mensaje_json['Variables']['Celular'])
        self.assertEqual(variables.email(), mensaje_json['Variables']['Email'])
        self.assertEqual(variables.nombre(), mensaje_json['Variables']['Nombre'])
        self.assertEqual(variables.modelo(), [mensaje_json['Variables']['Modelo']])
        self.assertEqual(variables.marcas(), mensaje_json['Variables']['Marca'].split('|'))
        self.assertEqual(variables.provincia(), mensaje_json['Variables']['Provincia'])

    def test_pedido_recibir_mensaje_sin_token_debe_lanzar_excepcion(self):
        mensaje_json = PedidoBuilder.recibir_mensaje()
        del (mensaje_json['Token'])
        pedido = PedidoRecibirMensaje.nuevo_para(mensaje_json)
        self.assertRaises(ErrorDePedido, pedido.token)

    def test_pedido_recibir_mensaje_sin_texto_debe_lanzar_excepcion(self):
        mensaje_json = PedidoBuilder.recibir_mensaje()
        del (mensaje_json['Body'])
        pedido = PedidoRecibirMensaje.nuevo_para(mensaje_json)
        self.assertRaises(ErrorDePedido, pedido.texto)

    def test_pedido_recibir_mensaje_debe_crearlo_correctamente(self):
        mensaje_json = PedidoBuilder.recibir_mensaje(telefono='13133131', nombre='Roman', celular='15151515',
                                                     marca='Ford', modelo='Fiesta', provincia='La Pampa')
        pedido = PedidoRecibirMensaje.nuevo_para(mensaje_json)

        self.assertEqual(pedido.texto(), mensaje_json['Body'])
        self.assertEqual(pedido.evento(), mensaje_json['Event'])
        self.assertEqual(pedido.token(), mensaje_json['Token'])
        variables = pedido.variables()
        self.assertEqual(variables.telefono(), mensaje_json['Variables']['Telefono'])
        self.assertEqual(variables.celular(), mensaje_json['Variables']['Celular'])
        self.assertEqual(variables.email(), mensaje_json['Variables']['Email'])
        self.assertEqual(variables.nombre(), mensaje_json['Variables']['Nombre'])
        self.assertEqual(variables.modelo(), [mensaje_json['Variables']['Modelo']])
        self.assertEqual(variables.marcas(), mensaje_json['Variables']['Marca'].split('|'))
        self.assertEqual(variables.provincia(), mensaje_json['Variables']['Provincia'])

    def test_pedido_recibir_mensaje_sin_variables_debe_responder_valores_por_debecto(self):
        mensaje_json = PedidoBuilder.recibir_mensaje()
        mensaje_json['Variables'] = {}
        pedido = PedidoRecibirMensaje.nuevo_para(mensaje_json)

        self.assertEqual(pedido.evento(), mensaje_json['Event'])
        self.assertEqual(pedido.texto(), mensaje_json['Body'])
        self.assertEqual(pedido.token(), mensaje_json['Token'])
        variables = pedido.variables()
        self.assertEqual(variables.telefono(), '')
        self.assertEqual(variables.celular(), '')
        self.assertEqual(variables.email(), '')
        self.assertEqual(variables.nombre(), 'Posible Comprador')
        self.assertEqual(variables.modelo(), [])
        self.assertEqual(variables.marcas(), [])
        self.assertEqual(variables.provincia(), '')
