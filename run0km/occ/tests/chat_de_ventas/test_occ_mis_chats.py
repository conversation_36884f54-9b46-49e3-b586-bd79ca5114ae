import json

from django.urls import reverse

from occ.errores import CriterioFiltradorDeChatsInexistenteException
from occ.tests.chat_de_ventas.contexto import CreadorDeContextoParaChat
from occ.views import FiltradorDeChats
from testing.base import BaseLoggedTest, BaseFixturedTest


class EntornoParaOCCMisChats(object):
    def __init__(self, fixture, contexto):
        self.vendedor = fixture['vend_1']
        self.occ_mis_chats_url = reverse('occ-mis-chats')
        self.chats_perdidos = contexto.crear_chats_perdidos_para(vendedor=self.vendedor, cantidad=1)
        self.chat_convertido = contexto.crear_chats_convertidos_para(vendedor=self.vendedor, cantidad=1)
        self.chat_vigente = contexto.crear_chats_vigentes_para(vendedor=self.vendedor, cantidad=1)


class FiltradorDeChatsTest(BaseFixturedTest):
    def setUp(self):
        super(FiltradorDeChatsTest, self).setUp()
        self.contexto_chats = CreadorDeContextoParaChat(self.fixture)
        self.entorno_occ = EntornoParaOCCMisChats(self.fixture, self.contexto_chats)

    def test_filtrar_por_chats_perdidos_deberia_devolver_chats_perdidos(self):
        criterios_de_filtro = [FiltradorDeChats.PERDIDOS]
        filtrador_de_chats = FiltradorDeChats(lista_de_criterios=criterios_de_filtro)
        lista_de_chats = filtrador_de_chats.filtrar(self.entorno_occ.vendedor.chats_de_ventas.all())
        self.assertCountEqual(lista_de_chats, self.entorno_occ.chats_perdidos)

    def test_filtrar_por_chats_convertidos_deberia_devolver_chats_convertidos(self):
        criterios_de_filtro = [FiltradorDeChats.CONVERTIDOS]
        filtrador_de_chats = FiltradorDeChats(lista_de_criterios=criterios_de_filtro)
        lista_de_chats = filtrador_de_chats.filtrar(self.entorno_occ.vendedor.chats_de_ventas.all())
        self.assertCountEqual(lista_de_chats, [self.entorno_occ.chat_convertido[0].chat])

    def test_filtrar_por_chats_vigentes_deberia_devolver_chats_vigentes(self):
        criterios_de_filtro = [FiltradorDeChats.VIGENTES]
        filtrador_de_chats = FiltradorDeChats(lista_de_criterios=criterios_de_filtro)
        lista_de_chats = filtrador_de_chats.filtrar(self.entorno_occ.vendedor.chats_de_ventas.all())
        self.assertCountEqual(lista_de_chats, self.entorno_occ.chat_vigente)

    def test_filtrar_por_filtro_inexistente_deberia_levantar_excepcion(self):
        criterios_de_filtro = ['filtro_invalido']
        self.assertRaises(CriterioFiltradorDeChatsInexistenteException, FiltradorDeChats,
                          lista_de_criterios=criterios_de_filtro)

    def test_filtrar_por_chats_convertidos_y_perdidos_deberia_devolver_ambos(self):
        criterios_de_filtro = [FiltradorDeChats.CONVERTIDOS, FiltradorDeChats.PERDIDOS]
        filtrador_de_chats = FiltradorDeChats(lista_de_criterios=criterios_de_filtro)
        lista_de_chats = filtrador_de_chats.filtrar(self.entorno_occ.vendedor.chats_de_ventas.all())
        self.assertCountEqual(lista_de_chats, [self.entorno_occ.chat_convertido[0].chat] + self.entorno_occ.chats_perdidos)


class OCCMisChatsViewTest(BaseLoggedTest):
    def setUp(self):
        super(OCCMisChatsViewTest, self).setUp()
        self.contexto_chats = CreadorDeContextoParaChat(self.fixture)
        self.entorno_occ = EntornoParaOCCMisChats(self.fixture, self.contexto_chats)

    def test_filtro_chats_perdidos_solo_devuelve_perdidos(self):
        data = {'criterios_de_filtrador': json.dumps([FiltradorDeChats.PERDIDOS])}
        response = self.client.get(path=self.entorno_occ.occ_mis_chats_url, data=data)
        self.assertEqual(response.status_code, 200)
        self._assertResponseContieneChats(response, self.entorno_occ.chats_perdidos)

    def test_filtro_chats_convertidos_solo_devuelve_convertidos(self):
        data = {'criterios_de_filtrador': json.dumps([FiltradorDeChats.CONVERTIDOS])}
        response = self.client.get(path=self.entorno_occ.occ_mis_chats_url, data=data)
        self.assertEqual(response.status_code, 200)
        self._assertResponseContieneChats(response, [self.entorno_occ.chat_convertido[0].chat])

    def test_filtro_chats_vigentes_no_devuelve_nada(self):
        data = {'criterios_de_filtrador': json.dumps([FiltradorDeChats.VIGENTES])}
        response = self.client.get(path=self.entorno_occ.occ_mis_chats_url, data=data)
        self.assertEqual(response.status_code, 200)
        self._assertResponseContieneChats(response, [])

    def test_filtro_chats_perdidos_y_convertidos_devuelve_ambos(self):
        data = {'criterios_de_filtrador': json.dumps([FiltradorDeChats.PERDIDOS, FiltradorDeChats.CONVERTIDOS])}
        response = self.client.get(path=self.entorno_occ.occ_mis_chats_url, data=data)
        self.assertEqual(response.status_code, 200)
        self._assertResponseContieneChats(response, self.entorno_occ.chats_perdidos + [self.entorno_occ.chat_convertido[0].chat])

    def _assertResponseContieneChats(self, response, chats):
        chats_de_response = response.context[0].dicts[3]['chats'].object_list
        self.assertCountEqual(chats_de_response, chats)
