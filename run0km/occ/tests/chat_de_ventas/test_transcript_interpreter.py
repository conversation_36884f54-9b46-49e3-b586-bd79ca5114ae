from django.test import TestCase
from django.utils.timezone import localtime, now, datetime, make_aware

from occ.models import MensajeDeChat
from occ.soporte import TranscriptInterpreter


class TranscriptInterpreterTest(TestCase):
    def setUp(self):
        super(TranscriptInterpreterTest, self).setUp()
        self.hoy = localtime(now())

    def _ejemplo(self):
        transcript = [
            '09:21:42',
            'B: (+5s) <PERSON><PERSON>, te puedo ayudar en algo?',
            '(+5s) <PERSON><PERSON>, te puedo ayudar en algo?',
            'B: (+30s) Te interesa alguna marca en particular?',
            'U: (+21s) <PERSON><PERSON>, queria saber un poco mas sobre este plan..',
            'B: (+10s) Tenemos las siguientes marcas para ofrecerte: Volkswagen, Fiat, Ford, Renault, Chevrolet, '
            'Citroen, Peugeot, Toyota. Quer\xe9s que te mande m\xe1s informaci\xf3n? de cual?',
            'U: (+30s) Chevrolet',
            'B: (+11s) Quer\xe9s que te mande m\xe1s info de Chevrolet por whatsapp?',
            'B: Quer\xe9s que te mande m\xe1s info de Chevrolet por whatsapp?',
            'U: (+165s) hola buenos dias me interesa el chevrolet onix',
        ]
        return transcript

    def _assert_mensaje(self, hoy, mensaje, hora_esperada, texto_esperado, emisor_esperado):
        self.assertEqual(mensaje, {
            'hora': make_aware(datetime.combine(hoy, datetime.strptime(hora_esperada, "%H:%M:%S").time())),
            'emisor': emisor_esperado,
            'texto': texto_esperado})

    def test_transcript_valido_debe_generar_mensaje(self):
        interpreter = TranscriptInterpreter()
        mensajes = interpreter.interpretar_mensajes_desde(self._ejemplo(),
                                                          nombre_bot='Mariela',
                                                          nombre_usuario='Roman')
        self.assertEqual(len(mensajes), 7)
        self._assert_mensaje(self.hoy, mensajes[0],
                             hora_esperada="09:21:47",
                             texto_esperado='[09:21:47] Mariela: Hola, te puedo ayudar en algo?',
                             emisor_esperado=MensajeDeChat.VENDEDOR)
        self._assert_mensaje(self.hoy, mensajes[1],
                             hora_esperada="09:22:17",
                             texto_esperado='[09:22:17] Mariela: Te interesa alguna marca en particular?',
                             emisor_esperado=MensajeDeChat.VENDEDOR)
        self._assert_mensaje(self.hoy, mensajes[2],
                             hora_esperada="09:22:38",
                             texto_esperado='[09:22:38] Roman: Hola, queria saber un poco mas sobre este plan..',
                             emisor_esperado=MensajeDeChat.CLIENTE)
        self._assert_mensaje(self.hoy, mensajes[3],
                             hora_esperada="09:22:48",
                             texto_esperado='[09:22:48] Mariela: Tenemos las siguientes marcas para ofrecerte: '
                                            'Volkswagen, Fiat, Ford, Renault, Chevrolet, Citroen, Peugeot, Toyota. '
                                            'Quer\xe9s que te mande m\xe1s informaci\xf3n? de cual?',
                             emisor_esperado=MensajeDeChat.VENDEDOR)
        self._assert_mensaje(self.hoy, mensajes[4],
                             hora_esperada="09:23:18",
                             texto_esperado='[09:23:18] Roman: Chevrolet',
                             emisor_esperado=MensajeDeChat.CLIENTE)
        self._assert_mensaje(self.hoy, mensajes[5],
                             hora_esperada="09:23:29",
                             texto_esperado='[09:23:29] Mariela: Quer\xe9s que te mande m\xe1s info de'
                                            ' Chevrolet por whatsapp?',
                             emisor_esperado=MensajeDeChat.VENDEDOR)
        self._assert_mensaje(self.hoy, mensajes[6],
                             hora_esperada="09:26:14",
                             texto_esperado='[09:26:14] Roman: hola buenos dias me interesa el chevrolet onix',
                             emisor_esperado=MensajeDeChat.CLIENTE)

    def test_linea_sin_hora_no_debe_generar_mensaje(self):
        interpreter = TranscriptInterpreter()
        ejemplo = ['09:21:42', 'B: Hola, te puedo ayudar en algo?']
        mensajes = interpreter.interpretar_mensajes_desde(ejemplo,
                                                          nombre_bot='Mariela',
                                                          nombre_usuario='Roman')
        self.assertEqual(len(mensajes), 0)

    def test_linea_sin_emisor_no_debe_generar_mensaje(self):
        interpreter = TranscriptInterpreter()
        ejemplo = ['09:21:42', '(+5s) Hola, te puedo ayudar en algo?']
        mensajes = interpreter.interpretar_mensajes_desde(ejemplo,
                                                          nombre_bot='Mariela',
                                                          nombre_usuario='Roman')
        self.assertEqual(len(mensajes), 0)
