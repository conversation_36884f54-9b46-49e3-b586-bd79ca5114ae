from lib.sales_chat_service import SalesChatConnectionError


class Chat<PERSON>roviderSuccessMock(object):
    def __init__(self, assign_operator_url, send_message_url):
        pass

    def enviar_asignacion_de_operador(self, token, url):
        return True

    def send_message(self, token, message):
        pass


class ChatProviderFailMock(object):
    def __init__(self, assign_operator_url, send_message_url):
        pass

    def enviar_asignacion_de_operador(self, token, url):
        return False

    def send_message(self, token, message):
        raise SalesChatConnectionError(message='error', request='request', response='response')
