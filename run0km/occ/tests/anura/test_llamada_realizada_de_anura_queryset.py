from dateutil.relativedelta import relativedelta
from django.utils import timezone

from layers.application.commands.anura.pedir_llamado import PedirLlamadoPorAnuraComando
from layers.application.commands.anura.registrar_llamada_realizada import RegistrarLlamadaRealizadaAnuraComando
from occ.models.anura import LlamadaRealizadaDeAnura
from testing.base import BaseLoggedTest


class LlamadaRealizadaDeAnuraQuerysetTest(BaseLoggedTest):
    def setUp(self):
        super(LlamadaRealizadaDeAnuraQuerysetTest, self).setUp()
        self.vendedor_habilitado = self.vendedor
        self.vendedor_habilitado.configuracion_de_servicios().habilitar_llamadas()
        self.vendedor_habilitado.responsable().configuracion_de_servicios().habilitar_llamadas()
        self.vendedor_habilitado.responsable().obtener_concesionaria().configuracion_de_servicios().habilitar_llamadas()
        self.telefono = "+5491144556688"
        self.creador_de_contexto.set_supervisor_default(self.vendedor.supervisor)
        self.prospecto = self.prospectos = self.creador_de_contexto.asignar_prospecto_nuevo_a(self.vendedor_habilitado,
                                                                                              telefono=self.telefono)

    def test_minutos_gastados_mensualmente_para_vendedor_sin_especificar_mes_y_anio_devuelve_minutos_del_mes_actual(
            self):
        # dado que tenemos un vendedor habilitado con llamados realizados que suman 10 minutos de duracion
        self._crear_llamados(cantidad_de_llamados=10, duracion_en_segundos_por_llamado=60, fecha=timezone.now(),
                             llamador=self.vendedor_habilitado)

        # entonces el metodo de minutos gastados del manager de LlamadaRealizadaDeAnura devuelve 10 minutos de duracion
        # para el mes actual
        minutos_gastados_actuales = LlamadaRealizadaDeAnura.objects.minutos_gastados_mensualmente_para(
            vendedor=self.vendedor_habilitado)
        self.assertEqual(10, minutos_gastados_actuales)

    def test_minutos_gastados_mensualmente_para_vendedor_se_redondean_para_arriba(self):
        # dado que tenemos un vendedor habilitado con llamados realizados que suman 7.5 minutos de duracion
        self._crear_llamados(cantidad_de_llamados=10, duracion_en_segundos_por_llamado=45, fecha=timezone.now(),
                             llamador=self.vendedor_habilitado)

        # entonces el metodo de minutos gastados del manager de LlamadaRealizadaDeAnura devuelve 8 minutos de duracion
        # para el mes actual
        minutos_gastados_actuales = LlamadaRealizadaDeAnura.objects.minutos_gastados_mensualmente_para(
            vendedor=self.vendedor_habilitado)
        self.assertEqual(8, minutos_gastados_actuales)

    def test_minutos_gastados_mensualmente_para_vendedor_sin_especificar_mes_devuelve_minutos_del_mes_actual_del_anio_especificado(
            self):
        # dado que tenemos un vendedor habilitado con llamados realizados que suman 10 minutos de duracion
        fecha = timezone.now() - relativedelta(years=1)
        anio = fecha.year
        self._crear_llamados(cantidad_de_llamados=10, duracion_en_segundos_por_llamado=60, fecha=fecha,
                             llamador=self.vendedor_habilitado)

        # entonces el metodo de minutos gastados del manager de LlamadaRealizadaDeAnura devuelve 10 minutos de duracion
        # para el mes actual y anio especificado
        minutos_gastados_actuales = LlamadaRealizadaDeAnura.objects.minutos_gastados_mensualmente_para(
            vendedor=self.vendedor_habilitado, anio=anio)
        self.assertEqual(10, minutos_gastados_actuales)

    def test_minutos_gastados_mensualmente_para_vendedor_sin_especificar_anio_devuelve_minutos_del_mes_especificado_del_anio_actual(
            self):
        # dado que tenemos un vendedor habilitado con llamados realizados que suman 10 minutos de duracion
        mes = 3
        fecha = timezone.now().replace(month=mes)
        self._crear_llamados(cantidad_de_llamados=10, duracion_en_segundos_por_llamado=60, fecha=fecha,
                             llamador=self.vendedor_habilitado)

        # entonces el metodo de minutos gastados del manager de LlamadaRealizadaDeAnura devuelve 10 minutos de duracion
        # para el mes especificado y anio actual
        minutos_gastados_actuales = LlamadaRealizadaDeAnura.objects.minutos_gastados_mensualmente_para(
            vendedor=self.vendedor_habilitado, mes=mes)
        self.assertEqual(10, minutos_gastados_actuales)

    def test_minutos_gastados_mensualmente_para_vendedor_especificando_mes_y_anio_devuelve_minutos_para_ese_mes_y_anio(
            self):
        # dado que tenemos un vendedor habilitado con llamados realizados que suman 10 minutos de duracion
        mes = 3
        anio = 2015
        fecha = timezone.now().replace(year=anio, month=mes)
        self._crear_llamados(cantidad_de_llamados=10, duracion_en_segundos_por_llamado=60, fecha=fecha,
                             llamador=self.vendedor_habilitado)

        # entonces el metodo de minutos gastados del manager de LlamadaRealizadaDeAnura devuelve 10 minutos de duracion
        # para el mes y anio especificados
        minutos_gastados_actuales = LlamadaRealizadaDeAnura.objects.minutos_gastados_mensualmente_para(
            vendedor=self.vendedor_habilitado, mes=mes, anio=anio)
        self.assertEqual(10, minutos_gastados_actuales)

    def _crear_llamados(self, cantidad_de_llamados, duracion_en_segundos_por_llamado, llamador, fecha):
        for _ in range(cantidad_de_llamados):
            self._crear_llamada_realizada_de_duracion(llamador=llamador, fecha=fecha,
                                                      duracion_en_segundos=duracion_en_segundos_por_llamado)

    def _crear_llamada_realizada_de_duracion(self, llamador, fecha, duracion_en_segundos):
        pedir_llamado_comando = PedirLlamadoPorAnuraComando()
        pedir_llamado_comando.set_llamador(llamador)
        pedir_llamado_comando.set_prospecto(self.prospecto)
        pedir_llamado_comando.set_telefono(self.telefono)
        pedir_llamado_result = pedir_llamado_comando.execute()
        intento_de_llamado = pedir_llamado_result.get_object()

        comando = RegistrarLlamadaRealizadaAnuraComando()
        comando.set_arguments({
            "id_llamado_delivery": intento_de_llamado.id,
            "id_llamado_anura": 123,
            "fecha_comienzo": fecha.strftime(RegistrarLlamadaRealizadaAnuraComando.DATETIME_FORMATO),
            "audio_file_url": "https://test.com/test.mp3",
            "duracion": duracion_en_segundos,
            "precio": 6
        })
        result = comando.execute()
        return result
