# coding=utf-8
import mock
from django.test.utils import override_settings

from lib.smscover.tests import SMSServiceAnswerMock, SMSCommunicationFailMock
from occ.campania_envios_sms import EmisorDeCampaniasDeSMS
from occ.models import EnvioDeMensaje, LogDeErrorDeSMS, CampaniaDeComunicacion, EnvioDeCampaniaDeSMS
from occ.sms_estrategias_de_envio import DeliverySMS
from prospectos.models import TelefonoExtra
from testing.base import BaseFixturedTest


@override_settings(SMSCOVER_SMS_SENDER_CLASS='lib.smscover.SMSSender')
class EnvioDeCampaniaDeSMSTest(BaseFixturedTest):
    def setUp(self):
        super(EnvioDeCampaniaDeSMSTest, self).setUp()
        self.campania = self.fixture['cv_1']
        self.template_de_mensaje = ""
        self.vendedor = self.fixture['vend_1']

        self.servicio_de_mensaje = EmisorDeCampaniasDeSMS()
        self.errores_pre_envio = LogDeErrorDeSMS.objects.count()
        EnvioDeMensaje.objects.all().delete()
        DeliverySMS.reset_instance()

    def _get_cantidad_de_telefonos_de_prospecto(self, prospecto):
        return prospecto.telefono_extra.count() + 1

    def _get_cantidad_de_envios_de_campania(self, campania):
        return sum([self._get_cantidad_de_telefonos_de_prospecto(prospecto) for prospecto
                    in campania.prospectos.all()])

    def _assert_envios_de_un_prospecto(self, campania, prospecto):
        envios = EnvioDeCampaniaDeSMS.objects.filter(campania=campania, envio__prospecto=prospecto)
        self.assertEqual(envios.count(), self._get_cantidad_de_telefonos_de_prospecto(prospecto))

    def _assert_resultado_de_envio(self, campania, resultado, resultado_esperado, mensaje_esperado):
        self.assertEqual(resultado.fue_exitoso(), resultado_esperado)
        self.assertEqual(resultado.detalle(), mensaje_esperado)

        self.assertEqual(campania.envio_mensajes_correcto, resultado_esperado)
        self.assertEqual(campania.envio_mensajes_correcto_info, mensaje_esperado)

    def _assert_envio_fallido(self, campania, resultado, mensaje_esperado):
        self._assert_resultado_de_envio(campania, resultado, False, mensaje_esperado)
        self.assertFalse(self.campania.envios.exists())

    def _assert_envio_exitoso(self, campania, resultado, mensaje_esperado):
        self._assert_resultado_de_envio(campania, resultado, True, mensaje_esperado)

    def _assert_log_de_error(self, campania, tipo, descripcion, request_fue_registrado,
                             response_fue_registrado):
        self.assertEqual(LogDeErrorDeSMS.objects.count(), self.errores_pre_envio + 1,
                          'Se deberia haber registrado un error')
        ultimo_error = LogDeErrorDeSMS.objects.filter(campania=campania).last()

        self.assertEqual(str(ultimo_error.tipo), tipo)
        self.assertEqual(str(ultimo_error.descripcion), descripcion)
        self.assertEqual(str(ultimo_error.request) != '', request_fue_registrado)
        self.assertEqual(str(ultimo_error.response) != '', response_fue_registrado)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSCommunicationFailMock().call)
    def test_si_hay_falla_en_la_comunicacion_retorna_envio_fallido(self, mock_call):
        resultado = self.servicio_de_mensaje.enviar_campania(
            self.campania, self.template_de_mensaje, self.vendedor)

        self._assert_envio_fallido(self.campania, resultado, 'Fallo el envio de SMS - Error de comunicación')

        self._assert_log_de_error(self.campania, 'Error de comunicación',
                                  'Error al realizar la comunicación con el servicio',
                                  request_fue_registrado=True, response_fue_registrado=False)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.authentication_error().call)
    def test_si_hay_falla_en_la_autenticacion_retorna_envio_fallido(self, mock_call):
        resultado = self.servicio_de_mensaje.enviar_campania(
            self.campania, self.template_de_mensaje, self.vendedor)

        self._assert_envio_fallido(
            self.campania, resultado, "Fallo el envio de SMS - El servicio ha notificado alguna falla")

        self._assert_log_de_error(
            self.campania, 'El servicio ha notificado alguna falla',
            'ERROR_SEGURIDAD: No dispone de los privilegios para consumir el Web Service. Cod:>l63 key:12',
            request_fue_registrado=True, response_fue_registrado=True)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.request_errors().call)
    def test_si_hay_falla_en_el_request_retorna_envio_fallido(self, mock_call):
        resultado = self.servicio_de_mensaje.enviar_campania(self.campania, self.template_de_mensaje, self.vendedor)

        self._assert_envio_fallido(
            self.campania, resultado, "Fallo el envio de SMS - El servicio ha notificado alguna falla")

        self._assert_log_de_error(
            self.campania, 'El servicio ha notificado alguna falla',
            'ERROR_FORMATO: El xml enviado no tiene el formato correcto ---- ERROR_FORMATO: The \'numeroTelefono\' '
            'element is invalid - The value \'1525\' is invalid according to its datatype \'String\' '
            '- The Pattern constraint failed.',
            request_fue_registrado=True, response_fue_registrado=True)

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully().call)
    def test_si_la_comunicacion_es_exitosa_retorna_envio_correcto(self, mock_call):
        resultado = self.servicio_de_mensaje.enviar_campania(self.campania, self.template_de_mensaje, self.vendedor)
        self._assert_envio_exitoso(self.campania, resultado, '3 mensajes enviados correctamente')
        self.assertEqual(EnvioDeMensaje.objects.count(), self._get_cantidad_de_envios_de_campania(self.campania))
        for prospecto in self.campania.prospectos.all():
            self._assert_envios_de_un_prospecto(self.campania, prospecto)

        self.assertEqual(LogDeErrorDeSMS.objects.count(), self.errores_pre_envio,
                          'No deberia haber registrado errores')

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully_query().call)
    def test_nueva_campania_con_mensaje_invalido_no_deberia_generar_envios(self, mock_call):
        self.campania = self.fixture['cv_1']
        template_de_mensaje = "{nombre}, " + "{}{}"

        resultado = self.servicio_de_mensaje.enviar_campania(self.campania, template_de_mensaje, self.vendedor)
        self._assert_envio_fallido(self.campania, resultado, 'Mensaje con caracteres invalidos')
        self.assertFalse(EnvioDeMensaje.objects.exists())

    @override_settings(DIAS_PARA_NO_ENVIAR_MENSAJES_SMS=1)
    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully_query().call)
    def test_nueva_campania_no_envia_mensajes_a_telefonos_que_han_sido_enviados_en_campania_anterior(self, mock_call):
        self.campania_con_cinco_prospectos_y_un_telefono_cada_uno = self.fixture['cv_4']
        self.campania_dos_prospectos_iguales = self.fixture['cv_2']

        template_de_mensaje = "{nombre}, "

        resultado_uno = self.servicio_de_mensaje.enviar_campania(
            self.campania_con_cinco_prospectos_y_un_telefono_cada_uno, template_de_mensaje, self.vendedor)

        self._assert_envio_exitoso(
            self.campania_con_cinco_prospectos_y_un_telefono_cada_uno, resultado_uno,
            '5 mensajes enviados correctamente')
        # self.campania_con_cinco_prospectos_y_un_telefono_cada_uno.
        resultado_dos = self.servicio_de_mensaje.enviar_campania(
            self.campania_dos_prospectos_iguales, template_de_mensaje, self.vendedor)

        self._assert_envio_fallido(
            self.campania_dos_prospectos_iguales, resultado_dos,
            '0 mensajes enviados correctamente, 2 mensajes rechazados por haber sido enviados en campanias anteriores')

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSServiceAnswerMock.successfully().call)
    def test_deberia_notificar_numero_invalido(self, mock_call):
        prospecto = self.fixture['p_1']
        prospecto.telefono = 'º11 33464353'
        prospecto.save()
        extra = TelefonoExtra(prospecto=prospecto, vendedor=self.vendedor, telefono='45454545')
        extra.save()
        campania = CampaniaDeComunicacion.nueva_campania_con(nombre='nombre', mensaje='mensaje',
                                                             medio=self.fixture['medio_1'],
                                                             vendedor=self.vendedor, prospectos=[prospecto])
        resultado = self.servicio_de_mensaje.enviar_campania(campania, self.template_de_mensaje, self.vendedor)

        self._assert_envio_exitoso(
            campania, resultado, '1 mensajes enviados correctamente, 1 telefonos con formato incorrecto')
        self.assertEqual(EnvioDeMensaje.objects.count(), 1)
        self.assertEqual(LogDeErrorDeSMS.objects.count(), self.errores_pre_envio,
                          'No deberia haber registrado errores')


@override_settings(SMSCOVER_SMS_SENDER_CLASS='lib.smscover.SMSSender')
class EnvioMensajeTest(BaseFixturedTest):
    def setUp(self):
        super(EnvioMensajeTest, self).setUp()
        DeliverySMS.reset_instance()
        self.campania_dos_prospectos_un_telefono = self.fixture['cv_2']
        self.campania_un_prospecto_dos_telefonos = self.fixture['cv_3']
        prospecto = self.campania_un_prospecto_dos_telefonos.prospectos.first()
        telefono_extra = prospecto.telefono_extra.first()
        telefono_extra.telefono = '15151515'
        telefono_extra.save()

        self.template_de_mensaje = ""
        self.vendedor = self.fixture['vend_1']

        self.servicio_de_mensaje = EmisorDeCampaniasDeSMS()
        self.mensajes_pre_envio = EnvioDeMensaje.objects.count()

    @mock.patch("lib.smscover.SMSPushService.call", side_effect=SMSCommunicationFailMock().call)
    def test_si_hay_falla_en_la_comunicacion_no_guarda_envio_de_mensaje(self, mock_call):
        self.servicio_de_mensaje.enviar_campania(
            self.campania_un_prospecto_dos_telefonos, self.template_de_mensaje, self.vendedor)

        self.assertEqual(
            self.mensajes_pre_envio, EnvioDeMensaje.objects.all().count(),
            'No deberia haberse regristrado ningún envio nuevos')

    @mock.patch("lib.smscover.SMSPushService.call",
                side_effect=SMSServiceAnswerMock.successfully().call)
    def test_comunicacion_exitosa_guarda_envios_correctamente_con_dos_prospectos_un_telefono(self,
                                                                                             mock_call):
        self.servicio_de_mensaje.enviar_campania(self.campania_dos_prospectos_un_telefono, self.template_de_mensaje,
                                                 self.vendedor)
        primer_envio_de_mensaje_guardado = \
            EnvioDeCampaniaDeSMS.objects.filter(campania=self.campania_dos_prospectos_un_telefono)[0]
        segudo_envio_de_mensaje_guardado = \
            EnvioDeCampaniaDeSMS.objects.filter(campania=self.campania_dos_prospectos_un_telefono)[1]
        prospecto_uno = self.campania_dos_prospectos_un_telefono.prospectos.all()[0]
        prospecto_dos = self.campania_dos_prospectos_un_telefono.prospectos.all()[1]

        self.assertEqual(self.mensajes_pre_envio + 2, EnvioDeMensaje.objects.all().count(),
                         'Deberia haberse regristrado dos envios nuevos')
        self.assertEqual(primer_envio_de_mensaje_guardado.telefono, prospecto_uno.telefono)
        self.assertEqual(segudo_envio_de_mensaje_guardado.telefono, prospecto_dos.telefono)
        self.assertEqual(primer_envio_de_mensaje_guardado.campania, self.campania_dos_prospectos_un_telefono)
        self.assertEqual(segudo_envio_de_mensaje_guardado.campania, self.campania_dos_prospectos_un_telefono)

    @mock.patch("lib.smscover.SMSPushService.call",
                side_effect=SMSServiceAnswerMock.successfully().call)
    def test_comunicacion_exitosa_guarda_envios_correctamente_con_un_prospecto_dos_telefonos(self,
                                                                                             mock_call):
        self.servicio_de_mensaje.enviar_campania(self.campania_un_prospecto_dos_telefonos, self.template_de_mensaje,
                                                 self.vendedor)
        envios_de_campania_un_prospecto_dos_telefonos = EnvioDeCampaniaDeSMS.objects.filter(
            campania=self.campania_un_prospecto_dos_telefonos)
        primer_envio_de_mensaje_guardado = envios_de_campania_un_prospecto_dos_telefonos[0]
        segundo_envio_de_mensaje_guardado = envios_de_campania_un_prospecto_dos_telefonos[1]
        prospecto_uno = self.campania_un_prospecto_dos_telefonos.prospectos.all()[0]
        telefono_extra = prospecto_uno.telefono_extra.all()[0]

        self.assertEqual(self.mensajes_pre_envio + 2, EnvioDeCampaniaDeSMS.objects.all().count(),
                         'Deberia haberse regristrado dos envios nuevos')

        self.assertEqual(primer_envio_de_mensaje_guardado.telefono, telefono_extra.telefono)
        self.assertEqual(segundo_envio_de_mensaje_guardado.telefono, prospecto_uno.telefono)
        self.assertEqual(primer_envio_de_mensaje_guardado.campania, self.campania_un_prospecto_dos_telefonos)
        self.assertEqual(segundo_envio_de_mensaje_guardado.campania, self.campania_un_prospecto_dos_telefonos)
