import datetime
import unittest

from occ.integracion.calculadora_de_cotas import CalculadoraDeCotas


class CalculadoraDeCotasTest(unittest.TestCase):
    def test_01_a_mediado_de_enero_las_cotas_son_el_mes_de_diciembre_del_anio_anterior_con_antiguedad_1(self):
        mediados_de_enero = datetime.datetime(2000, 1, 15, 12, 30, 30)
        comienzo_de_diciembre = datetime.datetime(1999, 12, 1)
        treinta_meses_atras = datetime.datetime(1997, 6, 1)
        antiguedad = 1

        cota_superior = CalculadoraDeCotas().fecha_cota_superior(antiguedad=antiguedad, fecha=mediados_de_enero)
        cota_inferior = CalculadoraDeCotas().fecha_cota_inferior_desde(antiguedad_limite=30,
                                                                       fecha_cota_superior=cota_superior)
        self.assertEqual(comienzo_de_diciembre, cota_superior)
        self.assertEqual(treinta_meses_atras, cota_inferior)

    def test_02_a_mediado_de_enero_las_cotas_son_el_mes_de_noviembre_del_anio_anterior_con_antiguedad_2(self):
        mediados_de_enero = datetime.datetime(2000, 1, 15, 12, 30, 30)
        comienzo_de_diciembre = datetime.datetime(1999, 11, 1)
        treinta_meses_atras = datetime.datetime(1997, 5, 1)
        antiguedad = 2

        cota_superior = CalculadoraDeCotas().fecha_cota_superior(antiguedad=antiguedad, fecha=mediados_de_enero)
        cota_inferior = CalculadoraDeCotas().fecha_cota_inferior_desde(antiguedad_limite=30,
                                                                       fecha_cota_superior=cota_superior)
        self.assertEqual(comienzo_de_diciembre, cota_superior)
        self.assertEqual(treinta_meses_atras, cota_inferior)

    def test_03_a_mediado_de_mes_las_cotas_es_comienzo_de_mes_con_antiguedad_0(self):
        mediados_de_enero = datetime.datetime(2000, 1, 15, 12, 30, 30)
        comienzo_de_diciembre = datetime.datetime(2000, 1, 1)
        treinta_meses_atras = datetime.datetime(1997, 7, 1)
        antiguedad = 0

        cota_superior = CalculadoraDeCotas().fecha_cota_superior(antiguedad=antiguedad, fecha=mediados_de_enero)
        cota_inferior = CalculadoraDeCotas().fecha_cota_inferior_desde(antiguedad_limite=30,
                                                                       fecha_cota_superior=cota_superior)
        self.assertEqual(comienzo_de_diciembre, cota_superior)
        self.assertEqual(treinta_meses_atras, cota_inferior)

    def test_04_a_mediado_de_mes_las_cota_un_dia_anterior_con_antiguedad_menos_uno(self):
        mediados_de_enero = datetime.datetime(2000, 1, 15, 12, 30, 30)
        comienzo_de_diciembre = datetime.datetime(2000, 1, 14)
        treinta_meses_atras = datetime.datetime(1997, 7, 14)
        antiguedad = -1

        cota_superior = CalculadoraDeCotas().fecha_cota_superior(antiguedad=antiguedad, fecha=mediados_de_enero)
        cota_inferior = CalculadoraDeCotas().fecha_cota_inferior_desde(antiguedad_limite=30,
                                                                       fecha_cota_superior=cota_superior)
        self.assertEqual(comienzo_de_diciembre, cota_superior)
        self.assertEqual(treinta_meses_atras, cota_inferior)




if __name__ == '__main__':
    unittest.main()
