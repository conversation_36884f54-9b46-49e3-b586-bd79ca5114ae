from occ.errores_de_chat import ErrorDePedido


class Pedido(object):
    def __init__(self, pedido_json):
        super(Pedido, self).__init__()
        self._pedido_json_crudo = pedido_json
        self._pedido_json = self._normalizar(pedido_json)

    def as_json(self):
        return self._pedido_json_crudo

    def _get_atributo(self, atributo, requerido=True):
        valor = self._pedido_json.get(atributo, None)
        if not valor and requerido:
            raise ErrorDePedido.pedido_invalido('El atributo %s es requerido' % atributo)
        return valor

    def _get_atributo_opcional(self, atributo, valor_por_defecto):
        try:
            valor = self._get_atributo(atributo, requerido=True)
        except ErrorDePedido:
            valor = valor_por_defecto
        return valor

    def _normalizar(self, pedido_json):
        normalizado = {key.lower(): value for key, value in list(pedido_json.items())}
        return normalizado

    @classmethod
    def nuevo_para(cls, pedido_json):
        return cls(pedido_json)


class PedidoDeChat(Pedido):
    def __init__(self, pedido_json):
        super(PedidoDeChat, self).__init__(pedido_json)
        self._variables = self._leer_variables()

    def token(self):
        return self._get_atributo('token')

    def evento(self):
        return self._get_atributo('event')

    def variables(self):
        return self._variables

    def _leer_variables(self):
        variables_json = self._get_atributo_opcional('variables', valor_por_defecto={})
        return VariablesDeContacto.nuevo_para(variables_json)


class PedidoDeOperador(PedidoDeChat):
    def nombre_de_campania(self):
        return self._get_atributo('campaign')

    def marcas(self):
        return self.variables().marcas()

    def transcript(self):
        return self._get_atributo_opcional('transcript', valor_por_defecto=[])


class RespuestaBindOperador(Pedido):
    def __init__(self, pedido_json):
        super(RespuestaBindOperador, self).__init__(pedido_json)
        self._variables = self._leer_variables()

    def is_binded(self):
        return self._get_atributo_opcional('isbinded', valor_por_defecto=False)

    def transcript(self):
        return self._get_atributo_opcional('transcript', valor_por_defecto=[])

    def variables(self):
        return self._variables

    def _leer_variables(self):
        variables_json = self._get_atributo_opcional('variables', valor_por_defecto={})
        return VariablesDeContacto.nuevo_para(variables_json)


class PedidoRecibirMensaje(PedidoDeChat):
    def texto(self):
        return self._get_atributo('body')


class DispatcherDePedidoDeChat(object):
    KEY_EVENTO = 'Event'

    @classmethod
    def nuevo_para(cls, pedido_json):
        evento = pedido_json.get(cls.KEY_EVENTO)
        for subclass in cls.__subclasses__():
            if subclass.maneja(evento):
                return subclass(pedido_json)
        raise ErrorDePedido.pedido_invalido(motivo='No existe un dispatcher que maneje este pedido: %s' % evento)

    @classmethod
    def maneja(cls, evento):
        return cls.evento() == evento

    @classmethod
    def evento(cls):
        raise NotImplementedError('Subclass responsibility!')

    def __init__(self, pedido_json):
        self.pedido_json = pedido_json

    def ejecutar_pedido_en(self, servicio, parameters=None):
        if not parameters:
            parameters = {}

        self.ejecutar_servicio_con(servicio, parameters)

    def ejecutar_servicio_con(self, servicio, parameters):
        raise NotImplementedError('Subclass responsibility!')


class DispatcherDePedidoDeOperador(DispatcherDePedidoDeChat):
    @classmethod
    def evento(cls):
        return 'SessionStart'

    def construir_pedido(self):
        return PedidoDeOperador.nuevo_para(self.pedido_json)

    def ejecutar_servicio_con(self, servicio, parameters):
        pedido = self.construir_pedido()
        servicio.atender_pedido_de_operador(pedido)


class DispatcherDePedidoRecibirMensaje(DispatcherDePedidoDeChat):
    @classmethod
    def evento(cls):
        return 'VisitorMessage'

    def construir_pedido(self):
        return PedidoRecibirMensaje.nuevo_para(self.pedido_json)

    def ejecutar_servicio_con(self, servicio, parameters):
        pedido = self.construir_pedido()
        servicio.recibir_mensaje(pedido=pedido, **parameters)


class DispatcherDePedidoDeSesionTerminada(DispatcherDePedidoDeChat):
    @classmethod
    def evento(cls):
        return 'SessionEnd'

    def ejecutar_servicio_con(self, servicio, parameters):
        servicio.finalizar_chat(**parameters)


class VariablesDeContacto(Pedido):
    def nombre(self):
        return self._get_atributo_opcional('nombre', valor_por_defecto='Posible Comprador')

    def telefono_y_celular(self):
        telefonos = [self.telefono(), self.celular()]
        telefonos = [each for each in telefonos if each != '']
        return ', '.join(telefonos)

    def canal(self):
        return self._get_atributo_opcional('canal', valor_por_defecto="")

    def telefono(self):
        return self._get_atributo_opcional('telefono', valor_por_defecto='')

    def celular(self):
        return self._get_atributo_opcional('celular', valor_por_defecto='')

    def email(self):
        return self._get_atributo_opcional('email', valor_por_defecto='')

    def provincia(self):
        return self._get_atributo_opcional('provincia', valor_por_defecto='')

    def modelo(self):
        return self._get_atributo_opcional('modelo', valor_por_defecto='')

    def marcas(self):
        marcas = self._get_atributo_opcional('marca', valor_por_defecto='')
        if marcas == '':
            return []
        else:
            lista = marcas.split('|')
            return self._quitar_duplicados(lista)

    def _quitar_duplicados(self, lista):
        sin_duplicados = []
        [sin_duplicados.append(item) for item in lista if item not in sin_duplicados]
        return sin_duplicados

    def location(self):
        return self._get_atributo_opcional('location', valor_por_defecto='')

    def as_json(self):
        variables = self._pedido_json_crudo
        variables.pop('Celular', None)
        variables['Telefono'] = self.telefono_y_celular()
        return variables


