import datetime

from django.conf import settings

from crms.utils.loggers import LoggerDeCRMs
from lib.api_client import ClientComunicationError
from lib.api_dietrich import APIDietrichRequest, APIDietrichClient
from lib.timing import measure_time_to_evaluate
from occ.integracion.clientes import ClienteDeIntegracion


class ClienteDietrich(ClienteDeIntegracion):
    def __init__(self):
        super(ClienteDietrich, self).__init__()
        self._logger = LoggerDeCRMs().para_dietrich()
        self._sender = APIDietrichClient(
            url=settings.API_DIETRICH_URL,
            user=settings.API_DIETRICH_USUARIO,
            password=settings.API_DIETRICH_PASSWORD,
            authorization_code=settings.API_DIETRICH_CODIGO_DE_AUTORIZACION)

    def sincronizar_prospectos(self, prospectos):
        generador = GeneradorDeAPIDietrichRequest.nuevo()
        request = generador.generar_para_todos(prospectos)
        try:
            response, milliseconds = measure_time_to_evaluate(self._sender.call, request)
            self._logger.info('Llamada a la api dietric para %d demoro %0.2f ms', prospectos.count(), milliseconds)
        except ClientComunicationError as error:
            self._logger.info('Error api dietrich: %s - Response %s', str(error), error.response)
            raise error
        return response

    @classmethod
    def nombre(cls):
        return 'dietrich'


class GeneradorDeAPIDietrichRequest(object):

    @classmethod
    def nuevo(cls):
        return cls()

    @classmethod
    def formato_de_fecha_y_hora(cls, a_datetime):
        # ISO 8601 Time Representation
        return a_datetime.strftime(settings.API_DIETRICH_FORMATO_FECHA_HORA)

    @classmethod
    def formato_de_fecha(cls, a_datetime):
        # ISO 8601 Time Representation
        return a_datetime.strftime(settings.API_DIETRICH_FORMATO_FECHA)

    def generar_para_todos(self, prospectos):
        request_compuesto = None
        prospectos_optimizados = self._optimizar_consulta(prospectos)
        for prospecto in prospectos_optimizados.all():
            request = self.generar_para(prospecto)
            if request_compuesto is not None:
                request_compuesto = request_compuesto + request
            else:
                request_compuesto = request
        return request_compuesto

    def generar_para(self, prospecto):
        informacion_adicional = prospecto.obtener_informacion_adicional()
        nombre, apellido = prospecto.nombre_y_apellido()
        return APIDietrichRequest.new_with(
            id_lead=prospecto.id,
            usuario=self._usuario_de(prospecto),
            nombre_completo=prospecto.nombre_completo(),
            nombre=nombre,
            apellido=apellido,
            telefonos=self._telefonos_de(prospecto),
            emails=self._emails_de(prospecto),
            provincia=prospecto.obtener_provincia(),
            localidad=prospecto.obtener_localidad(),
            marca=prospecto.obtener_marca().codigo(),
            modelo=prospecto.modelos_como_string(),
            comentarios=self._comentarios_de(prospecto),
            origen=prospecto.campania.categoria.nombre,
            fecha_alta_origen=self.formato_de_fecha_y_hora(prospecto.fecha_creacion),
            tipo=settings.API_DIETRICH_TIPO,
            subtipo=settings.API_DIETRICH_SUBTIPO,
            campania=prospecto.campania.nombre_descriptivo(),
            proveedor=str(prospecto.campania.categoria.distribuidor or ""),
            ocupacion=informacion_adicional.ocupacion(),
            fecha_de_nacimiento=self._fecha_de_nacimiento_de(informacion_adicional),
            sexo=str(prospecto.campania.categoria.distribuidor or ""),
            usuario_vendedor=self._usuario_de(prospecto),
            documento=informacion_adicional.documento(),
            codigo_postal=None, #No manejamos codigo postal
            cargo=informacion_adicional.cargo(),
            cantidad_de_integrantes_familiares=informacion_adicional.cantidad_de_integrantes_familiares(),
            auto=informacion_adicional.auto(),
            cliente=informacion_adicional.cliente(),
            hobby=informacion_adicional.hobby(),
            producto=informacion_adicional.producto(),
            horario_de_contacto=self._horario_de_contacto_de(informacion_adicional),
            dias_de_contacto=informacion_adicional.dias_de_contacto_crudos(),
            clasificacion=informacion_adicional.clasificacion(),
            motivo=informacion_adicional.motivo(),
            submotivo=informacion_adicional.submotivo(),
            valor_movil=informacion_adicional.valor_movil(),
            valor_cuota=self._valor_cuota_de(informacion_adicional),
            color=informacion_adicional.color(),
            precio_de_venta_con_iva=informacion_adicional.precio_de_venta_con_iva(),
            precio_de_lista=informacion_adicional.precio_de_lista(),
            fecha_de_venta=self._fecha_de_venta_de(prospecto),
            duracion=self._duracion_de(prospecto),
            producto_a_comprar=self._producto_de(prospecto),
            estado=self._formato_estado(prospecto.obtener_estado())
        )

    def _optimizar_consulta(self, prospectos):
        return prospectos.select_related(
            'vendedor__user', 'campania', '_informacion_adicional').prefetch_related(
                'comentarios', 'ventas', 'telefono_extra', 'email_extra')

    def _usuario_de(self, prospecto):
        if prospecto.esta_asignado():
            return prospecto.vendedor.username()
        else:
            return ''

    def _valor_cuota_de(self, informacion_adicional):
        valor_cuota = informacion_adicional.valor_cuota()
        if valor_cuota:
            return float("{0:.2f}".format(valor_cuota))
        return valor_cuota

    def _fecha_de_nacimiento_de(self, informacion_adicional):
        fecha_de_nacimiento = informacion_adicional.fecha_de_nacimiento()
        if fecha_de_nacimiento:
            return self.formato_de_fecha_y_hora(fecha_de_nacimiento)
        return None

    def _producto_de(self, prospecto):
        adapter = ProductoAComprarDeDietrichAdapter()
        return adapter.adapt_this(prospecto)

    def _duracion_de(self, prospecto):
        llamados_realizados = prospecto.llamados_realizados()
        if llamados_realizados and llamados_realizados.last().obtener_duracion():
            return self._formato_duracion(llamados_realizados.last().obtener_duracion())
        return ''

    def _horario_de_contacto_de(self, informacion_adicional):
        adapter = HorarioDeContactoDeDietrichAdapter()
        return adapter.adapt_this(informacion_adicional.inicio_de_horario_de_contacto(),
                                  informacion_adicional.fin_de_horario_de_contacto())

    def _telefonos_de(self, prospecto):
        adapter = ListaDietrichRequestAdapter()
        return adapter.adapt_this(prospecto.telefonos_activos())

    def _comentarios_de(self, prospecto):
        adapter = CommentarioDietrichRequestAdapter()
        return [adapter.adapt_this(comment) for comment in prospecto.comentarios.all()]

    def _emails_de(self, prospecto):
        adapter = ListaDietrichRequestAdapter()
        return adapter.adapt_this(prospecto.emails_activos())

    def _formato_duracion(self, duracion):
        return str(datetime.timedelta(seconds=duracion))

    def _formato_estado(self, estado):
        if estado == "V":
            return "Vendido"
        if estado == "P":
            return "En_Proceso"
        if estado == "F":
            return "Finalizado"
        if estado == "N":
            return "Nuevo"
        if estado == "nn":
            return "Nombre_Desconocido"
        else:
            return "Desconocido"

    def _fecha_de_venta_de(self, prospecto):
        venta = prospecto.obtener_venta_activa()
        if venta is not None:
            return self.formato_de_fecha(venta.fecha())
        else:
            return None


class ProductoAComprarDeDietrichAdapter(object):
    def adapt_this(self, prospecto):
        planes = prospecto.obtener_planes()
        if planes:
            tipo_de_plan = planes[0].tipo()
            if tipo_de_plan:
                return tipo_de_plan.nombre()
        return None


class HorarioDeContactoDeDietrichAdapter(object):
    def adapt_this(self, inicio_de_horario, fin_de_horario):
        return str(inicio_de_horario)+';'+str(fin_de_horario)


class ListaDietrichRequestAdapter(object):
    def __init__(self):
        self._separador = ';'
        self._limite = 3

    def adapt_this(self, lista):
        lista_limitada = lista[0:self._limite]
        return self._separador.join(lista_limitada)


class CommentarioDietrichRequestAdapter(object):
    def adapt_this(self, comment):
        return {
            'Fecha': self._formato_de_fecha_y_hora(comment.datetime),
            'Descripcion': comment.comentario
        }

    def _formato_de_fecha_y_hora(self, a_datetime):
        # ISO 8601 Time Representation
        return GeneradorDeAPIDietrichRequest.formato_de_fecha_y_hora(a_datetime)
