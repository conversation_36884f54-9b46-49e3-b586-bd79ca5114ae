from django.conf import settings

from occ import tasks
from occ.models import SolicitudDeAmistadDeFacebook, PerfilDeFacebook
from vendedores.models import ConfiguracionDeServicios


class ServicioDeFacebook(object):
    @classmethod
    def nuevo(cls):
        servicio = cls()
        return servicio

    def esta_habilitado(self, supervisor):
        try:
            configuracion_de_servicios = ConfiguracionDeServicios.objects.get(_vendedor=supervisor)
            return configuracion_de_servicios.facebook_habilitado()
        except ConfiguracionDeServicios.DoesNotExist:
            return False

    def enviar_solicitud_para(self, prospecto):
        responsable = prospecto.obtener_responsable()
        if responsable and self.esta_habilitado(responsable):
            email_del_prospecto = prospecto.obtener_email_activo()
            marca = prospecto.obtener_marca().codigo()
            prospecto_pk = prospecto.pk
            solicitudes = SolicitudDeAmistadDeFacebook.objects.filter(prospecto_id=prospecto_pk)
            if email_del_prospecto and self._es_una_marca_valida(marca) and not solicitudes.exists():
                transaction_id = self._generar_transaction_id_para(prospecto, marca)
                tasks.enviar_solicitud_para_prospecto.delay(
                    prospecto_pk, email_del_prospecto, marca, transaction_id)

    def _es_una_marca_valida(self, marca):
        return marca and marca.lower() not in settings.FACEMACHINE_MARCAS_EXCLUIDAS

    def enviar_solicitudes_para_todos_los_prospectos(self, prospectos, responsable):
        if responsable and self.esta_habilitado(responsable):
            ids_de_prospectos = list(prospectos.values_list('id', flat=True))
            tasks.enviar_solicitud_para_todos_los_prospectos.delay(ids_de_prospectos)

    def enviar_pedido_de_actualizacion_de_solicitudes_y_contactos_entre(self, fecha_desde, fecha_hasta):
        tasks.actualizar_solicitudes_de_amistad_enviadas.delay(
            fecha_desde=fecha_desde, fecha_hasta=fecha_hasta)

    def actualizar_solicitudes(self, informacion_de_solicitudes):
        for registro in informacion_de_solicitudes:
            transaction_id = self._obtener_transaction_id_desde(registro)
            registro_id = registro['Id']
            estado = self._obtener_estado_desde(registro)
            try:
                solicitud = SolicitudDeAmistadDeFacebook.con_transaction_id(transaction_id=transaction_id)
            except SolicitudDeAmistadDeFacebook.DoesNotExist:
                pass
            else:
                solicitud.actualizar(registro_id, estado)

    def generar_contactos_de_facebook_desde(self, registros):
        info_de_perfiles = self._obtener_informacion_de_perfiles_desde(registros)
        dict_de_perfiles = info_de_perfiles['perfiles']
        solicitudes = self._obtener_solicitudes_desde(info_de_perfiles)
        urls = set(dict_de_perfiles.keys())
        urls_ya_existentes = PerfilDeFacebook.objects.filter(url__in=urls).values_list('url', flat=True)
        urls_no_existentes = urls.difference(urls_ya_existentes)
        self._crear_perfiles_no_existentes(urls_no_existentes, dict_de_perfiles, solicitudes)
        self._actualizar_perfiles_existentes(urls_ya_existentes, dict_de_perfiles)

    def _crear_perfiles_no_existentes(self, urls, dict_de_perfiles, solicitudes):
        perfiles = []
        for url in urls:
            info_de_perfil = dict_de_perfiles[url]
            id_de_registro = str(info_de_perfil['id_de_registro'])
            prospecto_id = solicitudes.get(id_de_registro, None)
            if prospecto_id is not None:
                perfil = self._perfil_desde(url, info_de_perfil, prospecto_id)
                perfiles.append(perfil)
        PerfilDeFacebook.objects.bulk_create(perfiles)

    def _actualizar_perfiles_existentes(self, urls, dict_de_perfiles):
        if urls:
            perfiles = PerfilDeFacebook.objects.filter(url__in=urls)
            for perfil in perfiles.all():
                info_de_perfil = dict_de_perfiles[perfil.url]
                perfil.actualizar(foto=info_de_perfil['foto'], nombre=info_de_perfil['nombre'])

    def _obtener_informacion_de_perfiles_desde(self, registros):
        ids_de_registros = []
        perfiles = {}
        for record in registros:
            id_de_registro = record["Id"]
            url = record["ProfileLink"]
            info_perfil = {'id_de_registro': id_de_registro, 'foto': record["ImageLink"], 'nombre': record["Name"]}
            perfiles.update({url: info_perfil})
            ids_de_registros.append(id_de_registro)
        return {'ids_de_registros': ids_de_registros, 'perfiles': perfiles}

    def _perfil_desde(self, url, informacion_de_perfil, prospecto_id):
        perfil = PerfilDeFacebook(
            url=url, foto=informacion_de_perfil['foto'],
            nombre=informacion_de_perfil['nombre'],
            prospecto_id=prospecto_id)
        return perfil

    def _generar_transaction_id_para(self, prospecto, marca):
        return '%s%s' % (prospecto.pk, marca)

    def _obtener_transaction_id_desde(self, registro):
        return registro['Message']['QueueMessage']['TransactionId']

    def _obtener_estado_desde(self, registro):
        if registro['Success']:
            return SolicitudDeAmistadDeFacebook.EXITOSA
        else:
            return SolicitudDeAmistadDeFacebook.FALLIDA

    def _obtener_solicitudes_desde(self, info_de_perfiles):
        ids_de_registro = info_de_perfiles['ids_de_registros']
        solicitudes = SolicitudDeAmistadDeFacebook.objects.filter(registro_id__in=ids_de_registro)
        return {valor['registro_id']: valor['prospecto_id'] for valor in
                solicitudes.values('registro_id', 'prospecto_id')}
