# coding=utf-8


class ErrorDePedido(Exception):
    @classmethod
    def nombre_de_campania_inexistente(cls, nombre):
        return cls('No existe una campaña de nombre %s' % nombre)

    @classmethod
    def pedido_invalido(cls, motivo):
        return cls('Pedido invalido - %s' % motivo)

    @classmethod
    def no_hay_vendedores_que_cumplan_los_requisitos(cls):
        return cls('No hay vendedores que cumplan los requisitos.')


class CantidadDeReintentosSuperadaError(Exception):
    @classmethod
    def nuevo(cls):
        return cls('Se ha superado los intentos de participaciones')


class ErrorDeCompulsa(Exception):
    @classmethod
    def operacion_invalida_para_compulsa_finalizada(cls):
        return cls('Operacion invalida para compulsa finalizada')


class ErrorDeServicioDeChat(Exception):
    @classmethod
    def nuevo(cls, mensaje):
        return cls(mensaje)

    @classmethod
    def proveedor_no_responde(cls):
        return cls.nuevo('El proveedor de chat no responde')

    @classmethod
    def compulsa_no_esta_vigente(cls):
        return cls.nuevo('La compulsa no esta vigente')

    @classmethod
    def vendedor_no_participa(cls):
        return cls.nuevo('El vendedor no participa en la compulsa')
