from django.db import models


class PublicationMessageQueryset(models.QuerySet):
    def create_or_get(self, content):
        try:
            return self.get()
        except self.model.DoesNotExist:
            return self.model.create(content=content)


class ConversationQueryset(models.QuerySet):
    def create_or_get(self, user_contact_data):
        try:
            return self.get(_user_contact_data=user_contact_data)
        except self.model.DoesNotExist:
            return self.model.create(user_contact_data=user_contact_data)


class UserContactDataQueryset(models.QuerySet):
    def create_or_get(self, phone=None, email=None, name=None):
        try:
            return self.get()
        except self.model.DoesNotExist:
            return self.model.create(phone=phone, email=email, name=name)