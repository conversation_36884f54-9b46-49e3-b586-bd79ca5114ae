{% extends "occ_layout.html" %}
{% load static from staticfiles %}

{% block css %}
    {{ block.super }}
    <link href="{%  static 'css/paginado.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />
{% endblock %}

{% block js %}
    {{ block.super }}
    <script type="text/javascript" src="{%  static 'js/paginator.js' %}"></script>
{% endblock %}

{% block occ_content %}

    <nav class="solapas">
        {% if user.vendedor.configuracion_de_servicios.puede_modificar_servicio_de_propuestas %}
        <a href="{% url 'proposals-index' %}" id="proposals-index" title="Propuestas">Propuestas</a>
        {% endif %}
        {% if user.vendedor.configuracion_de_servicios.puede_modificar_servicio_de_chat %}
            <a href="{% url 'occ-mis-chats' %}">Mis Chats</a>
        {% endif %}
        <a href="{% url 'occ-campanias' %}" class="activo">Campañas</a>
        <a href="{% url 'occ-configuracion' %}">Configuración</a>
    </nav>
    <form method="POST" action="." class="formMedio">
        <label for="buscar">Medio</label>
        <select class="medio" name="accion" id="accion">
            <option value="sms">SMS</option>
        </select>
    </form>
    <div id="listaCampanas">
        <div id="botonesTop">
            {% if user.vendedor.configuracion_de_servicios.tiene_sms_habilitado %}
                <a href="{% url 'occ-campania-nueva' %}">Crear Nueva campaña</a>
            {% endif %}
        </div>
        <div class="contenedor-lista">
            <div class="titulo-lista">
                <h3>Resumen de Campañas</h3>
            </div>
            <div class="lista">
                <table>
                    <tr class="titulo">
                        <td>Fecha
                            <button></button>
                        </td>
                        <td>Vendedor
                            <button></button>
                        </td>
                        <td>Prospectos
                            <button></button>
                        </td>
                        <td>Envios
                            <button></button>
                        </td>
                        <td>Medio
                            <button></button>
                        </td>
                        <td>Detalle
                            <button></button>
                        </td>
                    </tr>
                    {% for campania in campanias %}
                        <tr>
                            <td>{{ campania.fecha_de_creacion|date:"d/m/Y" }}</td>
                            <td>{{ campania.vendedor }}</td>
                            <td>{{ campania.prospectos.count }}</td>
                            <td>{{ campania.cantidad_de_envios }}</td>
                            <td>{{ campania.medio }}</td>
                            <td><a href="{% url 'occ-campania-detalle' campania.pk %}">Ver detalle</a></td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
        </div>
    </div>

    {% include "paginado.html" with pagina=campanias %}

{% endblock %}
