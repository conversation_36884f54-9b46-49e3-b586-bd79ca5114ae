{% extends "occ_configuracion_layout.html" %}

{% block js %}
    {{ block.super }}
    <script type="text/javascript" src="{{ STATIC_URL }}js/csrf_token.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/system_unavailable.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/occ-configuracion.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            var configuration = OCCConfiguration();
            {% if tiene_permiso_para_modificar_servicio_de_llamados %}
            configuration.addButton(
                'llamadas', 'spin_llamadas',
                {{staff_a_cargo_puede_utilizar_servicio_de_llamados|yesno:"true,false"}}, "{% url 'occ-servicio-de-llamadas' %}");
            {% endif %}
            {% if tiene_permiso_para_crear_propuestas %}
            configuration.addButton(
                'propuestas', 'spin_propuestas',
                {{supervisores_pueden_crear_propuestas|yesno:"true,false"}}, "{% url 'occ-propuestas' %}");
            {% endif %}
        });
    </script>
{% endblock %}

{% block solapas %}
    {% if tiene_permiso_para_crear_propuestas%}
    <a href="{% url 'proposals-index' %}" id="proposals-index" title="Propuestas">Propuestas</a>
    {% endif %}
    <a href="{% url 'occ-configuracion' %}" class="activo">Configuración</a>
{% endblock %}

{% block configuracion_servicios  %}
    {% if tiene_permiso_para_modificar_servicio_de_llamados %}
    <li id="llamadas" class="llamadas">
        <h1>Permite a tus supervisores gestionar los permisos de llamadas desde la aplicación!</h1>
        <button><label><span></span> Llamados por IP</label></button>
        <input type="hidden" value="0"/>
        <div id="spin_llamadas" class='spin-holder'></div>
    </li>
    {% endif %}

    {% if tiene_permiso_para_crear_propuestas%}
    <li id="propuestas" class="propuestas">
        <h1>Habilita la creación de propuestas a los supervisores.</h1>
        <button><label><span></span></label></button>
        <input type="hidden" value="0"/>
        <div id="spin_propuestas" class='spin-holder'></div>
    </li>
    {% endif %}
{% endblock %}