# coding=utf-8

import logging
import unicodedata

from django.conf import settings
from django.utils import timezone

from lib.timing import timing
from occ.errores import EmisorDeCampaniasDeSMSError
from occ.models import EnvioDeMensaje, CampaniaDeComunicacion
from occ.occ_notificaciones.mensajes_wrappers import MensajeAProspectoWrapper
from occ.sms_estrategias_de_envio import GeneradorDeLotes, DeliverySMS
from lib.smscover import SMSMessage, SMSTemplateText

logger = logging.getLogger(__name__)


class PedidoDeEnvios(object):
    TELEFONO_INVALIDO = 0
    TELEFONO_INACTIVO = 1
    TELEFONO_NO_MOVIL = 2
    TELEFONO_ENVIADO_RECIENTEMENTE = 3

    def __init__(self):
        self.cant_telefonos_no_enviados = 0
        self._motivos_de_fallo = {self.TELEFONO_INVALIDO: 0, self.TELEFONO_INACTIVO: 0,
                                  self.TELEFONO_NO_MOVIL: 0, self.TELEFONO_ENVIADO_RECIENTEMENTE: 0}
        self._mensajes_a_enviar = []
        self._enviados_recientemente = self._telefonos_enviados_recientementes_en_campanias()

    def _telefonos_enviados_recientementes_en_campanias(self):
        fecha_limite = timezone.now() - timezone.timedelta(days=settings.DIAS_PARA_NO_ENVIAR_MENSAJES_SMS)
        envios = EnvioDeMensaje.objects.creados_posterior_a(fecha_limite)
        telefonos = envios.proveniente_de_campanias_de_comunicacion().telefonos()
        return telefonos

    def agregar_reporte_de_falla(self, notificacion, telefono):
        self._motivos_de_fallo[notificacion] += 1

    def _reporte_para_notificacion(self, notificacion, mensaje):
        cantidad = self._motivos_de_fallo[notificacion]
        if cantidad > 0:
            return mensaje % cantidad
        else:
            return ''

    def generar_reporte(self):
        reporte = '%s mensajes enviados correctamente' % self.cantidad_de_mensajes_a_enviar()

        reporte += self._reporte_para_notificacion(self.TELEFONO_ENVIADO_RECIENTEMENTE,
                                                   ', %s mensajes rechazados por haber sido enviados en campanias anteriores')

        reporte += self._reporte_para_notificacion(self.TELEFONO_INVALIDO,
                                                   ', %s telefonos con formato incorrecto')
        reporte += self._reporte_para_notificacion(self.TELEFONO_INACTIVO, ', %s telefonos no activos')
        reporte += self._reporte_para_notificacion(self.TELEFONO_NO_MOVIL, ', %s no correspondian a celulares')

        return reporte

    # Parametrisado de este modo porque no tenemos reificado el concepto de telefono
    def agregar_pedido_de_envio(self, telefono, telefono_activo, es_telefono_movil, template, prospecto):

        if not self.es_telefono_valido(telefono):
            self.agregar_reporte_de_falla(self.TELEFONO_INVALIDO, telefono)
            return
        if not telefono_activo:
            self.agregar_reporte_de_falla(self.TELEFONO_INACTIVO, telefono)
            return
        if es_telefono_movil is False:  # Si es null perinitos enviar
            self.agregar_reporte_de_falla(self.TELEFONO_NO_MOVIL, telefono)
            return

        if telefono in self._enviados_recientemente:
            self.agregar_reporte_de_falla(self.TELEFONO_ENVIADO_RECIENTEMENTE, telefono)
        else:
            mensaje = self._crear_mensaje(telefono, template, prospecto)
            self._mensajes_a_enviar.append(mensaje)

    # por ahora paso "hardcodeado" la variable ('nombre'), no llegue a implementar #get_var_names del objeto template
    def _crear_mensaje(self, telefono, template, prospecto):
        # Tira error porque solo permite un ID de longitud 30
        id_mensaje = '%s%d' % (telefono, prospecto.pk)
        message = SMSMessage.new_from_template(id_mensaje, template,
                                               {'nombre': self._primer_nombre_del_prospecto(prospecto)}, telefono)
        return MensajeAProspectoWrapper.nuevo_para(prospecto, message)

    def _primer_nombre_del_prospecto(self, prospecto):
        nombres = prospecto.nombre.split()
        if len(nombres) > 0:
            return self._reemplazar_acentos(nombres[0])
        else:
            return ''

    def cantidad_de_mensajes_a_enviar(self):
        return len(self._mensajes_a_enviar)

    def tiene_mensajes_a_enviar(self):
        return self.cantidad_de_mensajes_a_enviar() > 0

    def mensajes_a_enviar(self):
        return self._mensajes_a_enviar

    def es_telefono_valido(self, telefono):
        telefono_sin_espacios = telefono.replace(" ", "")
        return len(telefono_sin_espacios) >= 7 and telefono_sin_espacios.isdigit()

    def _reemplazar_acentos(self, string_con_acentos):
        string_sin_acentos = ''.join(
            (c for c in unicodedata.normalize('NFD', string_con_acentos) if unicodedata.category(c) != 'Mn'))
        return string_sin_acentos


class ResultadoDeEnvios(object):

    def __init__(self, errores_de_validacion=None, fue_envio_exitoso=False, detalle=''):
        super(ResultadoDeEnvios, self).__init__()
        self._errores_de_validacion = errores_de_validacion or []
        self._fue_envio_exitoso = fue_envio_exitoso
        self._detalle = detalle

    @classmethod
    def caracteres_invalidos(cls):
        return cls.nuevo_con_error_de_validacion("Mensaje con caracteres invalidos")

    @classmethod
    def credito_insuficiente(cls):
        return cls.nuevo_con_error_de_validacion("Credito insuficiente")

    @classmethod
    def nuevo_con_error_de_validacion(cls, mensaje_de_error):
        return cls.nuevo_con_errores_de_validacion([mensaje_de_error])

    @classmethod
    def nuevo_con_errores_de_validacion(cls, errores):
        return cls(errores_de_validacion=errores)

    @classmethod
    def nuevo_con_resultado_de_envio(cls, fue_exitoso, detalle):
        return cls(fue_envio_exitoso=fue_exitoso, detalle=detalle, errores_de_validacion=[])

    def fue_exitoso(self):
        return not self.tiene_errores_de_validacion() and self._fue_envio_exitoso

    def detalle(self):
        return self._detalle or self._detalles_de_errores_de_validacion()

    def errores_de_validacion(self):
        return self._errores_de_validacion

    def tiene_errores_de_validacion(self):
        return len(self._errores_de_validacion) > 0

    def _detalles_de_errores_de_validacion(self):
        return ', '.join(self._errores_de_validacion)


class EmisorDeCampaniasDeSMS(object):
    def __init__(self):
        super(EmisorDeCampaniasDeSMS, self).__init__()
        self._servicio = DeliverySMS.default()
        self._nombre = DeliverySMS.CAMPANIA

    def crear_y_enviar_campania(self, medio, prospectos, template_de_mensaje, vendedor):
        pedido_de_envios = self._generar_pedidos_envios_desde_lista_de_prospectos(prospectos, template_de_mensaje)
        errores = self.validar_envios_de_prospectos(prospectos, vendedor, template_de_mensaje, pedido_de_envios)
        if len(errores) > 0:
            return ResultadoDeEnvios.nuevo_con_errores_de_validacion(errores)

        campania = CampaniaDeComunicacion.nueva_campania_con(
            medio=medio, vendedor=vendedor, mensaje=template_de_mensaje, prospectos=prospectos)
        resultado = self.enviar_campania(campania, template_de_mensaje, vendedor, pedido_de_envios)
        return resultado

    # ENVIO
    # template_de_mensaje debe tener la pinta '{nombre}, bla bla bla bla....'
    def enviar_campania(self, campania, template_de_mensaje, vendedor, pedido_de_envios=None):
        try:
            resultado = self._enviar_campania(campania, template_de_mensaje, vendedor, pedido_de_envios)
        except EmisorDeCampaniasDeSMSError as exc:
            resultado = ResultadoDeEnvios.nuevo_con_resultado_de_envio(fue_exitoso=False, detalle=str(exc))
        campania.resultado_de_envio(resultado, vendedor)
        return resultado

    def _enviar_campania(self, campania, template_de_mensaje, vendedor, pedido_de_envios=None):
        if not self.validar_caracteres_de_mensajes(template_de_mensaje):
            return ResultadoDeEnvios.caracteres_invalidos()

        pedido_de_envios = pedido_de_envios or self._generar_pedidos_de_envios_para(campania, template_de_mensaje)
        if not pedido_de_envios.tiene_mensajes_a_enviar():
            return ResultadoDeEnvios.nuevo_con_error_de_validacion(pedido_de_envios.generar_reporte())

        if not self._tiene_credito_para_enviar(vendedor, cantidad=pedido_de_envios.cantidad_de_mensajes_a_enviar()):
            return ResultadoDeEnvios.credito_insuficiente()

        fue_envio_exitoso = self._enviar_pedidos(campania, pedido_de_envios)
        return ResultadoDeEnvios.nuevo_con_resultado_de_envio(fue_envio_exitoso, pedido_de_envios.generar_reporte())

    def _enviar_pedidos(self, campania, pedido_de_envios):
        fue_envio_exitoso = self._servicio.envia(
            nombre_de_cliente=self._nombre,
            mensajes=pedido_de_envios.mensajes_a_enviar(),
            lot_generator=self._generador_de_lotes_para(campania))
        return fue_envio_exitoso

    def _generador_de_lotes_para(self, campania):
        generador = GeneradorDeLotes.nuevo(campania)
        return generador

    def _generar_pedidos_de_envios_para(self, campania, template):
        return self._generar_pedidos_envios_desde_lista_de_prospectos(campania.prospectos.all(), template)

    @timing
    def _generar_pedidos_envios_desde_lista_de_prospectos(self, prospectos, template_de_mensaje):
        pedido_de_envios = PedidoDeEnvios()
        for prospecto in prospectos.prefetch_related('telefono_extra').all():
            mensaje = template_de_mensaje
            if prospecto.tiene_nombre():
                mensaje = "{nombre}, " + template_de_mensaje
            template = SMSTemplateText(mensaje)
            self._agregar_pedido_de_envios_de_prospecto(prospecto, template, pedido_de_envios)
        return pedido_de_envios

    def _agregar_pedido_de_envios_de_prospecto(self, prospecto, template, pedido_de_envios):
        for telefono_extra in prospecto.telefono_extra.all():
            pedido_de_envios.agregar_pedido_de_envio(telefono_extra.telefono, telefono_extra.activo,
                                                     telefono_extra.es_telefono_movil, template, prospecto)

        pedido_de_envios.agregar_pedido_de_envio(prospecto.telefono, prospecto.telefono_activo,
                                                 prospecto.es_telefono_movil, template, prospecto)

    @classmethod
    def tiene_caracteres_validos(cls, mensaje):
        for caracter in mensaje:
            if caracter not in settings.CARACTERES_VALIDOS_DE_SMS:
                return False
        return True

    def validar_caracteres_de_mensajes(self, template_de_mensaje):
        mensaje_de_template = template_de_mensaje[len('{nombre},'):]
        return self.tiene_caracteres_validos(mensaje_de_template)

    # VALIDACION
    def validar_envios_de_prospectos(self, prospectos, vendedor, template, pedido_de_envios=None):
        pedido_de_envios = pedido_de_envios or self._generar_pedidos_envios_desde_lista_de_prospectos(
            prospectos, template)
        errors = self._validar_credito_para_enviar_prospectos(pedido_de_envios, vendedor)
        errors.extend(self._validar_cantidad_de_envios(pedido_de_envios))
        return errors

    def _validar_credito_para_enviar_prospectos(self, pedido_de_envios, vendedor):
        errors = []
        cantidad = pedido_de_envios.cantidad_de_mensajes_a_enviar()
        if not vendedor.tiene_credito_para_enviar_sms(cantidad):
            errors.append('No tiene crédito suficiente para enviar esta cantidad de mensajes')
        return errors

    def _validar_cantidad_de_envios(self, pedido_de_envios):
        errors = []
        if not pedido_de_envios.tiene_mensajes_a_enviar():
            mensaje_error = pedido_de_envios.generar_reporte()
            errors.append(mensaje_error)
        return errors

    def _tiene_credito_para_enviar(self, vendedor, cantidad):
        return hasattr(vendedor, 'credito') and vendedor.credito.tiene_credito_para_enviar(cantidad)

