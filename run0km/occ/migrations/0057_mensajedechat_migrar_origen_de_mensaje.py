# -*- coding: utf-8 -*-
# Generated by Django 1.11.14 on 2020-06-17 03:10


from django.db import migrations, models
import django.db.models.deletion


def migratar_origen_de_chat(apps, schema_editor):
    mensaje_de_chat_class = apps.get_model("occ", "MensajeDeChat")
    origen_de_mensaje_class = apps.get_model("occ", "OrigenDeMensaje")
    origen_propuesta_class = apps.get_model("occ", "OrigenPropuesta")

    origen, creado = origen_de_mensaje_class.objects.get_or_create(_codigo='D')
    mensajes = mensaje_de_chat_class.objects.filter(_origen_prospuesta__isnull=True)
    mensajes.update(_origen=origen)

    for propuesta_id in origen_propuesta_class.objects.values_list("_propuesta", flat=True).distinct():
        origen, creado = origen_de_mensaje_class.objects.get_or_create(_codigo='P', _propuesta_id=propuesta_id)
        mensaje_de_chat_class.objects.filter(_origen_prospuesta___propuesta_id=propuesta_id).update(_origen=origen)


def undo_migratar_origen_de_chat(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('occ', '0056_enviodemensaje_migrar_origen_de_mensaje'),
    ]

    operations = [
        migrations.RenameField(
            model_name='mensajedechat',
            old_name='_origen',
            new_name='_origen_prospuesta',
        ),
        migrations.AddField(
            model_name='mensajedechat',
            name='_origen',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='_mensajes_chat', to='occ.OrigenDeMensaje'),
            preserve_default=False,
        ),
        migrations.RunPython(migratar_origen_de_chat, undo_migratar_origen_de_chat),
        migrations.RemoveField(
            model_name='mensajedechat',
            name='_origen_prospuesta',
        ),
    ]
