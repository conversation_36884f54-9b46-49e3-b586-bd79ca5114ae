# -*- coding: utf-8 -*-
# Generated by Django 1.11.14 on 2020-06-14 23:27


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('propuestas', '0014_auto_20180806_1443'),
        ('occ', '0054_auto_20190321_1533'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrigenDeMensaje',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_codigo', models.CharField(choices=[(b'D', b'Desconocido'), (b'P', b'Propuesta')], default=b'D', max_length=1)),
                ('_propuesta', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='_origen_de_mensajes', to='propuestas.Propuesta')),
            ],
        ),
        migrations.CreateModel(
            name='OrigenDesconocido',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
            },
            bases=('occ.origendemensaje',),
        ),
        migrations.CreateModel(
            name='OrigenIncontactable',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
            },
            bases=('occ.origendemensaje',),
        ),
        migrations.CreateModel(
            name='OrigenPropuestaNuevo',
            fields=[
            ],
            options={
                'proxy': True,
                'indexes': [],
            },
            bases=('occ.origendemensaje',),
        ),
    ]
