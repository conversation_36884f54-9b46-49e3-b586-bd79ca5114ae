# -*- coding: utf-8 -*-
# Generated by Django 1.11.14 on 2019-01-08 19:26


import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('prospectos', '0119_actualizacion_de_vendedores_en_asignacion'),
        ('vendedores', '0052_configuraciondeservicios__llamadas_habilitadas'),
        ('occ', '0046_auto_20190102_1313'),
    ]

    operations = [
        migrations.CreateModel(
            name='IntentoDeLlamado',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_telefono', models.CharField(max_length=64)),
                ('_fecha', models.DateTimeField(auto_now_add=True)),
                ('_prospecto',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='intentos_de_llamado',
                                   to='prospectos.Prospecto')),
                ('_vendedor',
                 models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='intentos_de_llamado',
                                   to='vendedores.Vendedor')),
            ],
        ),
        migrations.CreateModel(
            name='LlamadaRealizadaDeAnura',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_fecha_comienzo', models.DateTimeField()),
                ('_id_externo', models.CharField(max_length=255)),
                ('_duracion', models.PositiveIntegerField(default=0)),
                ('_audio_url', models.URLField()),
                ('_intento_de_llamado',
                 models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='llamada_realizada',
                                      to='occ.IntentoDeLlamado')),
            ],
        ),
    ]
