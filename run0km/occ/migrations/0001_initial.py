# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0030_auto_20150814_1708'),
        ('vendedores', '0009_vendedor_whatsapp_habilitado'),
    ]

    operations = [
        migrations.CreateModel(
            name='CampaniaDeComunicacion',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nombre', models.CharField(max_length=255)),
                ('mensaje', models.CharField(max_length=145)),
                ('fecha_de_creacion', models.DateTimeField(auto_now_add=True)),
                ('envio_mensajes_correcto', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'campa\xf1a de comunicaci\xf3n',
                'verbose_name_plural': 'campa\xf1as de comunicaci\xf3n',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='CreditoDeSMS',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('credito_base', models.PositiveIntegerField(default=0)),
                ('credito_extra', models.PositiveIntegerField(default=0)),
                ('vendedor', models.OneToOneField(related_name='credito', null=True, to='vendedores.Vendedor')),
            ],
            options={
                'verbose_name': 'Credito de SMS',
                'verbose_name_plural': 'Creditos de SMS',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='EnvioDeMensaje',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('id_mensaje', models.CharField(max_length=255)),
                ('mensaje', models.CharField(default=b'', max_length=255, blank=True)),
                ('telefono', models.CharField(default=b'', max_length=64, blank=True)),
                ('fecha', models.DateTimeField(null=True, blank=True)),
                ('estado', models.CharField(default=b'P', max_length=1, choices=[(b'P', b'En proceso'), (b'R', b'Realizado'), (b'F', b'Envio Fallido')])),
                ('campania', models.ForeignKey(related_name='envios', to='occ.CampaniaDeComunicacion')),
                ('prospecto', models.ForeignKey(related_name='mensajes_de_campanias', to='prospectos.Prospecto')),
            ],
            options={
                'verbose_name': 'Envio de Mensaje',
                'verbose_name_plural': 'Envio de Mensajes',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='LogDeErrorDeSMS',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('tipo', models.TextField(default=b'')),
                ('datos_del_error', models.TextField(default=b'')),
                ('campania', models.ForeignKey(to='occ.CampaniaDeComunicacion')),
            ],
            options={
                'verbose_name': 'Log de error de SMS',
                'verbose_name_plural': 'Logs de errores de SMS',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Medio',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('nombre', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name': 'medio',
                'verbose_name_plural': 'medios',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='RespuestaDeMensaje',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('fecha', models.DateTimeField(null=True, blank=True)),
                ('mensaje', models.CharField(max_length=255)),
                ('envio', models.OneToOneField(related_name='respuesta', to='occ.EnvioDeMensaje')),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='campaniadecomunicacion',
            name='medio',
            field=models.ForeignKey(to='occ.Medio'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='campaniadecomunicacion',
            name='prospectos',
            field=models.ManyToManyField(to='prospectos.Prospecto'),
            preserve_default=True,
        ),
    ]
