# -*- coding: utf-8 -*-
# Generated by Django 1.11.14 on 2023-07-12 21:19


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('occ', '0060_auto_20200617_0032'),
    ]

    operations = [
        migrations.RenameField(
            model_name='logdeintegracion',
            old_name='_configuracion',
            new_name='configuracion',
        ),
        migrations.RenameField(
            model_name='logdeintegracion',
            old_name='_descripcion',
            new_name='descripcion',
        ),
        migrations.RenameField(
            model_name='logdeintegracion',
            old_name='_fecha',
            new_name='fecha',
        ),
        migrations.RenameField(
            model_name='logdeintegracion',
            old_name='_fue_exitoso',
            new_name='fue_exitoso',
        ),
        migrations.AddField(
            model_name='logdeintegracion',
            name='prospectos_id',
            field=models.TextField(blank=True, default=None, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='configuraciondeintegracion',
            name='_antiguedad',
            field=models.SmallIntegerField(choices=[(-1, b'Online (desde ayer)'), (0, b'Primero del mes'), (1, b'1 mes'), (2, b'2 meses'), (3, b'3 meses')], default=1),
        ),
    ]
