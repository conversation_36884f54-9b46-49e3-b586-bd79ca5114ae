class RequestAdapter(object):
    def __init__(self, request):
        self.request = request

    def get(self, key, default=None):
        raise NotImplementedError('subclass responsibility!')


class RequestAdapterToReadFromPOST(RequestAdapter):
    def get(self, key, default=None):
        return self.request.POST.get(key, default)


class RequestAdapterToReadFromData(RequestAdapter):
    def get(self, key, default=None):
        return self.request.data.get(key, default)
