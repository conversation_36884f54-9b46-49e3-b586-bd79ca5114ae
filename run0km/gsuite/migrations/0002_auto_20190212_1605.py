# -*- coding: utf-8 -*-
# Generated by Django 1.11.14 on 2019-02-12 19:05


import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('vendedores', '0052_configuraciondeservicios__llamadas_habilitadas'),
        ('gsuite', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GSuiteDriveSpreadsheetForDeliveryRunUser',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_spreadsheet_key', models.CharField(max_length=255)),
                ('_fecha_de_creacion', models.DateTimeField(auto_now_add=True)),
                ('_supervisor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE,
                                                     related_name='_gsuite_drive_spreadsheet',
                                                     to='vendedores.Vendedor')),
            ],
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name='gsuiteusersession',
            name='_last_update',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
