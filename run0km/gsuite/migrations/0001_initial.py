# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-04-11 18:01


from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import oauth2client.contrib.django_util.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GSuiteUserConfiguration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_credentials', oauth2client.contrib.django_util.models.CredentialsField(null=True)),
                ('_email', models.EmailField(blank=True, default=None, max_length=254, null=True)),
                ('_user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='_gsuite_credentials', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='GSuiteUserSession',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_history_id', models.CharField(blank=True, max_length=70, null=True)),
                ('_last_update', models.DateTimeField(auto_now_add=True)),
                ('_user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='_gsuite_session', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
