import mock
from oauth2client.client import OAuth2Credentials

from gsuite.gmail.client_gmail import ApplicationGmail
from gsuite.models.configuration import GSuiteUserConfiguration, GSuiteUserSession
from testing.base import BaseFixturedTest


class GMailMessagesMock(object):

    @classmethod
    def message_list(cls):
        return {
            'nextPageToken': '16436750327852620052',
            'resultSizeEstimate': 103,
            'messages': [
                {'id': '162abe15b3075673', 'threadId': '162abe15b3075673'},
                {'id': '162abbb73fa4a1c2', 'threadId': '162ab99680bd6a5f'},
                {'id': '16295fd36a7fb0f8', 'threadId': '16295fd36a7fb0f8'}]
        }

    @classmethod
    def history(cls):
        return {'historyId': '1561945', 'history': [
            {'messages': [{'id': '162b5d5e8eca39d5', 'threadId': '162b5d5e8eca39d5'}], 'id': '1561880'},
            {'messages': [{'id': '162b5d5e8eca39d5', 'threadId': '162b5d5e8eca39d5'}], 'id': '1561889'},
            {'messages': [{'id': '162b5d5e8eca39d5', 'threadId': '162b5d5e8eca39d5'}], 'id': '1561890'}, {
                'labelsRemoved': [{'labelIds': ['UNREAD'],
                                    'message': {'labelIds': ['IMPORTANT', 'CATEGORY_UPDATES', 'INBOX'],
                                                 'id': '162b5d5e8eca39d5', 'threadId': '162b5d5e8eca39d5'}}],
                'messages': [{'id': '162b5d5e8eca39d5', 'threadId': '162b5d5e8eca39d5'}], 'id': '1561891'},
            {'messages': [{'id': '162b5d5e8eca39d5', 'threadId': '162b5d5e8eca39d5'}], 'id': '1561942'}]}

    @classmethod
    def batch_message_list(cls):
        return {
            'nextPageToken': '16436750327852620052',
            'resultSizeEstimate': 103,
            'messages': [
                {'id': '162abe15b3075673', 'threadId': '162abe15b3075673'},
                {'id': '162abbb73fa4a1c2', 'threadId': '162ab99680bd6a5f'},
                {'id': '16295fd36a7fb0f8', 'threadId': '16295fd36a7fb0f8'}]
        }


class GmailMessagesTest(BaseFixturedTest):

    def setUp(self):
        super(GmailMessagesTest, self).setUp()
        self.application = ApplicationGmail()
        self.supervisor = self.fixture['sup_1']

    def _create_configuration_for(self, vendedor):
        configuration = GSuiteUserConfiguration.new_for(user=vendedor.user, email='<EMAIL>')
        configuration._credentials = OAuth2Credentials(
            access_token='1', client_id='1', client_secret='1', refresh_token='1', token_expiry='1',
            token_uri='1', user_agent='1')
        configuration.save()
        return configuration

    def _remove_gsuite_session_for(self, supervisor):
        GSuiteUserSession.objects.filter(_user=supervisor.user).delete()

    @mock.patch('googleapiclient.http.BatchHttpRequest.execute')
    @mock.patch('googleapiclient.http.HttpRequest.execute')
    def test_get_initial_sincronization(self, gmail_api, batch_http_request_mock):
        pass
        # TODO: pendiente ver como mockear el batch, ya que evalua el callback, ver como es la estructura del response
        # message_list = GMailMessagesMock.batch_message_list()
        # gmail_api.return_value = GMailMessagesMock.message_list()
        # batch_http_request_mock.return_value = message_list
        # self._create_configuration_for(self.supervisor)
        # self._remove_gsuite_session_for(self.supervisor)
        # client = self.application.client_for(self.supervisor.user)
        # messages = client.get_new_messages()
        # self._assert_message(messages, message_list)
        # self._assert_initial_session(client)

    def _assert_message(self, messages, expected_messages):
        for message_info in expected_messages:
            self._assert_message_in(message_info, messages)

    def _assert_initial_session(self, client):
        session = client.session()
        # self.assertEqual(session.history_id())

    def _assert_message_in(self, message_info, messages):
        message_id = message_info['id']
        messages = [each for each in messages if each.id()==message_id]
        self.assertEqual(len(messages), 1)
        message = messages[0]

        self.assertEqual(message.history_id(), message_info['historyId'])
