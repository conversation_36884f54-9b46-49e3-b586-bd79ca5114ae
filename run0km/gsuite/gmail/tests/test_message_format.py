import base64
from unittest import TestCase

from gsuite.gmail.errors import InvalidFormatException
from gsuite.gmail.formats import GMail<PERSON><PERSON><PERSON><PERSON><PERSON>atter, MessageFormatParser

#TODO
class MessageFormatParserTest(TestCase):
    def _full_format_message_without_parts_for(self, subject, sender, receiver, datetime_string, body):
        base64_body = self._convertir_a_base64(body)
        return {
            'internalDate': '1523553157000',
            'historyId': '1627',
            'payload': {
                'mimeType': 'text/html',
                'headers': [
                    {'name': 'Received',
                     'value': 'from 212114871592 named unknown by gmailapi.google.com with HTTPREST; Thu, 12 Apr 2018 13:12:37 -0400'},
                    {'name': 'Content-Type',
                     'value': 'text/html; charset="us-ascii"'},
                    {'name': 'MIME-Version',
                     'value': '1.0'}, {
                        'name': 'Content-Transfer-Encoding',
                        'value': '8bit'},
                    {'name': 'to', 'value': receiver},
                    {'name': 'from', 'value': sender},
                    {'name': 'subject', 'value': subject},
                    {'name': 'Date', 'value': datetime_string},
                    {'name': 'Message-Id',
                     'value': '<CAB6aDuBeuEFKQBxrg1skB1-sxENwMnVnZZyanp=<EMAIL>>'}],
                'body': {'data': base64_body, 'size': len(base64_body)},
                'partId': '',
                'filename': ''},
            'snippet': 'Concesionario Oficial Volkswagen - TAKE UP 1.0 5P [propuesta-descripcion] PRECIO: ARS 251.502 Conoc\xe9 todas las caracter\xedsticas de esta promo imperdible. No dejes pasar esta oportunidad. M\xe1s informaci\xf3n',
            'sizeEstimate': 36279, 'threadId': '162bad8412c240e5', 'labelIds': ['SENT'],
            'id': '162bad8412c240e5'}

    def _convertir_a_base64(self, body):
        return base64.urlsafe_b64encode(body.encode("utf-8")).decode("utf-8")

    def _full_format_message_without_subject(self):
        return {
            'internalDate': '1524077409144',
            'historyId': '6431984',
            'payload': {
                'mimeType': 'text/html',
                'headers': [
                    {'name': 'From', 'value': 'Martina Uro <<EMAIL>>'}
                ],
                'body': {
                    'data': 'PGJyPjxhIGhyZWY9Imh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9c1Nt'
                             'ZkhGSkRQU1UiPmh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9c1NtZkhGSkRQU1U8L2E-',
                    'size': 105},
                'partId': '', 'filename': ''},
            'snippet': ' https://www.youtube.com/watch?v=sSmfHFJDPSU',
            'sizeEstimate': 100,
            'threadId': '162da17b378f9a72',
            'labelIds': ['CHAT'],
            'id': '162da17b378f9a72'
        }

    def _full_format_message_for(self, subject, sender, receiver, datetime_string, body):
        base64_body = self._convertir_a_base64(body)
        return {
            'internalDate': '1516755858000',
            'historyId': '1396628',
            'payload': {
                'mimeType': 'multipart/alternative',
                'body': {'size': 0},
                'partId': '',
                'filename': '',
                'headers': [
                    {'name': 'Delivered-To', 'value': '<EMAIL>'},
                    {'name': 'Received',
                     'value': 'by ************* with SMTP id d93csp76731edc;        Tue, 23 Jan 2018 17:04:19 -0800 (PST)'},
                    {'name': 'X-Received',
                     'value': 'by *********** with SMTP id o3mr3619337wmf.17.1516755859750;        Tue, 23 Jan 2018 17:04:19 -0800 (PST)'},
                    {'name': 'ARC-Seal',
                     'value': 'i=1; a=rsa-sha256; t=1516755859; cv=none;        d=google.com; s=arc-20160816;        b=swrfR2YwIAb1pbXWNJeAmuV27di3H6qnMIS449NG0I0gETANb6vinJnG4pdW8OTwKG         cZtidDYF7zx6YqYlAD4agz4HWLamC39gdOf1dg1BkV6Hmkrgia4vgc/1/PX8PsT375jt         kWqtqYfPoZJwiaCQC6nkhnh3P3qUYzwyRTOYqDWeO/ork5C2K/ipMloIOteZWJ8o3r0W         Bt/jqn+YJURKr4idXD9BM8QEfgt9qT4PgWggaos4Lqq5y6gHAeWBBxAtFhL4lN5OmyBZ         H4W7nwnPVzJ58rFi6W5/NijNBeI3mICjyc0sxHcHnXc0JgSwlk5aezER+Lb1gC2w61m6         2MMg=='},
                    {'name': 'ARC-Message-Signature',
                     'value': 'i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20160816;        h=to:subject:message-id:date:from:references:in-reply-to:mime-version         :dkim-signature:arc-authentication-results;        bh=TknQ8GDFUeXBeaiUQzcd/AwN8OYUZelmjZizK252iao=;        b=IQ519Zlx0Qh7xt/A2FjteuQ+5t0HMllGxFzAAuhItibEtN6UwDG3aG19MPtYWw7Cx1         n0nonWFRZqwOEDbBtjbojcbN2EwEFr6tFfgTz6s7+3xXjcS+N74HZIRCXkrLLbrUK6sf         hieIu0QntKBakXR97pXlFYe2aNJwjNUYVFSIaQsWLSxI45eyQbJzdx5xi4At7CQ5mpIA         ZmQNzZEtWdYciInUXP2V35Bd5x6S/8UbY688s7Mlo1RQrpLkmV7xkF1hlf/34NIGF3UX         4t+aTJzCTxyEe+eMaipfUe5zT2sLMPHuCJiZOu/xtqtE3MlC0de2Ihz7nx5NUNYEjXTK         HKYA=='},
                    {'name': 'ARC-Authentication-Results',
                     'value': 'i=1; mx.google.com;       dkim=pass header.i=@gmail.com header.s=20161025 header.b=PjJ+vZu7;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=gmail.com'},
                    {'name': 'Return-Path', 'value': '<<EMAIL>>'},
                    {'name': 'Received',
                     'value': 'from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])        by mx.google.com with SMTPS id r28sor827256wra.0.2018.***********.19        for <<EMAIL>>        (Google Transport Security);        Tue, 23 Jan 2018 17:04:19 -0800 (PST)'},
                    {'name': 'Received-SPF',
                     'value': 'pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;'},
                    {'name': 'Authentication-Results',
                     'value': 'mx.google.com;       dkim=pass header.i=@gmail.com header.s=20161025 header.b=PjJ+vZu7;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=gmail.com'},
                    {'name': 'DKIM-Signature',
                     'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=gmail.com; s=20161025;        h=mime-version:in-reply-to:references:from:date:message-id:subject:to;        bh=TknQ8GDFUeXBeaiUQzcd/AwN8OYUZelmjZizK252iao=;        b=PjJ+vZu7KD2wspQ/XPMM0VmoWDg1aOJldHAnXTTLwUJKV2w/1ANnJqEzliIIyDSdi4         UAqPCIXjZ3GJXzZooSTV7EhOysNj6orf5CQOuRSoEHZ3CD+GXeM584o86LA1xS5k4K0a         Ib9nxRTR79HhWtoDdlZ2W1lZbhg/viObiQPUJWy1CL9BC8cfZKO3qxPs+bKv6kO27IND         hFITS2Fx1AqlXTajhaFaLwKZMmpIvxkEmts70B5Z0xn8ThPxbsiRJOukFLzEzTLdtqUP         +AIZkHPDm9a8SbcQOc3NkPT9XHxtuFtHj9tRnmfvrqdjwYq78Iqi/SSmqJgAKw6A4m1C         C+GQ=='},
                    {'name': 'X-Google-DKIM-Signature',
                     'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20161025;        h=x-gm-message-state:mime-version:in-reply-to:references:from:date         :message-id:subject:to;        bh=TknQ8GDFUeXBeaiUQzcd/AwN8OYUZelmjZizK252iao=;        b=ba7boCHN6EtBm5J20o1T7gGSryVKU4LqaasrTlHoLeqrjnkoeQfn1wUFaPzPTLhuYz         SrEsVotDhEO4zCDkM87BdXQnvrbYgXNmsh/9p7nU0xg+5qY6qD/gB0XV6HbmaUIJMnaf         YxNhs2Lap9AuJAthY3tzPUdJCxaxLmBZRzhmLNQLsnLGlRa0HeFPiLfllYZPItKxapNb         suwgS8BruBDL7l3QnJzz61SY9wOWEZzTvdUp81SamqMDWsHDPZVw4IjkzFaznnFlyZ3d         7egBpcniMl5iQGXK9tnXzq/YxKIuFKo/dU9fYpHjB7mZmUAEt+RRWoMthxCpiucymLu7         2pdQ=='},
                    {'name': 'X-Gm-Message-State',
                     'value': 'AKwxytcLSC0UP5yUSrXY9aQqqgH6RDsJ++MtiT4k4aa73H+NFG0zfGuN E2TNnP2oEt5oPr9JkK+0mt6iLeq38HXfvWHSIRM='},
                    {'name': 'X-Google-Smtp-Source',
                     'value': 'AH8x227sXHRvwpRlMKJD+HW/ErfFifrqc4mGLeibuyFGydNOlp4VrG0TpgTVAsrOmdduCtmo8bC0fZ8pvaYTQWHsl40='},
                    {'name': 'X-Received',
                     'value': 'by 10.223.186.194 with SMTP id w2mr3690088wrg.154.1516755858762; Tue, 23 Jan 2018 17:04:18 -0800 (PST)'},
                    {'name': 'MIME-Version', 'value': '1.0'}, {'name': 'Received',
                                                                   'value': 'by 10.223.152.13 with HTTP; Tue, 23 Jan 2018 17:04:18 -0800 (PST)'},
                    {'name': 'In-Reply-To',
                     'value': '<CAKkgbgF_Wp0F5BfM=<EMAIL>>'},
                    {'name': 'References',
                     'value': '<CAKkgbgF_Wp0F5BfM=<EMAIL>>'},
                    {'name': 'From', 'value': '"Burella Juan M." <%s>' % sender},
                    {'name': 'Date', 'value': datetime_string},
                    {'name': 'Message-ID',
                     'value': '<<EMAIL>>'},
                    {'name': 'Subject', 'value': subject},
                    {'name': 'To', 'value': receiver},
                    {'name': 'Content-Type',
                     'value': 'multipart/alternative; boundary="089e082468d42c2ef905637b3efe"'}],
                'parts': [
                    {
                        'mimeType': 'text/plain',
                        'headers': [{'name': 'Content-Type', 'value': 'text/plain; charset="UTF-8"'}],
                        'body': {
                            'data': base64_body,
                            'size': len(base64_body)
                        },
                        'partId': '0',
                        'filename': ''
                    },
                    {
                        'mimeType': 'text/html',
                        'headers': [
                            {
                                'name': 'Content-Type',
                                'value': 'text/html; charset="UTF-8"'
                            },
                            {
                                'name': 'Content-Transfer-Encoding',
                                'value': 'quoted-printable'
                            }
                        ],
                        'body': {
                            'data': 'PGRpdiBkaXI9Imx0ciI-cHJvYmFuZG88L2Rpdj48ZGl2IGNsYXNzPSJnbWFpbF9leHRyYSI-PGJyPjxkaXYgY2xhc3M9ImdtYWlsX3F1b3RlIj5PbiBUdWUsIEphbiAyMywgMjAxOCBhdCA5OjU0IFBNLCAgPHNwYW4gZGlyPSJsdHIiPiZsdDs8YSBocmVmPSJtYWlsdG86amJ1cmVsbGFAZXJ5eHNvbHVjaW9uZXMuY29tLmFyIiB0YXJnZXQ9Il9ibGFuayI-amJ1cmVsbGFAZXJ5eHNvbHVjaW9uZXMuY29tLmFyPC9hPiZndDs8L3NwYW4-IHdyb3RlOjxicj48YmxvY2txdW90ZSBjbGFzcz0iZ21haWxfcXVvdGUiIHN0eWxlPSJtYXJnaW46MCAwIDAgLjhleDtib3JkZXItbGVmdDoxcHggI2NjYyBzb2xpZDtwYWRkaW5nLWxlZnQ6MWV4Ij5oZWxsbyB3b3JsZDxicj4NCjwvYmxvY2txdW90ZT48L2Rpdj48YnI-PC9kaXY-DQo=',
                            'size': 407
                        },
                        'partId': '1',
                        'filename': ''
                    }
                ]
            },
            'snippet': 'probando On Tue, Jan 23, 2018 at 9:54 PM, &lt;<EMAIL>&gt; wrote: hello world',
            'sizeEstimate': 5623,
            'threadId': '16125a905f959022',
            'labelIds': ['IMPORTANT', 'CATEGORY_PERSONAL', 'INBOX'],
            'id': '16125b1b8cf6b58c'
        }

    def _full_format_raro(self):
        return {
            'internalDate': '1524077750000',
            'historyId': '6432080',
            'payload': {
                'mimeType': 'multipart/mixed',
                'body': {'size': 0},
                'partId': '',
                'filename': '',
                'headers': [
                    {'name': 'Delivered-To', 'value': '<EMAIL>'},
                    {'name': 'Received',
                     'value': 'by ************* with SMTP id 87csp889435iok;        Wed, 18 Apr 2018 11:55:52 -0700 (PDT)'},
                    {'name': 'X-Received',
                     'value': 'by 2002:a9d:3514:: with SMTP id o20-v6mr1939298otc.200.1524077752142;        Wed, 18 Apr 2018 11:55:52 -0700 (PDT)'},
                    {'name': 'ARC-Seal',
                     'value': 'i=1; a=rsa-sha256; t=1524077752; cv=none;        d=google.com; s=arc-20160816;        b=Tak2Ft828hNmh14EfJafCUD5WxiTwQ6RNCTXGMZb6ri2dQ6uYFda7w+7CaYd6QDMCh         3x/auOdzFxQnRVZ0vJYqUsvCRi8rVRkxC6gHxUcsor4H5jdQpukJ9F0xtr0ASnx/hTdb         RHZQEW56EKnOb9/xQTz9OYdhKwkU6MjCcxgU9MiFz6w7GB1QdoGPvrdOGK1bl+RDOcMR         R2uUdh/IHdGzihQJ8/Um3mbWIT1bFYPfUou+AL5r4dCu67fYQZIp3rrP6ipnIeaqksmO         eDUuK4CP3YK7ofLXEZX4UDNLAn28KJu0a21KBHz2DcdTLqCIKJLOKbhEdMnwN79dn/aR         EQ4Q=='},
                    {'name': 'ARC-Message-Signature',
                     'value': 'i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20160816;        h=to:subject:message-id:date:from:references:in-reply-to:mime-version         :dkim-signature:arc-authentication-results;        bh=OSi3Nj21ursWN/YoW8KjNro/C3z2y/SpZfgkUFEG8JM=;        b=pVh26iU4pnteDUO04k2HAHAkuCJqWIYnR5vnvnsW//pUO6a5ZmsrZRdMft8jUYONaV         gcvpzwYgSNhnL8vbKJ0VXVJyDL4aCzVb3OBHiG8P2keC1L98O1+sgTNTl4QD5RiRZ8pd         S+FKMZ65XpE/Kw72TVgQR3AVd6Xx7zLMinjUGpnxUwDd7GLfjcrO8q7aZfTCF1sGDsq9         hDyS9E/lhdUs9e/d2YTQehdM+fh+wQiTZjFxKrgyb6p9HcNO9jA1wBURdbcePJXmKds6         hfQh0lxsgpjWC0Hn//lITnoPYciE99W5+un5lLJgR6VAnw6Jq/XwrmPvHIC/lQ3WqOnw         VFEg=='},
                    {'name': 'ARC-Authentication-Results',
                     'value': 'i=1; mx.google.com;       dkim=pass header.i=@gmail.com header.s=20161025 header.b=fhKr92YP;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=gmail.com'},
                    {'name': 'Return-Path', 'value': '<<EMAIL>>'}, {'name': 'Received',
                                                                                    'value': 'from mail-sor-f41.google.com (mail-sor-f41.google.com. [*************])        by mx.google.com with SMTPS id d12-v6sor927724oth.211.2018.***********.52        for <<EMAIL>>        (Google Transport Security);        Wed, 18 Apr 2018 11:55:52 -0700 (PDT)'},
                    {'name': 'Received-SPF',
                     'value': 'pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;'},
                    {'name': 'Authentication-Results',
                     'value': 'mx.google.com;       dkim=pass header.i=@gmail.com header.s=20161025 header.b=fhKr92YP;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=gmail.com'},
                    {'name': 'DKIM-Signature',
                     'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=gmail.com; s=20161025;        h=mime-version:in-reply-to:references:from:date:message-id:subject:to;        bh=OSi3Nj21ursWN/YoW8KjNro/C3z2y/SpZfgkUFEG8JM=;        b=fhKr92YPH5zAR0g2zDmRdiqTxGSx/Lo32fEAAmc74j0sqjh1tWfAbj4x0CFkreHKVO         M9ckHD78mUSD1k4u0YZJTGRn8L0Rp4zkoD6Mq0wR/EPNw7wSeizoZ4Rle66CH+E3e0d3         NpsU0VlB7RMaLs6eZTp3eTEmkCtPPC+2T5aiDSSWNtUOSwZMReLwPPwYYxPN16vOt42q         hLMYn4bTqE9nXCuEreGC+X6hczkXvr3hBzC4guGzdTolmlKAJC12o8aYuoeloaFJ9AZ3         HWfJan42ILTiBE+H/OIn3FjqftXpduGlPnLXb3M93EjBHDjBrtSd4Khp7uAHiL7IeEFZ         vQWw=='},
                    {'name': 'X-Google-DKIM-Signature',
                     'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20161025;        h=x-gm-message-state:mime-version:in-reply-to:references:from:date         :message-id:subject:to;        bh=OSi3Nj21ursWN/YoW8KjNro/C3z2y/SpZfgkUFEG8JM=;        b=uZnPObqg1LEe80wuYPyy06lqumx61dLt0yn2C4HIVL8N6N1/4Z1gTtHN5+5JYJqEZ4         vMPF1ZZL1ndyGJCSzXEDgDvxSOSaJq3hqXXrtzjdkTaA9eHQFqFUalcvaXaUKCeDvdnq         m6VfDgrqWJbdzO9tguo9rY/A2mQFTYDV7zkbX9AuLTBG5mFJKmj4jZgOwqXZi5J2HAGK         SrQNBP/o77ZoAqQWoeNkknoWSRUX/Fpd+0o9vDtIU/uau9Ih01fAC8e5qChrC75f4Kxs         IGuPx2kE1tscx2IHm+lHZ7QkuarIck+R8hLeK/236RS+FDKLibwg3Kpmp0OxechbsnuZ         D/xg=='},
                    {'name': 'X-Gm-Message-State',
                     'value': 'ALQs6tB/NzmEdocY+7tDfFbRwbnHuuC0IKfA0Za7BlaA6dCA/H7gZi9O lExqCnacbttEfWjpyoxT3b3flmndfcv6uccl+zQ='},
                    {'name': 'X-Google-Smtp-Source',
                     'value': 'AIpwx49ty+s10G8jF3Nk1CrJ8UdVY7JXJeOXoFI1NNpH7313B4Y6zBATFuIAyxpkr4D406KGGawMyAcV/809MkxfEo4='},
                    {'name': 'X-Received',
                     'value': 'by 2002:a9d:59ae:: with SMTP id u46-v6mr1939453oth.208.1524077751559; Wed, 18 Apr 2018 11:55:51 -0700 (PDT)'},
                    {'name': 'MIME-Version', 'value': '1.0'},
                    {'name': 'Received',
                     'value': 'by ************* with HTTP; Wed, 18 Apr 2018 11:55:50 -0700 (PDT)'},
                    {'name': 'In-Reply-To',
                     'value': '<<EMAIL>>'},
                    {'name': 'References',
                     'value': '<<EMAIL>>'},
                    {'name': 'From', 'value': 'Federico Lorenzi <<EMAIL>>'},
                    {'name': 'Date', 'value': 'Wed, 18 Apr 2018 15:55:50 -0300'}, {'name': 'Message-ID',
                                                                                       'value': '<<EMAIL>>'},
                    {'name': 'Subject', 'value': 'Fwd: Family Trip 2017 v2.xlsx'},
                    {'name': 'To', 'value': '<EMAIL>'},
                    {'name': 'Content-Type', 'value': 'multipart/mixed; boundary="000000000000fe72b1056a2400f7"'}
                ],
                'parts': [
                    {
                        'mimeType': 'multipart/alternative',
                        'body': {'size': 0},
                        'partId': '0',
                        'filename': '',
                        'headers': [{
                            'name': 'Content-Type',
                            'value': 'multipart/alternative; boundary="000000000000fe72ad056a2400f5"'}
                        ],
                        'parts': [
                            {
                                'mimeType': 'text/plain',
                                'headers': [{'name': 'Content-Type', 'value': 'text/plain; charset="UTF-8"'}],
                                'body': {
                                    'data': 'RmxvciB0ZSBlbnZpbyBsbyBkZSBFdXJvcGEsIGZpamF0ZSBlbiBsYSBob2phICJRVUUgSEFDRVIiIHF1ZSB0ZSBkYSB0aXBzIGRlDQpjb3N0b3MgeSBsdWdhcmVzIHBhcmEgcmVjb3JyZXIuDQoNCg0KLS0tLS0tLS0tLSBGb3J3YXJkZWQgbWVzc2FnZSAtLS0tLS0tLS0tDQpGcm9tOiBMb3JlbnppLCBCZWxlbiA8QmVsZW4uTG9yZW56aUB1bmlsZXZlci5jb20-DQpEYXRlOiAyMDE4LTA0LTE4IDE1OjQ0IEdNVC0wMzowMA0KU3ViamVjdDogRmFtaWx5IFRyaXAgMjAxNyB2Mi54bHN4DQpUbzogImxmbG9yZW56aUBnbWFpbC5jb20iIDxsZmxvcmVuemlAZ21haWwuY29tPg0KDQoNClZhIGxvIHF1ZSB0ZW5nbyENCg0KSU1QT1JUQU5UIE5PVElDRTogIFRoaXMgZW1haWwgYW5kIGFueSBhdHRhY2htZW50cyBtYXkgY29udGFpbiBpbmZvcm1hdGlvbg0KdGhhdCBpcyBjb25maWRlbnRpYWwgYW5kIHByaXZpbGVnZWQuIEl0IGlzIGludGVuZGVkIHRvIGJlIHJlY2VpdmVkIG9ubHkgYnkNCnBlcnNvbnMgZW50aXRsZWQgdG8gcmVjZWl2ZSB0aGUgaW5mb3JtYXRpb24uIElmIHlvdSBhcmUgbm90IHRoZSBpbnRlbmRlZA0KcmVjaXBpZW50LCBwbGVhc2UgZGVsZXRlIGl0IGZyb20geW91ciBzeXN0ZW0gYW5kIG5vdGlmeSB0aGUgc2VuZGVyLiBZb3UNCnNob3VsZCBub3QgY29weSBpdCBvciB1c2UgaXQgZm9yIGFueSBwdXJwb3NlIG5vciBkaXNjbG9zZSBvciBkaXN0cmlidXRlIGl0cw0KY29udGVudHMgdG8gYW55IG90aGVyIHBlcnNvbi4NCg==',
                                    'size': 754
                                },
                                'partId': '0.0',
                                'filename': ''},
                            {
                                'mimeType': 'text/html',
                                'headers': [
                                    {'name': 'Content-Type',
                                     'value': 'text/html; charset="UTF-8"'},
                                    {
                                        'name': 'Content-Transfer-Encoding',
                                        'value': 'quoted-printable'}],
                                'body': {
                                    'data': 'PGRpdiBkaXI9Imx0ciI-RmxvciB0ZSBlbnZpbyBsbyBkZSBFdXJvcGEsIGZpamF0ZSBlbiBsYSBob2phICZxdW90O1FVRSBIQUNFUiZxdW90OyBxdWUgdGUgZGEgdGlwcyBkZSBjb3N0b3MgeSBsdWdhcmVzIHBhcmEgcmVjb3JyZXIuPGRpdj48YnI-PC9kaXY-PGRpdj48YnI-PGRpdiBjbGFzcz0iZ21haWxfcXVvdGUiPi0tLS0tLS0tLS0gRm9yd2FyZGVkIG1lc3NhZ2UgLS0tLS0tLS0tLTxicj5Gcm9tOiA8YiBjbGFzcz0iZ21haWxfc2VuZGVybmFtZSI-TG9yZW56aSwgQmVsZW48L2I-IDxzcGFuIGRpcj0ibHRyIj4mbHQ7PGEgaHJlZj0ibWFpbHRvOkJlbGVuLkxvcmVuemlAdW5pbGV2ZXIuY29tIj5CZWxlbi5Mb3JlbnppQHVuaWxldmVyLmNvbTwvYT4mZ3Q7PC9zcGFuPjxicj5EYXRlOiAyMDE4LTA0LTE4IDE1OjQ0IEdNVC0wMzowMDxicj5TdWJqZWN0OiBGYW1pbHkgVHJpcCAyMDE3IHYyLnhsc3g8YnI-VG86ICZxdW90OzxhIGhyZWY9Im1haWx0bzpsZmxvcmVuemlAZ21haWwuY29tIj5sZmxvcmVuemlAZ21haWwuY29tPC9hPiZxdW90OyAmbHQ7PGEgaHJlZj0ibWFpbHRvOmxmbG9yZW56aUBnbWFpbC5jb20iPmxmbG9yZW56aUBnbWFpbC5jb208L2E-Jmd0Ozxicj48YnI-PGJyPg0KDQoNCg0KDQoNCjxkaXYgbGFuZz0iRU4tVVMiIGxpbms9IiMwNTYzQzEiIHZsaW5rPSIjOTU0RjcyIj4NCjxkaXYgY2xhc3M9Im1fLTkxNzExNzM3NzkwMzExNzE5MDhXb3JkU2VjdGlvbjEiPg0KPHAgY2xhc3M9Ik1zb05vcm1hbCI-PHNwYW4gbGFuZz0iRVMtQVIiPlZhIGxvIHF1ZSB0ZW5nbyE8dT48L3U-PHU-PC91Pjwvc3Bhbj48L3A-DQo8L2Rpdj4NCjxkaXY-DQo8cCBzdHlsZT0iY29sb3I6IzgwODA4MDtmb250LWZhbWlseTpjb3VyaWVyIG5ldyI-SU1QT1JUQU5UIE5PVElDRTogwqBUaGlzIGVtYWlsIGFuZCBhbnkgYXR0YWNobWVudHMgbWF5IGNvbnRhaW4gaW5mb3JtYXRpb24gdGhhdCBpcyBjb25maWRlbnRpYWwgYW5kIHByaXZpbGVnZWQuIEl0IGlzIGludGVuZGVkIHRvIGJlIHJlY2VpdmVkIG9ubHkgYnkgcGVyc29ucyBlbnRpdGxlZCB0byByZWNlaXZlIHRoZSBpbmZvcm1hdGlvbi4gSWYgeW91IGFyZSBub3QNCiB0aGUgaW50ZW5kZWQgcmVjaXBpZW50LCBwbGVhc2UgZGVsZXRlIGl0IGZyb20geW91ciBzeXN0ZW0gYW5kIG5vdGlmeSB0aGUgc2VuZGVyLiBZb3Ugc2hvdWxkIG5vdCBjb3B5IGl0IG9yIHVzZSBpdCBmb3IgYW55IHB1cnBvc2Ugbm9yIGRpc2Nsb3NlIG9yIGRpc3RyaWJ1dGUgaXRzIGNvbnRlbnRzIHRvIGFueSBvdGhlciBwZXJzb24uDQo8L3A-DQo8L2Rpdj4NCjxicj4NCjwvZGl2Pg0KDQo8L2Rpdj48YnI-PC9kaXY-PC9kaXY-DQo=',
                                    'size': 1334},
                                'partId': '0.1',
                                'filename': ''}]},
                    {'mimeType': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'headers': [
                        {'name': 'Content-Type',
                         'value': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; name="Family Trip 2017 v2.xlsx"'},
                        {'name': 'Content-Disposition', 'value': 'attachment; filename="Family Trip 2017 v2.xlsx"'},
                        {'name': 'Content-Transfer-Encoding', 'value': 'base64'},
                        {'name': 'X-Attachment-Id', 'value': 'e327464721d4dd66_0.1'}
                    ],
                     'body': {
                         'attachmentId': 'ANGjdJ9nUualjdFcAq9hITthhogoKRbAK8jhJ0UfL-MEOHy3Vw36zF184GZecLqJijYPx_H58o89q4AdU_0vFCub14m4V9-yNzZB5IBpvdQ00pucecZq0SQY3mbO-iMjshCW5RiIDt4g676x1YZkm1Odhd-DypbMbsrGGolNV3ntdX4joDZHXg5yXQKOxXZrf3MRnKP6tFTtcu4OsnKamhsGpwC6ogcPHW8rL3O3mG6LvAuf7FeGI57gSWdh2hbcC0J0-KAcQ_JdYy_7nd4vw4oCG__2Q5l3kNPqrdREeRBRcVz1r1eKMXvdhUT0YuuYR-wC806XtjcnHpYiZD_sOLnFh1N8LbEKicKmJIGhUlpHJtSHDuP1EE4Qd56N7sgP3i6m-PJWiQfGYBL5kuQv',
                         'size': 21043}, 'partId': '1', 'filename': 'Family Trip 2017 v2.xlsx'}
                ]},
            'snippet': 'Flor te envio lo de Europa, fijate en la hoja &quot;QUE HACER&quot; que te da tips de costos y lugares para recorrer. ---------- Forwarded message ---------- From: Lorenzi, Belen &lt;Belen.Lorenzi@',
            'sizeEstimate': 36510,
            'threadId': '162da1ceec5e2fab',
            'labelIds': ['UNREAD', 'CATEGORY_PERSONAL', 'INBOX'],
            'id': '162da1ceec5e2fab'
        }

    def _full_format_with_multipart(self, subject, sender, receiver, datetime_string, body):
        base64_body = self._convertir_a_base64(body)
        return {
            'internalDate': '1524172026000',
            'historyId': '7476467',
            'payload': {
                'mimeType': 'multipart/mixed', 'body': {'size': 0}, 'partId': '', 'filename': '',
                'headers': [{'name': 'Delivered-To', 'value': '<EMAIL>'},
                             {'name': 'Received',
                              'value': 'by 2002:a4f:e317:0:0:0:0:0 with SMTP id p23-v6csp1162312ivm;        Thu, 19 Apr 2018 14:07:12 -0700 (PDT)'},
                             {'name': 'X-Google-Smtp-Source',
                              'value': 'AIpwx4/7um1qKtWV+T8P/TIF7nc7aS7lhBjWfFT6bbeuyoFrQj0Nw3lZTHX0O/4xAAyE4L6THOBK'},
                             {'name': 'X-Received',
                              'value': 'by 2002:a17:902:9a04:: with SMTP id v4-v6mr7473267plp.21.1524172031759;        Thu, 19 Apr 2018 14:07:11 -0700 (PDT)'},
                             {'name': 'ARC-Seal',
                              'value': 'i=1; a=rsa-sha256; t=1524172031; cv=none;        d=google.com; s=arc-20160816;        b=cuaPOfjdlLg2pC/YsteWOCi/F1ayJ1kjjER8ck+m0Rv82Jp++Lm2x+WINVmYMMHSy+         K2Crxupv4MbyDDHbKriPu322pvveOmhfzMr5zgOf+/E51RMEhccRn5Ie9GiB7x9eq7wG         E7nc0bXu58LdMkVpxcRWi10fWkLf/3Z/7KU0/0gDxVci78IM1iuMbIIZ6SYQq1oeokpO         1h6ZjuwWF3iOq3PRXaRXtf0r1RmK7bQCO38BKRElkFxxXtFl418wsA6zc+b3eQBh0Er/         XzQd+u1jEkSnixlKoy55CRtJR016X+SjJfnblRWHzGqpepo1k2swS3s1xjzv9tBXxQqY         +B7g=='},
                             {'name': 'ARC-Message-Signature',
                              'value': 'i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20160816;        h=mime-version:content-language:accept-language:message-id:date         :thread-index:subject:to:from:dkim-signature         :arc-authentication-results;        bh=utQK8Nv7UaM9qtZqBk/C7ejwwCVCz13mnZFpl+JOJKo=;        b=IYUe+aFhuN3L+6tuqkBdzPsxVLD85Rh7+JmHC8fJqX39e8zLWkl+ceWzURoRcP9AE4         fspU1tyDSMaYLe/1axoiezwKhwav52MJHFckcXVRa4BzilQmwRxrvHodj2rWrzswm6nb         z0jFwaVcBjY2HcAbyo8iTLFY+u09sIto54GrhPbZWH9Bsfsx+U2jpwqGLalpV8HYOiWG         WRUKptXP7eiGKkDFV7apQhKz2yTe2s0xgdM8PuwhARb+yjZAANFwPqa0+aHq4uTqoG3w         fhDV5ygGqDn3E5i+ISLnE6V3q89jr1Px+CNuE0hKjyzf0ynNW6Ldt/LThK6zIl/NffrM         8eEQ=='},
                             {'name': 'ARC-Authentication-Results',
                              'value': 'i=1; mx.google.com;       dkim=pass header.i=@hotmail.com header.s=selector1 header.b=RBzwdYY/;       spf=pass (google.com: <NAME_EMAIL> designates ********** as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=hotmail.com'},
                             {'name': 'Return-Path', 'value': '<<EMAIL>>'},
                             {'name': 'Received',
                              'value': 'from NAM02-CY1-obe.outbound.protection.outlook.com (mail-oln040092004049.outbound.protection.outlook.com. [**********])        by mx.google.com with ESMTPS id x3si1989963pfb.327.2018.***********.10        for <<EMAIL>>        (version=TLS1_2 cipher=ECDHE-RSA-AES128-SHA bits=128/128);        Thu, 19 Apr 2018 14:07:11 -0700 (PDT)'},
                             {'name': 'Received-SPF',
                              'value': 'pass (google.com: <NAME_EMAIL> designates ********** as permitted sender) client-ip=**********;'},
                             {'name': 'Authentication-Results',
                              'value': 'mx.google.com;       dkim=pass header.i=@hotmail.com header.s=selector1 header.b=RBzwdYY/;       spf=pass (google.com: <NAME_EMAIL> designates ********** as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=QUARANTINE dis=NONE) header.from=hotmail.com'},
                             {'name': 'DKIM-Signature',
                              'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed; d=hotmail.com; s=selector1; h=From:Date:Subject:Message-ID:Content-Type:MIME-Version; bh=utQK8Nv7UaM9qtZqBk/C7ejwwCVCz13mnZFpl+JOJKo=; b=RBzwdYY/3xHC80HkouGPqqUrRB5KZibyFX/Jt1WUrQKDnIjMzdKTRRB5+a1FWGqSlAMkufaX7DY+BlxH3kpwgu8EM0EIy5vQ/ZNICfAPbc32Zu270RPvsIIJlJHP5ad6hjYs7eh4BY0gMUb4Qq/KdKFWm21E0WlQIHp2zuYL+GczQ101J2zGD5dGzYh46nQ6S/yo5uehT+IZceXEOcgGMi0dO8ODOYYRclEj6wTyjEac+MyQPZQagTp0Czj09n5aD/+uYaR+95kNudw0Or9bRXyamTOW2fh0mUFihp4IBXZncoX1HgtNurETLKTnebPuYkiSKyhBimQhUTRDq4taKQ=='},
                             {'name': 'Received',
                              'value': 'from CY1NAM02FT035.eop-nam02.prod.protection.outlook.com (************) by CY1NAM02HT012.eop-nam02.prod.protection.outlook.com (*************) with Microsoft SMTP Server (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384_P256) id 15.20.675.14; Thu, 19 Apr 2018 21:07:06 +0000'},
                             {'name': 'Received',
                              'value': 'from DM5PR0201MB3479.namprd02.prod.outlook.com (************) by CY1NAM02FT035.mail.protection.outlook.com (*************) with Microsoft SMTP Server (version=TLS1_2, cipher=TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384_P256) id 15.20.675.14 via Frontend Transport; Thu, 19 Apr 2018 21:07:06 +0000'},
                             {'name': 'Received',
                              'value': 'from DM5PR0201MB3479.namprd02.prod.outlook.com ([fe80::207b:d78b:fc32:8374]) by DM5PR0201MB3479.namprd02.prod.outlook.com ([fe80::207b:d78b:fc32:8374%13]) with mapi id 15.20.0675.015; Thu, 19 Apr 2018 21:07:06 +0000'},
                             {'name': 'From', 'value': sender},
                             {'name': 'To', 'value': receiver},
                             {'name': 'Subject', 'value': subject},
                             {'name': 'Thread-Index', 'value': 'AQHT2CJfXUbBmWQq8UmZQZ3TrbBkbw=='},
                             {'name': 'Date', 'value': datetime_string},
                             {'name': 'Message-ID',
                              'value': '<<EMAIL>>'},
                             {'name': 'Accept-Language', 'value': 'es-MX, en-US'},
                             {'name': 'Content-Language', 'value': 'es-MX'},
                             {'name': 'X-MS-Has-Attach', 'value': 'yes'},
                             {'name': 'X-MS-TNEF-Correlator', 'value': ''},
                             {'name': 'x-incomingtopheadermarker',
                              'value': 'OriginalChecksum:22C90DA273F29ADB32EB11752061A1837A6D3B8A4388A47DA09FC0EB87CAF20D;UpperCasedChecksum:ABA5AF2058669B06453BDC2B3D0A971485568EE1B30AC990F6649EB0ED4D5CF4;SizeAsReceived:6977;Count:44'},
                             {'name': 'x-tmn', 'value': '[eoQVAqrJvaTcBtWLDIC5tyhO0rPmZjEG]'},
                             {'name': 'x-ms-publictraffictype', 'value': 'Email'},
                             {'name': 'x-microsoft-exchange-diagnostics',
                              'value': '1;CY1NAM02HT012;7:RikvUHSlRitcdsmAxdbMeOZdOFMZHTWEW1ZKO0N99iJ8+Uo31gtekN/IOaa7MYZLiMRy3k4P/bgeKDVpW3vG0gHTiiAH4EGE/UJGGXcUZhkrrbvtfaQxjk0qG28i4duvC3Szh0ZfDu7QJ00GTpCOPoyp65VNRKAo7gNpQwrrFmOb0NvrvT1Owzc8n7AnYDrI8nOFTs/BqD9qhUfEUKel/r+Uc2gxk7mc2z/uuyTpbantffdbof6fWRxg7TMzMNCL'},
                             {'name': 'x-incomingheadercount', 'value': '44'},
                             {'name': 'x-eopattributedmessage', 'value': '0'},
                             {'name': 'x-microsoft-antispam',
                              'value': 'UriScan:;BCL:0;PCL:0;RULEID:(7020095)(201702061078)(5061506573)(5061507331)(1603103135)(2017031320274)(2017031324274)(2017031323274)(2017031322404)(1601125374)(1603101448)(1701031045);SRVR:CY1NAM02HT012;'},
                             {'name': 'x-ms-traffictypediagnostic', 'value': 'CY1NAM02HT012:'},
                             {'name': 'x-exchange-antispam-report-cfa-test',
                              'value': 'BCL:0;PCL:0;RULEID:(444000031);SRVR:CY1NAM02HT012;BCL:0;PCL:0;RULEID:;SRVR:CY1NAM02HT012;'},
                             {'name': 'x-forefront-prvs', 'value': '0647963F84'},
                             {'name': 'x-forefront-antispam-report',
                              'value': 'SFV:NSPM;SFS:(7070007)(98901004);DIR:OUT;SFP:1901;SCL:1;SRVR:CY1NAM02HT012;H:DM5PR0201MB3479.namprd02.prod.outlook.com;FPR:;SPF:None;LANG:;'},
                             {'name': 'x-microsoft-antispam-message-info',
                              'value': 'yOD+RBeBCdFQ35gALomwTVLcnw2FP8Q4iDM3tesvMxUZTWPXAkRk5k1R/DhDEAyErbn98deEp4hmsShRbCJT2jWm4be8eYZghchzHfjpvs4Hf3+wZYWUiNLPvhuAgLHHtJh94XXZvt2y/xsWmA9QYj4JjOqiCI/ZhJZGuMMfk6LZOFwLKCdPelabF8BmvRS/'},
                             {'name': 'Content-Type',
                              'value': 'multipart/mixed; boundary="_005_DM5PR0201MB347931FB075394C12677EB14A7B50DM5PR0201MB3479_"'},
                             {'name': 'MIME-Version', 'value': '1.0'},
                             {'name': 'X-MS-Office365-Filtering-Correlation-Id',
                              'value': '0c5d749f-61e7-42a1-3723-08d5a63981df'},
                             {'name': 'X-OriginatorOrg', 'value': 'hotmail.com'},
                             {'name': 'X-MS-Exchange-CrossTenant-RMS-PersistedConsumerOrg',
                              'value': '9a4e3081-9524-43cf-bfc3-dcaef82d5da1'},
                             {'name': 'X-MS-Exchange-CrossTenant-Network-Message-Id',
                              'value': '0c5d749f-61e7-42a1-3723-08d5a63981df'},
                             {'name': 'X-MS-Exchange-CrossTenant-rms-persistedconsumerorg',
                              'value': '9a4e3081-9524-43cf-bfc3-dcaef82d5da1'},
                             {'name': 'X-MS-Exchange-CrossTenant-originalarrivaltime',
                              'value': '19 Apr 2018 21:07:06.2841 (UTC)'},
                             {'name': 'X-MS-Exchange-CrossTenant-fromentityheader',
                              'value': 'Internet'}, {'name': 'X-MS-Exchange-CrossTenant-id',
                                                       'value': '84df9e7f-e9f6-40af-b435-aaaaaaaaaaaa'},
                             {'name': 'X-MS-Exchange-Transport-CrossTenantHeadersStamped',
                              'value': 'CY1NAM02HT012'}],
                'parts': [
                    {
                        'mimeType': 'multipart/alternative',
                        'body': {'size': 0},
                        'partId': '0',
                        'filename': '',
                        'headers': [{
                            'name': 'Content-Type',
                            'value': 'multipart/alternative; boundary="_000_DM5PR0201MB347931FB075394C12677EB14A7B50DM5PR0201MB3479_"'
                        }],
                        'parts': [{
                            'mimeType': 'text/plain',
                            'headers': [
                                {'name': 'Content-Type', 'value': 'text/plain; charset="utf-8"'},
                                {'name': 'Content-Transfer-Encoding', 'value': 'base64'}
                            ],
                            'body': {'data': base64_body,
                                      'size': len(base64_body)}, 'partId': '0.0', 'filename': ''},
                            {'mimeType': 'text/html',
                             'headers': [{'name': 'Content-Type', 'value': 'text/html; charset="utf-8"'},
                                          {'name': 'Content-ID',
                                           'value': '<<EMAIL>>'},
                                          {'name': 'Content-Transfer-Encoding', 'value': 'base64'}],
                             'body': {
                                 'data': 'PGh0bWw-DQo8aGVhZD4NCjxtZXRhIGh0dHAtZXF1aXY9IkNvbnRlbnQtVHlwZSIgY29udGVudD0idGV4dC9odG1sOyBjaGFyc2V0PXV0Zi04Ij4NCjwvaGVhZD4NCjxib2R5IHN0eWxlPSJwYWRkaW5nLWJvdHRvbTo0MHB4Ij4NCjxkaXYgc3R5bGU9ImZvbnQtc2l6ZTogMTBwdDsgIj4NCjxwIGRpcj0ibHRyIiBzdHlsZT0ibWFyZ2luLXRvcDowO21hcmdpbi1ib3R0b206MDsiPjxicj4NCjwvcD4NCjxwIGRpcj0ibHRyIiBzdHlsZT0ibWFyZ2luLXRvcDowO21hcmdpbi1ib3R0b206MDsiPjxicj4NCjwvcD4NCjxkaXYgaWQ9IlNpZ25hdHVyZUJveCIgZGlyPSJsdHIiIHN0eWxlPSJtYXJnaW4tdG9wOjA7bWFyZ2luLWJvdHRvbTowOyI-RW52aWFkbyBkZXNkZSBQZXJzb25hbCBMRyBTcGlyaXQgTFRFPC9kaXY-DQo8L2Rpdj4NCjwvYm9keT4NCjwvaHRtbD4NCg==',
                                 'size': 430}, 'partId': '0.1', 'filename': ''}]},
                    {'mimeType': 'image/jpeg',
                     'headers': [{'name': 'Content-Type', 'value': 'image/jpeg; name="20180419_180337.jpg"'},
                                  {'name': 'Content-Description', 'value': '20180419_180337.jpg'},
                                  {'name': 'Content-Disposition',
                                   'value': 'attachment; filename="20180419_180337.jpg"; size=1743102; creation-date="Thu, 19 Apr 2018 21:07:05 GMT"; modification-date="Thu, 19 Apr 2018 21:07:05 GMT"'},
                                  {'name': 'Content-ID',
                                   'value': '<<EMAIL>>'},
                                  {'name': 'Content-Transfer-Encoding', 'value': 'base64'}], 'body': {
                        'attachmentId': 'ANGjdJ_7vUG3Az6uRcARvYsxpxN-txAUtsLRL403E0Fg4Q8k88YVSwZxfimmiP75K1SNhIZ05WQuN-9aJvvnWuQGIkH-y7KSNVFUxoTNMotsaeNbSaaOGH-ySym3hGMDYYrMsn5BZ8OG-_5KQuehh3rKenMHOLo49dbMU3QG1V6l_PJQjdVRE8tYwPMnUMooCL5IHqoVXiO2hCe8O4_ZEEepq5zbrR-G8WzU5zYCIWBNOXhkICAKqwGtTJydzeSCNZt-h7FWcLeW-A7yBsLpm8NmIjKzoa6KtqeyvcgODO79j8qY8bD84uh2NNsQ8AlsTWCeg6ck0z7n4QJpXYfZ7Xo-S0o8u-IUf4AddUCQJrYIRe58EB63W3KMRRubF8L1MsQisw7xulAGB1SR_riT',
                        'size': 1743102}, 'partId': '1', 'filename': '20180419_180337.jpg'},
                    {'mimeType': 'image/jpeg',
                     'headers': [{'name': 'Content-Type', 'value': 'image/jpeg; name="20180419_180324.jpg"'},
                                  {'name': 'Content-Description', 'value': '20180419_180324.jpg'},
                                  {'name': 'Content-Disposition',
                                   'value': 'attachment; filename="20180419_180324.jpg"; size=2088347; creation-date="Thu, 19 Apr 2018 21:07:05 GMT"; modification-date="Thu, 19 Apr 2018 21:07:05 GMT"'},
                                  {'name': 'Content-ID',
                                   'value': '<<EMAIL>>'},
                                  {'name': 'Content-Transfer-Encoding', 'value': 'base64'}], 'body': {
                        'attachmentId': 'ANGjdJ-9r7cyKeYnJ_kHQiCaDHNtZW-IeeE6yAgpfy3vJ04q7HSIqz1MJSYSKU2KycJLw_PM9yPOjagtXOk-ShnJ26OEDR-u7zNAG8BZUt7hugTmToDJf3Vdu6zRMdgYQMtQQDWxz9kJOYjg4NhNqg8thA1OE6xE2n-gJNYZcVCh5w3P4XpQ-dGsJm_ZnRe53X3eb01q_BmFFVRRt2g0hgou2ucbYWPqiPrAcVqB20OthO901k0FtGrMwnprIL0G4qNFQQv6ql-p43030jJiW3ZH1tfEVo5tpeQDlBsS_al-DIJvrQX2HeCk8CUUX5DmI42hiAzDIPagFsehrwCBg7daltv5PSxaxvLSHiZvxqh0J_nmy_E4n0M-w3dTafshuZHBnP0xkniWjIljRShQhhWKwzY_bepaNx2-awLnMA',
                        'size': 2088347}, 'partId': '2', 'filename': '20180419_180324.jpg'}]},
            'snippet': 'Enviado desde Personal LG Spirit LTE',
            'sizeEstimate': 5252352,
            'threadId': '162dfbb860ca53c0',
            'labelIds': ['UNREAD', 'CATEGORY_PERSONAL', 'INBOX'],
            'id': '162dfbb860ca53c0'
        }

    def _full_format_with_multipart_related(self, subject, sender, receiver, datetime_string, body):
        base64_body = self._convertir_a_base64(body)
        return {'internalDate': '1531255221000', 'historyId': '2229',
                'payload': {'mimeType': 'multipart/report', 'body': {'size': 0}, 'partId': '', 'filename': '',
                             'headers': [{'name': 'Delivered-To', 'value': '<EMAIL>'},
                                          {'name': 'Received',
                                           'value': 'by 2002:adf:9d89:0:0:0:0:0 with SMTP id p9-v6csp4213652wre;        Tue, 10 Jul 2018 13:40:21 -0700 (PDT)'},
                                          {'name': 'X-Received',
                                           'value': 'by 2002:adf:ec04:: with SMTP id x4-v6mr8015811wrn.245.1531255221913;        Tue, 10 Jul 2018 13:40:21 -0700 (PDT)'},
                                          {'name': 'ARC-Seal',
                                           'value': 'i=1; a=rsa-sha256; t=1531255221; cv=none;        d=google.com; s=arc-20160816;        b=fskCRwLL/Sy5KVCp4mcnKVdBb3VeIXExdq5M8uJ+apFg8sYA5x5wjLddg/SfjVmtGe         233rfBZytOc89UpncdCyjvbl7kRuZ3W+brE5Nxc2YR1fkwfqTEi3iRtUnUiGi4D3BWZL         tb1C+ez0+JuVOwQH+2tzG88bdiAyXZY3IsfjjC4ISE7iBlbkdDhJhdsZBfjpabd8zJfv         0gsO+vXOqXQVS6JWOZHIF/2d3lra8RAl/mTN+XXBxPiwlx2eb0PiS0j03lGRPWnmBYmP         7SsIfvnm6jUtawLMI5ZFtzjzi+REy/GL16P8pAdy1bY1wcOqUzV8kq6z4blEhsPSn2iu         vGBw=='},
                                          {'name': 'ARC-Message-Signature',
                                           'value': 'i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20160816;        h=date:message-id:in-reply-to:references:subject:auto-submitted:to         :from:dkim-signature:arc-authentication-results;        bh=BTEb4/AlKi7bBxSVxxrYsRr8c3wWDta5h3LomCC5c2w=;        b=Hv646VaFiDX4eAVww7IiorgArbf8c6pZPFG/JmgIfs8+CdPU2zQ3vYILmd2vtz9/HR         hQ/LshRYk8xFg74gkeh7p8DJZOepfx56JKae5bW94wfSJnDKYUOX73zzs3Zb7JI/Bp3X         F41Il/5QcbMg5FLYy6WQ0da7URB8YXfDxInzTDoU8phM2bgbKfEluIwmiqddABcq4dDA         8jdvC9eXMu2LX+PCxByB6WKFMQQbn04aW7VqfeVWNRYw05+Yn48VAp4Z1I3ennWwLTjl         aSpj76VcyCz7qIUGbvh62p9u+zNbZTa3Xm7tVCB/VWaqpnU40cP1pnxTqVdzmdRh1aJ4         zetg=='},
                                          {'name': 'ARC-Authentication-Results',
                                           'value': 'i=1; mx.google.com;       dkim=pass header.i=@googlemail.com header.s=20161025 header.b=BVHqLfAD;       spf=pass (google.com: best guess record for <NAME_EMAIL> designates ************* as permitted sender) smtp.helo=mail-sor-f69.google.com;       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=googlemail.com'},
                                          {'name': 'Return-Path', 'value': '<>'}, {'name': 'Received',
                                                                                       'value': 'from mail-sor-f69.google.com (mail-sor-f69.google.com. [*************])        by mx.google.com with SMTPS id t7-v6sor5995860wrp.50.2018.***********.21        for <<EMAIL>>        (Google Transport Security);        Tue, 10 Jul 2018 13:40:21 -0700 (PDT)'},
                                          {'name': 'Received-SPF',
                                           'value': 'pass (google.com: best guess record for <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;'},
                                          {'name': 'Authentication-Results',
                                           'value': 'mx.google.com;       dkim=pass header.i=@googlemail.com header.s=20161025 header.b=BVHqLfAD;       spf=pass (google.com: best guess record for <NAME_EMAIL> designates ************* as permitted sender) smtp.helo=mail-sor-f69.google.com;       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=googlemail.com'},
                                          {'name': 'DKIM-Signature',
                                           'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=googlemail.com; s=20161025;        h=from:to:auto-submitted:subject:references:in-reply-to:message-id         :date;        bh=BTEb4/AlKi7bBxSVxxrYsRr8c3wWDta5h3LomCC5c2w=;        b=BVHqLfADrbZjU86MZ5IEl7g6QZHrF7brRI3oYIHQzdpUH/sl6XLJWi1kDnLDChZ4Av         rFSBV9xa9Y2akutLbR/WDR6gxMeAYMnmPtX6a4gbfKyYLg9p5cvhH4u+iq8JXiH11Tcl         6VkoEpBO2WdQstASrs5+8eTs29dEhOAYfF4K8dsXjBIKfIVUqL1Y3fnTQwen+fMJrAjm         fglSzz1bs8uvqe3DgAVOQMuoDvPwxfxZ4bCQS4bAEqjx7hx/zg12c6HG5QEhvW8FYtfX         kWHKgR/HokU85pA/+08SjE+ntN9N8nq8Cas4dZRTccq1f7yY5E/2xrxKodTKwVEgMYNS         h3lA=='},
                                          {'name': 'X-Google-DKIM-Signature',
                                           'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20161025;        h=x-gm-message-state:from:to:auto-submitted:subject:references         :in-reply-to:message-id:date;        bh=BTEb4/AlKi7bBxSVxxrYsRr8c3wWDta5h3LomCC5c2w=;        b=aEuo/hQIjf3RUT3xcJHzFdu2gbXxu1r34cNoeOdPMaNBoeYwAyyyHh6cpC1qCJ70I7         qWj70AN3BHppoyFPZNMFnjeysKZ/exK4zJVE8KT4KecAMqAfW5oicweGD3OnsqRWT102         +sjnLfkPJ3cSFWBrcfppaFQ4Xp2r2BZUPcIS1jQ5pnHFS9geo9ZAnrQofXcNgR6u3Ay7         +UtbAgYUpEAv1c69lCzx4pVM6dFD51O+u7SJd+G9p1YNOc67CkkZudVifhbODXK7CaRb         d+Oug4O2QFqyIMvCAtgcsZ3AhWjXHNnWK8YUr6WOd1YFhtTHqAG82f0c2i9JdXQ7v3vF         Ezcg=='},
                                          {'name': 'X-Gm-Message-State',
                                           'value': 'APt69E1YbjlMtzxn8aGJ52RnGQVJIqSSxbLl1mF0hCg9bH0VnYylxHgQ BBatZo+WFDiCh9AcrAzXwqyjC+f34a2KUfWN2pE/qg=='},
                                          {'name': 'X-Google-Smtp-Source',
                                           'value': 'AAOMgpeL5IiTBFiHSJVzhazzOnFzLXs9dO5FkiHUVV7jQr/eY04wvJkBrcQyNVxuB4xr3I7FlYi4yK0o5cJDXn9lwzvuEdCWVwtjUwk='},
                                          {'name': 'X-Received',
                                           'value': 'by 2002:adf:a211:: with SMTP id p17-v6mr18581165wra.196.1531255221522;        Tue, 10 Jul 2018 13:40:21 -0700 (PDT)'},
                                          {'name': 'Content-Type',
                                           'value': 'multipart/report; boundary="0000000000008a09030570ab2305"; report-type=delivery-status'},
                                          {'name': 'Return-Path', 'value': '<>'}, {'name': 'Received',
                                                                                       'value': 'by 2002:adf:a211:: with SMTP id p17-v6mr20634434wra.196; Tue, 10 Jul 2018 13:40:21 -0700 (PDT)'},
                                          {'name': 'From', 'value': sender},
                                          {'name': 'To', 'value': receiver},
                                          {'name': 'Auto-Submitted', 'value': 'auto-replied'},
                                          {'name': 'Subject', 'value': subject},
                                          {'name': 'References',
                                           'value': '<CABJCDVSk_g8SC7u8g8S5=D=8OoMsu81UC3QVNE5c_Cis=<EMAIL>>'},
                                          {'name': 'In-Reply-To',
                                           'value': '<CABJCDVSk_g8SC7u8g8S5=D=8OoMsu81UC3QVNE5c_Cis=<EMAIL>>'},
                                          {'name': 'X-Failed-Recipients', 'value': '<EMAIL>'},
                                          {'name': 'Message-ID',
                                           'value': '<<EMAIL>>'},
                                          {'name': 'Date', 'value': datetime_string}],
                             'parts': [{'mimeType': 'multipart/related', 'body': {'size': 0}, 'partId': '0',
                                         'filename': '', 'headers': [{'name': 'Content-Type',
                                                                         'value': 'multipart/related; boundary="0000000000008a0d1d0570ab2306"'}],
                                         'parts': [{'mimeType': 'multipart/alternative', 'body': {'size': 0},
                                                     'partId': '0.0', 'filename': '', 'headers': [
                                                 {'name': 'Content-Type',
                                                  'value': 'multipart/alternative; boundary="0000000000008a0d240570ab2307"'}],
                                                     'parts': [{'mimeType': 'text/plain', 'headers': [
                                                         {'name': 'Content-Type',
                                                          'value': 'text/plain; charset="UTF-8"'}],

                                                                 'body': {'data': base64_body,
                                                                           'size': len(base64_body)},
                                                                 'partId': '0.0', 'filename': ''},
                                                                {'mimeType': 'text/html', 'headers': [
                                                                    {'name': 'Content-Type',
                                                                     'value': 'text/html; charset="UTF-8"'}],
                                                                 'body': {
                                                                     'data': 'DQo8aHRtbD4NCjxoZWFkPg0KPHN0eWxlPg0KKiB7DQpmb250LWZhbWlseTpSb2JvdG8sICJIZWx2ZXRpY2EgTmV1ZSIsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7DQp9DQo8L3N0eWxlPg0KPC9oZWFkPg0KPGJvZHk-DQo8dGFibGUgY2VsbHBhZGRpbmc9IjAiIGNlbGxzcGFjaW5nPSIwIiBjbGFzcz0iZW1haWwtd3JhcHBlciIgc3R5bGU9InBhZGRpbmctdG9wOjMycHg7YmFja2dyb3VuZC1jb2xvcjojZmZmZmZmOyI-PHRib2R5Pg0KPHRyPjx0ZD4NCjx0YWJsZSBjZWxscGFkZGluZz0wIGNlbGxzcGFjaW5nPTA-PHRib2R5Pg0KPHRyPjx0ZCBzdHlsZT0ibWF4LXdpZHRoOjU2MHB4O3BhZGRpbmc6MjRweCAyNHB4IDMycHg7YmFja2dyb3VuZC1jb2xvcjojZmFmYWZhO2JvcmRlcjoxcHggc29saWQgI2UwZTBlMDtib3JkZXItcmFkaXVzOjJweCI-DQo8aW1nIHN0eWxlPSJwYWRkaW5nOjAgMjRweCAxNnB4IDA7ZmxvYXQ6bGVmdCIgd2lkdGg9NzIgaGVpZ2h0PTcyIGFsdD0iRXJyb3IgSWNvbiIgc3JjPSJjaWQ6aWNvbi5wbmciPg0KPHRhYmxlIHN0eWxlPSJtaW4td2lkdGg6MjcycHg7cGFkZGluZy10b3A6OHB4Ij48dGJvZHk-DQo8dHI-PHRkPjxoMiBzdHlsZT0iZm9udC1zaXplOjIwcHg7Y29sb3I6IzIxMjEyMTtmb250LXdlaWdodDpib2xkO21hcmdpbjowIj4NCkFkZHJlc3Mgbm90IGZvdW5kDQo8L2gyPjwvdGQ-PC90cj4NCjx0cj48dGQgc3R5bGU9InBhZGRpbmctdG9wOjIwcHg7Y29sb3I6Izc1NzU3NTtmb250LXNpemU6MTZweDtmb250LXdlaWdodDpub3JtYWw7dGV4dC1hbGlnbjpsZWZ0Ij4NCllvdXIgbWVzc2FnZSB3YXNuJ3QgZGVsaXZlcmVkIHRvIDxhIHN0eWxlPSdjb2xvcjojMjEyMTIxO3RleHQtZGVjb3JhdGlvbjpub25lJz48Yj5raW9zY295dGVsQGhvdG1haWwuY29tPC9iPjwvYT4gYmVjYXVzZSB0aGUgYWRkcmVzcyBjb3VsZG4ndCBiZSBmb3VuZCwgb3IgaXMgdW5hYmxlIHRvIHJlY2VpdmUgbWFpbC4NCjwvdGQ-PC90cj4NCjwvdGJvZHk-PC90YWJsZT4NCjwvdGQ-PC90cj4NCjwvdGJvZHk-PC90YWJsZT4NCjwvdGQ-PC90cj4NCjx0ciBzdHlsZT0iYm9yZGVyOm5vbmU7YmFja2dyb3VuZC1jb2xvcjojZmZmO2ZvbnQtc2l6ZToxMi44cHg7d2lkdGg6OTAlIj4NCjx0ZCBhbGlnbj0ibGVmdCIgc3R5bGU9InBhZGRpbmc6NDhweCAxMHB4Ij4NClRoZSByZXNwb25zZSBmcm9tIHRoZSByZW1vdGUgc2VydmVyIHdhczo8YnIvPg0KPHAgc3R5bGU9ImZvbnQtZmFtaWx5Om1vbm9zcGFjZSI-DQo1NTAgNS41LjAgUmVxdWVzdGVkIGFjdGlvbiBub3QgdGFrZW46IG1haWxib3ggdW5hdmFpbGFibGUuIFtWRTFFVVIwMUZUMDQxLmVvcC1FVVIwMS5wcm9kLnByb3RlY3Rpb24ub3V0bG9vay5jb21dDQo8L3A-DQo8L3RkPg0KPC90cj4NCjwvdGJvZHk-PC90YWJsZT4NCjwvYm9keT4NCjwvaHRtbD4NCg==',
                                                                     'size': 1429}, 'partId': '0.0.1',
                                                                 'filename': ''}]}, {'mimeType': 'image/png',
                                                                                       'headers': [
                                                                                           {'name': 'Content-Type',
                                                                                            'value': 'image/png; name="icon.png"'},
                                                                                           {
                                                                                               'name': 'Content-Disposition',
                                                                                               'value': 'attachment; filename="icon.png"'},
                                                                                           {
                                                                                               'name': 'Content-Transfer-Encoding',
                                                                                               'value': 'base64'},
                                                                                           {'name': 'Content-ID',
                                                                                            'value': '<icon.png>'}],
                                                                                       'body': {
                                                                                           'attachmentId': 'ANGjdJ8E69rLNr-dOQDmyPq4bqOb0JijBel3pXDphCyk6vAal4SW_zOUGSWntLW05DM2es9gRdPZVqq3QzjCacm4cYpcxsy6jPlpeWn5_PUI6h9vb0Myhdbj3z-94xbTclRYAEOXhY3dNvkd3ifnRtB_olI0_CAIxP-grJpW9Sy6RoRTkK-XVSNy1ZPg5AI12heICIaxBiSw5xz8pgNj9GROegsqHFTKiO0GYr-EhKftfHkrzC6DqefRL0Lk1f0fAf_ZBa_faXRquYYt17jVZu27aBz6DQGkFXNyl0hrRiitd2qYHtBQUF-QhBvLSEpzi9rBmRvj27PrHEIl7WvpDm7RjQJubDWLEh3JT8ONl8X8rhEgRhAUlc-pNStmY2WRvvZUmE6-c8-vAoxxaahR',
                                                                                           'size': 1450},
                                                                                       'partId': '0.1',
                                                                                       'filename': 'icon.png'}]},
                                        {'mimeType': 'message/delivery-status', 'body': {'size': 0},
                                         'partId': '1', 'filename': '',
                                         'headers': [{'name': 'Content-Type', 'value': 'message/delivery-status'}],
                                         'parts': [{'mimeType': 'text/plain', 'headers': [
                                             {'name': 'Reporting-MTA', 'value': 'dns; googlemail.com'},
                                             {'name': 'Arrival-Date',
                                              'value': 'Tue, 10 Jul 2018 13:40:21 -0700 (PDT)'},
                                             {'name': 'X-Original-Message-ID',
                                              'value': '<CABJCDVSk_g8SC7u8g8S5=D=8OoMsu81UC3QVNE5c_Cis=<EMAIL>>'}],
                                                     'body': {
                                                         'data': '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
                                                         'size': 389}, 'partId': '1.0', 'filename': ''}]},
                                        {'mimeType': 'message/rfc822', 'body': {'size': 0}, 'partId': '2',
                                         'filename': '',
                                         'headers': [{'name': 'Content-Type', 'value': 'message/rfc822'}],
                                         'parts': [{'mimeType': 'multipart/alternative', 'body': {'size': 0},
                                                     'partId': '2.0', 'filename': '', 'headers': [
                                                 {'name': 'DKIM-Signature',
                                                  'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=mailface-com-ar.20150623.gappssmtp.com; s=20150623;        h=mime-version:from:date:message-id:subject:to;        bh=6KZabizuieM7dPES5ApLZC3i0B/d5NVPlY9aPaV/S58=;        b=mozOOLkCXdE1/DJ1Ssd6+UK13U/VW92/SSk+GXndypS2FpKiS/kLydYF3MbcOmcWs/         kHy5shN4Ga615KITtp3CNiYmcEnz/fnoR0hjkIfF1v92yx7WhC/rEVarRakZLsUkNX8L         NKbHrYlm7VKj/+6z9shSRJTXBXaOc5URyDRUQ80WOpoB17RebqzfTtcgk8Kwv9oc5Khl         CLEdwqLg9XJz+m4wJzs9WMHYVjeS1A8KDeYfeax+JDZsX+tVub6XBpEBFHQb1Ihqq8dY         Eh3cNayko0CDu+ToFvZYNzDHNL7QUXKh4ahvL9Krwcer6SaJ2VdBuKGcGdYHUZU62oWF         UrTA=='},
                                                 {'name': 'X-Google-DKIM-Signature',
                                                  'value': 'v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20161025;        h=x-gm-message-state:mime-version:from:date:message-id:subject:to;        bh=6KZabizuieM7dPES5ApLZC3i0B/d5NVPlY9aPaV/S58=;        b=rv6/H/WNu3z8jAfbBH7TjXhxMVx+vUaYBzeTArFhHVszOhkYBLS4lvTGRghHdgTllS         DiIbnGWfDsPZort42R0HtSjNpff/w7Pl7HVBwdbwWqOHAdBWa8Z3aWK5zpGUFsDKFPYS         uLgPcMTlc3MqHgSHGavLIgAx23+xpQOqmc1LB9QeUnq/kgxgUK11M563AKcqQ+jPo+EW         mepHOESegbEhvcp4AydQOdgUOrwDGhLgQBpllipyvCoFGEKaWKMTW92VMKDrJgLupGZ1         P4TNsAp2r3DaldMh74BZoNCqeAy8R/8CRP0pZ1c6dZZyiED2MOzwfdgzGgth3LNs5IcC         FAdA=='},
                                                 {'name': 'X-Gm-Message-State',
                                                  'value': 'APt69E3AgULpwlOrNIKGaYJr8E9DgwT4zXlS3TzeN1nNaRj7PaPX1wK5 KTd86tsleJHO2sJls98OncYQnt+w8wZ43nwE8fFZZLRB'},
                                                 {'name': 'X-Google-Smtp-Source',
                                                  'value': 'AAOMgpcLokHGWriUtFpFVhhcwGfmv3VT80SUzgNLnR9Orr+OIfu/oYhH3atne8+Wskl61rMDGbFT8uLH+mQMF+NBSXY='},
                                                 {'name': 'X-Received',
                                                  'value': 'by 2002:adf:a211:: with SMTP id p17-v6mr18581156wra.196.1531255221143; Tue, 10 Jul 2018 13:40:21 -0700 (PDT)'},
                                                 {'name': 'Received',
                                                  'value': 'from 579267720012 named unknown by gmailapi.google.com with HTTPREST; Tue, 10 Jul 2018 16:40:20 -0400'},
                                                 {'name': 'MIME-Version', 'value': '1.0'},
                                                 {'name': 'from', 'value': '<EMAIL>'},
                                                 {'name': 'Date', 'value': 'Tue, 10 Jul 2018 16:40:20 -0400'},
                                                 {'name': 'Message-ID',
                                                  'value': '<CABJCDVSk_g8SC7u8g8S5=D=8OoMsu81UC3QVNE5c_Cis=<EMAIL>>'},
                                                 {'name': 'Subject', 'value': 'propuesta'},
                                                 {'name': 'To', 'value': '<EMAIL>'},
                                                 {'name': 'Content-Type',
                                                  'value': 'multipart/alternative; boundary="0000000000008458e20570ab231c"'}],
                                                     'parts': [{'mimeType': 'text/plain', 'headers': [
                                                         {'name': 'Content-Type',
                                                          'value': 'text/plain; charset="UTF-8"'}], 'body': {
                                                         'data': 'YnVlbmFzIHRhcmRlcyAgbG8gbGxhbWUgcmVpdGVyYWRhcyB2ZWNlcyBwYXJhIGFzZXNvcnJhbG8gcG9yIGxhIGNvbnN1bHRhIHENCm5vcyBoaXpvIHBlcm8gbm8gbWUgcHVlZGUgY211bmljYXIuIGEgcXVlIGhvcmEgcG9kZW1vcyBjb21iaW5hciBxIHB1ZWRhDQpoYWJsYXIgYXNpIGxvIGxsYW1vPyBncmFjaWFzIGJ1ZW5hcyB0YXJkZXMNCg==',
                                                         'size': 193}, 'partId': '2.0.0', 'filename': ''},
                                                                {'mimeType': 'text/html', 'headers': [
                                                                    {'name': 'Content-Type',
                                                                     'value': 'text/html; charset="UTF-8"'},
                                                                    {'name': 'Content-Transfer-Encoding',
                                                                     'value': 'quoted-printable'}], 'body': {
                                                                    'data': 'YnVlbmFzIHRhcmRlc8KgIGxvIGxsYW1lIHJlaXRlcmFkYXMgdmVjZXMgcGFyYSBhc2Vzb3JyYWxvIHBvciBsYSBjb25zdWx0YSBxIG5vcyBoaXpvIHBlcm8gbm8gbWUgcHVlZGUgY211bmljYXIuIGEgcXVlIGhvcmEgcG9kZW1vcyBjb21iaW5hciBxIHB1ZWRhIGhhYmxhciBhc2kgbG8gbGxhbW8_IGdyYWNpYXMgYnVlbmFzIHRhcmRlcw0K',
                                                                    'size': 192}, 'partId': '2.0.1',
                                                                 'filename': ''}]}]}]},
                'snippet': 'Address not found Your message wasn&#39;t <NAME_EMAIL> because the address couldn&#39;t be found, or is unable to receive mail. The response from the remote server was: 550 5.5.0',
                'sizeEstimate': 13001, 'threadId': '16485ec6a852b176',
                'labelIds': ['UNREAD', 'CATEGORY_PERSONAL', 'INBOX'], 'id': '16485ec6ee57da87'}

    def test_full_format_with_parts(self):
        message_parser = MessageFormatParser.named('full')
        subject = 'Re: testing'
        sender = '<EMAIL>'
        receiver = '<EMAIL>'
        datetime_string = 'Tue, 23 Jan 2018 22:04:18 -0300'
        body = 'probando\r\n\r\nOn Tue, Jan 23, 2018 at 9:54 PM, <<EMAIL>>' \
               ' wrote:\r\n\r\n> hello world\r\n>\r\n'
        example = self._full_format_message_for(subject, sender, receiver, datetime_string, body)
        message = message_parser.parse(example)
        self.assertEqual(message.id(), example['id'])
        self.assertEqual(message.history_id(), example['historyId'])
        self.assertEqual(message.thread_id(), example['threadId'])
        self.assertEqual(message.subject(), subject)
        self.assertEqual(message.sender(), sender)
        self.assertEqual(message.receiver(), receiver)
        self.assertEqual(message.date_time().strftime(MessageFormatParser.datetime_formatter()), datetime_string)

        self.assertEqual(message.body(), body)

    def test_full_format_without_parts(self):
        message_parser = MessageFormatParser.named('full')
        subject = 'Re: testing'
        sender = '<EMAIL>'
        receiver = '<EMAIL>'
        datetime_string = 'Tue, 23 Jan 2018 22:04:18 -0300'
        #envia un str
        body = 'probando\r\n\r\nOn Tue, Jan 23, 2018 at 9:54 PM, <<EMAIL>>' \
               ' wrote:\r\n\r\n> hello world\r\n>\r\n'
        example = self._full_format_message_without_parts_for(subject, sender, receiver, datetime_string, body)
        message = message_parser.parse(example)
        self.assertEqual(message.id(), example['id'])
        self.assertEqual(message.history_id(), example['historyId'])
        self.assertEqual(message.thread_id(), example['threadId'])
        self.assertEqual(message.subject(), subject)
        self.assertEqual(message.sender(), sender)
        self.assertEqual(message.receiver(), receiver)
        self.assertEqual(message.date_time().strftime(MessageFormatParser.datetime_formatter()), datetime_string)
        self.assertEqual(message.body(), body)

    def test_full_format_without_receiver(self):
        message_parser = MessageFormatParser.named('full')
        example = self._full_format_message_without_subject()
        self.assertRaises(InvalidFormatException, message_parser.parse, message_json=example)

    def test_full_format_raro(self):
        message_parser = MessageFormatParser.named('full')
        subject = 'Re: testing'
        body = '\r\n\r\nEnviado desde Personal LG Spirit LTE\n\r'
        sender = '<EMAIL>'
        receiver = '<EMAIL>'
        datetime_string = 'Thu, 19 Apr 2018 21:07:06 +0000'
        example = self._full_format_with_multipart(subject, sender, receiver, datetime_string, body)
        message = message_parser.parse(example)
        self.assertEqual(message.id(), example['id'])
        self.assertEqual(message.history_id(), example['historyId'])
        self.assertEqual(message.thread_id(), example['threadId'])
        self.assertEqual(message.subject(), subject)
        self.assertEqual(message.sender(), sender)
        self.assertEqual(message.receiver(), receiver)
        self.assertEqual(message.date_time().strftime(MessageFormatParser.datetime_formatter()), datetime_string)
        self.assertEqual(message.body(), body)

    def test_full_format_with_multipart_related(self):
        message_parser = MessageFormatParser.named('full')
        subject = 'Re: testing'
        body = '\r\n\r\nEnviado desde Personal LG Spirit LTE\n\r'
        sender = '<EMAIL>'
        receiver = '<EMAIL>'
        datetime_string = 'Tue, 10 Jul 2018 13:40:21 -0700'
        datetime_string_pdt = datetime_string + ' (PDT)'
        example = self._full_format_with_multipart_related(subject, sender, receiver, datetime_string_pdt, body)
        message = message_parser.parse(example)
        self.assertEqual(message.id(), example['id'])
        self.assertEqual(message.history_id(), example['historyId'])
        self.assertEqual(message.thread_id(), example['threadId'])
        self.assertEqual(message.subject(), subject)
        self.assertEqual(message.sender(), sender)
        self.assertEqual(message.receiver(), receiver)
        self.assertEqual(message.date_time().strftime(MessageFormatParser.datetime_formatter()), datetime_string)
        self.assertEqual(message.body(), body)


class GMailHeaderFormatterTest(TestCase):
    def test_sender_formatter(self):
        receiver = '<EMAIL>'
        header = {'name': 'From', 'value': '"Burella Juan M." <%s>' % receiver}
        headers = {}
        formatter = GMailHeaderFormatter.for_this(header)
        formatter.add_header_to(header, headers)
        self.assertEqual(headers['Sender'], '<EMAIL>')

    def test_sender_formatter_without_name(self):
        receiver = '<EMAIL>'
        header = {'name': 'From', 'value': '%s' % receiver}
        headers = {}
        formatter = GMailHeaderFormatter.for_this(header)
        formatter.add_header_to(header, headers)
        self.assertEqual(headers['Sender'], '<EMAIL>')

    def test_receiver_formatter(self):
        sender = '<EMAIL>'
        header = {'name': 'To', 'value': sender}
        headers = {}
        formatter = GMailHeaderFormatter.for_this(header)
        formatter.add_header_to(header, headers)
        self.assertEqual(headers['Receiver'], sender)

    def test_datetime_formatter(self):
        datetime_string = 'Tue, 23 Jan 2018 22:04:18 -0300'
        header = {'name': 'Date', 'value': datetime_string}
        headers = {}
        formatter = GMailHeaderFormatter.for_this(header)
        formatter.add_header_to(header, headers)
        self.assertEqual(headers['Datetime'].strftime(MessageFormatParser.datetime_formatter()), datetime_string)

    def test_datetime_formatter_with_greenwich_mean_time(self):
        datetime_string = 'Wed, 11 Jul 2018 15:31:34 +0000'
        datetime_string_with_gmt = datetime_string + ' (GMT-00:00)'
        header = {'name': 'Date', 'value': datetime_string_with_gmt}
        headers = {}
        formatter = GMailHeaderFormatter.for_this(header)
        formatter.add_header_to(header, headers)
        self.assertEqual(headers['Datetime'].strftime(MessageFormatParser.datetime_formatter()), datetime_string)

    def test_subject_formatter(self):
        subject = 'Re: testing'
        header = {'name': 'Subject', 'value': subject}
        headers = {}
        formatter = GMailHeaderFormatter.for_this(header)
        formatter.add_header_to(header, headers)
        self.assertEqual(headers['Subject'], subject)
