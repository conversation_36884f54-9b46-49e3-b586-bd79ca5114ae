# -*- coding: utf-8 -*-
import errno
import os
import re

from django.conf import settings
from django.core.files import File
from django.db import transaction
from django.utils.timezone import now
from rest_framework_xml.renderers import XMLRenderer

from layers.application.commands.ingreso_de_prospectos.puesto_de_ingreso_de_prospectos import \
    PuestoDeIngresoDeProspectos
from occ.servicio_de_chat_de_ventas import ServicioDeChatDeVentas
from prospectos import tasks
from prospectos.aplicacion.commands.tarjeta_de_credito import AgregarTarjetaDeCreditoAProspecto
from prospectos.models import Proveedor, SubidaErronea, Prospecto
from prospectos.models.carga import CargadorDeProspectos
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.models.exceptions import ProspectoRepetidoException, LoteDeProspectosRechazadoException
from prospectos.models.origen import OrigenDeProspectoAPI


class ApiProspectoCreator(object):

    def __init__(self):
        super(ApiProspectoCreator, self).__init__()
        self._administrador_de_pedidos = AdministradorDePedidos()
        self._puesto_de_ingreso = PuestoDeIngresoDeProspectos.nuevo()
        # Los nombres de campos vienen con _ algunos, asi que la regex le quita esos __.
        self._nombres_de_campos_del_prospecto = [re.sub('^[^A-Za-z]*', '', campo.name) for campo in Prospecto._meta.get_fields()]

    def create_prospectos_desde(self, ejecutor, cd):
        campania = cd['campania']
        campos_generales = {'campania': campania, 'proveniente_de_chat': cd.get('proveniente_de_chat', False)}
        if cd['responsable']:
            campos_generales['responsable'] = cd['responsable']
        if cd['vendedor']:
            campos_generales['vendedor'] = cd['vendedor']
        if cd['source']:
            source_id = cd['source'].strip()
            if source_id:
                proveedor, nuevo = Proveedor.objects.get_or_create(source_id=source_id)
                campos_generales['proveedor'] = proveedor

        prospectos = cd.pop('prospectos')
        resultado = self._pedir_acceso_e_ingresar_prospectos_desde(prospectos, campos_generales, campania.origen)
        self._completar_informacion_desde_servicios_para(resultado.ids_prospecto_exitosos())
        self._verificar_procedencia_de_chat(resultado.datos_de_prospectos_provenientes_de_chat())
        return self._generar_respuesta_desde(cd, ejecutor, resultado)

    def _pedir_acceso_e_ingresar_prospectos_desde(self, datos_de_prospectos, campos_generales, origen):
        try:
            resultado = self._puesto_de_ingreso.ingresar(
                datos_de_prospectos,
                metodo_constructor=self._crear_prospectos_desde,
                argumentos=[datos_de_prospectos, campos_generales, origen])
        except LoteDeProspectosRechazadoException as exc:
            resultado = ResultadoDeCargaDeProspectosViaAPI.lote_fallido(datos_de_prospectos, mensaje_de_error=str(exc))

        return resultado

    @transaction.atomic
    def _crear_prospectos_desde(self, datos_de_prospectos, campos_generales, origen):
        resultado = ResultadoDeCargaDeProspectosViaAPI.nuevo()
        filas = 0
        for prospecto in datos_de_prospectos:
            if not prospecto.__class__ == dict:
                continue
            filas += 1
            fallo, datos_extra = self._obtener_columnas_extras(prospecto)
            if not fallo:
                self._crear_prospecto(prospecto, datos_extra, campos_generales, origen, filas - 1, resultado)
        resultado.registrar_filas(filas)
        return resultado

    def _crear_prospecto(self, prospecto, datos_extra, campos_generales, origen, indice, resultado):
        proveniente_de_chat = campos_generales.get('proveniente_de_chat', False)
        token_de_chat = self._extraer_token_de_chat_y_remover_de_campos_extra(datos_extra)
        
        self._mover_ip_de_datos_extra(datos_extra, prospecto)
        marca_de_la_tarjeta = self._extraer_marca_de_tarjeta_de_credito_y_remover_de_campos_extra(datos_extra)
        cargador = CargadorDeProspectos()
        try:
            resultado_de_carga = cargador.cargar_prospecto(
                datos_generales=campos_generales,
                datos_de_prospecto=prospecto,
                datos_extra=datos_extra,
                codigo_de_tipo_de_origen=origen,
                origen_de_prospecto=OrigenDeProspectoAPI(),
                proveniente_de_chat=proveniente_de_chat)
        except ProspectoRepetidoException:
            resultado.registrar_repetido(indice, prospecto)
        else:
            if resultado_de_carga.es_erroneo():
                resultado.registrar_falla(indice, prospecto, mensaje_de_error=resultado_de_carga.mensaje_de_error())
            else:
                nuevo_prospecto = resultado_de_carga.prospecto()
                if not nuevo_prospecto.responsable:
                    self._administrador_de_pedidos.asignar_prospecto_automaticamente(nuevo_prospecto)
                else:
                    if not resultado_de_carga.fue_mergeado():
                        self._administrador_de_pedidos.contabilizar_carga_de_prospecto_con_responsable(nuevo_prospecto)
                    if proveniente_de_chat:
                        resultado.registrar_prospecto_proveniente_de_chat(nuevo_prospecto, token_de_chat)
                self._agregar_tarjeta_de_credito_al_prospecto(nuevo_prospecto, marca_de_la_tarjeta)
                resultado.registrar_id_prospecto_exitosos(nuevo_prospecto.pk)

    def _agregar_tarjeta_de_credito_al_prospecto(self, prospecto, marca_de_tarjeta):
        if marca_de_tarjeta is not None:
            comando = AgregarTarjetaDeCreditoAProspecto()
            arguments = {
                'id_prospecto': prospecto.pk,
                'marcas_de_las_tarjetas': [marca_de_tarjeta]
            }
            comando.set_arguments(arguments)
            resultado = comando.execute()
            return resultado

    def _verificar_procedencia_de_chat(self, datos_de_prospectos_provenientes_de_chat):
        servicio_de_chat = ServicioDeChatDeVentas()
        for dato in datos_de_prospectos_provenientes_de_chat:
            servicio_de_chat.generar_chat_convertido_para_prospecto(dato['prospecto'], dato['token_de_chat'])

    def _obtener_columnas_extras(self, prospecto):
        fallo, columnas_extra = self._obtener_columnas_extras_dentro_de_elemento_extra(
            prospecto, fallo=False, columnas_extra=[])
        # Nueva forma: campos que no apliquen al prospecto. Lista de tuplas (nombre,valor)
        columnas_extra = self._obtener_columnas_extras_desde_campos_adicionales_del_prospecto(prospecto, columnas_extra)
        return fallo, columnas_extra

    def _obtener_columnas_extras_dentro_de_elemento_extra(self, prospecto, fallo, columnas_extra):
        # Vieja forma de revisar datos extra: Revisar si tienen la palabra 'extra'.
        if 'extra' in prospecto:
            if prospecto['extra'].__class__ == list:
                extras = prospecto['extra']
                for extra in extras:
                    if extra.__class__ == dict and 'nombre' in extra and 'valor' in extra:
                        columnas_extra.append(extra)
                    else:
                        fallo = True
            else:
                fallo = True
        return fallo, columnas_extra

    def _obtener_columnas_extras_desde_campos_adicionales_del_prospecto(self, prospecto, columnas_extra):
        for nombre_de_campo, valor in list(prospecto.items()):
            if self._es_un_campo_extra(nombre_de_campo, valor):
                columnas_extra.append({'nombre': nombre_de_campo, 'valor': valor})
        return columnas_extra

    def _completar_informacion_desde_servicios_para(self, lista_de_id_de_prospectos):
        for prospecto_id in lista_de_id_de_prospectos:
            tasks.completar_informacion_de_telefonos_de_prospecto.delay(prospecto_id)
            tasks.completar_informacion_de_geolocalizacion_desde_ip_de_prospecto.delay(prospecto_id)

    def _generar_respuesta_desde(self, cd, ejecutor, resultado):
        response_data = dict()
        response_data['exitosos'] = str(resultado.cantidad_filas_exitosas())
        response_data['repetidos-ignorados'] = str(resultado.cantidad_filas_repetidas())
        response_data['filas'] = str(resultado.filas())
        if resultado.tiene_filas_erroneas():
            log = self._loggear_error(cd, ejecutor, resultado)
            from django.urls import reverse
            log_url = reverse('admin:prospectos_subidaerronea_change', args=(log.id,))
            response_data['log'] = log_url
            response_data['filas_erroneas'] = str(resultado.detalle_prospectos_erroneos())
        return response_data

    def _loggear_error(self, data, ejecutor, resultado):
        log_data = data.copy()
        exitosos = resultado.cantidad_filas_exitosas()
        log_data['prospectos'] = resultado.prospectos_erroneos()
        fallidas = resultado.cantidad_filas_erroneas()
        origen = data['origen']
        campania = data['campania']
        responsable = data['responsable']
        vendedor = data['vendedor']
        xml_renderer = XMLRenderer()
        try:
            filename = self._nombre_de_archivo()
            with open(filename, 'w') as file_erroneas:
                file_erroneas.write(xml_renderer.render(log_data))
            with open(filename, 'r') as f:
                log_erroneas = File(f)
                log = SubidaErronea.guardar_log(log_erroneas, ejecutor, fallidas, exitosos,
                                                origen, campania, responsable, vendedor)
            os.remove(filename)
        except EnvironmentError:  # parent of IOError, OSError *and* WindowsError where available
            raise
        return log

    def _nombre_de_archivo(self):
        path = os.path.join(settings.MEDIA_ROOT, 'erroneas')
        try:
            if not os.path.exists(path):
                os.makedirs(path)
        except OSError as exception:
            if exception.errno != errno.EEXIST:
                raise
        filename = os.path.join(path, now().strftime('api-log-%Y-%m-%d-%H-%M-%S.xml'))
        return filename

    def _mover_ip_de_datos_extra(self, datos_extra, prospecto):
        if 'ip' in prospecto:
            return
        for dato_extra in datos_extra:
            if dato_extra.get('nombre').lower() == 'ip':
                prospecto['ip'] = dato_extra.get('valor')
                return

    def _es_un_campo_extra(self, nombre_de_campo, valor):
        return self._campo_no_existe_en_el_prospecto(nombre_de_campo) and valor != 'skip' and nombre_de_campo != 'extra'

    def _campo_no_existe_en_el_prospecto(self, nombre_de_campo):
        return nombre_de_campo not in self._nombres_de_campos_del_prospecto

    def _extraer_marca_de_tarjeta_de_credito_y_remover_de_campos_extra(self, datos_extra):
        valor = self._obtener_valor_y_remover_campos_extra('tarjeta', datos_extra)
        if valor:
            return valor.upper()
        else:
            return valor

    def _extraer_token_de_chat_y_remover_de_campos_extra(self, datos_extra):
        return self._obtener_valor_y_remover_campos_extra('Operator.Token', datos_extra)

    def _obtener_valor_y_remover_campos_extra(self, nombre_de_campo, datos_extra): 
        for index, dato_extra in enumerate(datos_extra):
            try:
                nombre = dato_extra['nombre']
                if nombre == nombre_de_campo:
                    valor = dato_extra['valor']
                    datos_extra.pop(index)
                    return valor
            except KeyError:
                return None


class ResultadoDeCargaDeProspectosViaAPI(object):
    def __init__(self):
        super(ResultadoDeCargaDeProspectosViaAPI, self).__init__()
        self._filas_erroneas = {}
        self._errores = {}
        self._filas_de_repetidos = {}
        self._ids_prospecto_exitosos = []
        self._datos_de_prospectos_provenientes_de_chat = []
        self._filas = 0

    @classmethod
    def nuevo(cls):
        return cls()

    @classmethod
    def lote_fallido(cls, prospectos, mensaje_de_error):
        resultado = cls()
        for indice, prospecto in enumerate(prospectos):
            resultado.registrar_falla(indice, prospecto, mensaje_de_error)
        return resultado

    def registrar_falla(self, indice, prospecto, mensaje_de_error=None):
        self._filas_erroneas[indice] = prospecto
        if mensaje_de_error:
            self._errores[indice] = mensaje_de_error

    def registrar_repetido(self, indice, prospecto):
        self._filas_de_repetidos[indice] = prospecto

    def registrar_id_prospecto_exitosos(self, id_prospecto):
        self._ids_prospecto_exitosos.append(id_prospecto)
        
    def registrar_prospecto_proveniente_de_chat(self, prospecto, token_de_chat):
        self._datos_de_prospectos_provenientes_de_chat.append({'prospecto': prospecto, 'token_de_chat': token_de_chat})

    def tiene_filas_erroneas(self):
        return self.cantidad_filas_erroneas() > 0

    def cantidad_filas_erroneas(self):
        return len(self._filas_erroneas)

    def cantidad_filas_repetidas(self):
        return len(self._filas_de_repetidos)

    def cantidad_filas_exitosas(self):
        return len(self._ids_prospecto_exitosos)

    def filas_erroneas(self):
        return self._filas_erroneas

    def ids_prospecto_exitosos(self):
        return self._ids_prospecto_exitosos

    def datos_de_prospectos_provenientes_de_chat(self):
        return self._datos_de_prospectos_provenientes_de_chat

    def prospectos_erroneos(self):
        return list(self._filas_erroneas.values())

    def indeices_erroneos(self):
        return list(self._filas_erroneas.keys())

    def registrar_filas(self, filas):
        self._filas = filas

    def filas(self):
        return self._filas

    def errores(self):
        return self._errores

    def detalle_prospectos_erroneos(self):
        detalles = []
        for key in list(self._filas_erroneas.keys()):
            fila = self._filas_erroneas[key]
            errores = self._errores.get(key)
            if errores:
                fila['errores'] = errores
            detalles.append(fila)
        return detalles
