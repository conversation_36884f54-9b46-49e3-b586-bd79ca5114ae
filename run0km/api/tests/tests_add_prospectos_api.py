#-*- coding: utf-8 -*-

from unittest import skipIf

import mock
from django.conf import settings
from django.test.utils import override_settings
from django.utils import timezone
from freezegun import freeze_time

from api.tests.validador import ValidadorDeIngresoDeProspectoViaAPI
from lib.client_geolocation.tests import RequestGeolocationMock
from core.tests.clients.proveedor_de_prospectos import ProveedorProspectosViaAPI
from core.tests.locker_helper import <PERSON>r<PERSON>elper
from prospectos.models import Prospecto, PedidoDeProspecto, MarcaDeTarjetaDeCredito, TarjetaDeProspecto
from testing.base import BaseFixturedTest
from testing.factories import PedidosDeProspectoFactory
from testing.test_utils import PedidoDeCreacionXML


@override_settings(REALIZAR_NORMALIZACION=False)
@override_settings(REALIZAR_CHECKEO_DE_WHATSAPP=False)
class ApiAddProspectosTest(BaseFixturedTest):
    """
        Refactor a mitad de camino, falta terminar para que todos estos tests usen el validador y el proveedor
    """

    def setUp(self):
        super(ApiAddProspectosTest, self).setUp()
        self._locker_helper = LockerHelper()
        self.validador = ValidadorDeIngresoDeProspectoViaAPI.new_for(self)
        self.proveedor_prospectos_via_api = ProveedorProspectosViaAPI.nuevo_con_usuario_por_defecto(
            creador_de_contexto=self.creador_de_contexto, http_client=self.client)
        self.campania_de_sms = self.fixture['camp_1']
        self.origen_mailing = self.fixture['tipo_m']
        self.supervisor_uno = self.fixture['sup_1']
        self.vendedor_de_supervisor_uno = self.fixture['vend_1']
        self.datos_por_defecto = PedidoDeCreacionXML().desde_api_con_formato_simple()
        self.creador_de_contexto.crear_marcas_de_tarjetas_de_credito()

    def test_pedido_debe_agregar_prospecto_con_datos_extra(self):

        # Dado
        campos_extra_a_revisar = {'tipocompra': 'true', 'marcausado': 'fiat', 'modelousado': 'linea',
                                  'anticipo': '15000', 'cuotaestimada': '7000', 'dni': '38.598.684',
                                  'modelo': 'ka'}
        nombre = 'Nora llesqueso'
        telefono = '12321232'
        extras_adicionales = {'extra1': 'extra'}

        # Cuando
        response = self.proveedor_prospectos_via_api.ingresar_con(
            supervisor=self.supervisor_uno, vendedor=self.vendedor_de_supervisor_uno, nombre=nombre,
            telefono=telefono, email='', tarjeta='visa', camposalteable='skip', campos_extras=extras_adicionales,
            **campos_extra_a_revisar)

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        self.validador.assert_existe_prospecto_con(nombre=nombre, telefono=telefono)

        prospecto = Prospecto.objects.con_nombre(nombre=nombre)
        self._assert_campos_extra_agregados(prospecto, dict(list(campos_extra_a_revisar.items())+list(extras_adicionales.items())))
        self._assert_tarjeta_agregada(prospecto, 'VISA')
        self._assert_campos_no_agregados(prospecto, [('camposalteable', 'skip')])

    def test_pedido_debe_agregar_prospecto_y_guardar_el_telefono_sin_normalizar(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen='', campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212'}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 1', telefono='12121212', campania=self.campania_de_sms)
        prospecto = Prospecto.objects.con_nombre(nombre='Nombre Prospecto 1')
        self.assertEqual(prospecto.obtener_telefono_sin_normalizar(), '12121212')

    def test_pedido_sin_origen_debe_agregar_prospectos(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen='', campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212'},
                {'nombre': 'Nombre Prospecto 2', 'telefono': '13131313'}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=2, exitosas=2)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 1', telefono='12121212', campania=self.campania_de_sms)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 2', telefono='13131313', campania=self.campania_de_sms)

    def test_pedido_con_origen_y_campania_validos_debe_agregar_prospectos(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen=self.campania_de_sms.origen, campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212'},
                {'nombre': 'Nombre Prospecto 2', 'telefono': '13131313'}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=2, exitosas=2)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 1', telefono='12121212', campania=self.campania_de_sms)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 2', telefono='13131313', campania=self.campania_de_sms)

    def test_pedido_con_origen_invalido_para_la_campania_debe_responder_error_y_no_agregar_ningun_prospecto(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen=self.origen_mailing.codigo, campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212'},
                {'nombre': 'Nombre Prospecto 2', 'telefono': '13131313'}])

        # Entonces
        self.validador.assert_respuesta_fallida(
            response=response, mensaje='No existe una campaña con el nombre y origen indicados')
        self.validador.assert_no_existe_prospecto_con_nombre(nombre='Nombre Prospecto 1')
        self.validador.assert_no_existe_prospecto_con_nombre(nombre='Nombre Prospecto 2')

    def test_pedido_sin_telefonos_debe_agregar_prospectos(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen=self.campania_de_sms.origen, campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '', 'email': '<EMAIL>'}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 1', email='<EMAIL>', campania=self.campania_de_sms)

    def test_ingresos_debe_contabiliza_asignacion_en_pedido_de_prospecto(self):
        # TODO: mover a un caso de test a prospectos/distribucion/pedidos
        # Dado
        hoy = timezone.now().date()
        p1 = PedidosDeProspectoFactory(supervisor=self.fixture['sup_1'], credito=10, yapa=5, consumido=2,
                                       asignar_a='V', fecha=hoy, vendedor=self.fixture['vend_2'])
        p2 = PedidosDeProspectoFactory(supervisor=self.fixture['sup_2'], credito=100, yapa=5, consumido=0,
                                       asignar_a='V', fecha=hoy, vendedor=self.fixture['vend_3'])
        p1.cambiar_calidades_por(calidades=[self.fixture['tipo_s'], ])
        p2.cambiar_calidades_por(calidades=[self.fixture['tipo_s'], ])

        # Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen=self.campania_de_sms.origen, campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212',
                 'responsable': p1.supervisor.username(), 'vendedor': self.fixture['vend_1'].username()},
                {'nombre': 'Nombre Prospecto 2', 'telefono': '13131313'}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=2, exitosas=2)

        p1 = PedidoDeProspecto.objects.get(id=p1.id)
        p2 = PedidoDeProspecto.objects.get(id=p2.id)

        # encolados = ColaDeProspectosACompletarInfoDeTelefonos().encolados()
        # self.assertEqual(2, encolados.count())
        pro1 = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self.assertEqual(pro1.responsable, p1.supervisor)
        self.assertEqual(pro1.vendedor, self.fixture['vend_1'])
        self.assertEqual(pro1.pedido, p1)
        self.assertEqual(pro1.consumido_en_pedido, pro1.campania.categoria.valor)
        self.assertEqual(p1.consumido, 3)
        # self.assertEqual(1, encolados.filter(prospecto_id=pro1.id).count())

        pro2 = Prospecto.objects.get(nombre="Nombre Prospecto 2")
        self.assertEqual(pro2.responsable, p2.supervisor)
        self.assertEqual(pro2.vendedor, self.fixture['vend_3'])
        self.assertEqual(pro2.pedido, p2)
        self.assertEqual(pro2.consumido_en_pedido, pro2.campania.categoria.valor)
        self.assertEqual(p2.consumido, 1)
        # self.assertEqual(1, encolados.filter(prospecto_id=pro2.id).count())

    @freeze_time("2016-07-10 13:21:34")
    def test_pedido_sin_responsable_debe_agregar_prospecto_con_responsable_del_vendedor(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen=self.campania_de_sms.origen, campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212',
                 'responsable': '', 'vendedor': self.fixture['vend_1'].username()}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        prospecto_uno = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self.assertEqual(prospecto_uno.vendedor, self.vendedor_de_supervisor_uno)
        self.assertEqual(prospecto_uno.responsable, self.supervisor_uno)
        ahora = timezone.now()
        self.assertEqual(prospecto_uno.fecha_de_asignacion_a_supervisor(), ahora)
        self.assertEqual(prospecto_uno.fecha_de_asignacion_a_vendedor(), ahora)

    @freeze_time("2016-07-10 13:21:34")
    def test_pedido_con_responsable_sin_vendedor_debe_tener_fecha_de_asignacion_de_responsable(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen=self.campania_de_sms.origen, campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212',
                 'responsable': self.supervisor_uno.username(), 'vendedor': ''}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        prospecto_uno = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self.assertEqual(prospecto_uno.responsable, self.supervisor_uno)
        ahora = timezone.now()
        self.assertEqual(prospecto_uno.fecha_de_asignacion_a_supervisor(), ahora)
        self.assertIsNone(prospecto_uno.vendedor)
        self.assertIsNone(prospecto_uno.fecha_de_asignacion_a_vendedor())

    def test_pedido_sin_responsable_debe_quedar_sin_asignacion(self):
        # Dado / Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen=self.campania_de_sms.origen, campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212', 'supervisor': '', 'vendedor': ''}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        prospecto_uno = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self.assertIsNone(prospecto_uno.vendedor)
        self.assertIsNone(prospecto_uno.responsable)
        self.assertIsNone(prospecto_uno.fecha_de_asignacion_a_supervisor())
        self.assertIsNone(prospecto_uno.fecha_de_asignacion_a_vendedor())

    # Geolocation
    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_ip_debe_configurar_geolocalizacion(self, send_request_mock):
        # Given / When
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen='', campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212', 'ip': '***********'}])

        # Then
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        pro1 = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self.assert_geolocalizacion(prospecto=pro1, ip='***********', latitud=-34.7203, localidad='Quilmes',
                                    longitud=-58.2694, provincia='Buenos Aires')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_pedidio_con_campo_provincia_localidad_desde_ip_invalido_no_debe_ser_configurado(self, send_request_mock):
        # Given / When
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen='', campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212', 'ip': 'lol'}])

        # Then
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        pro1 = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self.assertEqual(pro1.obtener_ip(), 'lol')
        self.assertEqual(pro1.obtener_localidad(), '')
        self.assertEqual(pro1.obtener_provincia(), '')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.http_404_not_found())
    def test_pedido_con_campo_provincia_localidad_desde_ip_no_es_configurado_cuando_falla_el_servicio(
            self, send_request_mock):
        # Given / When
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen='', campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212', 'ip': 'lol'}])

        # Then
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        pro1 = Prospecto.objects.get(nombre="Nombre Prospecto 1")
        self.assertEqual(pro1.obtener_ip(), 'lol')
        self.assertEqual(pro1.obtener_localidad(), '')
        self.assertEqual(pro1.obtener_provincia(), '')

    # Lock de datos de contacto
    @skipIf(True, reason='Deploy incompleto')
    @mock.patch('core.locker.mem_locker.Locker.do_locking')
    def test_pedido_con_datos_de_contexto_bloqueados_debe_responder_error(self, mock_lock_resource):
        # Dado
        self._locker_helper.configurar_locker_lanzar_recurso_bloqueado(mock_lock_resource)

        # Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen='', campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212'},
                {'nombre': 'Nombre Prospecto 2', 'telefono': '13131313'}])

        # Entonces
        self.validador.assert_respuesta_fallida(
            response=response, mensaje='Los datos de contacto estan momentaneamente bloqueados.')
        self.validador.assert_no_existe_prospecto_con_nombre(nombre='Nombre Prospecto 1')
        self.validador.assert_no_existe_prospecto_con_nombre(nombre='Nombre Prospecto 2')

    @mock.patch('core.locker.mem_locker.Locker.lock_resource')
    def test_al_tercer_intento_carga_desbloqueada_debe_ingresar_los_prospectos(self, mock_lock_resource):
        # Dado
        self._locker_con_tercer_reintento_exitoso(mock_lock_resource)

        # Cuando
        response = self.proveedor_prospectos_via_api.ingresar_prospectos(
            origen='', campania=self.campania_de_sms.nombre, info_de_prospectos=[
                {'nombre': 'Nombre Prospecto 1', 'telefono': '12121212'},
                {'nombre': 'Nombre Prospecto 2', 'telefono': '13131313'}])

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=2, exitosas=2)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 1', telefono='12121212', campania=self.campania_de_sms)
        self.validador.assert_existe_prospecto_con(
            nombre='Nombre Prospecto 2', telefono='13131313', campania=self.campania_de_sms)

    @freeze_time("2016-07-10 13:21:34")
    def test_pedido_con_llamado_sugerido_debe_agregar_prospecto_con_llamado_programado(self):
        # Dado
        nombre = 'Nora llesqueso'
        telefono = '12321232'
        fecha_reunion_a_programar_string = '2016-07-11 13:55'

        # Cuando
        response = self.proveedor_prospectos_via_api.ingresar_con(
            supervisor=self.supervisor_uno, vendedor=self.vendedor_de_supervisor_uno, nombre=nombre,
            telefono=telefono, email='', debe_programar_reunion=True,
            fecha_reunion_a_programar=fecha_reunion_a_programar_string)

        # Entonces
        self.validador.assert_respuesta_exitosa(response, filas_leida=1, exitosas=1)
        self.validador.assert_existe_prospecto_con(nombre=nombre, telefono=telefono)
        prospecto_agregado = Prospecto.objects.get(nombre=nombre)
        self.assertTrue(prospecto_agregado.tiene_llamado())
        llamado = prospecto_agregado.llamado
        self.assertEqual(timezone.localtime(llamado.obtener_fecha()).strftime(settings.API_FORMATO_FECHA_Y_HORA),
                         fecha_reunion_a_programar_string)

    #  Asserts
    def assert_geolocalizacion(self, prospecto, ip, latitud, localidad, longitud, provincia):
        geolocalizacion = prospecto.obtener_geolocalizacion()
        self.assertEqual(geolocalizacion.ip, ip)
        self.assertEqual(geolocalizacion.latitud, latitud)
        self.assertEqual(geolocalizacion.localidad, localidad)
        self.assertEqual(geolocalizacion.longitud, longitud)
        self.assertEqual(geolocalizacion.provincia, provincia)

    def _assert_campos_extra_agregados(self, prospecto, campos_extra_a_revisar):
        campos_extra = prospecto.obtener_campos_extra()
        for nombre, valor in list(campos_extra_a_revisar.items()):
            self.assertEqual(campos_extra.filter(nombre=nombre, valor=valor).count(), 1,
                             'No existe campo con nombre y valor: %s, %s' % (nombre, valor))
        self.assertEqual(campos_extra.count(), len(campos_extra_a_revisar))

    def _assert_tarjeta_agregada(self, prospecto, nombre_de_marca):
        marca = MarcaDeTarjetaDeCredito.objects.con_nombre(nombre_de_marca)
        self.assertTrue(TarjetaDeProspecto.objects.con_marca_y_prospecto(marca, prospecto))

    def _assert_campos_no_agregados(self, prospecto, campos_extra_no_agregados):
        campos_extra = prospecto.obtener_campos_extra()
        for campo_extra in campos_extra_no_agregados:
            nombre = campo_extra[0]
            valor = campo_extra[1]
            self.assertEqual(campos_extra.filter(nombre=nombre, valor=valor).count(), 0)

    # Helpers
    def _locker_con_tercer_reintento_exitoso(self, mock_lock_resource):
        # - Primer Intento: telefono de prospecto 1 deslockeado, telefono de prospecto 2 lockeado.
        # - Segundo intento telefono de prospecto 1 lockeado, telefono de prospecto 2 no consulta.
        # - Tercer intento: telefono de prospecto 1 y 2 deslockeados.

        # Asume que el prospecto tiene telefono y email
        mock_lock_resource.side_effect = [True, False, False, True, True]
