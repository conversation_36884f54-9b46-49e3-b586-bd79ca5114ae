# coding=utf-8
from api.forms import ApiAddProspectosAdminForm
from campanias.models import CategoriaDeCampania, Campania
from testing.base import BaseFixturedTest


class ApiAddProspectosAdminFormTest(BaseFixturedTest):

    def setUp(self):
        super(ApiAddProspectosAdminFormTest, self).setUp()
        self.campania_de_sms = self.fixture['camp_1']
        self.origen_mailing = self.fixture['tipo_m']

    def _crear_campania_externa_con_nombre(self, nombre):
        origen = self.fixture['tipo_s']
        concesionaria = self.fixture['conce_1']
        try:
            categoria = CategoriaDeCampania.objects.externas().get(tipo_de_origen=origen)
        except CategoriaDeCampania.DoesNotExist:
            categoria = CategoriaDeCampania.nueva_externa('externa', origen)
        campania = Campania.objects.create(concesionaria=concesionaria, nombre=nombre, categoria=categoria)
        return campania

    def test_el_origen_inexistente_debe_indicar_error(self):
        data = {'origen': 'Q', 'campania': self.campania_de_sms.nombre, 'responsable': 'sarasa', 'vendedor': 'saresa', 'prospectos': ''}
        form = ApiAddProspectosAdminForm(data)
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('origen'), ['El tipo de origen seleccionado es inexistente.'])

    def test_campania_debe_ser_obligatoria(self):
        data = {'origen': self.origen_mailing.codigo, 'campania': '',
                'responsable': 'sarasa', 'vendedor': 'saresa', 'prospectos': ''}
        form = ApiAddProspectosAdminForm(data)
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('campania'), ['Este campo es obligatorio.'])

    def test_campania_inexistente_para_ese_origien_debe_indicar_error(self):
        data = {'origen': self.origen_mailing.codigo, 'campania': self.campania_de_sms.nombre,
                'responsable': 'sarasa', 'vendedor': 'saresa', 'prospectos': ''}
        form = ApiAddProspectosAdminForm(data)
        self.assertFalse(form.is_valid())
        self.assertIn('No existe una campaña con el nombre y origen indicados',
                      form.errors.get('__all__'))

    def test_campania_inexistente_debe_indicar_error(self):
        data = {'campania': 'sarasa', 'responsable': 'sarasa', 'vendedor': 'saresa', 'prospectos': ''}
        form = ApiAddProspectosAdminForm(data)
        self.assertFalse(form.is_valid())
        self.assertNotIn('origen', form.errors)
        self.assertEqual(form.errors.get('campania'), ['No existe una campaña con el nombre sarasa'])

    def test_responsable_inexistente_debe_indicar_error(self):
        data = {'origen': self.origen_mailing.codigo, 'campania': self.campania_de_sms.nombre,
                'responsable': 'sarasa', 'vendedor': 'saresa', 'prospectos': ''}
        form = ApiAddProspectosAdminForm(data)
        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors.get('responsable'), ['No existe un supervisor con el username indicado'])

    def test_vendedor_inexistente_debe_indicar_error(self):
        data = {'campania': self.campania_de_sms.nombre,
                'responsable': 'sup1', 'vendedor': 'sarasa', 'prospectos': ''}
        form = ApiAddProspectosAdminForm(data)
        self.assertFalse(form.is_valid())
        self.assertIn('El nombre del vendedor es incorrecto', form.errors.get('__all__'))

    def test_vendedor_de_otro_responsable_debe_indicar_error(self):
        data = {'campania': self.campania_de_sms.nombre,
                'responsable': 'sup1', 'vendedor': 'vend3', 'prospectos': ''}
        form = ApiAddProspectosAdminForm(data)
        self.assertFalse(form.is_valid())
        self.assertIn('Debe indicar un vendedor que este a cargo del Supervisor Responsable.',
                      form.errors.get('__all__'))

    def test_prospectos_vacios_debe_ser_valido(self):
        data = {'campania': self.campania_de_sms.nombre,
                'responsable': 'sup1', 'vendedor': 'vend2', 'prospectos': '{}'}
        form = ApiAddProspectosAdminForm(data)
        self.assertTrue(form.is_valid())

    def test_campania_con_externa_dupicada_debe_ser_valido(self):
        nombre = self.campania_de_sms.nombre
        self._crear_campania_externa_con_nombre(nombre)
        data = {'campania': nombre,
                'responsable': 'sup1', 'vendedor': 'vend2', 'prospectos': '{}'}
        form = ApiAddProspectosAdminForm(data)
        self.assertTrue(form.is_valid())

    def test_api_bad_request(self):
        pass
        # data = {'origen': 'Q', 'campania': 'sarasa', 'responsable': 'sarasa', 'vendedor': 'saresa', 'prospectos': ''}
        # response = self.client.post('/api/prospectos/add/', data=data, content_type='application/xml')
        # Testear que el request informa los errores igual que el form

        # Testear carga correcta:
        # - Con prospecto con otra campania indicada (usa la general)
        # - Con prospecto sin campania (usa la default)
        # - Con prospecto con campania Erronea -> queda en lista de erroneos

        # - Sin supervisor -> usa el general
        # - Con supervisor erroneo -> Queda en lista de erroneos
        # - Con supervisor en prospecto y supervisor general-> Queda con el general
        # - Con supervisor en prospecto y sin supervisor general-> supervisor del prospecto

        # - Sin vendedor en prosp con vend gral -> usa el general
        # - Con vendedor en prosp con vend gral -> usa el general
        # - Sin vendedor gral, con vend en prosp-> usa el del prospe
        # - Sin vendedor gral, con vend erroneo en prosp ->

        # - Con supervisor erroneo -> Queda en lista de erroneos
        # - Con supervisor en prospecto y supervisor general-> Queda con el general
        # - Con supervisor en prospecto y sin supervisor general-> supervisor del prospecto