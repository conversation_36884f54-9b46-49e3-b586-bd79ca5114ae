# coding=utf-8
from django.conf.global_settings import CACHES
from run0km.settings import *
import sys


DEBUG = True
# TEMPLATES_DEBUG = DEBUG
TEMPLATES[0]['OPTIONS']['debug'] = DEBUG

ALLOWED_HOSTS = ['delivery.run0km.com', 'local.delivery.com']

if 'test' not in sys.argv:
    INTERNAL_IPS = ('127.0.0.1', '0.0.0.0')
    INSTALLED_APPS += 'debug_toolbar',

    DEBUG_TOOLBAR_PANELS = [
        'ddt_request_history.panels.request_history.RequestHistoryPanel',  # Here it is
        'debug_toolbar.panels.versions.VersionsPanel',
        'debug_toolbar.panels.timer.TimerPanel',
        'debug_toolbar.panels.settings.SettingsPanel',
        'debug_toolbar.panels.headers.HeadersPanel',
        'debug_toolbar.panels.request.RequestPanel',
        'debug_toolbar.panels.sql.SQLPanel',
        'debug_toolbar.panels.templates.TemplatesPanel',
        'debug_toolbar.panels.staticfiles.StaticFilesPanel',
        'debug_toolbar.panels.cache.CachePanel',
        'debug_toolbar.panels.signals.SignalsPanel',
        'debug_toolbar.panels.logging.LoggingPanel',
        'debug_toolbar.panels.redirects.RedirectsPanel',
        'debug_toolbar.panels.profiling.ProfilingPanel',
    ]

    DEBUG_TOOLBAR_CONFIG = {
        # only requred for debug_toolbar versions below 1.8
        'SHOW_TOOLBAR_CALLBACK': 'ddt_request_history.panels.request_history.allow_ajax',
    }

    MIDDLEWARE_CLASSES += 'debug_toolbar.middleware.DebugToolbarMiddleware',


    def show_toolbar(request):
        return False


    DEBUG_TOOLBAR_CONFIG = {
        'INTERCEPT_REDIRECTS': False,
        'SHOW_TOOLBAR_CALLBACK': show_toolbar,
        # 'EXTRA_SIGNALS': ['myproject.signals.MySignal'],
        #'HIDE_DJANGO_SQL': False,
        'TAG': 'div',
        'DEBUG_TOOLBAR_MEDIA_ROOT': ' /usr/lib/python2.7/site-packages/debug_toolbar/',
        'RENDER_PANELS': True
        }

RAVEN_CONFIG = {
    'dsn': '',
}

DBUSER = 'root'
DBPASSWORD = 'root'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'deliveryrun_dev',
        'USER': DBUSER,
        'PASSWORD': DBPASSWORD,
        'HOST': '',
        'PORT': '',
    }
}
# INSTALLED_APPS = INSTALLED_APPS + ('debug_toolbar',)


if 'test' in sys.argv:
    # http://tech.marksblogg.com/faster-django-testing.html
    # http://www.daveoncode.com/2013/09/23/effective-tdd-tricks-to-speed-up-django-tests-up-to-10x-faster/
    #DEBUG = False
    #TEMPLATE_DEBUG = False

    # Descomentar cuando: quiero correr tests rapidamente
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',  # Add 'postgresql_psycopg2', 'mysql', 'sqlite3' or 'oracle'.
        'NAME': 'deliveryrun_test.db',  # Or path to database file if using sqlite3.
        'USER': '',  # Not used with sqlite3.
        'PASSWORD': '',  # Not used with sqlite3.
        'HOST': '',  # Set to empty string for localhost. Not used with sqlite3.
        'PORT': '',  # Set to empty string for default. Not used with sqlite3.
    }

    # Descomentar cuando: tengo que correr muchos tests y sqlite tira segmentation fault
    # DATABASES['default'] = {
    #     'ENGINE': 'django.db.backends.mysql',
    #     'NAME': 'deliveryrun_testing',
    #     'USER': 'root',
    #     'PASSWORD': 'admin',
    #     'HOST': '',
    #     'PORT': '',
    # }

    # PASSWORD_HASHERS = (
    #    'django.contrib.auth.hashers.MD5PasswordHasher',
    # )

# LOGGING['handlers']['file'] = {
#     'level': 'DEBUG',
#     'class': 'logging.FileHandler',
#     'filename': 'debug.log',
# }
#
# LOGGING['loggers']['model_admin_logger'] = {
#             'level': 'DEBUG',
#             'handlers': ['file'],
#             'propagate': True,
#         }

# LOGGING['loggers']['requests'] = {
#             'level': 'DEBUG',
#             'handlers': ['file'],
#             'propagate': True,
#             'formatter': 'verbose',
#         }
#
# LOGGING['loggers']['requests.packages.urllib3'] = {
#     'level': 'DEBUG',
#     'handlers': ['file'],
#     'propagate': True,
# }

# LOGGING['handlers']['console'] = {
#             'level': 'DEBUG',
#             'class': 'logging.StreamHandler',
#         }
# LOGGING['loggers']['django.db.backends'] = {
#             'level': 'DEBUG',
#             'handlers': ['console'],
# }

# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.memcached.MemcachedCache',
#         'LOCATION': 'unix:/tmp/memcached.sock',
#     }
# }

PAGE_SIZE = 5
DEFAULT_FROM_EMAIL = '<EMAIL>'

# PROD Stream API
# STREAM_API_KEY = 'rwcaj29v52qn'
# STREAM_API_SECRET = 'gqyq6v268mdqcf3efnwkvq253wb3gmmfbxkfzvm7sda6x7uex9wweyxchhxyenr4'
# STREAM_API_ID = 15312

# STAGING Stream API
# STREAM_API_KEY = 'wg48smp8ybpd'
# STREAM_API_SECRET = 'r3m7yhy4pw2qpa75nsk6xpyye5pb2qcgqregwvhxk28mzauq3gdhpzmktn7rt7mq'
# STREAM_API_ID = 14469w

# DEV Stream API
STREAM_API_KEY = 'xbzeh4rry6k3'
STREAM_API_SECRET = 'qmr2emcpfnvf6ebwjajpr62xu7kkykguw2g7kkjjmhug4r7bafjnz5wst9ezg7dm'
STREAM_API_ID = 15311

SMSCOVER_SMS_SENDER_CLASS = 'lib.smscover.SMSMockSender'

CHAT_PROVIDER = 'occ.soporte.MockChatProviderService'
CHAT_ASSIGN_OPERATOR_URL = ''
CHAT_SEND_MESSAGE_URL = ''
CHAT_SELECTOR_PARTICIPANTES_CLASS_NAME = 'occ.chat_seleccion_de_participantes.SelectorParticipantesCompulsaConfiguradoDesdeSettings'
CHAT_ID_VENDEDORES_SELECCIONADOS = [181, 284] #Leonel Rubin, Erika Martinez


# NORMALIZACION_SERVICE_URL = 'http://www.smsmasivos.biz/normalizador/service1.svc?WSDL'

## Deshabilitar las migraciones al correr los tests: ##
DESHABILITAR_MIGRACIONES_EN_TESTS = False
ES_AMBIENTE_DE_TESTING = True

REALIZAR_NORMALIZACION = True
REALIZAR_NORMALIZACION_TEST = False
EJECUTAR_SMSPUSH_TEST = False
EJECUTAR_GEOIPPLUGIN_TEST = False
EJECUTAR_CRM_TEST = False
DIAS_PARA_NO_ENVIAR_MENSAJES_SMS = 0

EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"
# EMAIL_BACKEND = "sendgrid_backend.SendgridBackend"
# SENDGRID_API_KEY = "CHANGEME: SENDGRID_PRODUCTION_API_KEY"

SIRENA_URL_API_KEY = ''

# Development App
# PUSHER_APP_ID = '287021'
# PUSHER_KEY = 'c05c45405a52975572b1'
# PUSHER_SECRET = '19a347028322749f9bf9'
# PUSHER_CLUSTER = 'mt1'

# Mock app
PUSHER_APP_ID = '1234'
PUSHER_KEY = 'mock_key'
PUSHER_SECRET = 'shhhh'
PUSHER_CLUSTER = 'mt1'

# Credenciales Anura
ANURA_PASSWORD = ""

# GOOGLE
GOOGLE_REDIRECT_URI = 'http://local.delivery.com:8000/occ/gsuite/authorize/'
# Produccion
# GSUITE_CLIENT_ID = '212114871592-hdctdvfp6vnumtf2bq53entb4d5ehvm7.apps.googleusercontent.com'
# GSUITE_SECRET = 'YzgN0n-62_kX8tlnXCU-F1h4'

# Dev
GSUITE_CLIENT_ID = '168693492852-64lp2lrpvqt0pa1kvo490pdps8bkfusj.apps.googleusercontent.com'
GSUITE_SECRET = 'HvsEkRtWbkakV-ZDotd2D4uM'
GOOGLE_OAUTH2_CLIENT_SECRETS_JSON = os.path.join(PROJECT_PATH, 'dev_client_secrets.json')

# Este setting acompaña a otros dos settings: EAVISOS_PROVIDER y EAVISOS_PROVIDER_URL (ver settings.py)
EAVISOS_PROVIDER_ACCESS_TOKEN = ''


CACHES['locker'] = {
    'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
}

class DisableMigrations(object):
    def __contains__(self, item):
        return True

    def __getitem__(self, item):
        return None


if DESHABILITAR_MIGRACIONES_EN_TESTS and 'test' in sys.argv[1:]:
    MIGRATION_MODULES = DisableMigrations()
#######################################################
