# coding: utf-8

# Django settings for run0km project.
import os
from datetime import time

import raven

from lib.redescover import SocialNetworksService

HOST = 'delivery.run0km.com'
SCHEMA = 'https'
TITULO = 'Run0Km.com'
DEBUG = True
VERSION = "1.6.10"
ES_AMBIENTE_DE_TESTING = False

TAMANIO_DE_PAGINA = 25
AMOUNT_OF_BACKUPS_TO_KEEP = 1

ADMINS = (
    # ('Your Name', '<EMAIL>'),
)

MANAGERS = ADMINS

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.',  # Add 'postgresql_psycopg2', 'mysql', 'sqlite3' or 'oracle'.
        'NAME': '',  # Or path to database file if using sqlite3.
        'USER': '',  # Not used with sqlite3.
        'PASSWORD': '',  # Not used with sqlite3.
        'HOST': '',  # Set to empty string for localhost. Not used with sqlite3.
        'PORT': '',  # Set to empty string for default. Not used with sqlite3.
    }
}

PROJECT_PATH = os.path.abspath(os.path.dirname(__file__))
CANTIDAD_MESES_RANKING = 4
GOOGLE_OAUTH2_CLIENT_SECRETS_JSON = os.path.join(PROJECT_PATH, 'client_secrets.json')
# Local time zone for this installation. Choices can be found here:
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# although not all choices may be available on all operating systems.
# On Unix systems, a value of None will cause Django to use the same
# timezone as the operating system.
# If running in a Windows environment this must be set to the same as your
# system time zone.
TIME_ZONE = 'America/Argentina/Buenos_Aires'

# Language code for this installation. All choices can be found here:
# http://www.i18nguy.com/unicode/language-identifiers.html
LANGUAGE_CODE = 'es-AR'

SITE_ID = 1

# If you set this to False, Django will make some optimizations so as not
# to load the internationalization machinery.
USE_I18N = True

# If you set this to False, Django will not format dates, numbers and
# calendars according to the current locale.
USE_L10N = True

# If you set this to False, Django will not use timezone-aware datetimes.
USE_TZ = True

# Absolute filesystem path to the directory that will hold user-uploaded files.
# Example: "/home/<USER>/media.lawrence.com/media/"
MEDIA_ROOT = os.path.join(PROJECT_PATH, '../../media')

# URL that handles the media served from MEDIA_ROOT. Make sure to use a
# trailing slash.
# Examples: "http://media.lawrence.com/media/", "http://example.com/media/"
MEDIA_URL = '/media/'

# Absolute path to the directory static files should be collected to.
# Don't put anything in this directory yourself; store your static files
# in apps' "static/" subdirectories and in STATICFILES_DIRS.
# Example: "/home/<USER>/media.lawrence.com/static/"
STATIC_ROOT = os.path.join(PROJECT_PATH, '../../static')

# URL prefix for static files.
# Example: "http://media.lawrence.com/static/"
STATIC_URL = '/static/'

# Additional locations of static files
STATICFILES_DIRS = (
    # Put strings here, like "/home/<USER>/static" or "C:/www/django/static".
    # Always use forward slashes, even on Windows.
    # Don't forget to use absolute paths, not relative paths.
    os.path.join(PROJECT_PATH, '../static'),
)

# List of finder classes that know how to find static files in
# various locations.
STATICFILES_FINDERS = (
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    #    'django.contrib.staticfiles.finders.DefaultStorageFinder',
)

# Make this unique, and don't share it with anybody.
SECRET_KEY = '4dg)!b1cw_fi1=8r=7is64f!fw#w5%b6lla15ub$sgarn-pbf*'

MIDDLEWARE_CLASSES = (
    'django.middleware.common.CommonMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.auth.middleware.SessionAuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',

    'admin_reorder.middleware.ModelAdminReorder',

    'run0km.login_required_middleware.LoginRequiredMiddleware',
    # Uncomment the next line for simple clickjacking protection:
    # 'django.middleware.clickjacking.XFrameOptionsMiddleware',
)

AUTH_USER_MODEL = 'users.User'

ROOT_URLCONF = 'run0km.urls'

# Python dotted path to the WSGI application used by Django's runserver.
WSGI_APPLICATION = 'run0km.wsgi.application'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(PROJECT_PATH, '../templates'), ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.contrib.auth.context_processors.auth',
                'django.template.context_processors.debug',
                'django.template.context_processors.i18n',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
                'django.template.context_processors.csrf',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.request',
                'occ.procesador_de_contexto_para_las_notificaciones.token_del_feed_del_usuario_registrado',
                'occ.novedades.procesador_de_contexto_para_novedades.novedades_para_usuario',
                'run0km.procesador_de_contexto_por_dominio.datos_de_personalizacion_de_dominio',
                'alertas.controlador_de_alertas.agregar_datos_de_alertas_al_contexto',
                'core.context_processors.generic_layout_context',
            ],
        },
    },
]

INSTALLED_APPS = (
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.sites',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'admin_shortcuts',
    # Uncomment the next line to enable the admin:
    'django.contrib.admin',
    # Uncomment the next line to enable admin documentation:
    # 'django.contrib.admindocs',

    'rest_framework.authtoken',
    'rest_framework_docs',
    'raven.contrib.django.raven_compat',
    'widget_tweaks',
    'django_extensions',
    'admin_reorder',
    'djcelery_email',
    'dal',
    'dal_select2',

    'core',
    'crms',
    'whatsapp',
    'users',
    'vendedores',
    'gerentes',
    'log_de_errores',
    'campanias',
    'equipos',
    'prospectos',
    'objetivos',
    'permisos',
    'alertas',
    'concesionarias',
    'webservice_utils',
    'api',
    'mobileapi',
    'conversaciones',
    'occ',
    'embed_video',
    'reportes',
    'proposalsproxy',
    'propuestas',
    'gsuite'
)

# Orden de los componentes del index del admin.
ADMIN_REORDER = (
    # Keep original label and models
    # Core
    {'app': 'core',
     'label': 'Configuraciones',
     'models': ('core.Sistema',
                'occ.ConfiguracionDeIntegracion',
                )},
    # Users
    {'app': 'users',
     'models': ('users.User',
                'users.PermisoDePersonificacion',
                'authtoken.Token'
                )},
    # Concesionarias
    {'app': 'concesionarias',
     'models': ('concesionarias.Concesionaria', 'concesionarias.Rubro',
                'concesionarias.Sitio', 'propuestas.ConcesionariaExterna', 'concesionarias.CampoDetalleProspecto'
                )},
    'gerentes', 'vendedores', 'equipos', 'campanias', 'objetivos', 'permisos',
    'alertas', 'api',

    # Prospectos
    {'app': 'prospectos',
     'models': ('prospectos.Prospecto',
                'prospectos.PedidoDeProspecto',
                'reportes.DescriptorDeReporteDeDistribucion',
                'prospectos.Proveedor',
                'prospectos.CargaFallidaDeJotform',
                'prospectos.SubidaErronea',
                'prospectos.Compra',
                'prospectos.MotivoDeFinalizacion',
                'prospectos.Venta',
                'prospectos.Rechazo',
                'prospectos.Llamado',
                'prospectos.Comentario',
                'prospectos.Marca',
                'prospectos.Alias',
                'prospectos.Modelo',
                'prospectos.MarcaDeTarjetaDeCredito',
                'propuestas.TipoDePlan',
                'propuestas.Plan',
                'propuestas.Propuesta',
                'prospectos.PeticionDeProspectoPorParteDelVendedor',
                'prospectos.PermisoPedirProspectoSegunDisponibilidad',
                'prospectos.CirculacionDeProspecto'
                )},

    # OCC:
    {'app': 'occ',
     'models': ('occ.PreguntaInvalidaDePublicacionEAvisos',
                'occ.Medio',
                'occ.Novedad',
                'occ.CampaniaDeComunicacion',
                'occ.EnvioDeMensaje',
                'occ.RespuestaDeMensaje',
                'occ.CreditoDeSMS',
                'occ.Compulsa',
                'conversaciones.MensajesWhatsapp',
                'occ.IntentoDeLlamado',
                'occ.LlamadaRealizadaDeAnura',
                'whatsapp.MessageInMeta',
                'whatsapp.MetaTemplate',
                'whatsapp.Variable',
                'whatsapp.Operador',
                'whatsapp.GrupoOperadores',
                'whatsapp.ConversationInMeta',
                'conversaciones.EnlaceConMeta',
                )},
    # Log de errores:
    {'app': 'log_de_errores',
     'label': 'Logs',
     'models': (
         'webservice_utils.HttpConnectionsLog',
         'prospectos.CargaFallidaDeJotform',
         'prospectos.SubidaErronea',
         'prospectos.LogDeExportacionDeProspecto',
         'occ.LogDeErrorDeSMS',
         'occ.LogDeErrorActualizacionDeEstadoSMS',
         'prospectos.LogDeErrorNormalizador',
         'prospectos.LogDeErrorChequeadorDeWhatsapp',
         'prospectos.LogDeErrorDeCRM',
         'prospectos.LogDeErrorDeInformacionDeRedesSociales',
         'occ.LogDeIntegracion',
         'propuestas.RegistroDiarioDeEnvioDePropuesta',
         'prospectos.RegistroDeResultadoDeAsignacionInicial',
     )},
    {
        'app': 'mobileapi',
        'label': 'Mobile',
        'models': ('mobileapi.SesionAppMobile',)
    },
    {'app': 'crms',
     'label': 'CRMS',
     'models': (
         'crms.ConfiguracionDePilot',
         'crms.ConfiguracionDeTecnom',
         'crms.ConfiguracionDeMaipu',
         'crms.ConfiguracionDeSirena',
         'crms.ConfiguracionDeGoogleSpreadsheet',
     )},
)

ADMIN_SHORTCUTS = [
    {
        'shortcuts': [
            {
                'url': '/',
            },
            {
                'url_name': 'admin:asignacion_inicial',
                'title': 'Asignación Inicial',
                'class': 'asignacion-inicial'
            },
            {
                'url_name': 'admin:reportes_descriptordereportededistribucion_add',
                'title': 'Generar Reporte de Distribución',
                'class': 'reporte-de-distribucion-de-prospectos'
            },
            {
                'url_name': 'admin:reporte-simplificado-de-prospectos',
                'title': 'Reporte Simplificado',
                'class': 'reporte-simplificado-de-prospectos'
            },
        ]
    },
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
    )
}

LOGIN_URL = '/login/'
LOGIN_REDIRECT_URL = '/'

LOGIN_EXEMPT_URLS = {
    r'^google07c49a59444306f6.html$',
    r'^about\.html$',
    r'^legal/',  # allow any URL under /legal/*
    r'^whatsapp/',
    r'^api/',
    r'^mobile/',
    r'^reset/',
    r'^prospectos/jotform/',
    r'^media/campanias/',
    r'^media/concesionarias/',
    r'^media/whatsapp/',
    r'^media/whatsapp_db_files/',
    r'^media/whatsapp_html_output/',
    r'^reportes/ver-reporte/(?P<programacion_id>\d+)/(?P<tipo>\d)/(?P<hash>[A-Za-z\d]+)$',
    r'^occ/chat/pedido_operador/$',
    r'^occ/anura/llamado-finalizado/$',
    r'^occ/anura/audio-disponible/$',
    r'^occ/chat/(?P<chat_pk>[\w\-]+)/recibir_evento/$',
    r'^propuestas/api/?$',
    r'^terms-and-conditions/?$',
    r'^occ/eavisos/mensaje/?$',
}

ADMIN_ALLOWED_URLS = {
    r'^admin/',
    r'^media/',
    r'^personificar/',
    r'^dejar-de-personificar/',
    r'^reportes/distribucion/(?P<hash_de_descriptor>[0-9A-Za-z\d]+)$',
    r'^api/prospectos/doc/$',
    r'^occ/novedades/(?P<novedad_pk>\d+)/preview/$',
}

RAVEN_CONFIG = {
    'dsn': 'https://<EMAIL>/5624768?verify_ssl=0',
}

# A sample logging configuration. The only tangible logging
# performed by this configuration is to send an email to
# the site admins on every HTTP 500 error when DEBUG=False.
# See http://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse'
        }
    },
    'handlers': {
        'mail_admins': {
            'level': 'ERROR',
            'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler'
        },
        # 'sentry': {
        #     'level': 'ERROR',
        #     'class': 'raven.handlers.logging.SentryHandler',
        #     'dsn': RAVEN_CONFIG['dsn'],
        # },
    },
    'loggers': {
        # '': {
        #     'handlers': ['sentry'],
        #     'level': 'ERROR',
        #     'propagate': False,
        # },
        'django.request': {
            'handlers': ['mail_admins'],
            'level': 'ERROR',
            'propagate': True,
        },
    }
}

TEST_RUNNER = 'django.test.runner.DiscoverRunner'

from permisos.configuraciones_de_permisos import permisos

PERMISOS = permisos
USERNAME_ADMIN = 'admin'
USERNAME_DISTRIBUCION = 'distribucion'
BACKEND_USERS = [USERNAME_ADMIN, USERNAME_DISTRIBUCION]
CAMPANIAS_GENERICAS = ['1', '2', '3']
CREDITO_INICIAL_PARA_CAMPANIA = 1000

PAGE_SIZE = 10
DATA_UPLOAD_MAX_NUMBER_FIELDS = 10000

# Conversaciones por Whatsapp
WHATSAPP_META_VENDEDORES_HABILITADOS = []
WS_META_URL = 'https://chat.soybot.com:8087/WhatsAppProxy/SendMessage'
WS_META_TOKEN = ''


CHAT_CONVERSACIONES_POR_PAGINA = 8
CHAT_MENSAJES_EN_VISTA_PREVIA = 3
CHAT_NOMBRE_BOT = 'Mariela'

FERIADOS = ['1/1', '24/3', '1/5', '25/5', '9/7', '8/12', '25/12']

HORARIOS_LABORALES = {
    'ENTRADA_DIA_SEMANA': time(9, 0, 0, 0),
    'SALIDA_DIA_SEMANA': time(18, 0, 0, 0),
    'ENTRADA_SABADO': time(9, 0, 0, 0),
    'SALIDA_SABADO': time(14, 0, 0, 0)
}

SELECTOR_HORARIOS_MINUTOS_DE_MARGEN = 10
SELECTOR_HORARIOS_CANTIDAD_DE_LLAMADOS_DIARIOS = 10


SUBJECT_ALERTA_INACTIVIDAD = '%(concesionaria)s - %(#)d PROSPECTOS'
SUBJECT_ALERTA_SUPERVISOR = '%s - PROSPECTOS SIN TRABAJAR.'
SUBJECT_ALERTA_SIN_ASIGNAR = '%(concesionaria)s - %(#)d PROSPECTOS SIN ASIGNAR.'
SUBJECT_ALERTA_LOGISTICA = 'RUN0KM - Alertas del sistema:'
SUBJECT_AVISO_RECHAZO = 'RUN0KM - PROSPECTOS Rechazados por %s.'
SUBJECT_PEDIDO_SERVICIO_WHATSAPP = 'RUN0KM - Pedido de Servicio de WhatsApp del supervisor %s (concesionaria: %s).'
SUBJECT_PEDIDO_SERVICIO_CHAT = 'RUN0KM - Pedido de Servicio de Chat del supervisor %s (concesionaria: %s).'
SUBJECT_PEDIDO_SERVICIO_SMS = 'RUN0KM - Pedido de Servicio de Sms del supervisor %s (concesionaria: %s).'
SUBJECT_REPORTES_PROGRAMADOS = '%(concesionaria)s - Reporte %(frecuencia_reporte)s.'

EMAIL_LOGISTICA = '<EMAIL>'

DELTA_ALERTA_LLAMADOS = 5

NORMALIZACION_SERVICE_URL = 'http://portabilidad.coverone.la/services/normalizer.svc?WSDL'
REALIZAR_NORMALIZACION = True
REALIZAR_CHECKEO_DE_WHATSAPP = True

DIAS_PARA_DETERMINAR_VENDEDOR_ES_INACTIVIO = 180  # 6 meses
DIAS_ANTES_DE_REPETIR_DATOS_DE_CONTACTO = 7
DIAS_ANTES_DE_QUE_NO_SE_PUEDA_RECHAZAR = 4
HORAS_PRIORIDAD_ANDROID = 24
FACTOR_DE_ASIGNACION_DEFAULT = 10
INGRESO_LOCKEO_DE_DATOS_DE_CONTACTOS_CANTIDAD_DE_REINTENTOS = 3

INGRESO_LOCKEO_DE_DATOS_DE_CONTACTOS_MILISEGUNDOS_DE_ESPERA_ENTRE_REINTENTOS = 500

CHANGUI_OBJETIVO = 5
PROPOSAL_TOKEN = '2c4f5a86367a4a39afcea51a6f79bd51'
PROPOSALS_SERVER_URL = 'https://www.run0km.com/'
WHATSAPP_RECIBIR_URL = "http://190.111.232.45:8888/centralmodulews/api/news"
WHATSAPP_ENVIAR_URL = "http://190.111.232.45:8888/centralmodulews/api/campaign"
ANTIGUEDAD_DE_PROSPECTOS_A_ENVIAR_WHATSAPP_DE_BIENVENIDA = 3
DIAS_DESDE_ULTIMA_ACTIVIDAD_PARA_ENVIAR_WHATSAPP_DE_BIENVENIDA = 1
MESES_PARA_BUSCAR_PROSPECTOS_PARA_RESPUESTAS_DE_WHATSAPP = 12

SMSCOVER_SMS_SENDER_CLASS = 'lib.smscover.SMSSender'
SMSCOVER_CLIENT_CLASS = 'lib.smscover.SMSCoverClient'
SMSCOVER_USER = "run0km"
SMSCOVER_KEY = "87629369823742"
SMSCOVER_ENVIOS_HORARIO_INICIO = 9
SMSCOVER_ENVIOS_HORARIO_FIN = 23
SMSCOVER_LIMITE_TAMANIO_MENSAJE = 145
CARACTERES_VALIDOS_DE_SMS = "@£$¥èéùìòLFØøCRÅåΔ_ΦΓΛΩΠΨΣΘΞESCÆæßÉSP!¤%&‘()*+,-./0123456789:;<=>?¡ABCDEFGHIJK LMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà "

EJECUTAR_SMSPUSH_TEST = False

FACEMACHINE_MARCAS_EXCLUIDAS = ['fiat']
FACEMACHINE_LIMITE_ITERACIONES_SOBRE_RESPUESTA = 600
WHATSAPP_LIMITE_TAMANIO_MENSAJE = 600

TAMANIO_PAGINA_DETALLE_CAMPANIA_OCC = 15
EJECUTAR_FACEMACHINE_TEST = False

DIAS_PARA_NO_ENVIAR_MENSAJES_SMS = 5
DIAS_COMO_MENSAJE_NO_LEIDO = 30

SOCIAL_NETWORKS_SERVICE = SocialNetworksService
SOCIAL_NETWORKS_SERVICE_BATCH_SIZE = 30
SOCIAL_NETWORKS_SERVICE_URL = 'http://www.smsmasivos.biz/Enriquecimiento.WebService/api/Service/Submit'
SOCIAL_NETWORKS_SERVICE_TOKEN = 'f5213f49-fc5c-4b72-a96c-434b9659e408'  # 'cd82e0a6-b204-46ba-9785-9f016bf0be'
SOCIAL_NETWORKS_SERVICE_TESTING = False

CHAT_PROVIDER = 'lib.sales_chat_service.api.ChatProviderService'

CHAT_ASSIGN_OPERATOR_URL = 'http://chat.smsmasivos.biz:8081/Operators/Bind'
CHAT_SEND_MESSAGE_URL = 'http://chat.smsmasivos.biz:8081/Operators/Message'

CHAT_PADDING_TIMEOUT_IN_SECONDS = 5
CHAT_CANTIDAD_DE_REINTENTOS_POR_ERROR_DE_COMUNICACION = 5
CHAT_EXPIRACION_DE_COMPULSA_EN_SEGUNDOS = 300.0
CHAT_SELECTOR_PARTICIPANTES_CLASS_NAME = 'occ.chat_seleccion_de_participantes.SelectorParticipantesCompulsa'

# Estos settings son para poder acceder al a API de EAvisos
EAVISOS_PROVIDER = 'occ.servicio_de_eavisos.ServicioDeEAvisos'
EAVISOS_PROVIDER_URL = 'https://www.run0km.com/conversations/answer/'
EAVISOS_PROVIDER_ACCESS_TOKEN = '1bea59fd-03ba-4f8c-ae79-dead10909e25'

TECNOM_TEMPLATE_URL = 'https://%s.tecnomcrm.com/api/v1/webconnector/consultas/adf'
MAIPU_URL = 'https://timm20.mundomaipu.com.ar/CRM/Servicios/ServicioMundoMaipu.asmx/createLeadRunCrm'

PILOT_URL = 'https://api.pilotsolution.net/webhooks/welcome.php'
PILOT_DEFAULT_API_KEY = 'F2AB8C31-661F-4721-9B14-E681A660F515'
PILOT_DEFAULT_CODIGO_DE_ORIGEN = '90844C2E'
PILOT_CONTACT_TYPE = 1  # Electrónico
# PILOT_BUSINESS_TYPE = 3  # Plan de Ahorro
# PILOT_DEBUG = True
PILOT_NOTIFICATION_EMAIL = None

SIRENA_URL = 'https://api.getsirena.com/v1/lead/vehicle'
SIRENA_UTM_SOURCE = "Delivery"  # Nombre que nos identifica como proveedores de prospectos en el sistema de Sirena

CRM_INTEGRATION_API_TOKEN = '3670e40d30361998a38593d08a300f6d882dcf6d'
FACEMACHINE_API_TOKEN = '0a0ec7e5-dfc3-474b-8635-bc57af074844'

PDF_CONVERTER = '/usr/local/bin/wkhtmltopdf'
PROSPECTOS_A_COMPLETAR_INFO_DE_TELEFONOS = 10
DEFAULT_FROM_EMAIL = '<EMAIL>'
DEFAULT_ASUNTO_ALERTAS = 'Alertas'
DEFAULT_ASUNTO_REPORTES = 'Reportes'
DEVS_MAILING_LIST = ['<EMAIL>']

DEFAULT_ASUNTO_NOTIFICACIONES = 'Notificaciones'
NOTIFICACIONES_EMAIL_ASUNTO = 'Notificación de asignación de prospecto'

TWITTER_URL = 'https://twitter.com/'

# API DE INGRESOS DE PROSPECTOS

API_FORMATO_FECHA_Y_HORA = '%Y-%m-%d %H:%M'

# Credenciales Anura
ANURA_USER = '199'
ANURA_DOMAIN = 'runone.grancentral.com.ar'
ANURA_HOST = 'wss://webrtc.anura.com.ar:9084'
ANURA_PASSWORD = ''
ANURA_TIMEZONE = 'America/Argentina/Buenos_Aires'
ANURA_DATETIME_FORMATO = '%Y-%m-%d %H:%M:%S'

# GSuite
GSUITE_OWNER_EMAIL = "<EMAIL>"

API_MOBILE_LOCK_SECONDS = 5 * 60
API_MOBILE_FORMATO_FECHA = '%Y-%m-%d'
API_MOBILE_FORMATO_FECHA_HORA = '%Y-%m-%d-%H:%M:%S'
API_MOBILE_VERSION = '2.4-run0km-one'
API_MOBILE_FCM_SERVER_KEY = 'AAAA7TBYQH8:APA91bEnis1U5SAKypDESEqjC5wbEsqb9BwwYuMALKGEqmkhXVKlLm2wlVgeuHWQn5GxM3d-4KOpi4j_46a155SpzVBwUdKY4SJIp-_BFC-mEgIl0TVl9rwaHuIvjz1x3jDVoK6meH7z'
API_MOBILE_DIAS_EXPIRACION_DE_SESION = 5
API_MOBILE_LIMITE_DE_MODIFICACIONES = 200
API_MOBILE_LIMITE_LISTA_INICIAL_DE_PROSPECTOS = 100
API_MOBILE_LIMITE_LISTA_INICIAL_DE_CONVERSACIONES = 100

API_DIETRICH_URL = 'http://api.gdietrich.com:444/api/Contacto'
API_DIETRICH_USUARIO = 'Run'
API_DIETRICH_PASSWORD = 'Run2017'
API_DIETRICH_CODIGO_DE_AUTORIZACION = 'cnVuOlJ1bjIwMTc='
API_DIETRICH_FORMATO_FECHA_HORA = "%Y-%m-%dT%H:%M:%S"
API_DIETRICH_FORMATO_FECHA = "%Y-%m-%d"
API_DIETRICH_ORIGEN = 'Run0Km'
API_DIETRICH_TIPO = 'Lead'
API_DIETRICH_AWS_BUCKET = 'datos-dietrich'
API_DIETRICH_AWS_FILENAME_PREFIX = 'prod'
API_DIETRICH_SUBTIPO = 'Plan de ahorro'
API_INTEGRACION_LIMITE_DE_ANTIGUEDAD = 30
API_INTEGRACION_TAMANIO_DE_PAGINA = 50
API_INTEGRACION_CANTIDAD_MAXIMA_DE_PROSPECTOS_A_INTEGRAR = 400

REST_FRAMEWORK_DOCS = {
    'HIDE_DOCS': True
}

PROPUESTAS_URL_DETALLE = 'http://www.run0km.com/clasificados/%s/'

SINCRONIZACIONES = ['mobileapi.sincronizador.Sincronizador',
                    'occ.integracion.receptor.ReceptorDeNotificacionesParaIntegraciones',
                    'prospectos.models.entrega_de_datos.notificaciones.ReceptorDeNotificacionesAVendedor'
                    ]

DEBUG_PEDIDOS = []
PROPORCION_DE_ACTUALIZACION_DE_LIMITE_DIARIO_A_SUPERVISORES = 1.3

REGISTRAR_ACTIVIDAD_EXEMPT_URLS = {
    '^alertas/alerta-mensajes/$',
}

DEFAULT_CURRENCY_CODE = 'ARS'
CURRENCIES = (DEFAULT_CURRENCY_CODE,)

MARCA_BLANCA = ''
MAPA_DE_MARCAS = {'Volkswagen': ['(VW)', 'VW'],
                  'Toyota': ['(TOYOTA)'],
                  'Ford': ['(FORD)', '(FORD'],
                  'Fiat': ['(FIAT)'],
                  'Citroen': ['(CITROEN)', 'Citoren'],
                  'Chevrolet': ['(CHEVROLET)', 'Chevroelt'],
                  'Peugeot': ['(PEUGEOT)'],
                  'Chery': ['(CHERY)'],
                  'Renault': ['(RENAULT)'],
                  MARCA_BLANCA: ['marca blanca', 'marca-blanca', 'marcablanca']
                  }

TAWK_TO_URL = "https://embed.tawk.to/5b7d761af31d0f771d840a87/default"
TAWK_TO_EMAIL = "<EMAIL>"
MAILS_FALSOS = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

# Cuando un supervisor asigna objetivos de ventas para el mes en curso a sus vendedores
# "automagicamente" en la pagina de oportunidades, este es el numero minimo de ventas que se le pone de objetivo
MINIMA_CANTIDAD_OBJETIVO_VENTAS_ASIGNABLE_EN_OPORTUNIDADES = 4
# Cuando un supervisor asigna objetivos de ventas para el mes en curso a sus vendedores
# el numero de meses que se mira para atras de cada vendedor,
# para calcular el promedio de ventas que tuvo es este
CANTIDAD_DE_MESES_PARA_CALCULAR_PROMEDIO_DE_VENTAS_DE_VENDEDORES = 3

# Cuando un prospecto ingresa al sistema, si ingresa un repetido dentro de este gap debe ser mergeado
GAP_PARA_PROSPECTOS_MERGEABLES_EN_MILISEGUNDOS = 10*60*60*1000

WHATSAPP_META_VENDEDORES_HABILITADOS = []
WS_URL_STATUS_NOTIFICATION = "https://delivery.run0km.com/api/whatsapp/webhook/"
WS_META_URL = 'https://chat.smsmasivos.biz:8087/WhatsAppProxy/SendMessage'
WS_META_TOKEN = None
EMAIL_WHATSAPP_META_NOTIFICATION = '<EMAIL>'

try:
    from .local_settings import *
except ImportError:
    pass

IMPORT_CELERY_CONFIG = os.environ.get('IMPORT_CELERY_CONFIG', "false") == "true"
if IMPORT_CELERY_CONFIG:
    from .celeryconfig import *
