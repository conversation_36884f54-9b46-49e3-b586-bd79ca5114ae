import json
from django.core.exceptions import ObjectDoesNotExist
from django.views.generic import DetailView
from django.views.generic.list import ListView
from django.shortcuts import render, redirect
from django.http import JsonResponse
from django.contrib import messages

from permisos.templatetags.permisos import tiene_permiso_para
from equipos.models import Equipo
from equipos.forms import NombreEquipoForm

from prospectos.models.base import PedidoDeProspecto


class EquipoView(DetailView):
    template_name = "equipo.html"
    model = Equipo

    def get(self, *args, **kwargs):
        if not self.request.user.is_vendedor() or \
                not tiene_permiso_para(self.request.user.cargo, 'administrar_equipos'):
            return redirect('resumen')
        if not self.request.user.vendedor == self.get_object().supervisor:
            return redirect('vendedores')
        return super(EquipoView, self).get(*args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super(EquipoView, self).get_context_data(**kwargs)
        integrantes = self.object.integrantes
        supervisor = self.request.user.vendedor
        libres = supervisor.vendedores.filter(equipo__isnull=True)

        context['json_integrantes']= armar_json_de_vendedores(integrantes)
        context['json_libres']= armar_json_de_vendedores(libres)
        return context


def borrar(request, pk):
    if not request.user.is_vendedor() or not tiene_permiso_para(request.user.cargo, 'administrar_equipos'):
        return redirect('resumen')
    tiene_el_equipo_pedidos_asociados = PedidoDeProspecto.objects.filter(equipo=pk).exists()
    if tiene_el_equipo_pedidos_asociados:
        value = {
            'status': False,
        }
        return JsonResponse(value)
    supervisor = request.user.vendedor

    try:
        equipo = Equipo.objects.get(id=pk, supervisor=supervisor)
        equipo.delete()
    except ObjectDoesNotExist:
        pass

    return redirect('vendedores')


def renombrar(request, pk):
    response = {'status': False}
    if request.user.is_vendedor() and tiene_permiso_para(request.user.cargo, 'administrar_equipos'):
        supervisor = request.user.vendedor
        try:
            equipo = Equipo.objects.get(id=pk, supervisor=supervisor)
        except ObjectDoesNotExist:
            pass
        else:
            form = NombreEquipoForm({'nombre': request.GET.get('nombre').strip()})
            if form.is_valid():
                equipo.nombre = form.cleaned_data['nombre']
                equipo.save()
                response['status'] = True

    return JsonResponse(response)


def guardar_integrantes(request, pk):
    if request.user.is_vendedor() and tiene_permiso_para(request.user.cargo, 'administrar_equipos'):
        supervisor = request.user.vendedor
        try:
            equipo = Equipo.objects.get(id=pk, supervisor=supervisor)
        except ObjectDoesNotExist:
            pass
        else:
            integrantes = request.POST.get('integrantes')
            integrantes = json.loads(integrantes)
            equipo.integrantes.clear()
            supervisor.vendedores.filter(id__in=integrantes).update(equipo=equipo)
            msg = 'Se ha modificado el equipo: %s' % equipo.nombre
            messages.add_message(request, messages.SUCCESS, msg)
        return redirect('vendedores')
    return redirect('resumen')


## UTILS
def armar_json_de_vendedores(query):
    result = []
    for vendedor in query.all():
        nombre = vendedor.user.get_full_name()
        nombre = nombre if nombre else vendedor.user.username
        result.append((vendedor.id, nombre,))
    return json.dumps(result)
