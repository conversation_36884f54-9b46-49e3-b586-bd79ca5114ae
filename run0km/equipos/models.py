from django.db import models
from equipos.managers import EquipoManager


class Equipo(models.Model):
    nombre = models.Char<PERSON>ield(max_length=100, blank=False, null=False)
    supervisor = models.ForeignKey('vendedores.Vendedor', related_name='equipos')

    objects = EquipoManager()

    def __str__(self):
        return self.nombre

    def obtener_integrantes(self):
        return self.integrantes.all()

    def as_json(self):
        return{
            'id': self.id,
            'nombre': self.nombre
        }
