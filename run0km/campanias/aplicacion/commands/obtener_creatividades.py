# coding=utf-8
from django.conf import settings
from django.core.paginator import Paginator
from django.db.models import Count

from campanias.models import Campania
from layers.application import AppCommandLegacy, AppCommand
from layers.application.commands.validators.generic import NotNone
from layers.application.commands.validators.validator import Parameter


class ObtenerCreatividadesCommand(AppCommand):
    """
        Modela la operacion de obtener las creatividades de un rol. De forma paginada.
        Me definen un rol, y opcionalmente un numero de pagina
        y devuelvo una pagina de creatividades relevantes para ese rol.
        Por defecto devuelvo la primer pagina.

        Nota:
            Este objeto tiene acoplado resposabilidades del dominio. Nos falta mas objetos de esa capa.
    """

    def _initial_parameters(self):
        params = super(ObtenerCreatividadesCommand, self)._initial_parameters()
        params.append(Parameter("rol", [NotNone()]))
        params.append(Parameter("pagina", [NotNone()]))
        return params

    def _default_arguments(self):
        arguments = super(ObtenerCreatividadesCommand, self)._default_arguments()
        arguments['rol'] = None
        arguments['pagina'] = 1
        return arguments

    def can_execute(self):
        result = super(ObtenerCreatividadesCommand, self).can_execute()
        self._verificar_si_rol_esta_autorizado(result)
        return result

    def _execute_from_successful_result(self, result):
        pagina = self._pagina_de_creatividades()
        result.set_object(pagina)
        return result

    def _pagina_de_creatividades(self):
        creatividades = self._creatividades()
        creatividades = self._optimizar_queryset(creatividades)
        pagina = self._paginar(creatividades, self._numero_de_pagina())
        return pagina

    def _creatividades(self):
        if self._rol().es_gerente():
            creatividades = self._creatividades_de_gerente()
        else:
            creatividades = self._creatividades_de_supervisor()
        return creatividades

    def _verificar_si_rol_esta_autorizado(self, result):
        rol = self._rol()
        if rol is None or not self.esta_autorizado_rol(rol):
            result.add_error('No tiene permisos para realizar esta acción')

    def _optimizar_queryset(self, creatividades):
        return creatividades.annotate(Count('prospectos')).select_related('detalle', 'categoria__tipo_de_origen')

    def _creatividades_de_gerente(self):
        concesionaria = self._rol().obtener_concesionaria()
        creatividades = Campania.objects.campanias_de_concesionaria(concesionaria).annotate(Count('prospectos'))
        return creatividades

    def _creatividades_de_supervisor(self):
        creatividades = Campania.objects.campanias_de_vendedor(self._rol())
        return creatividades

    def _paginar(self, campanias, numero_de_pagina):
        paginator = Paginator(campanias, settings.TAMANIO_DE_PAGINA)
        pagina = paginator.page(numero_de_pagina)
        return pagina

    def _rol(self):
        return self.get_argument_named('rol')

    def _numero_de_pagina(self):
        return self.get_argument_named('pagina')

    def esta_autorizado_rol(self, rol):
        return not rol.es_gerente() or not rol.es_supervisor()
