from django.contrib import admin

from campanias.models import (
    Campania,
    DetalleCampania,
    CategoriaDeCampania,
    TipoDeOrigen,
    DistribuidorCampania
)


class DetalleCampaniaInlineAdmin(admin.TabularInline):
    model = DetalleCampania
    extra = 0


class DistribuidorCampaniaAdmin(admin.ModelAdmin):
    search_fields = ('name', )


class CampaniaAdmin(admin.ModelAdmin):
    inlines = [DetalleCampaniaInlineAdmin]
    search_fields = ('nombre', )
    list_display = ('__str__', 'calidad')
    list_filter = ('categoria',)


class CategoriaDeCampaniaAdmin(admin.ModelAdmin):
    search_fields = ('nombre', )
    list_display = ('__str__', 'calidad')


class TipoDeOrigenAdmin(admin.ModelAdmin):
    model = TipoDeOrigen

    def save_model(self, request, obj, form, change):
        super(TipoDeOrigenAdmin, self).save_model(request, obj, form, change)
        CategoriaDeCampania.nueva_externa(nombre='Externa ' + obj.nombre, calidad=3, valor=1, tipo_de_origen=obj)


admin.site.register(TipoDeOrigen, TipoDeOrigenAdmin)
admin.site.register(CategoriaDeCampania, CategoriaDeCampaniaAdmin)
admin.site.register(Campania, CampaniaAdmin)
admin.site.register(DistribuidorCampania, DistribuidorCampaniaAdmin)
