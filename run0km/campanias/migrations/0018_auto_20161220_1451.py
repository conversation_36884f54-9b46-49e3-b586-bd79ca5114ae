# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-12-20 17:51


from django.db import migrations


def crear_categorias_externas_para_campanias_existentes(apps, schema_editor):
    TipoDeOrigen = apps.get_model("campanias", "TipoDeOrigen")
    CategoriaDeCampania = apps.get_model("campanias", "CategoriaDeCampania")
    for tipo in TipoDeOrigen.objects.all():
        nueva_categoria = CategoriaDeCampania(
            nombre='Externa ' + tipo.nombre,
            tipo_de_origen=tipo,
            calidad=3,
            valor=1,
            es_externa=True
        )

        nueva_categoria.full_clean()
        nueva_categoria.save()


class Migration(migrations.Migration):

    dependencies = [
        ('campanias', '0017_auto_20161220_1450'),
    ]

    operations = [
        migrations.RunPython(crear_categorias_externas_para_campanias_existentes),

    ]
