# -*- coding: utf-8 -*-
# Generated by Django 1.11.14 on 2024-05-16 11:26
from __future__ import unicode_literals

from django.apps import apps
from django.db import migrations


def define_distribuidor(apps, schema_editor):
    DistribuidorCampania = apps.get_model("campanias", "DistribuidorCampania")
    CategoriaCampania = apps.get_model("campanias", "CategoriaDeCampania")
    distribuidor_interno, created = DistribuidorCampania.objects.get_or_create(name="Interno")
    CategoriaCampania.objects.filter(distribuidor__isnull=True,es_externa=True).update(distribuidor=distribuidor_interno)

def undo_define_distribuidor(apps, schema_editor):
    DistribuidorCampania = apps.get_model("campanias", "DistribuidorCampania")
    CategoriaCampania = apps.get_model("campanias", "CategoriaDeCampania")
    distribuidor_interno, created = DistribuidorCampania.objects.get_or_create(name="Interno")
    CategoriaCampania.objects.filter(distribuidor=distribuidor_interno,es_externa=True).update(distribuidor=None)

class Migration(migrations.Migration):
    dependencies = [
        ('campanias', '0021_auto_20230717_1801'),
    ]

    operations = [
        migrations.RunPython(define_distribuidor, undo_define_distribuidor),
    ]