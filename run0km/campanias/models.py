# -*- coding: utf-8 -*-
from django.core.exceptions import ValidationError
from django.db import models

from campanias.querysets import CampaniaQuerySet, CategoriaDeCampaniaQuerySet, TipoDeOrigenQuerySet


class TipoDeOrigen(models.Model):
    codigo = models.CharField(max_length=16, unique=True)
    nombre = models.CharField(max_length=64)

    objects = TipoDeOrigenQuerySet.as_manager()

    def clean(self):
        self.codigo = self.codigo.upper()

    class Meta:
        verbose_name = 'Calidad de Campaña'
        verbose_name_plural = 'Calidades de Campaña'

    def __str__(self):
        return '%s|%s' % (self.codigo, self.nombre)


class DistribuidorCampania(models.Model):
    name = models.CharField(max_length=100, blank=False, null=False)

    class Meta:
        verbose_name = "Distribuidor de Campaña"
        verbose_name_plural = "Distribuidores de Campaña"

    def __str__(self):
        return self.name


class CategoriaDeCampania(models.Model):
    PUNTAJES = list(zip(list(range(1, 7)), list(range(1, 7))))

    tipo_de_origen = models.ForeignKey('campanias.TipoDeOrigen', related_name='categorias_de_campania')
    nombre = models.CharField(max_length=100)
    valor = models.PositiveIntegerField(default=1)
    calidad = models.IntegerField(choices=PUNTAJES, default=3)
    es_externa = models.BooleanField(default=False)
    distribuidor = models.ForeignKey(DistribuidorCampania, null=True, blank=True, default=None)

    objects = CategoriaDeCampaniaQuerySet.as_manager()

    @classmethod
    def nueva_externa(cls, nombre, tipo_de_origen, valor=1, calidad=3):
        nueva_categoria = cls(
            nombre=nombre,
            tipo_de_origen=tipo_de_origen,
            calidad=calidad,
            valor=valor,
            es_externa=True
        )

        nueva_categoria.full_clean()
        nueva_categoria.save()

        return nueva_categoria

    class Meta:
        verbose_name = "Categoría de Campaña"

    def __str__(self):
        nombre = self.nombre or 'Sin nombre'
        es_externa = ' (externa)' if self.externa() else ''
        return '%s%s' % (nombre, es_externa)

    def externa(self):
        return self.es_externa


class Campania(models.Model):
    PUNTAJES = list(zip(list(range(1, 7)), list(range(1, 7))))

    # Campania debe ser unique case insensitive. En el admin funciona asi por default.
    nombre = models.CharField(max_length=100, blank=False, null=False)
    categoria = models.ForeignKey('campanias.CategoriaDeCampania', related_name='campanias')
    calidad = models.IntegerField(choices=PUNTAJES, blank=True, null=True, default=None)
    concesionaria = models.ForeignKey('concesionarias.Concesionaria', related_name='campanias_propias', blank=True,
                                      null=True)
    permite_circular_prospectos = models.BooleanField(default=True)

    objects = CampaniaQuerySet.as_manager()

    class Meta:
        verbose_name = "Campaña"
        unique_together = ('nombre', 'concesionaria')

    def clean(self):
        self._validar_categoria()
        self._validar_nombre_no_repetido_para_nuevas_campanias_genericas()

    def _validar_nombre_no_repetido_para_nuevas_campanias_genericas(self):
        campanias_genericas_de_mismo_nombre = Campania.objects.campanias_genericas_de_nombre(nombre=self.nombre)
        cantidad = campanias_genericas_de_mismo_nombre.count()
        yo_soy_la_unica_camp_creada = self._validar_que_si_estoy_editando_campania_yo_sea_la_unica_previamente_creada(
            cantidad=cantidad, campanias_genericas_de_mismo_nombre=campanias_genericas_de_mismo_nombre)
        if cantidad > 0 and self.concesionaria is None and not yo_soy_la_unica_camp_creada:
            raise ValidationError('Ya existe una campaña generica con ese nombre.')

    def _validar_que_si_estoy_editando_campania_yo_sea_la_unica_previamente_creada(self, cantidad,
                                                                                   campanias_genericas_de_mismo_nombre):
        yo_soy_la_unica_camp_creada = False
        if cantidad == 1:
            campania = campanias_genericas_de_mismo_nombre[0]
            yo_soy_la_unica_camp_creada = campania.id == self.id
        return yo_soy_la_unica_camp_creada

    def _validar_categoria(self):
        try:
            self.categoria
        except CategoriaDeCampania.DoesNotExist:
            raise ValidationError(self.__class__.mensaje_campanias_sin_categoria())
        if not self.categoria.es_externa and self.concesionaria:
            raise ValidationError('Solo las campañas externas deben tener concesiorarias.')
        if self.permite_circular_prospectos and self.categoria.es_externa:
            raise ValidationError('Las campañas que circulan prospectos no pueden tener categorias externas')

    def __str__(self):
        return "%s (%s)" % (self.nombre, self.categoria.tipo_de_origen.nombre)

    def nombre_origen(self):
        return self.categoria.tipo_de_origen.nombre

    @property
    def origen(self):
        return self.categoria.tipo_de_origen.codigo

    def get_calidad(self):
        if self.calidad:
            return self.calidad
        else:
            return self.categoria.calidad

    def valor_del_dato(self):
        return self.categoria.valor

    def calidad_numerica(self):
        return self.calidad

    def obtener_nombre(self):
        return self.nombre

    def obtener_detalle(self):
        return self.detalle

    def obtener_categoria(self):
        return self.categoria

    def obtener_concesionaria(self):
        return self.concesionaria

    def obtener_tipo_de_origen(self):
        return self.categoria.tipo_de_origen

    def tiene_permitido_circular_prospectos(self):
        return self.permite_circular_prospectos

    def nombre_descriptivo(self):
        if hasattr(self, 'detalle') and self.detalle.alias:
            return self.detalle.alias
        else:
            return self.nombre

    @classmethod
    def nueva(cls, nombre, categoria, concesionaria, calidad=None, permite_circular_prospectos=True):
        campania = cls(nombre=nombre, categoria=categoria, calidad=calidad, concesionaria=concesionaria,
                       permite_circular_prospectos=permite_circular_prospectos)
        campania.full_clean()
        campania.save()
        return campania

    @classmethod
    def nueva_interna(cls, nombre, categoria, calidad=None):
        if categoria.es_externa:
            raise ValidationError(message="Se deben ingresar categorias internas para las campañas internas")
        campania = cls(nombre=nombre, categoria=categoria, calidad=calidad)
        campania.full_clean()
        campania.save()
        return campania

    @classmethod
    def mensaje_campanias_sin_categoria(cls):
        return 'Las campañas deben tener una categoria.'


class DetalleCampania(models.Model):
    campania = models.OneToOneField('Campania', related_name='detalle', primary_key=True)
    alias = models.CharField(max_length=100, blank=True, null=True)
    pieza = models.FileField(upload_to='campanias', null=True, blank=True)
    texto = models.TextField(help_text='Texto de mensaje o mailing.')
    web = models.CharField(max_length=100, blank=True, null=True)
    descripcion = models.TextField()
    archivo = models.FileField(upload_to='campanias', null=True, blank=True)

    def __str__(self):
        return "Detalle campaña :%s" % self.campania.nombre

    class Meta:
        verbose_name = "Detalle de Campaña"
        verbose_name_plural = "Detalles de Campaña"

    def obtener_alias(self):
        return self.alias

    def obtener_texto(self):
        return self.texto

    def obtener_web(self):
        return self.web

    def obtener_descripcion(self):
        return self.descripcion

    def obtener_pieza(self):
        return self.pieza

    def obtener_archivo(self):
        return self.archivo
