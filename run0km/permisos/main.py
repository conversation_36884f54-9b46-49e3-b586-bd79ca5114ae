import abc


class Accion(object):
    def __init__(self, permiso, metodo):
        self.permiso = permiso
        self.metodo = metodo

    def ejecutar(self, *args, **kwargs):
        return self.metodo(*args, **kwargs)


class Permiso(object, metaclass=abc.ABCMeta):
    def ejecutar_por_vendedor(self, accion, *args, **kwargs):
        raise NotImplementedError(
            "El usuario no tiene permiso para ejecutar _como_ Vendedor"
        )

    def ejecutar_por_supervisor(self, accion, *args, **kwargs):
        raise NotImplementedError(
            "El usuario no tiene permiso para ejecutar _como_ Supervisor"
        )

    def ejecutar_por_gerente(self, accion, *args, **kwargs):
        raise NotImplementedError(
            "El usuario no tiene permiso para ejecutar _como_ Gerente"
        )

    def permite_ejecutar_por_vendedor(self):
        return False

    def permite_ejecutar_por_supervisor(self):
        return False

    def permite_ejecutar_por_gerente(self):
        return False


class PermisoDeGerente(Permiso):
    def ejecutar_por_gerente(self, accion, *args, **kwargs):
        return accion.ejecutar(*args, **kwargs)

    def permite_ejecutar_por_gerente(self):
        return True


class PermisoDeSupervisor(PermisoDeGerente):
    def ejecutar_por_supervisor(self, accion, *args, **kwargs):
        return accion.ejecutar(*args, **kwargs)

    def permite_ejecutar_por_supervisor(self):
        return True


class PermisoDeVendedor(PermisoDeSupervisor):
    def ejecutar_por_vendedor(self, accion, *args, **kwargs):
        return accion.ejecutar(*args, **kwargs)

    def permite_ejecutar_por_vendedor(self):
        return True


class Cargo(object, metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def ejecutar(accion, *args, **kwargs):
        """Intenta ejecutar una accion"""

    @abc.abstractmethod
    def puede_ejecutar(self, accion):
        """Devuelve True si el permiso de la accion es el adecuado para el Cargo del usuario"""


class Vendedor(Cargo):
    def ejecutar(self, accion, *args, **kwargs):
        return accion.permiso.ejecutar_por_vendedor(accion, *args, **kwargs)

    def puede_ejecutar(self, accion):
        return accion.permiso.permite_ejecutar_por_vendedor()


class Supervisor(Vendedor):
    def ejecutar(self, accion, *args, **kwargs):
        return accion.permiso.ejecutar_por_supervisor(accion, *args, **kwargs)

    def puede_ejecutar(self, accion):
        return accion.permiso.permite_ejecutar_por_supervisor()


# class Gerente(Supervisor):
#    def ejecutar(self, accion, *args, **kwargs):
#        return accion.permiso.ejecutar_por_gerente(accion, *args, **kwargs)
#    def puede_ejecutar(self, accion):
#        return accion.permiso.permite_ejecutar_por_gerente()

CARGOS = {
    "Vendedor": Vendedor,
    "Supervisor": Supervisor,
    # 'Gerente':      Gerente,
}
