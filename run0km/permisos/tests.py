from django.test import TestCase

from permisos.main import Accion, PermisoDeVendedor, PermisoDeSupervisor, PermisoDeGerente
from permisos.main import Vendedor, Supervisor#, Gerente

def doble(x):
    return 2*x

class TestDePermisos(TestCase):
    def setUp(self):
        super(TestDePermisos, self).setUp()
        self.vendedor = Vendedor()
        self.supervisor = Supervisor()
        #self.gerente = Gerente()

class PuedeEjecutarTest(TestDePermisos):
    def test_permisos_vendedor(self):
        """
        Testeo que todos los Cargos tengan permiso para ejecutar una accion con permisos de Vendedor
        """
        accion = Accion(PermisoDeVendedor(), doble)

        self.assertTrue(self.vendedor.puede_ejecutar(accion))
        self.assertTrue(self.supervisor.puede_ejecutar(accion))
        #self.assertTrue(self.gerente.puede_ejecutar(accion))

    def test_permisos_supervisor(self):
        """
        Testeo que solo Gerentes y Supervisores tengan permiso para ejecutar una accion con permisos de Supervisor
        """
        accion = Accion(PermisoDeSupervisor(), doble)
        self.assertFalse(self.vendedor.puede_ejecutar(accion))
        self.assertTrue(self.supervisor.puede_ejecutar(accion))
        #self.assertTrue(self.gerente.puede_ejecutar(accion))

    def test_permisos_gerentes(self):
        """
        Testeo que solo los Gerentes tengan permiso para ejecutar una accion con permisos de Gerente
        """
        accion = Accion(PermisoDeGerente(), doble)
        self.assertFalse(self.vendedor.puede_ejecutar(accion))
        self.assertFalse(self.supervisor.puede_ejecutar(accion))
        #self.assertTrue(self.gerente.puede_ejecutar(accion))

class EjecutarTest(TestDePermisos):
    def test_ejecutar_vendedor(self):
        """
        Testeo que todos los Cargos ejecuten correctamente una accion con permisos de Vendedor
        """
        accion = Accion(PermisoDeVendedor(), doble)
        self.assertEqual(4,self.vendedor.ejecutar(accion,2))
        self.assertEqual(4,self.supervisor.ejecutar(accion,2))
        #self.assertEqual(4,self.gerente.ejecutar(accion,2))
    def test_ejecutar_supervisor(self):
        """
        Testeo que solo Gerentes y Supervisores ejecuten correctamente una accion con permisos de Supervisor
        """
        accion = Accion(PermisoDeSupervisor(), doble)
        self.assertRaises(NotImplementedError, self.vendedor.ejecutar, accion, 2)
        self.assertEqual(4,self.supervisor.ejecutar(accion,2))
        #self.assertEqual(4,self.gerente.ejecutar(accion,2))

    def test_ejecutar_gerente(self):
        """
        Testeo que solo Gerentes ejecuten correctamente una accion con permisos de Gerente
        """
        accion = Accion(PermisoDeGerente(), doble)
        self.assertRaises(NotImplementedError, self.vendedor.ejecutar, accion, 2)
        self.assertRaises(NotImplementedError, self.supervisor.ejecutar, accion, 2)
        #self.assertEqual(4,self.gerente.ejecutar(accion,2))
