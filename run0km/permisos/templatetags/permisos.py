from django import template
from django.conf import settings

from occ.novedades.servicio_de_novedades import ServicioDeNovedades

register = template.Library()


@register.filter
def tiene_permiso_para(cargo, accion):
    if accion in settings.PERMISOS:
        return cargo.puede_ejecutar(settings.PERMISOS[accion])
    return False


@register.filter
def puede_acceder_a_novedades(usuario):
    servicio = ServicioDeNovedades()
    return servicio.puede_ver(usuario)


@register.filter
def no_pertenece_a_sitio_principal(usuario):
    return usuario.is_authenticated() and not usuario.pertenece_a_concesionaria_con_sitio_propio()
