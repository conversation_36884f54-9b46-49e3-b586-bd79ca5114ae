{% load static from staticfiles %}

<div class="ajustar"></div>
<div class="panel">
    <div class="titulo" data-toggle="collapse" data-target="#{{ id_panel }}" aria-expanded="true"
         aria-controls="">{{ titulo }}
        <strong id="ventas_totales" style="float: right;font-size: 14px;color: #FFF;font-weight: 700;padding-top: 5px;">
            Cantidad de Ventas Pendientes: {{ ventas_pendientes_totales }}
        </strong>
    </div>
    <div class="cuerpo collapse show container px-1" id="{{ id_panel }}">
        <table class='lista-vendedores' style="text-align: left; width: 100%; margin: 0px; table-layout: fixed;">
{% if ventas_pendientes_totales > 0 %}
    {% for ventas_de_supervisor in ventas_por_supervisor %}
            {% if ventas_de_supervisor.cantidad_de_pendientes > 0 %}
                {% if user.is_gerente %}
                    <thead>
                        <tr style="font-weight: bold;height: 50px">
                            <th style="width:100px"><img src="{% static 'img/arrow.png' %}" style="margin: 5px;vertical-align: -50%"/>Supervisor:</th>
                            <th colspan="9" id="nombre_{{ ventas_de_supervisor.supervisor.id }}">{{ ventas_de_supervisor.supervisor.user.get_full_name}}</th>
                        </tr>
                    </thead>
                {% endif %}
                {% for info_vendedor in ventas_de_supervisor.informacion_de_ventas %}
                    {% if info_vendedor.ventas_pendientes|length > 0 %}
                        <tr style="background-color: #E8E8E8;font-weight: bold;height: 50px">
                            <td>
                                {% if not user.is_gerente %} <img src="{% static 'img/arrow.png' %}" style="margin: 5px;vertical-align: -50%"/>{% endif %}
                                Vendedor:
                            </td>
                            <td colspan="9" style="text-align: left" id="nombre_{{ info_vendedor.vendedor.id }}">{{info_vendedor.vendedor}}</td>
                        </tr>
                        <tr>
                            <th style="text-align: left; padding: 10px;">Fecha de Realización</th>
                            <th style="text-align: left; padding: 10px;" colspan="2">Comprador</th>
                            <th style="text-align: left;">{{ rubro.etiqueta_marca.title }}</th>
                            <th style="text-align: left;">{{ rubro.etiqueta_modelo.title }}</th>
                            <th style="text-align: left;">Precio</th>
                            <th style="text-align: left;">Número de Contrato</th>
                            <th style="text-align: center;" colspan="2">Estado</th>
                            <th style=" text-align: left;">Cancelar Venta</th>
                        </tr>
                        {% for venta in info_vendedor.ventas_pendientes %}
                            <tr id="filas">
                                <td style="padding: 10px;">
                                    {{ venta.fecha_de_realizacion}}
                                </td>
                                <td colspan="2">
                                    <a class="comprador" href="{% url 'prospecto' venta.prospecto.pk %}" title="Click para ir al prospecto {{ venta.nombre_de_comprador }}">
                                        {{ venta.nombre_de_comprador }}
                                    </a>
                                </td>
                                <td>{{ venta.marca }}</td>
                                <td>
                                    <input class="input-modelo-de-venta" id="modelo-de-venta-{{ venta.id }}" value="{{ venta.modelo }}" onchange="guardarCampo('modelo','{{ venta.id }}')" >
                                </td>
                                <td>
                                    <input class="input-precio-de-venta" id="precio-de-venta-{{ venta.id }}" value="{{ venta.precio }}" onchange="guardarCampo('precio','{{ venta.id }}')" >
                                </td>
                                <td>
                                    <input id= "numero_de_contrato-de-venta-{{ venta.id }}" class="input-numero-de-contrato-de-venta" onchange="guardarCampo('numero_de_contrato', '{{ venta.id }}')"
                                     value="{{ venta.numero_de_contrato | default:""}}" size="13" {% if venta.cancelada %}disabled{% endif %}/>
                                </td>
                                <td style="text-align: center;" id="estado_de_venta_pendiente_{{ venta.id }}" colspan="2">
                                    <a class="vendedor-deshabilitado" href="#" onclick="AprobarVenta('{{venta.id}}'); return false" title="Aprobar"></a>
                                </td>
                                <td style="text-align: center;">
                                    <a href="#" onclick="cancelarVenta('{{ venta.id }}', '{{ venta.prospecto.pk }}')" title="Cancelar Venta">
                                    <img style="padding-right: 20px;" src="{% static 'img/cerrar2.png' %}"/>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endif %}
    {% endfor %}
{% else %}
    <tr>
        <th></th>
        <th style="color: grey; padding: 10px;">Sin Ventas</th>
        <th colspan="8"></th>
    </tr>
{% endif %}
</table>
    </div>
</div>