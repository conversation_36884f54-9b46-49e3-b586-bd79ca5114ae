{%extends "base.html"%}
{% load permisos %}
{% load static from staticfiles %}

{% block js%}
  {{block.super}}
    <link href="{% static "css/ranking.css" %}" type="text/css" rel="stylesheet"/>
    <link href="{% static "css/solapas.css" %}" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" src="{% static "js/csrf_token.js" %}"></script>
    <script type="text/javascript" src="{% static "js/system_unavailable.js" %}"></script>
    <script type="text/javascript" src="{% static "js/ranking_vendedores.js" %}"></script>
    <script type="text/javascript">

        // Poner vendedores en comparacion a ..

        var filtro_rankings_url = "{% url 'filtro_rankings' %}";
        var iniciar_competencia_url = "{% url 'iniciar_competencia' '0000' %}";
        var ranking_habilitado = "{{ ranking_habilitado }}";
        var csrf_token = '{{csrf_token}}';
        var anio = "{{ anio }}";

        $( document ).ready(function() {
            $('#anioactual').val(new Date().getFullYear());
            $('#anioanterior').val(new Date().getFullYear() - 1);
            $('#cmbMes').val(new Date().getMonth() + 1);
            actualizar_vista(ranking_habilitado);
        });
    </script>
{% endblock %}

{% block content %}

{% csrf_token %}

{% for vendedor in vendedores %}
    <input type="hidden" id="nombre_{{ vendedor.id }}" value="{{ vendedor.user.get_full_name }}"/>
{% endfor %}

<div class="principal prospecto-resumen contenido">
    <div class="titulo">
        <img src="{% static "img/titulo.png" %}"/>
       <div>
           <h1>
               Ventas e incentivos
           </h1>
       </div>
    </div>
    <div class="contenedor">
        <div class="contenedor-borde">
            {% if dominio.banner_ranking%}
            <img style="margin-bottom: 30px" src="{{dominio.banner_ranking}}" />
            {% endif %}
            {% with 'ranking' as solapa_seleccionada %}
                {% include 'solapas_menu_ventas.html' %}
            {% endwith %}

            <div class="bandeja-entrada" style="text-align: center">
                <p id="lblTitulo" style="position:relative;top:5px">Ranking de Vendedores Por Cantidad</p>
            </div>

            {% if user.is_gerente  %}
                <div style="text-align: right;margin-right:50px;margin-top:20px">
                    Supervisor:
                    <select onchange="supervisor_changed()" id="cmbSupervisor">
                        <option value="0">Todos</option>
                        {% for vendedor in vendedores  %}
                          {% if vendedor.cargo == 'Supervisor' %}
                            <option value="{{ vendedor.id }}">{{ vendedor.user.get_full_name }}</option>
                          {% endif %}
                        {% endfor %}
                    </select>
                </div>
                <br>
            {% endif %}

            <div style="text-align: right;margin-right:50px;">
                {% if not user.is_gerente  %}
                    <br/>
                {% endif %}
                {% if not user.is_vendedor or user.vendedor.cargo == 'Supervisor' %}
                    <a id='btnIniciarCompetencia' href="javascript:iniciar_competencia();"><img
                            src="{% static "img/btn-iniciar-competencia.png" %}"/></a>
                {% endif %}
            </div>

            <div style="float:right;margin-right:250px" class='spin-holder' id="filtros-spinner"></div>
            <div id="filtros" style="text-align: right;margin-right:50px;margin-top:20px">
                <input id="anioactual" class="anioseleccionado" type="button" onclick="filtrar_por_anio(this.value)"/>
                <input id="anioanterior" class="anionoseleccionado" type="button" onclick="filtrar_por_anio(this.value)"/>
                <select id="cmbMes" onchange="filtrar_por_fecha()">
                    <option value="1">Enero</option>
                    <option value="2">Febrero</option>
                    <option value="3">Marzo</option>
                    <option value="4">Abril</option>
                    <option value="5">Mayo</option>
                    <option value="6">Junio</option>
                    <option value="7">Julio</option>
                    <option value="8">Agosto</option>
                    <option value="9">Septiembre</option>
                    <option value="10">Octubre</option>
                    <option value="11">Noviembre</option>
                    <option value="12">Diciembre</option>
                </select>
            </div>

            <div id="divVer" style="text-align: center;margin-top: 20px">
                <a id="lnkVerPorMonto" href="javascript:ver_por_monto()"><img src="{% static "img/money.png" %}">Ver por
                    monto</a>
                <a style="display:none" id="lnkVerPorCantidad" href="javascript:ver_por_cantidad()"><img
                        src="{% static "img/calculator.png" %}">Ver por cantidad</a>
            </div>

            <table id="tblGanadores" style="color:#0040FF;font-weight:Normal;margin-left: auto;margin-right: auto;width: 500px;text-align: center;margin-top: 10px">
                <tr style="background-color: #CEE3F6">
                    <th>MEDALLA</th>
                    <th>VENDEDOR</th>
                    {% if user.vendedor.es_supervisor %}
                    <th>EQUIPO</th>
                    {% endif %}
                    <th>VENTAS</th>
                </tr>
                <tr>
                    <td><img src="{% static "img/medalla-oro.png" %}"/></td>
                    <td id="vendedor1_nombre">{%if vendedores_ganadores.0.vendedor%}{{vendedores_ganadores.0.vendedor.user.get_full_name}}{%else%}-{%endif%}</td>
                    {% if user.vendedor.es_supervisor %}
                        <td id="vendedor1_equipo">{%if vendedores_ganadores.0.equipo%}{{vendedores_ganadores.0.equipo.nombre}}{%else%}-{%endif%}</td>
                    {% endif %}
                    <td id="vendedor1_ventas">{%if vendedores_ganadores.0.cantidad%}{{vendedores_ganadores.0.cantidad}}{%else%}-{%endif%}</td>
                </tr>
                <tr>
                    <td><img src="{% static "img/medalla-plata.png" %}"/></td>
                    <td id="vendedor2_nombre">{%if vendedores_ganadores.1.vendedor%}{{vendedores_ganadores.1.vendedor.user.get_full_name}}{%else%}-{%endif%}</td>
                    {% if user.vendedor.es_supervisor %}
                        <td id="vendedor2_equipo">{%if vendedores_ganadores.1.vendedor.equipo%}{{vendedores_ganadores.1.vendedor.equipo.nombre}}{%else%}-{%endif%}</td>
                    {% endif %}
                    <td id="vendedor2_ventas">{%if vendedores_ganadores.1.cantidad%}{{vendedores_ganadores.1.cantidad}}{%else%}-{%endif%}</td>
                </tr>
                <tr>
                    <td><img src="{% static "img/medalla-bronce.png" %}"/></td>
                    <td id="vendedor3_nombre">{%if vendedores_ganadores.2.vendedor%}{{vendedores_ganadores.2.vendedor.user.get_full_name}}{%else%}-{%endif%}</td>
                    {% if user.vendedor.es_supervisor %}
                        <td id="vendedor3_equipo">{%if vendedores_ganadores.2.vendedor.equipo%}{{vendedores_ganadores.2.vendedor.equipo.nombre}}{%else%}-{%endif%}</td>
                    {% endif %}
                    <td id="vendedor3_ventas">{%if vendedores_ganadores.2.cantidad%}{{vendedores_ganadores.2.cantidad}}{%else%}-{%endif%}</td>
                </tr>
            </table>

            {% if user.is_gerente or user.cargo|tiene_permiso_para:"administrar_vendedores" %}
            <div style="text-align: center;margin-top: 40px">
                <input onclick="window.location.assign('{% url 'ranking_historico' %}')" class="verrankinghistorico" type="button" value="VER RANKING HISTORICO">
            </div>
            {% endif %}

        </div>
    </div>
    <img src="{% static "img/sombra-contenedor.png" %}"/>
</div>

{% endblock %}
