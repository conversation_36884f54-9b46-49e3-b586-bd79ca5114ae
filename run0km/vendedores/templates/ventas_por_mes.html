{% extends "nuevo-maquetado/base.html" %}
{% load widget_tweaks %}
{% load vendedores_utils %}
{% load static from staticfiles %}

{% block css %}
    {{ block.super }}
    <link href="{% static 'css/staff.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />
    <link href="{% static 'css/excel.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />
    <link href="{% static 'css/ventas.css' %}?v={{ version }}" type="text/css" rel="stylesheet" />
    <link href="{% static 'css/solapas.css' %}?v={{ version }}" type="text/css" rel="stylesheet"/>
{% endblock %}

{% block js %}
    {{ block.super }}
    <script type="text/javascript" src="{% static 'lib/chart/Chart.min.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/ranking_vendedores.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/ventas_por_mes.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/ranking_historico.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/system_unavailable.js' %}?v={{ version }}"></script>
    <script type="text/javascript" src="{% static 'js/csrf_token.js' %}?v={{ version }}"></script>
    <script type="application/javascript">
        var finalizar_seguimiento_url = "{% url "finalizar_seguimiento" pk='9999' %}";
        var editar_venta_url = "{% url "editar-venta" %}";
        var desaprobar_venta_url = "{% url 'desaprobar-venta' %}";
        var cancelar_venta_url = "{% url 'cancelar-venta' pk='9999' %}";
        var id_mes_field = '{{form.mes.auto_id}}';
        var id_anio_field = '{{form.anio.auto_id}}';
        var id_proveedor_field = '{{ form.proveedor.auto_id }}'
        var aprobar_venta_url = "{% url 'aprobar-venta' %}";
        var meses_en_grafico = {{ meses_en_grafico|safe }};
        var listas_ventas = {{ listas_ventas|safe }};
        var maxima_cantidad_de_ventas = obtenerMaximaCantidadDeVentas();
        var ventas_por_mes_url = "{% url 'ventas_por_mes' anio='9999' mes='88' %}";
    </script>
    <script type="text/javascript" src="{% static 'js/ventas_por_mes.js' %}?v={{ version }}"></script>
    {% if puede_editar_ventas %}
    <script>
        $(function(){
            mostrar_comparacion(false, true, function () {
            });
        });
    </script>
    {% endif %}
{% endblock %}

{% block content %}

    <div id="ventana_overlay" style="display:none"></div>

    <form id="desaprobar-form" action="{% url 'desaprobar-venta' %}" method="POST">
        {% csrf_token %}
        <input name="pk" id="desaprobar-pk" type="hidden" value="">
    </form>
    <form id="aprobar-form" action="{% url 'aprobar-venta' %}" method="POST">
        {% csrf_token %}
        <input name="pk" id="aprobar-pk" type="hidden" value="">
    </form>
    <form id="actualizar-cantidad-form" action="{% url 'actualizar-cantidad-de-ventas' %}" method="POST">
        {% csrf_token %}
        <input name="anio" type="hidden" value="{{anio}}">
        <input name="mes" type="hidden" value="{{mes}}">
        <input name="vendedor" id="vendedor" type="hidden" value="">
        <input name="cantidad" id="cantidad" type="hidden" value="">
    </form>
    <form name="exportar-csv" id="exportar-csv" action="{% url 'exportar_ventas_por_mes' %}" method="POST">
        {% csrf_token %}
        <input name="ventas" type="hidden" value="{{ ids_ventas_form }}" />
    </form>

    <div class="principal prospecto-resumen contenido">
        <div class="titulo">
            <img src="{% static 'img/titulo.png' %}"/>
            <div><h2>Ventas e incentivos</h2></div>
        </div>
        <div class="contenedor">
             <div class="contenedor-borde">
                {% with 'ventas' as solapa_seleccionada %}
                    {% include 'solapas_menu_ventas.html' %}
                {% endwith %}
                 {% if user.is_gerente or user.vendedor.es_supervisor %}
                    <div class="holder-comparador" id="holder_comparador" style="display: none">
                        <div class="barra-titulo" style="width: 98%; margin: auto;"><h1>Comparador de Ventas</h1></div>
                        <table style="width: 100%; margin: 20px 20px">
                            <tr>
                                <td style="padding-right: 50px">
                                    <a href="{% url 'ranking_historico' %}">
                                        <img class="ver-ranking-historico" src="{{ STATIC_URL }}img/ver_historico.png"
                                             title="Ver ranking histórico"/>
                                    </a>
                                    <div class="canvas-comparador">
                                        <canvas id="canvas_comparador" height="150px" width="660px"></canvas>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                     <hr/>
                {% endif %}

                <div class="form_filtro" style="margin: 20px;">
                    <label for="{{ form.mes.auto_id }}" style="margin-right: 10px;">Mes</label>
                    {{ form.mes|attr:"style: margin-right: 25px;" }}
                    <label for="{{ form.anio.auto_id }}" style="margin-right: 10px;">Año</label>
                    {{ form.anio|attr:"style: margin-right: 25px;" }}
                    {% if user.role.puede_acceder_a_los_proveedores_de_datos %}
                    <label for="{{ form.proveedor.auto_id }}" style="margin-right: 10px;">Proveedor</label>
                    {{ form.proveedor|attr:"style: margin-right: 25px;" }}
                    {% endif %}
                    <input class="boton-default transparente" type="submit" value="Filtrar" onclick="RedireccionarPorAnioYMes();">
                    {% if puede_exportar_ventas %}
                    <button onclick="exportarVentas();" title="Exportar ventas a csv" class="excel"></button>
                    {% endif %}
                </div>
                 {% if user.is_vendedor and user.vendedor.tiene_cargo_vendedor %}
                     {% if es_mes_actual %}
                         {% include "ventas_pendientes_para_vendedor.html" with titulo="Historial de Ventas Pendientes" id_panel='historial-de-ventas-pendientes' %}
                         {% include "ventas_aprobadas_para_vendedor.html" %}
                     {% else %}
                        {% include "ventas_aprobadas_para_vendedor.html" %}
                        {% include "ventas_pendientes_para_vendedor.html" with titulo="Ventas Pendientes" id_panel='ventas-pendientes'%}
                        {% include "ventas_bloqueadas_para_vendedor.html" %}
                     {% endif %}
                     {% include "ventas_canceladas_para_vendedor.html" %}
                 {% else %}
                     {% if es_mes_actual %}
                         {% include "ventas_pendientes.html" with titulo="Historial de Ventas Pendientes" id_panel='historial-de-ventas-pendientes' %}
                         {% include "ventas_aprobadas.html" %}
                     {% else %}
                         {% include "ventas_aprobadas.html" %}
                         {% include "ventas_pendientes.html" with titulo="Ventas Pendientes" id_panel='ventas-pendientes' %}
                         {% include "ventas_bloqueadas.html" %}
                     {% endif %}
                     {% include "ventas_canceladas.html" %}
                 {% endif %}
             </div>

        </div>
        <img src="{% static 'img/sombra-contenedor.png' %}"/>
    </div>

{% endblock %}

