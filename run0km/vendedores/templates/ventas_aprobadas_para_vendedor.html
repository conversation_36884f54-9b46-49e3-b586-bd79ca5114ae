{% load vendedores_utils %}
{% load static from staticfiles %}

<div class="ajustar"></div>
<div class="panel">
    <div class="titulo" data-toggle="collapse" data-target="#ventas-aprobadas-detalle" aria-expanded="true"
         aria-controls="">Ventas Aprobadas en {{ mes|nombre_de_mes }} de {{ anio }}
        <strong id="ventas_totales" style="float: right;font-size: 14px;color: #FFF;font-weight: 700;padding-top: 5px;">
            Ventas totales: {{ ventas_aprobadas_totales }}
        </strong>
    </div>

    <div class="cuerpo collapse show container px-1" id="ventas-aprobadas-detalle">
        {% include "mensajes.html" %}

        <table class='lista-vendedores' id="lista-ventas" style="text-align: left; width: 100%; margin: 0px; table-layout: fixed;">
            {% if not informacion_de_ventas.ventas_aprobadas %}
                <tr>
                    <th></th>
                    <th style="color: grey; padding: 10px;">Sin Ventas</th>
                    <th colspan="7"></th>
                </tr>
            {% else %}
                <tr>
                    <th style="text-align: left; padding: 10px;">Fecha</th>
                    <th style="text-align: center; padding: 10px;" colspan="2">Comprador</th>
                    <th style="text-align: center;">{{ rubro.etiqueta_marca.title }}</th>
                    <th style="text-align: center;">{{ rubro.etiqueta_modelo.title }}</th>
                    <th style="text-align: center;">Precio</th>
                    <th style="text-align: center;">Número de Contrato</th>
                    <th style="text-align: right;">Estado</th>
                    <th></th>
                </tr>

                {% for venta in informacion_de_ventas.ventas_aprobadas %}
                <tr id="filas">
                    {% if venta.fecha_de_aprobacion %}
                    <td id="fecha-aprobacion-actual-{{ venta.id }}" class="{{venta.fecha_de_realizacion}}" style="padding: 10px;">
                        {{ venta.fecha_de_aprobacion}}</td>
                    {% else %}
                    <td id="fecha-aprobacion-actual-{{ venta.id }}" class="{{venta.fecha_de_realizacion}}" style="padding: 10px;">
                        {{ venta.fecha_de_realizacion}}</td>
                    {% endif %}
                    <td colspan="2">
                        <a class="comprador" href="{% url 'prospecto' venta.prospecto.pk %}" title="Click para ir al prospecto {{ venta.nombre_de_comprador }}">
                            {{ venta.nombre_de_comprador }}
                        </a>
                    </td>
                    <td style="text-align: center;">{{ venta.marca }}</td>
                    <td style="text-align: center;">{{ venta.modelo }}</td>
                    <td style="text-align: center;">{{ venta.precio }}</td>
                    <td style="text-align: center;">{{ venta.numero_de_contrato | default:""}}</td>
                    <td style="text-align: right;" class="columna_venta_aprobada" id="estado_de_venta_{{ venta.id }}">
                        <a class="vendedor-habilitado" href="#" style="cursor: default;"></a>
                    </td>
                    <td>
                    {% with venta.fecha_de_aprobacion as fecha_aprobacion and venta.fecha_de_realizacion as fecha_realizacion %}
                        {% if fecha_realizacion.year <= fecha_aprobacion.year %}
                            <p title="Fecha de Realización" id="fecha-aprobacion-antigua-{{ venta.id }}" style="font-size:11px; text-align:left;" >&nbsp ({{venta.fecha_de_realizacion|date:"M Y"}})</p>
                        {% endif %}
                    {% endwith %}
                    </td>
                </tr>
                {% endfor %}
          {% endif %}
        </table>
    </div>
</div>