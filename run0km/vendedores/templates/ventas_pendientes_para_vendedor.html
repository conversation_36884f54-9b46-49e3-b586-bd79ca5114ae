{% load static from staticfiles %}

<div class="ajustar"></div>
<div class="panel">
    <div class="titulo" data-toggle="collapse" data-target="#{{ id_panel }}" aria-expanded="true"
         aria-controls="">{{ titulo }}
        <strong id="ventas_totales" style="float: right;font-size: 14px;color: #FFF;font-weight: 700;padding-top: 5px;">
            Cantidad de Ventas Pendientes: {{ ventas_pendientes_totales }}
        </strong>
    </div>
    <div class="cuerpo collapse show container px-1" id="{{ id_panel }}">
        <table class='lista-vendedores' style="text-align: left; width: 100%; margin: 0px; table-layout: fixed;">
         {% if informacion_de_ventas.ventas_pendientes|length > 0 %}
            <tr>
                <th style="text-align: left; padding: 10px;">Fecha de Realización</th>
                <th style="text-align: center; padding: 10px;" colspan="2">Comprador</th>
                <th style="text-align: center;">{{ rubro.etiqueta_marca.title }}</th>
                <th style="text-align: center;">{{ rubro.etiqueta_modelo.title }}</th>
                <th style="text-align: center;">Precio</th>
                <th style="text-align: center;">Número de Contrato</th>
            </tr>
            {% for venta in informacion_de_ventas.ventas_pendientes %}
            <tr id="filas">
                <td style="padding: 10px;">
                    {{ venta.fecha_de_realizacion}}
                </td>
                <td style="text-align: center;" colspan="2">
                    <a class="comprador" href="{% url 'prospecto' venta.prospecto.pk %}" title="Click para ir al prospecto {{ venta.nombre_de_comprador }}">
                        {{ venta.nombre_de_comprador }}
                    </a>
                </td>
                <td style="text-align: center;">{{ venta.marca }}</td>
                <td style="text-align: center;">{{ venta.modelo }}</td>
                <td style="text-align: center;">{{ venta.precio }}</td>
                <td style="text-align: center;">{{ venta.numero_de_contrato | default:""}}</td>
            </tr>
            {% endfor %}
         {% else %}
            <tr>
                <th></th>
                <th style="color: grey; padding: 10px;">Sin Ventas</th>
                <th colspan="8"></th>
            </tr>
         {% endif %}
</table>
    </div>
</div>