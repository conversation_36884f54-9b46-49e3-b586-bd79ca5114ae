from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import EmailMessage
from django.utils import timezone

from vendedores.detector_inactividad import DetectorDeInactividad
from vendedores.models import Vendedor
from vendedores.utils.emisor_de_notificaciones import EnviadorDeMailsDeAlerta


class NotificadorDeAlertas(object):
    def __init__(self):
        super(NotificadorDeAlertas, self).__init__()
        self._emisor = EnviadorDeMailsDeAlerta()
        self._detector = DetectorDeInactividad()

    def notificar_a_supervisores_por_prospectos_no_asignados(self):
        supervisores = self._supervisores_con_notificaciones_habilitadas()
        un_dia = timezone.timedelta(days=1)

        supervisores_a_alertar = self._detector.datos_de_supervisores_con_prospectos_sin_asignar_por(
            supervisores, un_dia)
        for datos_supervisor in supervisores_a_alertar:
            self._alertar_supervisor_por_prospectos_no_asignados(datos_supervisor, self._detector)

    def notificar_a_supervisores_por_inactividad_de_vendedores(self):
        supervisores = self._supervisores_con_notificaciones_habilitadas()
        cuatro_dias = timezone.timedelta(days=4)
        supervisores_a_alertar = self._detector.datos_de_supervisores_con_vendedores_en_falta_por(
            supervisores, cuatro_dias)

        for datos_supervisor in supervisores_a_alertar:
            self._emisor.enviar_mail_por_inactividad_a_supervisor(
                datos_supervisor['supervisor'], datos_supervisor['cantidad'], datos_supervisor['vendedores'])

    def notificar_a_vendedores_por_inactividad(self):
        vendedores = self._vendedores_con_alerta_diaria_activada()
        un_dia = timezone.timedelta(days=1)
        vendedores_en_falta = self._detector.datos_de_vendedores_en_falta_por(vendedores, un_dia)

        for datos in vendedores_en_falta['vendedores']:
            cantidades_por_campania = self._detector.cantidades_por_origen_y_campania(datos['prospectos'])
            self._emisor.enviar_mail_por_inactividad_a_vendedor(
                datos['vendedor'], datos['cantidad'], cantidades_por_campania)

    def notificar_a_distribucion_por_prospectos_no_asignados(self):
        un_dia = timezone.timedelta(days=1)
        distribucion = self._usuario_distribucion()
        detector = DetectorDeInactividad()
        datos_sin_responsable = detector.datos_de_prospectos_sin_responsable_por_mas_de(un_dia)
        if self._debe_enviar_notificacion_a_distribucion(datos_sin_responsable, distribucion):
            mensaje = self._contenido_de_mensaje_por_prospectos_no_asignados_para(datos_sin_responsable)
            asunto = self._asunto_para_prospectos_no_asignados_a_distribucion(datos_sin_responsable)
            email = EmailMessage(asunto, mensaje, distribucion.email, [distribucion.email], [], headers={})
            email.send()

    def notificar_alarma_a_logistica(self):
        # [Juan] Movi este metodo a esta clase (estaba suelto por ahi),
        # TODO: refactorizar este super metodo.

        # Prospectos sin supervisor a cargo mayor a 24hs.
        un_dia = timezone.timedelta(days=1)
        prospectos_no_asignados = self._detector.datos_de_prospectos_sin_responsable_por_mas_de(un_dia)

        # Prospectos sin trabajar por 7 dias
        vendedores = Vendedor.objects.exclude(user__username__in=settings.BACKEND_USERS)
        siete_dias = timezone.timedelta(days=7)
        prospectos_sin_trabajar_x_7_dias = self._detector.datos_de_vendedores_en_falta_por(
            vendedores, siete_dias)
        for datos_vendedores in prospectos_sin_trabajar_x_7_dias['vendedores']:
            datos_vendedores['prospectos'] = self._detector.cantidades_por_origen(
                datos_vendedores['prospectos'])

        # Supervisores con datos sin asignar por mas de 4 dias
        cuatro_dias = timezone.timedelta(days=4)
        supervisores = Vendedor.objects.exclude(cargo='Vendedor').exclude(user__username__in=settings.BACKEND_USERS)
        supervisores_con_no_asignados = self._detector.datos_de_supervisores_con_prospectos_sin_asignar_por(
            supervisores,
            cuatro_dias)
        for datos_supervisores in supervisores_con_no_asignados:
            datos_supervisores['prospectos'] = self._detector.cantidades_por_origen(
                datos_supervisores['prospectos'])

        # Usuarios sin actividad por mas de 30 dias
        vendedores = Vendedor.objects.exclude(user__username__in=settings.BACKEND_USERS)
        treinta_dias = timezone.timedelta(days=30)
        usuarios_sin_actividad_x_30 = self._detector.usuarios_sin_actividad_por(vendedores, treinta_dias)
        # Usuarios sin actividad por mas de 7 dias
        siete_dias = timezone.timedelta(days=7)
        usuarios_sin_actividad_x_7 = self._detector.usuarios_sin_actividad_por(vendedores, siete_dias)

        if prospectos_no_asignados['cantidad'] > 0 \
                or prospectos_sin_trabajar_x_7_dias['cantidad'] > 0 \
                or len(supervisores_con_no_asignados) > 0 \
                or len(usuarios_sin_actividad_x_7) > 0 \
                or len(usuarios_sin_actividad_x_30) > 0:
            enviador = EnviadorDeMailsDeAlerta()
            enviador.enviar_mails_a_logistica(prospectos_no_asignados,
                                              prospectos_sin_trabajar_x_7_dias,
                                              supervisores_con_no_asignados,
                                              usuarios_sin_actividad_x_30,
                                              usuarios_sin_actividad_x_7)

    def _supervisores_con_notificaciones_habilitadas(self):
        supervisores = Vendedor.objects.activos().con_cargo_supervisor().con_alerta_a_supervisores_activadas()
        supervisores = supervisores.con_notificaciones_habilitadas()
        return supervisores

    def _vendedores_con_alerta_diaria_activada(self):
        vendedores = Vendedor.objects.activos().con_alerta_diaria_activada()
        vendedores = vendedores.con_notificaciones_habilitadas()
        return vendedores

    def _alertar_supervisor_por_prospectos_no_asignados(self, datos_supervisor, detector):
        cantidades_por_origen = detector.cantidades_por_origen_y_campania(datos_supervisor['prospectos'])
        supervisor = datos_supervisor['supervisor']
        cantidad = datos_supervisor['cantidad']
        self._emisor.enviar_mail_por_prospectos_sin_asignar(supervisor, cantidad, cantidades_por_origen)

    def _asunto_para_prospectos_no_asignados_a_distribucion(self, datos_sin_responsable):
        return "DISTRIBUCION: - %d prospectos sin asignar." % datos_sin_responsable['cantidad']

    def _contenido_de_mensaje_por_prospectos_no_asignados_para(self, datos_sin_responsable):
        mensaje = "Detalle: \n"
        for campania in datos_sin_responsable['campanias']:
            mensaje += "%s %s: %d \n" % (campania['origen'], campania['nombre'], campania['cantidad'])
        return mensaje

    def _debe_enviar_notificacion_a_distribucion(self, datos, usuario):
        return datos['cantidad'] > 0 and usuario.email

    def _usuario_distribucion(self):
        return get_user_model().objects.get(username=settings.USERNAME_DISTRIBUCION)
