# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0005_vendedor_cantidad_de_ventas_aprobadas'),
    ]

    operations = [
        migrations.CreateModel(
            name='VentasAprobadasPorMes',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('anio', models.PositiveSmallIntegerField()),
                ('mes', models.PositiveSmallIntegerField()),
                ('cantidad', models.PositiveIntegerField(default=0)),
                ('vendedor', models.ForeignKey(to='vendedores.Vendedor')),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name='ventasaprobadaspormes',
            unique_together=set([('vendedor', 'anio', 'mes')]),
        ),
        migrations.RemoveField(
            model_name='vendedor',
            name='cantidad_de_ventas_aprobadas',
        ),
    ]
