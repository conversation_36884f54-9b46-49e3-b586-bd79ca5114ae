

from django.db import migrations, models


def creacion_permisos_iniciales(apps, schema_editor):
    Vendedor = apps.get_model("vendedores", "Vendedor")
    PermisosDeVendedor = apps.get_model("vendedores", "PermisosDeVendedor")
    vendedores = Vendedor.objects.all().only('cargo')
    todos_los_permisos = []
    for vendedor in vendedores:
        permisos = PermisosDeVendedor(_vendedor=vendedor, _exportacion_de_prospectos=vendedor.cargo == 'Supervisor')
        todos_los_permisos.append(permisos)
    PermisosDeVendedor.objects.bulk_create(todos_los_permisos)


def undo_creacion_permisos_iniciales(apps, schema_editor):
    PermisosDeVendedor = apps.get_model("vendedores", "PermisosDeVendedor")
    permisos = PermisosDeVendedor.objects.all()
    permisos.delete()


class Migration(migrations.Migration):
    dependencies = [
        ('vendedores', '0048_permisosdevendedor'),
    ]

    operations = [
        migrations.RunPython(creacion_permisos_iniciales,
                             undo_creacion_permisos_iniciales)
    ]