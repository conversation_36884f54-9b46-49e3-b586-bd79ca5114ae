# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-01-17 02:06


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0043_configuraciondeservicios__vendedores_pueden_crear_propuestas'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConfiguracionDeCuentaDeEmailPropia',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_email', models.EmailField(max_length=254)),
                ('_password', models.CharField(blank=True, default=b'', max_length=64)),
                ('_duenio', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='_configuracion_cuenta_de_email', to='vendedores.Vendedor')),
            ],
        ),
    ]
