# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-02-09 18:22


from django.db import migrations
from django.db.models.functions import Length


def truncar_mensaje_incontactables(apps, schema_editor):
    configuracion_de_servicios_model = apps.get_model("vendedores", "ConfiguracionDeServicios")
    configuraciones_a_truncar = configuracion_de_servicios_model.objects.annotate(mensaje_len=Length('_mensaje_para_incontactables')).filter(mensaje_len__gt=145)
    configuraciones_a_truncar.update(_mensaje_para_incontactables='')


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0038_vendedor_limite_de_datos_diarios_al_supervisor_en_pedidos'),
    ]

    operations = [
        migrations.RunPython(truncar_mensaje_incontactables),
    ]
