# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-07-04 20:19

from django.core.exceptions import ObjectDoesNotExist
from django.db import migrations


def crear_programaciones(apps, schema_editor):
    # We can't import the Person model directly as it may be a newer
    # version than this migration expects. We use the historical version.
    Vendedor = apps.get_model("vendedores", "Vendedor")
    LogActividad = apps.get_model("vendedores", "LogActividad")

    for vendedor in Vendedor.objects.all():
        try:
            ultima_actividad = LogActividad.objects.filter(vendedor=vendedor).latest('ultima').ultima
        except ObjectDoesNotExist:
            ultima_actividad = None
        vendedor._ultima_actividad = ultima_actividad
        vendedor.save()


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0024_vendedor__ultima_actividad'),
    ]

    operations = [
        migrations.RunPython(crear_programaciones),
    ]
