# -*- coding: utf-8 -*-


from django.db import models, migrations


def asignar_concesionaria_de_supervisor_a_vendedores(apps, schema_editor):
    model_vendedor = apps.get_model("vendedores", "Vendedor")
    vendedores = model_vendedor.objects.filter(cargo='Vendedor')
    for vendedor in vendedores:
        if vendedor.supervisor and not vendedor.concesionaria == vendedor.supervisor.concesionaria:
            vendedor.concesionaria = vendedor.supervisor.concesionaria
            vendedor.save()


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0007_vendedor_ranking_habilitado'),
    ]

    operations = [
        migrations.RunPython(asignar_concesionaria_de_supervisor_a_vendedores),
    ]
