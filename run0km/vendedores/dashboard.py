# coding=utf-8
from datetime import time
from fractions import Fraction

from dateutil.rrule import rrule, MONTHLY
from django.db.models import Q
from django.utils import timezone

from layers.domain.metricas.helper import MetricaHelper
from layers.domain.metricas.productividad_general import MetricaDeProductividadGeneralDeVentasDeVendedores
from layers.domain.metricas.prospectos_finalizados import MetricaDeProspectosFinalizadosDeVendedores
from mobileapi.models import SesionAppMobile
from occ.models import ChatDeVentas
from prospectos.models import Prospecto, Venta, AsignacionDeProspecto
from vendedores.models import VentasAprobadasPorMes


class DashboardDeVendedor(object):
    def __init__(self, vendedor):
        super(DashboardDeVendedor, self).__init__()
        self._vendedor = vendedor

    @classmethod
    def nuevo_para(cls, vendedor):
        return cls(vendedor)

    def total_de_prospectos_en_rojo(self):
        return self._vendedor.prospectos_sin_atender_a_tiempo().count()

    def total_de_prospectos_con_llamado_vencido(self):
        prospectos = Prospecto.objects.de_vendedores([self._vendedor.id])
        total = prospectos.con_llamado_vencido().count()
        return total

    def total_de_prospectos_sin_agendar(self):
        prospectos = self.prospectos_sin_agendar()
        total = prospectos.count()
        return total

    def prospectos_sin_agendar(self):
        prospectos = self._vendedor.obtener_prospectos_como_vendedor()
        sin_agendar = prospectos.sin_agendar()
        return sin_agendar


# noinspection PyUnusedLocal
class DashboardDeSupervisor(object):
    def __init__(self, supervisor):
        super(DashboardDeSupervisor, self).__init__()
        self._supervisor = supervisor

    @classmethod
    def nuevo_para(cls, supervisor):
        return cls(supervisor)

    def total_de_prospectos_en_rojo(self, vendedores=None):
        total = 0
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            # Tenemos que iterar por vendedor porque la definicion de dias para
            # que este en rojo puede variar por vendedor
            cantidad = vendedor.prospectos_sin_atender_a_tiempo().count()
            total += cantidad
        return total

    def total_de_prospectos_con_llamado_vencido(self, vendedores=None):
        vendedores = vendedores or self._vendedores()
        ids_de_vendedores = [vendedor.id for vendedor in vendedores]
        prospectos = Prospecto.objects.de_vendedores(ids_de_vendedores)
        total = prospectos.con_llamado_vencido().count()
        return total

    def total_de_prospectos_sin_agendar(self, vendedores=None):
        vendedores = vendedores or self._vendedores()
        ids_de_vendedores = [vendedor.id for vendedor in vendedores]
        prospectos = Prospecto.objects.de_vendedores(ids_de_vendedores)
        total = prospectos.sin_agendar().count()
        return total

    def total_tiempos_de_respuesta(self, vendedores=None, fecha_desde=None, fecha_hasta=None):
        vendedores = vendedores or self._vendedores()
        ids_de_vendedores = [vendedor.id for vendedor in vendedores]
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)
        prospectos = Prospecto.objects.de_vendedores(ids_de_vendedores).entre_fechas(fecha_desde, fecha_hasta)
        tiempo = prospectos.tiempo_promedio_de_respuesta()
        return self._formatear_segundos_en_horas(tiempo)

    def total_tiempos_de_demora_en_finalizacion(self, vendedores=None, fecha_desde=None, fecha_hasta=None):
        vendedores = vendedores or self._vendedores()
        ids_de_vendedores = [vendedor.id for vendedor in vendedores]
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)
        prospectos = Prospecto.objects.de_vendedores(ids_de_vendedores)
        prospectos = prospectos.con_finalizacion_entre_fechas(fecha_desde, fecha_hasta)
        tiempo = prospectos.tiempo_promedio_de_finalizacion()
        return self._formatear_segundos_en_dias(tiempo)

    def total_de_conversiones_de_chats(self, vendedores=None, fecha_desde=None, fecha_hasta=None):
        vendedores = vendedores or self._vendedores()
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)
        porcentaje = self._porcentaje_de_conversiones_de_chats_de(vendedores, fecha_desde, fecha_hasta)
        return porcentaje

    def total_de_prospectos_recibidos(self, vendedores=None, fecha_desde=None, fecha_hasta=None):
        vendedores = vendedores or self._vendedores()
        ids_de_vendedores = [vendedor.id for vendedor in vendedores]
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)
        asignaciones = AsignacionDeProspecto.objects.a_vendedores(ids_de_vendedores)
        asignaciones = asignaciones.con_asignacion_a_responsable_entre_fechas(fecha_desde, fecha_hasta)
        return asignaciones.count()

    def total_de_cantidad_de_ventas(self, vendedores=None, fecha_desde=None, fecha_hasta=None):
        """
        Devuelvo el total de cantidad de ventas
        para un supervisor, de un subconjunto de sus vendedores, en un rango de fechas
        """
        vendedores = vendedores or self._vendedores()
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)

        ventas = Venta.objects.de_vendedores(vendedores).entre_fecha_de_realizacion(fecha_desde, fecha_hasta)
        ventas_aprobadas = ventas.aprobadas()
        return ventas_aprobadas.count()

    def total_de_prospectos_finalizados(self, vendedores=None, fecha_desde=None, fecha_hasta=None):
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)
        metrica = MetricaDeProspectosFinalizadosDeVendedores(vendedores, fecha_desde, fecha_hasta)
        return metrica.valor()

    def total_productividad_general(self, vendedores=None, fecha_desde=None, fecha_hasta=None):
        vendedores = vendedores or self._vendedores()
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)

        metrica = MetricaDeProductividadGeneralDeVentasDeVendedores(vendedores, fecha_desde, fecha_hasta)

        return {
            'porcentaje': metrica.valor(),
        }

    def detalle_de_prospectos_en_rojo(self, vendedores, fecha_desde, fecha_hasta):
        """
            No filtra por fechas es solo por compatibilidad, pendiente mejorar las metricas para que esto no sea
            necesario
        """
        datos = []
        total = 0
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            cantidad = vendedor.prospectos_sin_atender_a_tiempo().count()
            total += cantidad
            datos_vendedor = self._datos_de_vendedor(vendedor, cantidad)
            datos.append(datos_vendedor)

        promedio = self._calcular_promedio_de(total, vendedores)
        return {'vendedores': datos, 'promedio': promedio}

    def detalle_de_prospectos_con_llamados_vencido(self, vendedores, fecha_desde, fecha_hasta):
        """
            No filtra por fechas es solo por compatibilidad, pendiente mejorar las metricas para que esto no sea
            necesario
        """
        datos = []
        total = 0
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            cantidad = vendedor.obtener_prospectos_como_vendedor().con_llamado_vencido().count()
            total += cantidad
            datos_vendedor = self._datos_de_vendedor(vendedor, cantidad)
            datos.append(datos_vendedor)

        promedio = self._calcular_promedio_de(total, vendedores)
        return {'vendedores': datos, 'promedio': promedio}

    def detalle_de_prospectos_sin_agendar(self, vendedores, fecha_desde, fecha_hasta):
        """
            No filtra por fechas es solo por compatibilidad, pendiente mejorar las metricas para que esto no sea
            necesario
        """
        datos = []
        total = 0
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            cantidad = vendedor.obtener_prospectos_como_vendedor().sin_agendar().count()
            total += cantidad
            datos_vendedor = self._datos_de_vendedor(vendedor, cantidad)
            datos.append(datos_vendedor)

        promedio = self._calcular_promedio_de(total, vendedores)
        return {'vendedores': datos, 'promedio': promedio}

    def detalle_de_tiempos_de_respuesta(self, vendedores, fecha_desde, fecha_hasta):
        datos = []
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            prospectos = vendedor.obtener_prospectos_como_vendedor().entre_fechas(fecha_desde, fecha_hasta)
            tiempo = prospectos.tiempo_promedio_de_respuesta()
            if not tiempo:
                tiempo = self._texto_para_indicar_que_no_hay_datos()
            else:
                tiempo = int(tiempo)

            datos_vendedor = self._datos_de_vendedor(vendedor, tiempo)
            datos.append(datos_vendedor)
        return {'vendedores': datos}

    def detalle_de_tiempos_de_demora_en_finalizacion(self, vendedores, fecha_desde, fecha_hasta):
        datos = []
        total = 0
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            prospectos = vendedor.obtener_prospectos_como_vendedor().con_finalizacion_entre_fechas(fecha_desde,
                                                                                                   fecha_hasta)
            tiempo = prospectos.tiempo_promedio_de_finalizacion()
            valor = self._tiempo_como_string(self._formatear_segundos_en_dias(tiempo))
            datos_vendedor = self._datos_de_vendedor(vendedor, valor)
            total += tiempo
            datos.append(datos_vendedor)
        cantidad_de_vendedores = len(vendedores)
        if cantidad_de_vendedores > 0:
            promedio = total / cantidad_de_vendedores
        else:
            promedio = 0
        return {'vendedores': datos, 'promedio': self._tiempo_como_string(self._formatear_segundos_en_dias(promedio))}

    def detalle_de_conversiones_de_chat(self, vendedores, fecha_desde, fecha_hasta):
        datos = []
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            if vendedor != self._supervisor:
                datos_vendedor = self._detalle_productividad_de_chat_de_vendedor(fecha_desde, fecha_hasta, vendedor)
                datos.append(datos_vendedor)
        promedio = self._porcentaje_de_conversiones_de_chats_de(vendedores, fecha_desde, fecha_hasta)
        return {'vendedores': datos, 'promedio': self._print_como_porcentaje(promedio)}

    def detalle_de_productividad_general(self, vendedores, fecha_desde, fecha_hasta):
        """Nota: Pensar aca porque no esta pidiendo la descripcion() de la metrica.
        ¿esta bien que pida cosas mas especificas (lo hace mas declarativo)?
        0
        ¿la metrica no deberia integrar este desglose y detalle en su metodo descripcion()?
        """
        metrica = MetricaDeProductividadGeneralDeVentasDeVendedores(vendedores, fecha_desde, fecha_hasta)

        detalle_de_vendedores = self._detalle_de_vendedores(metrica.productividad_de_vendedores())
        promedio = self._porcentaje_string(MetricaHelper().porcentaje(metrica.cantidad_de_vendedores_validos(),
                                                                      metrica.suma_de_porcentajes_validos()))

        return {
            'vendedores': detalle_de_vendedores,
            'promedio': promedio
        }

    def _detalle_de_vendedores(self, productividades_de_vendedores):
        detalle = []
        for productividad in productividades_de_vendedores:
            porcentaje = productividad['porcentaje']
            porcentaje_string = self._porcentaje_string(porcentaje)
            detalle_de_vendedor = self._datos_de_vendedor(productividad['vendedor'], porcentaje_string)
            detalle.append(detalle_de_vendedor)
        return detalle

    def _porcentaje_string(self, porcentaje):
        if porcentaje is None:
            promedio = '-'
        else:
            promedio = '{0}%'.format(porcentaje)
        return promedio

    def detalle_de_prospectos_finalizados(self, vendedores, fecha_desde, fecha_hasta):
        fecha_hasta = fecha_hasta or self._fecha_hasta_por_defecto()
        fecha_desde = fecha_desde or self._fecha_desde_por_defecto(fecha_hasta)
        metrica = MetricaDeProspectosFinalizadosDeVendedores(vendedores, fecha_desde, fecha_hasta)

        return metrica.descripcion()

    def detalle_de_usos_de_app_mobile(self, vendedores, fecha_desde, fecha_hasta):
        datos = []
        sesiones = list(SesionAppMobile.para_vendedores(vendedores))
        for vendedor in vendedores:
            texto_dias_que_no_utiliza_la_app = self._texto_dias_que_no_utiliza_la_app(vendedor, sesiones)
            datos_vendedor = self._datos_de_vendedor(vendedor, texto_dias_que_no_utiliza_la_app)
            datos.append(datos_vendedor)
        return {'vendedores': datos}

    def detalle_de_datos_recibidos(self, vendedores, fecha_desde, fecha_hasta):
        # [{'nombre': 'juan', 'total': 180,
        #   'entregas': [{'nombre': 'sms', 'valor': 45}, {'nombre': 'premium', 'valor': 135}]}]
        datos = []
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            prospectos = vendedor.obtener_prospectos_como_vendedor().entre_fechas_de_asignacion_a_responsable(
                fecha_desde, fecha_hasta)
            cantidad = prospectos.count()
            entregas = list(prospectos.cantidades_entregadas_por_calidad())
            datos_vendedor = self._datos_de_entrega_de_datos_a_vendedor(vendedor, cantidad, entregas)
            datos.append(datos_vendedor)
        return datos

    def detalle_de_ventas_por_vendedor(self, vendedores, fecha_desde, fecha_hasta):
        """
            Responde un json: [{'nombre': 'Hernan Morales', 'cantidadVentasDeChat': 45, 'cantidaTotalDeVentas': 60}]

        """
        datos = []
        vendedores = vendedores or self._vendedores()
        for vendedor in vendedores:
            ventas = Venta.objects.de_vendedor(vendedor).entre_fecha_de_realizacion(fecha_desde, fecha_hasta)
            ventas_aprobadas = ventas.aprobadas()
            cantidad = ventas_aprobadas.count()
            ventas_de_chat = ventas_aprobadas.provenientes_de_chat().count()
            datos_vendedor = self._datos_de_ventas_de_vendedor(vendedor, ventas_de_chat, cantidad)
            datos.append(datos_vendedor)
        return datos

    def _fecha_hasta_por_defecto(self):
        fecha_hasta = timezone.now()
        fecha_hasta = timezone.make_aware(timezone.datetime.combine(fecha_hasta, time.max))
        return fecha_hasta

    def _fecha_desde_por_defecto(self, fecha_hasta):
        fecha_desde = fecha_hasta - timezone.timedelta(days=30)
        fecha_desde = timezone.make_aware(timezone.datetime.combine(fecha_desde, time.min))
        return fecha_desde

    def _texto_dias_que_no_utiliza_la_app(self, vendedor, sesiones):
        for sesion in sesiones:
            if vendedor == sesion.vendedor():
                return '%d' % sesion.tiempo_desde_el_ultimo_acceso().days
        return self._texto_para_indicar_que_no_hay_datos()

    def _datos_de_ventas_de_vendedor(self, vendedor, ventas_de_chat, total):
        datos_vendedor = {
            'nombre': self._nombre_de_vendedor(vendedor),
            'cantidadVentasDeChat': ventas_de_chat,
            'cantidaTotalDeVentas': total
        }
        return datos_vendedor

    def _datos_de_entrega_de_datos_a_vendedor(self, vendedor, total, entregas):
        datos_vendedor = {'nombre': self._nombre_de_vendedor(vendedor), 'total': total, 'entregas': entregas}
        return datos_vendedor

    def _detalle_productividad_de_chat_de_vendedor(self, fecha_desde, fecha_hasta, vendedor):
        chats_de_vendedor = ChatDeVentas.objects.para_vendedor(vendedor)
        chats_finalizados = chats_de_vendedor.entre_fechas(fecha_desde, fecha_hasta).finalizados()
        valor = self._porcentaje_de_chats_convertidos(chats_finalizados)
        datos_vendedor = self._datos_de_productividad_de_chat_de_vendedor(
            vendedor,
            productividad=self._print_como_porcentaje(valor),
            cantidad_de_compulsas_ganadas=chats_finalizados.count())
        return datos_vendedor

    def _datos_de_productividad_de_chat_de_vendedor(self, vendedor, cantidad_de_compulsas_ganadas, productividad):
        datos_vendedor = {
            'nombre': self._nombre_de_vendedor(vendedor),
            'productividad': productividad,
            'cantidadDeCompulsasGanadas': cantidad_de_compulsas_ganadas
        }
        return datos_vendedor

    def _datos_de_vendedor(self, vendedor, valor):
        datos_vendedor = {'nombre': self._nombre_de_vendedor(vendedor), 'valor': valor, 'id': vendedor.id}
        return datos_vendedor

    def _porcentaje_de_conversiones_de_chats_de(self, vendedores, fecha_desde, fecha_hasta):
        chats = ChatDeVentas.objects.de_vendedores(vendedores)
        chats = chats.entre_fechas(fecha_desde, fecha_hasta).finalizados()
        return self._porcentaje_de_chats_convertidos(chats)

    def _porcentaje_de_chats_convertidos(self, chats):
        total = chats.count()
        if total == 0:
            return 0
        else:
            cantidad_de_conversiones = chats.convertidos().count()
            return float(cantidad_de_conversiones) / total

    def _nombre_de_vendedor(self, vendedor):
        if vendedor == self._supervisor:
            nombre = '%s (supervisor)' % vendedor.full_name()
        else:
            nombre = vendedor.full_name()
        return nombre

    def _vendedores(self):
        vendedores = self._supervisor.obtener_vendedores().consulta_limpia()
        return vendedores

    @classmethod
    def texto_para_indicar_que_no_hay_datos(cls):
        return '---'

    def calcular_ranking(self, cantidad_de_prospectos, cantidad_de_ventas):
        if cantidad_de_prospectos > 0:
            if cantidad_de_ventas > 0:
                return Fraction(cantidad_de_ventas, cantidad_de_prospectos)
            return Fraction(0)
        return None

    def _texto_para_indicar_que_no_hay_datos(self):
        return self.__class__.texto_para_indicar_que_no_hay_datos()

    def _ventas_default_para(self, vendedor_id, meses):
        datos = {each: 0 for each in meses}
        return {'id': vendedor_id, 'total': 0, 'datos': datos}

    def _meses_entre(self, fecha_desde, fecha_hasta):
        meses_por_anio = {}
        for fecha in rrule(MONTHLY, dtstart=fecha_desde, until=fecha_hasta):
            meses = meses_por_anio.setdefault(fecha.year, [])
            meses.append(fecha.month)
        return meses_por_anio

    def _query_por_meses(self, meses_por_anio):
        query = None
        for anio, meses in list(meses_por_anio.items()):
            query_del_anio = Q(anio=anio, mes__in=meses)
            if not query:
                query = query_del_anio
            else:
                query |= query_del_anio
        return query

    def _print_como_porcentaje(self, numero):
        return '{0:.0%}'.format(numero)

    def _formatear_segundos_en_horas(self, segundos):
        if not segundos:
            valor = self._texto_para_indicar_que_no_hay_datos()
        else:
            tiempo_en_minutos = float(segundos) / 60
            valor = '%.1f' % (tiempo_en_minutos / 60)
        return {'valor': valor, 'unidad': 'Horas'}

    def _tiempo_como_string(self, tiempo):
        if tiempo['valor'] == self._texto_para_indicar_que_no_hay_datos():
            return self._texto_para_indicar_que_no_hay_datos()
        else:
            return '%(valor)s' % tiempo

    def _formatear_segundos_en_dias(self, segundos):
        if not segundos:
            valor = self._texto_para_indicar_que_no_hay_datos()
        else:
            tiempo_en_horas = segundos / 3600
            valor = '%.f' % (tiempo_en_horas / 24)
        return {'valor': valor, 'unidad': 'Días'}

    def _calcular_promedio_de(self, total, vendedores):
        cantidad_de_vendedores = len(vendedores)
        if cantidad_de_vendedores > 0:
            promedio = round(float(total) / cantidad_de_vendedores, 0)
        else:
            promedio = 0
        return promedio


class DetalleDeMetrica(object):
    @classmethod
    def nuevo_para(cls, nombre, dashboard):
        for detalle_class in cls.__subclasses__():
            if detalle_class.nombre() == nombre:
                return detalle_class(dashboard)
        raise ValueError('El nombre %s no coincide con ningún detalle' % nombre)

    @classmethod
    def nombre(cls):
        raise NotImplementedError('Subclass responsability')

    def __init__(self, dashboard):
        super(DetalleDeMetrica, self).__init__()
        self._dashboard = dashboard

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        raise NotImplementedError('Subclass responsability')


class DetalleDeProspectosEnRojo(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'prospectosEnRojo'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_prospectos_en_rojo(vendedores, fecha_desde, fecha_hasta)


class DetalleDeProspectosConLlamadosVencidos(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'prospectosConLlamadosVencidos'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_prospectos_con_llamados_vencido(vendedores, fecha_desde, fecha_hasta)


class DetalleDeProspectosSinAgendar(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'prospectosSinAgendar'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_prospectos_sin_agendar(vendedores, fecha_desde, fecha_hasta)


class DetalleDeTiempoDeRespuesta(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'tiempoDeRespuesta'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_tiempos_de_respuesta(vendedores, fecha_desde, fecha_hasta)


class DetalleDeTiempoDeDemoraEnFinalizacion(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'tiempoDeDemoraEnFinalizacion'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_tiempos_de_demora_en_finalizacion(vendedores, fecha_desde, fecha_hasta)


class DetalleDeConversionesDeChat(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'conversionesDeChat'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_conversiones_de_chat(vendedores, fecha_desde, fecha_hasta)


class DetalleDeProductividadGeneral(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'productividadGeneral'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_productividad_general(vendedores, fecha_desde, fecha_hasta)


class DetalleDeProspectosFinalizados(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'prospectosFinalizados'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_prospectos_finalizados(vendedores, fecha_desde, fecha_hasta)


class DetalleDeUsosDeAppMobile(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'usosAppMobile'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_usos_de_app_mobile(vendedores, fecha_desde, fecha_hasta)


class DetalleDeDatosRecibidos(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'datosRecibidos'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_datos_recibidos(vendedores, fecha_desde, fecha_hasta)


class DetalleDeVentas(DetalleDeMetrica):
    @classmethod
    def nombre(cls):
        return 'metricaDeVentas'

    def datos(self, vendedores, fecha_desde, fecha_hasta):
        return self._dashboard.detalle_de_ventas_por_vendedor(vendedores, fecha_desde, fecha_hasta)
