from django.core.management.base import BaseCommand

from vendedores.utils.notificador_de_alertas import NotificadorDeAlertas


class Command(BaseCommand):
    help = 'Envia mail de alarma al usuario distribucion cuando detecte prospectos sin asignar hace mas de 24Hs'

    def handle(self, *args, **options):
        notificador = NotificadorDeAlertas()
        notificador.notificar_a_distribucion_por_prospectos_no_asignados()
