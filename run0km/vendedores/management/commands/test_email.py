from django.core.management.base import BaseCommand, CommandError
from django.utils.timezone import now
from datetime import timedelta
from django.conf import settings
from django.core.mail import EmailMessage

from vendedores.models import Vendedor

class Command(BaseCommand):
    help = 'Envia un mail de prueba'

    def handle(self, *args, **options):
        test_email()

def test_email():
    enviar_mails_a_vendedores()
    enviar_mails_a_supervisores()
    enviar_mails_a_logistica()


def enviar_mails_a_vendedores():
    bcc_mails = [settings.EMAIL_LOGISTICA]
    cuerpo = 'Usted no registra actividad hace mas de 24 hs.'
    email = EmailMessage(
                settings.SUBJECT_ALERTA_INACTIVIDAD,
                cuerpo,
                settings.DEFAULT_FROM_EMAIL,
                [],
                bcc_mails,
                headers = {})
    email.send()

def enviar_mails_a_supervisores():
    cuerpo  = 'Los siguientes vendedores no se conectan hace mas de 4 dias: XXXXXX'
    email = EmailMessage(
                settings.SUBJECT_ALERTA_SUPERVISOR,
                cuerpo,
                settings.DEFAULT_FROM_EMAIL,
                [settings.EMAIL_LOGISTICA],
                [],
                headers = {})
    email.send()

def enviar_mails_a_logistica():
    cuerpo  = 'Los siguientes vendedores no se conectan hace mas de 7 dias: XXX'
    email = EmailMessage(
                settings.SUBJECT_ALERTA_LOGISTICA,
                cuerpo,
                settings.DEFAULT_FROM_EMAIL,
                [settings.EMAIL_LOGISTICA],
                [],
                headers = {})
    email.send()
