from django.test import TestCase
from django.utils import timezone

from prospectos.models import Prospecto
from vendedores.tests.factories import un_vendedor_sin_prospectos, un_vendedor_con_prospectos_de_hace_mas_de_un_mes, \
    un_vendedor_con_prospectos_del_mes, un_vendedor_con_prospectos_del_mes_y_prospectos_de_hace_mas_de_un_mes, \
    un_vendedor_con_prospectos_de_hace_mas_de_una_semana, un_vendedor_con_prospectos_de_la_semana, \
    un_vendedor_con_prospectos_de_la_semana_y_prospectos_de_hace_mas_de_una_semana


class TiempoDeRespuestaTest(TestCase):
    def test_tiempo_promedio_mensual_y_semanal_cuando_el_vendedor_no_tiene_prospectos(self):
        vendedor = un_vendedor_sin_prospectos()
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_del_mes(), 0)
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_de_ultima_semana(), 0)

    def test_tiempo_promedio_mensual_cuando_el_vendedor_tiene_prospectos_que_no_son_del_mes(self):
        vendedor = un_vendedor_con_prospectos_de_hace_mas_de_un_mes()
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_del_mes(), 0)

    def test_tiempo_promedio_mensual_cuando_el_vendedor_tiene_prospectos_que_son_del_mes(self):
        vendedor = un_vendedor_con_prospectos_del_mes()
        resultado = Prospecto.objects.tiempo_promedio_de_respuesta()
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_del_mes(), resultado)

    def test_tiempo_promedio_mensual_cuando_el_vendedor_tiene_prospectos_que_son_del_mes_y_prospectos_que_no_lo_son(
            self):
        vendedor = un_vendedor_con_prospectos_del_mes_y_prospectos_de_hace_mas_de_un_mes()
        hace_un_mes = timezone.now() + timezone.timedelta(days=-31)
        prospectos = Prospecto.objects.exclude(asignacion__fecha_de_asignacion_a_vendedor__lte=hace_un_mes)
        resultado = prospectos.tiempo_promedio_de_respuesta()
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_del_mes(), resultado)

    def test_tiempo_promedio_semanal_cuando_el_vendedor_tiene_prospectos_que_no_son_de_la_semana(self):
        vendedor = un_vendedor_con_prospectos_de_hace_mas_de_una_semana()
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_de_ultima_semana(), 0)

    def test_tiempo_promedio_semanal_cuando_el_vendedor_tiene_prospectos_que_son_de_la_semana(self):
        vendedor = un_vendedor_con_prospectos_de_la_semana()
        resultado = Prospecto.objects.tiempo_promedio_de_respuesta()
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_de_ultima_semana(), resultado)

    def test_tiempo_promedio_semanal_cuando_el_vendedor_tiene_prospectos_que_son_de_la_semana_y_prospectos_que_no_lo_son(
            self):
        vendedor = un_vendedor_con_prospectos_de_la_semana_y_prospectos_de_hace_mas_de_una_semana()
        hace_una_semana = timezone.now() + timezone.timedelta(days=-7)
        prospectos = Prospecto.objects.exclude(asignacion__fecha_de_asignacion_a_vendedor__lte=hace_una_semana)
        resultado = prospectos.tiempo_promedio_de_respuesta()
        self.assertEqual(vendedor.tiempo_promedio_de_respuesta_de_ultima_semana(), resultado)
