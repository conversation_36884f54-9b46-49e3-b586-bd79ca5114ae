import datetime
from django.core.exceptions import ObjectDoesNotExist
from prospectos.models import <PERSON><PERSON>, <PERSON>enta
from testing.base import BaseLoggedGerenteTest
from vendedores.models import VentasAprobadasPorMes, Vendedor


class RankingVendedoresTest(BaseLoggedGerenteTest):
    def test_el_ranking_por_cantidad_por_gerente_retorna_los_mejores_vendedores_de_su_concesionaria(self):
        gerente = self.fixture['ger_1']
        vendedor = self.fixture['vend_1']

        now = datetime.datetime.now()
        mes = now.month
        anio = now.year
        cantidad = 10

        try:
            ventas_aprobadas = VentasAprobadasPorMes.objects.get(vendedor=vendedor,
                                                                 anio=anio,
                                                                 mes=mes)
        except ObjectDoesNotExist:
            ventas_aprobadas = VentasAprobadasPorMes()
        ventas_aprobadas.anio = anio
        ventas_aprobadas.mes = mes
        ventas_aprobadas.vendedor = vendedor
        ventas_aprobadas.cantidad = cantidad
        ventas_aprobadas.save()

        ventas = VentasAprobadasPorMes.objects.get_ranking_por_gerente(anio, mes, gerente)

        self.assertEqual(len(ventas), 1)
        self.assertEqual(ventas[0].cantidad, cantidad)

    def test_el_ranking_por_cantidad_por_supervisor_retorna_los_mejores_vendedores_a_su_cargo(self):
        supervisor = self.fixture['sup_1']
        vendedor = self.fixture['vend_1']

        now = datetime.datetime.now()
        mes = now.month
        anio = now.year
        cantidad = 10

        try:
            ventas_aprobadas = VentasAprobadasPorMes.objects.get(vendedor=vendedor,
                                                                 anio=anio,
                                                                 mes=mes)
        except ObjectDoesNotExist:
            ventas_aprobadas = VentasAprobadasPorMes()
        ventas_aprobadas.anio = anio
        ventas_aprobadas.mes = mes
        ventas_aprobadas.vendedor = vendedor
        ventas_aprobadas.cantidad = cantidad
        ventas_aprobadas.save()

        ventas = VentasAprobadasPorMes.objects.get_ranking_por_supervisor(anio, mes, supervisor)

        self.assertEqual(len(ventas), 1)
        self.assertEqual(ventas[0].cantidad, cantidad)

    def test_el_ranking_por_monto_por_supervisor_retorna_los_mejores_vendedores_a_su_cargo(self):
        supervisor = self.fixture['sup_1']
        vendedor = self.fixture['vend_1']
        now = datetime.datetime.now()
        mes = now.month
        anio = now.year
        precio = 100000
        prospecto = Prospecto.objects.filter(vendedor=vendedor)[0]
        venta = Venta(prospecto=prospecto,
                      fecha_de_realizacion=datetime.datetime(anio, mes, 15),
                      precio=precio,
                      vendedor=vendedor)
        venta.save()

        ventas = Vendedor.objects.get_ranking_por_monto_por_supervisor(anio, mes, supervisor)

        self.assertEqual(ventas[0][0], vendedor)
        self.assertEqual(ventas[0][1], precio)

    def test_el_ranking_por_monto_por_gerente_retorna_los_mejores_vendedores_a_su_cargo(self):
        gerente = self.fixture['ger_1']
        vendedor = self.fixture['vend_1']
        now = datetime.datetime.now()
        mes = now.month
        anio = now.year
        precio = 100000
        prospecto = Prospecto.objects.filter(vendedor=vendedor)[0]
        venta = Venta(prospecto=prospecto,
                      fecha_de_realizacion=datetime.datetime(anio, mes, 15),
                      precio=precio,
                      vendedor=vendedor)
        venta.save()

        ventas = Vendedor.objects.get_ranking_por_monto_por_gerente(anio, mes, gerente)

        self.assertEqual(ventas[0][0], vendedor)
        self.assertEqual(ventas[0][1], precio)

    def test_puedo_iniciar_la_competencia_para_un_supervisor(self):
        user_gerente = self.fixture['usr_ger_1']
        self.client.login(username=user_gerente.username, password=user_gerente.password)
        supervisor = self.fixture['sup_1']
        supervisor.ranking_habilitado = False
        supervisor.save()
        response = self.client.post('/vendedores/iniciar_competencia/' + str(supervisor.id) + '/')
        self.assertEqual(response.status_code, 200)
        supervisor = Vendedor.objects.get(id=supervisor.id)
        self.assertTrue(supervisor.ranking_habilitado)

    def test_el_ranking_por_anio_por_vendedor_retorna_sus_ventas_por_cada_mes(self):
        vendedor = self.fixture['vend_1']
        now = datetime.datetime.now()
        anio = now.year
        cantidad = 10

        venta = VentasAprobadasPorMes(anio=anio,
                                      mes=1,
                                      cantidad=cantidad,
                                      vendedor=vendedor)
        venta.save()

        ventas = VentasAprobadasPorMes.objects.get_ventas_por_vendedor_por_anio(vendedor, anio)
        ventas_enero = ventas.get(mes=1)

        self.assertEqual(ventas_enero.vendedor, vendedor)
        self.assertEqual(ventas_enero.cantidad, cantidad)

    def test_el_ranking_por_cantidad_por_supervisor_desempata_por_montos(self):
        supervisor = self.fixture['sup_1']
        vendedor1 = self.fixture['vend_1']
        vendedor2 = self.fixture['vend_2']

        now = datetime.datetime.now()
        mes = now.month
        anio = now.year
        cantidad = 10

        try:
            ventas_aprobadas = VentasAprobadasPorMes.objects.get(vendedor=vendedor1, anio=anio, mes=mes)
        except ObjectDoesNotExist:
            ventas_aprobadas = VentasAprobadasPorMes()
        ventas_aprobadas.anio = anio
        ventas_aprobadas.mes = mes
        ventas_aprobadas.vendedor = vendedor1
        ventas_aprobadas.cantidad = cantidad
        ventas_aprobadas.save()

        try:
            ventas_aprobadas = VentasAprobadasPorMes.objects.get(vendedor=vendedor2, anio=anio, mes=mes)
        except ObjectDoesNotExist:
            ventas_aprobadas = VentasAprobadasPorMes()
        ventas_aprobadas.anio = anio
        ventas_aprobadas.mes = mes
        ventas_aprobadas.vendedor = vendedor2
        ventas_aprobadas.cantidad = cantidad
        ventas_aprobadas.save()

        precio = 100000
        prospecto = Prospecto.objects.filter(vendedor=vendedor2)[0]
        venta = Venta(prospecto=prospecto,
                      fecha_de_realizacion=datetime.datetime(anio, mes, 15),
                      precio=precio,
                      vendedor=vendedor2)
        venta.save()

        ventas = VentasAprobadasPorMes.objects.get_ranking_por_supervisor(anio, mes, supervisor)

        self.assertEqual(len(ventas), 2)
        self.assertEqual(ventas[0].vendedor, vendedor2)
        self.assertEqual(ventas[1].vendedor, vendedor1)

    def test_el_ranking_por_cantidad_por_gerente_desempata_por_montos(self):
        gerente = self.fixture['ger_1']
        vendedor1 = self.fixture['vend_1']
        vendedor2 = self.fixture['vend_2']

        now = datetime.datetime.now()
        mes = now.month
        anio = now.year
        cantidad = 10

        try:
            ventas_aprobadas = VentasAprobadasPorMes.objects.get(vendedor=vendedor1, anio=anio, mes=mes)
        except ObjectDoesNotExist:
            ventas_aprobadas = VentasAprobadasPorMes()
        ventas_aprobadas.anio = anio
        ventas_aprobadas.mes = mes
        ventas_aprobadas.vendedor = vendedor1
        ventas_aprobadas.cantidad = cantidad
        ventas_aprobadas.save()

        try:
            ventas_aprobadas = VentasAprobadasPorMes.objects.get(vendedor=vendedor2, anio=anio, mes=mes)
        except ObjectDoesNotExist:
            ventas_aprobadas = VentasAprobadasPorMes()
        ventas_aprobadas.anio = anio
        ventas_aprobadas.mes = mes
        ventas_aprobadas.vendedor = vendedor2
        ventas_aprobadas.cantidad = cantidad
        ventas_aprobadas.save()

        precio = 100000
        prospecto = Prospecto.objects.filter(vendedor=vendedor2)[0]
        venta = Venta(prospecto=prospecto,
                      fecha_de_realizacion=datetime.datetime(anio, mes, 15),
                      precio=precio,
                      vendedor=vendedor2)
        venta.save()

        ventas = VentasAprobadasPorMes.objects.get_ranking_por_gerente(anio, mes, gerente)

        self.assertEqual(len(ventas), 2)
        self.assertEqual(ventas[0].vendedor, vendedor2)
        self.assertEqual(ventas[1].vendedor, vendedor1)