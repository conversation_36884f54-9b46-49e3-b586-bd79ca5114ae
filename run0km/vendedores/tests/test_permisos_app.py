from rest_framework.authtoken.models import Token

from mobileapi.tests.sincronizador.test_context import ContextoMobile
from testing.base import BaseFixturedTest
from vendedores.gestor import GestorDeVendedores


class HabilitarAppParaVendedoresTest(BaseFixturedTest):
    def setUp(self):
        super(HabilitarAppParaVendedoresTest, self).setUp()
        self.vendedor = self.fixture['vend_1']
        self.contexto_mobile = ContextoMobile()
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()

    def test_habilitar_app_setea_el_permiso_para_la_api_rest(self):
        self.assertFalse(Token.objects.filter(user=self.vendedor.user).exists())
        self.assertFalse(self.vendedor.configuracion_servicios.app_habilitada())

        self.contexto_mobile.habilitar_app_del_vendedor(vendedor=self.vendedor)

        self.assertTrue(self.vendedor.configuracion_servicios.app_habilitada())
        self.assertFalse(Token.objects.filter(user=self.vendedor.user).exists())

    def test_habilitar_app_de_vendedor_habilitado(self):
        self.contexto_mobile.habilitar_app_del_vendedor(vendedor=self.vendedor)

        self.assertTrue(self.vendedor.configuracion_servicios.app_habilitada())
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=self.vendedor, app_habilitada=True)
        self.assertTrue(self.vendedor.configuracion_servicios.app_habilitada())

    def test_deshabilitar_app_quita_el_permiso_y_el_token_para_la_api_rest(self):
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=self.vendedor, app_habilitada=True)
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=self.vendedor, app_habilitada=False)
        self.assertFalse(Token.objects.filter(user=self.vendedor.user).exists())
        self.assertFalse(self.vendedor.configuracion_servicios.app_habilitada())

    def test_deshabilitar_app_funciona_aunque_no_tuviera_el_permiso_y_el_token_previamente(self):
        self.assertFalse(Token.objects.filter(user=self.vendedor.user).exists())
        self.assertFalse(self.vendedor.configuracion_servicios.app_habilitada())
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=self.vendedor, app_habilitada=False)
        self.assertFalse(Token.objects.filter(user=self.vendedor.user).exists())
        self.assertFalse(self.vendedor.configuracion_servicios.app_habilitada())
