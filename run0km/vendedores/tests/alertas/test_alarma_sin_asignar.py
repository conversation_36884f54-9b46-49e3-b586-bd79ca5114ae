# coding=utf-8
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone

from core.tests.validators.django_email import EmailOutboxValidator
from testing.base import BaseFixturedTest
from testing.factories import ProspectosFactory
from vendedores.utils.notificador_de_alertas import NotificadorDeAlertas


class AlarmaSinAsignarTest(BaseFixturedTest):
    def setUp(self):
        super(AlarmaSinAsignarTest, self).setUp()
        self._email_validator = EmailOutboxValidator.new_for(self)
        self.supervisor = self.fixture['sup_1']
        self.campania_uno = self.fixture['camp_1']
        self.campania_dos = self.fixture['camp_2']

    def test_sin_prospectos_no_asignados(self):
        """
            # TODO: refactor pendiente. El escenario resultante no es evidente.
            Usa las factories para el escenario inicial.

            La Campaña 1 ingresaron ayer 2 prospectos y estan sin asignar y hay 3 prospecto para la campaña 2 con
            la misma condicion.

            Entonces, se notifica via un email la cantidad total (5) y el detalle por campaña.

        """
        # Dado
        self._email_validator.assert_outbox_is_empty()
        distribucion = self._configurar_mail_a_usuario_distribucion()
        self._crear_prospectos()

        # Cuando
        notificador = NotificadorDeAlertas()
        notificador.notificar_a_distribucion_por_prospectos_no_asignados()

        # Entonces
        self._email_validator.assert_outbox_with_size(size=1)
        email_enviado = self._email_validator.email_at(0)

        self._assert_notificacion_del_total_sin_asignar_en_el_subject(cantidad=5, email_enviado=email_enviado)
        self._email_validator.assert_receiver(distribucion.email, email_enviado)
        self._assert_detalle_campania(self.campania_uno, cantidad=2, email_enviado=email_enviado)
        self._assert_detalle_campania(self.campania_dos, cantidad=3, email_enviado=email_enviado)

    def _assert_detalle_campania(self, campania, cantidad, email_enviado):
        detalle = '%s: %d' % (campania.nombre, cantidad)
        self.assertTrue(detalle in email_enviado.body)

    def _assert_notificacion_del_total_sin_asignar_en_el_subject(self, cantidad, email_enviado):
        descripcion = '%d prospectos sin asignar' % cantidad
        self.assertTrue(descripcion in email_enviado.subject)

    def _configurar_mail_a_usuario_distribucion(self):
        distribucion = get_user_model().objects.get(username=settings.USERNAME_DISTRIBUCION)
        distribucion.email = "<EMAIL>"
        distribucion.save()
        return distribucion

    def _crear_prospectos(self):
        """
            Crea 2 prospectos de la campaña 1 sin asignar con fecha de ayer, 1 sin asginar con fecha actual
            Crea 3 prospectos de la campaña 2 sin asignar con fecha de ayer, 1 con fecha de ayer asignado a responsable
        """

        hace_24 = timezone.now() + timezone.timedelta(days=-1, seconds=-1)
        p1 = ProspectosFactory(campania=self.campania_uno)
        p1.fecha_creacion = hace_24
        p1.save()

        p2 = ProspectosFactory(campania=self.campania_uno)
        p2.fecha_creacion = hace_24
        p2.save()

        p3 = ProspectosFactory(campania=self.campania_uno)

        p1 = ProspectosFactory(campania=self.campania_dos)
        p1.fecha_creacion = hace_24
        p1.save()

        p2 = ProspectosFactory(campania=self.campania_dos)
        p2.fecha_creacion = hace_24
        p2.save()

        p3 = ProspectosFactory(campania=self.campania_dos)
        p3.fecha_creacion = hace_24
        p3.save()

        p4 = ProspectosFactory(campania=self.campania_dos, responsable=self.supervisor)
        p4.fecha_creacion = hace_24
        p4.save()

