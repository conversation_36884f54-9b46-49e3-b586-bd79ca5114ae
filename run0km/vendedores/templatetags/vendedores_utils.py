import json
import calendar
from django import template
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.utils.translation import ugettext_lazy as _
from core.models import Sistema
from vendedores.models import Vendedor

register = template.Library()


@register.simple_tag
def whatsapp_habilitado_por_supervisor():
    supervisores = Vendedor.objects.whatsapp_habilitado_por_supervisores()
    return mark_safe(
        json.dumps({vendedor['id']: vendedor['habilitado_para_mensajear_por_whatsapp'] for vendedor in
                    supervisores.values('id', 'habilitado_para_mensajear_por_whatsapp')}))


@register.filter
def nombre_de_mes(numero):
    return _(calendar.month_name[numero])


@register.filter
def formatear_ultima_actividad(minutos):
    if minutos is None:
        return '---'
    dias_desde_ultima_actividad = timezone.timedelta(minutes=minutos).days
    if dias_desde_ultima_actividad > 0:
        return 'Hace %d dias' % dias_desde_ultima_actividad
    else:
        return formatear_ultima_actividad_en_horas_y_minutos(minutos)


def formatear_ultima_actividad_en_horas_y_minutos(minutos):
    if minutos >= 60:
        return formatear_ultima_actividad_en_horas(minutos)
    elif minutos <= 5:
        return 'En linea'
    else:
        return 'Hace %d minutos' % minutos


def formatear_ultima_actividad_en_horas(minutos):
    horas = int(minutos / 60)
    minutos_restantes = minutos % 60
    if minutos_restantes > 0:
        return 'Hace %d hs %d min' % (horas, minutos_restantes)
    elif horas == 1:
        return 'Hace %d hora' % horas
    else:
        return 'Hace %d horas' % horas


@register.filter
def clase_por_actividad(minutos):

    if minutos is None:
        return 'vendedor-sin-actividad-reciente'
    elif minutos <= Sistema.instance().tiempo_maximo_de_inactividad_en_linea:
        return 'vendedor-en-linea'
    elif minutos <= Sistema.instance().tiempo_maximo_de_inactividad_actividad_hace_minutos:
        return 'vendedor-con-actividad-hace-minutos'
    elif minutos <= Sistema.instance().tiempo_maximo_de_inactividad_actividad_reciente:
        return 'vendedor-con-actividad-reciente'
    else:
        return 'vendedor-sin-actividad-reciente'
