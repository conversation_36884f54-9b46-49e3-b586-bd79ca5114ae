# coding=utf-8
from django.contrib import admin
from django.core.checks import messages
from django.db import models
from django.utils.encoding import force_text
from django.utils.timezone import localtime

from layers.application.commands.cambiar_configuracion_de_servicios import \
    CambiarConfiguracionDeServiciosDeVendedorCommando
from lib.model_admin_logger import ModelAdminClassProxy
from vendedores.admin_forms import VendedorAdminForm, AddVendedorAdminForm, AlwaysChangedModelForm
from vendedores.gestor import GestorDeVendedores
from vendedores.models import Vendedor, VentasAprobadasPorMes, ConfiguracionDeServicios, ConfiguracionDeCanal, \
    PermisoTransferencia
from vendedores.permisos_de_vendedor import PermisosDeVendedor


class ConfiguracionDeCanalInline(admin.TabularInline):
    verbose_name = 'Configuración de Canal'
    verbose_name_plural = 'Configuraciones de Canal'


class ConfiguracionDeCanalAdmin(admin.ModelAdmin):
    model = ConfiguracionDeCanal
    inline = [ConfiguracionDeCanalInline]
    list_display = ('_tipo', '_limite_de_envios')
    search_fields = ('_tipo',)

    readonly_fields = ['_tipo']

    def has_add_permission(self, request):
        return False


class ConfiguracionDeServiciosInline(admin.StackedInline):
    model = ConfiguracionDeServicios
    form = AlwaysChangedModelForm
    can_delete = False
    # verbose_name = u'Configuración'
    verbose_name_plural = 'Configuración de Servicios'

    def get_formset(self, request, obj=None, **kwargs):
        if obj is not None and not obj.es_supervisor():
            self.exclude = ['_mensaje_para_incontactables', '_mensaje_bienvenida', '_facebook_habilitado']
        else:
            self.exclude = ['_mensaje_para_incontactables', '_mensaje_bienvenida']
        return super(ConfiguracionDeServiciosInline, self).get_formset(request, obj, **kwargs)


class PermisosDeVendedorInline(admin.TabularInline):
    model = PermisosDeVendedor
    form = AlwaysChangedModelForm
    can_delete = False
    verbose_name_plural = 'Permisos de Vendedor'


class VendedoresAdmin(admin.ModelAdmin):
    form = VendedorAdminForm
    list_display = ('_user_fullname', 'cargo', '_user_username', '_user_email', '_ultimo_logueo')
    list_filter = ('cargo', 'eliminado', 'concesionaria')
    search_fields = ('user__username', 'user__email', 'user__first_name', 'user__last_name')
    inlines = [
        ConfiguracionDeServiciosInline, PermisosDeVendedorInline
    ]

    def get_form(self, request, obj=None, **kwargs):
        self.readonly_fields = ()
        if obj is None:
            self.form = AddVendedorAdminForm
            self.exclude = ('user',)
        else:
            self.readonly_fields = ('cargo', 'user', '_ultimo_acceso_al_listado_de_prospectos')
            self.form = VendedorAdminForm
            if not obj.es_supervisor():
                self.exclude = (
                    "ranking_habilitado", "dias_para_atender_prospecto", "ajuste_factor_de_asignacion_administrador", "_minutos_de_inactividad_maximos_para_circular_sus_prospectos"
                )
            else:
                self.exclude = []

        return super(VendedoresAdmin, self).get_form(request, obj, **kwargs)

    def _es_el_formset_de_configuracion_de_servicios(self, formset):
        return isinstance(formset.queryset.get(), ConfiguracionDeServicios)

    def save_formset(self, request, form, formset, change):
        # ConfiguracionDeServicios y PermisosDeVendedor se consideran formsets y usan el mismo método.
        super(VendedoresAdmin, self).save_formset(request, form, formset, change)
        configuracion_servicios = formset.queryset.get()
        if self._es_el_formset_de_configuracion_de_servicios(formset):
            vendedor = configuracion_servicios.vendedor()
            if not vendedor.eliminado:
                changed_data = self.get_changed_data_from(formset)
                comando = CambiarConfiguracionDeServiciosDeVendedorCommando()
                comando.set_arguments(vendedor=vendedor, campos_modificados=changed_data)
                comando.execute()

    def save_model(self, request, obj, form, change):
        esta_eliminado = form.cleaned_data['eliminado']
        cambio_eliminado = 'eliminado' in form.changed_data
        if esta_eliminado and cambio_eliminado:
            obj.deshabilitar()
        elif not esta_eliminado and cambio_eliminado:
            obj.habilitar()
        if change:
            gestor = GestorDeVendedores.nuevo()
            if 'supervisor' in form.changed_data:
                viejo_supervisor = Vendedor.all_objects.get(id=obj.id).supervisor
                gestor.reasignar_supervisor(vendedor=obj,
                                            viejo_supervisor=viejo_supervisor,
                                            nuevo_supervisor=obj.supervisor)
                self.message_user(request,
                                  'Tiene un nuevo supervisor asignado y se le desasignaron sus prospectos. '
                                  'Ademas, el nuevo supervisor ya puede personificar al vendedor.',
                                  level=messages.INFO, extra_tags='', fail_silently=False)
            if 'limite_de_datos_diarios_en_pedidos' in form.changed_data:
                gestor.configurar_limite_de_datos_diarios_en_pedidos_de(
                    vendedor=obj, nuevo_limite_diario=obj.limite_de_datos_diarios_en_pedidos)
        return super(VendedoresAdmin, self).save_model(request, obj, form, change)

    def get_queryset(self, request):
        # En vez de usar el default_manager uso all_objects para ver incluso a los eliminados
        qs = self.model.all_objects.get_queryset()
        qs = qs.annotate(ultimo_logueo=models.Max('logactividad__ultima'))
        ordering = self.get_ordering(request)
        if ordering:
            qs = qs.order_by(*ordering)
        return qs

    def get_changed_data_from(self, formset):
        changed_data = []
        for form in formset.forms:
            changed_data.extend(form.changed_data)
        return changed_data

    def _user_fullname(self, obj):
        return obj.user.get_full_name()

    _user_fullname.short_description = "Nombre completo"
    _user_fullname.admin_order_field = 'user__first_name'

    def _user_username(self, obj):
        return obj.user.username

    _user_username.short_description = "Nombre de usuario"
    _user_username.admin_order_field = 'user__username'

    def _user_email(self, obj):
        return obj.user.email

    _user_email.short_description = "Correo electrónico"
    _user_email.admin_order_field = 'user__email'

    def _ultimo_logueo(self, obj):
        ultimo_logueo = obj.ultimo_logueo
        if ultimo_logueo:
            return localtime(ultimo_logueo).strftime('%d/%m/%Y %H:%M')
        else:
            return '-'

    _ultimo_logueo.short_description = "Ultimo Logueo"
    _ultimo_logueo.admin_order_field = 'ultimo_logueo'

    def formfield_for_foreignkey(self, db_field, request=None, **kwargs):
        if db_field.name == "supervisor":
            kwargs["queryset"] = Vendedor.objects.filter(cargo='Supervisor')
        return super(VendedoresAdmin, self).formfield_for_foreignkey(db_field, request, **kwargs)


class VentasAprobadasPorMesAdmin(admin.ModelAdmin):
    list_display = ('vendedor', 'anio', 'mes', 'cantidad',)
    list_filter = ('vendedor', 'anio', 'mes',)


class PermisoTransferenciaAdmin(admin.ModelAdmin):
    list_display = ('vendedor', 'mostrar_permisos')
    search_fields = ('vendedor__user__first_name', 'vendedor__user__last_name')
    filter_horizontal = ('vendedores_permitidos',)

    def mostrar_permisos(self, obj):
        return u", ".join(
            [u"{} - {} {}".format(v.concesionaria.obtener_nombre() ,v.user.first_name, v.user.last_name) for v in obj.vendedores_permitidos.all()]
        )
    mostrar_permisos.short_description = u"Vendedores Permitidos"

    def formfield_for_foreignkey(self, db_field, request=None, **kwargs):
        if db_field.name == "vendedor":
            kwargs["queryset"] = Vendedor.objects.activos().order_by(
                'concesionaria__nombre', 'user__first_name', 'user__last_name'
            )
            formfield = super(PermisoTransferenciaAdmin, self).formfield_for_foreignkey(db_field, request, **kwargs)
            # Personalizamos cómo se muestra cada opción
            formfield.label_from_instance = lambda obj: u"{} - {} {}".format(
                obj.concesionaria.obtener_nombre() if obj.concesionaria else "Sin Concesionaria",
                obj.user.first_name,
                obj.user.last_name
            )
            return formfield
        return super(PermisoTransferenciaAdmin, self).formfield_for_foreignkey(db_field, request, **kwargs)

    def formfield_for_manytomany(self, db_field, request=None, **kwargs):
        if db_field.name == "vendedores_permitidos":
            # Modificamos el queryset para que esté ordenado
            kwargs["queryset"] = Vendedor.objects.activos().order_by(
                'concesionaria__nombre', 'user__first_name', 'user__last_name'
            )
            kwargs["widget"] = admin.widgets.FilteredSelectMultiple(
                db_field.verbose_name,
                is_stacked=False,
                attrs={'class': 'vendedores-permitidos'}
            )
        formfield = super(PermisoTransferenciaAdmin, self).formfield_for_manytomany(db_field, request, **kwargs)
        if db_field.name == "vendedores_permitidos":
            formfield.label_from_instance = lambda obj: u"{} - {} {}".format(
                force_text(obj.concesionaria.obtener_nombre()),
                force_text(obj.user.first_name),
                force_text(obj.user.last_name)
            )
        return formfield

admin.site.register(Vendedor, ModelAdminClassProxy(VendedoresAdmin,
                                                   ['limite_de_datos_nuevos_en_pedidos',
                                                    'limite_de_datos_diarios_en_pedidos']))
admin.site.register(VentasAprobadasPorMes, VentasAprobadasPorMesAdmin)
admin.site.register(ConfiguracionDeCanal, ConfiguracionDeCanalAdmin)
admin.site.register(PermisoTransferencia, PermisoTransferenciaAdmin)
