from django.core.exceptions import ValidationError, PermissionDenied

from prospectos.models import Prospecto, Venta
from prospectos.models.logger import Logger
from vendedores.models import VentasAprobadasPorMes


class GestorDeVenta(object):
    @classmethod
    def nuevo(cls):
        gestor = cls()
        return gestor

    def __init__(self):
        super(GestorDeVenta, self).__init__()
        self._logger = Logger()

    def nueva_venta(self, prospecto, vendedor, marca, modelo, fecha_de_realizacion, numero_de_contrato, precio):
        venta = Venta.nueva(prospecto=prospecto, vendedor=vendedor, marca=marca, modelo=modelo,
                            fecha_de_realizacion=fecha_de_realizacion, precio=precio,
                            numero_de_contrato=numero_de_contrato)

        self._incrementar_ventas_aprobadas(venta)
        prospecto = venta.prospecto
        self._cambiar_estado(prospecto=prospecto, estado=Prospecto.VENDIDO)
        self._logger.loggear_venta(venta)
        return venta

    def aprobar_venta(self, rol, venta):
        self._validar_rol(rol)
        venta.aprobar()
        self._incrementar_ventas_aprobadas(venta)
        responsable = venta.prospecto.responsable
        self._logger.loggear_venta_aprobada(venta.prospecto, responsable, nombre_del_evaluador=rol.full_name())

    def desaprobar_venta(self, rol, venta):
        self._validar_rol(rol)
        venta.desaprobar()
        self._decrementar_ventas_aprobadas(venta)
        responsable = venta.prospecto.responsable
        self._logger.loggear_venta_desaprobada(venta.prospecto, responsable, nombre_del_evaluador=rol.full_name())

    def cancelar_venta(self, rol, venta, motivo):
        self._validar_rol(rol)
        if not rol.tiene_asignado(venta.prospecto):
            raise ValidationError('No se puede cancelar una venta de prospectos que no tiene a cargo')
        venta.cancelar(motivo=motivo)

    def _cambiar_estado(self, prospecto, estado):
        prospecto.estado = estado
        prospecto.save()

    def _incrementar_ventas_aprobadas(self, venta):
        cuenta, nueva = VentasAprobadasPorMes.objects.get_or_create(vendedor=venta.vendedor,
                                                                    anio=venta.fecha_de_aprobacion.year,
                                                                    mes=venta.fecha_de_aprobacion.month,
                                                                    defaults={'cantidad': 1})
        if not nueva:
            cuenta.cantidad += 1
            cuenta.save()

    def _decrementar_ventas_aprobadas(self, venta):
        cuenta, nueva = VentasAprobadasPorMes.objects.get_or_create(vendedor=venta.vendedor,
                                                                    anio=venta.fecha_de_aprobacion.year,
                                                                    mes=venta.fecha_de_aprobacion.month,
                                                                    defaults={'cantidad': 0})
        if not nueva:
            cuenta.cantidad = max(cuenta.cantidad - 1, 0)
            cuenta.save()

    def _validar_rol(self, rol):
        if not self._puede_gestionar_ventas(rol):
            raise PermissionDenied()

    def _puede_gestionar_ventas(self, rol):
        return rol.es_gerente() or rol.es_supervisor()

    def validar_que_la_venta_pertenezca_al_vendedor_del_supervisor(self, id_venta, vendedor):
        if not self._puede_gestionar_ventas(vendedor) or int(id_venta) not in list(Venta.objects.de_supervisor(
                vendedor).values_list('id', flat=True)):
            raise PermissionDenied()

    def guardar_precio(self, venta, precio):
        venta.precio = str(precio)
        venta.clean_fields(exclude='vendedor')
        venta.save()

    def guardar_modelo(self, venta, nombre_de_modelo):
        venta.modelo = nombre_de_modelo
        venta.clean_fields(exclude='vendedor')
        venta.save()

    def guardar_numero_de_contrato(self, venta, numero_de_contrato):
        venta.numero_de_contrato = numero_de_contrato
        venta.clean_fields(exclude='vendedor')
        venta.save()
