import datetime
from operator import itemgetter

from dateutil.relativedelta import relativedelta
from django.core.exceptions import ObjectDoesNotExist
from django.db import models
from django.db.models import Sum, Max
from django.db.models.expressions import F, Case, When, Value
from django.utils import timezone

from core.querysets import DeliveryQuerySet
from prospectos.models import Venta


class ConfiguracionDeServiciosManager(models.Manager):
    def configurar_permisos_para_app_mobile_a(self, vendedores, habilitar=True):
        self.filter(_vendedor__in=vendedores).update(_app_habilitada=habilitar)

    def configurar_permisos_para_llamadas_a(self, vendedores, habilitar=True):
        self.filter(_vendedor__in=vendedores).update(_llamadas_habilitadas=habilitar)

    def de_vendedor(self, vendedor):
        return self.get(_vendedor=vendedor)


class VendedorQuerySet(DeliveryQuerySet):
    def activos(self):
        return self.filter(user__is_active=True)

    def inactivos(self):
        return self.filter(user__is_active=False)

    def con_cargo_vendedor(self):
        return self.filter(cargo=self.model.CARGO_VENDEDOR)

    def con_cargo_supervisor(self):
        return self.filter(cargo=self.model.CARGO_SUPERVISOR)

    def con_alerta_a_supervisores_activadas(self):
        return self.filter(alerta_a_supervisor=True)

    def con_notificaciones_habilitadas(self):
        return self.filter(configuracion_servicios___notificaciones_habilitadas=True)

    def con_alerta_diaria_activada(self):
        return self.filter(alerta_diaria=True)

    def con_supervisor(self, supervisor):
        return self.filter(supervisor=supervisor)

    def ordenar_por_concesionaria(self):
        return self.order_by('concesionaria__nombre', 'user__first_name')

    def con_nombre_de_usuario(self, username):
        return self.get(user__username=username)


class VendedorManager(models.Manager):
    def get_queryset(self):
        return VendedorQuerySet(self.model, using=self._db).filter(eliminado=False)

    # noinspection PyUnresolvedReferences
    def activos(self):
        return self.get_queryset().activos()

    # noinspection PyUnresolvedReferences
    def inactivos(self):
        return self.get_queryset().inactivos()

    # noinspection PyUnresolvedReferences
    def con_cargo_vendedor(self):
        return self.get_queryset().con_cargo_vendedor()

    def con_cargo_supervisor(self):
        return self.get_queryset().con_cargo_supervisor()

    def ids(self):
        return self.get_queryset().ids()

    # noinspection PyUnresolvedReferences
    def con_ids(self, ids):
        return self.get_queryset().con_ids(ids)

    # noinspection PyUnresolvedReferences
    def para_id_o_none(self, identificador):
        return self.get_queryset().para_id_o_none(identificador)

    def uno_con_nombre_de_usuario(self, username):
        return self.get(user__username=username)

    def con_configuraciones_de_integracion(self, configuraciones):
        return self.filter(_configuraciones_de_integracion__in=configuraciones)

    def ventas_bloqueadas_del_mes(self, vendedor, anio, mes, proveedor=None):
        ventas = Venta.objects.filter(vendedor=vendedor, fecha_de_realizacion__month=mes, fecha_de_realizacion__year=anio,
                                      fecha_de_aprobacion__isnull=False, estado=Venta.APROBADA,
                                      fecha_de_realizacion__lt=F('fecha_de_aprobacion'))
        if proveedor:
            ventas = ventas.de_proveedor(proveedor)
        ventas_bloqueadas = [venta for venta in ventas if venta.esta_bloqueada()]
        return ventas_bloqueadas

    def ventas_canceladas_del_mes(self, vendedor, anio, mes):
        ventas_canceladas = Venta.objects.de_vendedor(vendedor).con_fecha_de_cancelacion_en(mes, anio)
        return ventas_canceladas

    def vendedores_de_supervisor_ordenados_por_cantidad_de_ventas_del_mes(self, vendedores, supervisor, anio, mes):
        vendedores = vendedores.all() | self.filter(id=supervisor.id)
        vendedores_ordenados = vendedores.annotate(cantidad_de_ventas_del_mes=Sum(
            Case(When(ventas_aprobadas_por_mes__anio=anio, ventas_aprobadas_por_mes__mes=mes,
                      then='ventas_aprobadas_por_mes__cantidad'), default=Value(0)))).order_by(
            '-cantidad_de_ventas_del_mes')
        return vendedores_ordenados

    def supervisores_ordenados_por_concesionaria(self):
        return self.supervisores_activos().order_by('concesionaria__nombre')

    def supervisores_de_concesionarias(self, concesioarias):
        return self.supervisores_activos().filter(concesionaria__in=concesioarias)

    def supervisores_activos(self):
        return self.vendedores_activos().con_cargo_supervisor()

    def supervisores_activos_ordenados_por_concesionaria(self):
        return self.supervisores_activos().ordenar_por_concesionaria()

    def definir_orden(self, vendedor_1, vendedor_2):
        if vendedor_1.cargo == 'Vendedor' and vendedor_1.supervisor is None:
            return -1
        elif vendedor_2.cargo == 'Vendedor' and vendedor_2.supervisor is None:
            return 1
        elif vendedor_1.cargo == 'Supervisor' and vendedor_2.cargo == 'Supervisor':
            return self._comparar_vendedores(vendedor_1, vendedor_2)
        elif vendedor_1.cargo == 'Supervisor' and vendedor_2.cargo == 'Vendedor':
            if vendedor_2.supervisor.id == vendedor_1.id:
                return -1
            else:
                return self._comparar_vendedores(vendedor_1, vendedor_2.supervisor)
        elif vendedor_1.cargo == 'Vendedor' and vendedor_2.cargo == 'Supervisor':
            if vendedor_1.supervisor.id == vendedor_2.id:
                return 1
            else:
                return self._comparar_vendedores(vendedor_1.supervisor, vendedor_2)
        elif vendedor_1.cargo == 'Vendedor' and vendedor_2.cargo == 'Vendedor':
            return self._comparar_vendedores(vendedor_1.supervisor, vendedor_2.supervisor)

    def _comparar_vendedores(self, vendedor_1, vendedor_2):
        return (vendedor_1.id > vendedor_2.id) - (vendedor_1.id < vendedor_2.id)

    def vendedores_activos(self):
        return self.activos_en(self)

    def activos_en(self, vendedores):
        return vendedores.filter(user__is_active=True)

    def para(self, usuario):
        return self.get(user=usuario)

    def get_ranking_por_monto_por_lista_de_vendedores(self, vendedores, anio, mes):
        desde = datetime.datetime(anio, mes, 1)
        hasta = datetime.datetime(anio, mes, 1) + relativedelta(months=1) - datetime.timedelta(days=1)
        vendedores_con_montos_y_monto_historico = \
            [(x, x.dinero_en_ventas_entre(desde, hasta), x.dinero_en_ventas_historico()) for x in vendedores]
        vendedores_ordenados = sorted(vendedores_con_montos_y_monto_historico, key=itemgetter(1, 2), reverse=True)
        return [(x[0], x[1]) for x in vendedores_ordenados[:3]]

    def get_ranking_por_monto_por_supervisor(self, anio, mes, supervisor):
        vendedores = self.filter(supervisor=supervisor)
        return self.get_ranking_por_monto_por_lista_de_vendedores(vendedores, int(anio), int(mes))

    def get_ranking_por_monto_por_gerente(self, anio, mes, gerente):
        vendedores = self.filter(concesionaria=gerente.concesionaria)
        return self.get_ranking_por_monto_por_lista_de_vendedores(vendedores, int(anio), int(mes))

    def whatsapp_habilitado_por_supervisores(self):
        supervisores = self.con_cargo_supervisor().annotate(
            habilitado_para_mensajear_por_whatsapp=F('concesionaria__configuracion_servicios___whatsapp_habilitado'))
        return supervisores

    def anotar_ultima_asignacion_de_vendedores(self, vendedores):
        return vendedores.annotate(ultima_asignacion=Max('prospectos__asignacion__fecha_de_asignacion_a_vendedor'))

    def anotar_ultima_asignacion_de_supervisores(self, supervisores):
        return supervisores.annotate(ultima_asignacion=Max('prospectos__asignacion__fecha_de_asignacion_a_supervisor'))

    def con_app_habilitada(self):
        return self.vendedores_activos().filter(configuracion_servicios___app_habilitada=True)

    def de_proveedor(self, proveedor):
        return self.filter(
            prospecto__campania__categoria__distribuidor__id=proveedor
        )


class VentasAprobadasPorMesManager(models.Manager):
    def _ordenar_por_cantidad_y_monto(self, ventas_aprobadas_por_mes):
        ventas_con_cantidades_y_monto_historico = \
            [(x, x.cantidad, x.vendedor.dinero_en_ventas_historico()) for x in ventas_aprobadas_por_mes]
        ventas_ordenadas = sorted(ventas_con_cantidades_y_monto_historico, key=itemgetter(1, 2), reverse=True)
        return [x[0] for x in ventas_ordenadas[:3]]

    def get_ranking_por_supervisor(self, anio, mes, supervisor):
        ventas_aprobadas_por_mes = self.filter(vendedor__supervisor=supervisor, anio=anio, mes=mes)
        return self._ordenar_por_cantidad_y_monto(ventas_aprobadas_por_mes)

    def get_ranking_por_gerente(self, anio, mes, gerente):
        ventas_aprobadas_por_mes = self.filter(vendedor__concesionaria=gerente.concesionaria, anio=anio, mes=mes)
        return self._ordenar_por_cantidad_y_monto(ventas_aprobadas_por_mes)

    def get_ventas_por_vendedor_por_anio(self, vendedor, anio):
        return self.filter(vendedor=vendedor, anio=anio)

    def get_ventas_aprobadas_por_anio(self, lista_vendedores, anio):
        listas_ventas = []
        for vendedor in lista_vendedores:
            ventas_aprobadas = self.get_ventas_por_vendedor_por_anio(vendedor, anio)
            ventas = {"info": {
                "id": vendedor.id,
                "nombre": vendedor.full_name()
            }}
            for i in range(1, 13):
                ventas_del_mes = ventas_aprobadas.filter(mes=i)
                if len(ventas_del_mes) == 1:
                    ventas[i] = ventas_del_mes[0].cantidad
                else:
                    ventas[i] = 0
            listas_ventas.append(ventas)
        return listas_ventas

    def ventas_por_supervisor_por_mes(self, anio, mes, supervisor):
        """Devuelvo la cantidad de ventas hechas por los vendedores de un supervisor (sin incluirlo a el mismo)"""
        resultado = self.filter(anio=anio, mes=mes, vendedor__supervisor=supervisor).aggregate(suma=Sum('cantidad'))
        return 0 if resultado['suma'] is None else resultado['suma']

    def ventas_por_supervisor_por_meses_en_rango(self, anio_desde, mes_desde, anio_hasta, mes_hasta, vendedores):
        anios_y_meses = self._anios_y_meses_en_el_rango(anio_desde, mes_desde, anio_hasta, mes_hasta)
        total = 0
        for anio_mes in anios_y_meses:
            resultado = self.filter(anio=anio_mes[0], mes=anio_mes[1], vendedor__in=vendedores).aggregate(suma=Sum('cantidad'))
            subtotal = 0 if resultado['suma'] is None else resultado['suma']
            total = total + subtotal
        return total

    def _anios_y_meses_en_el_rango(self, anio_desde, mes_desde, anio_hasta, mes_hasta):
        tuplas = []
        if anio_desde > anio_hasta:
            return tuplas
        if anio_desde == anio_hasta and mes_desde > mes_hasta:
            return tuplas
        anio_actual = anio_desde
        mes_actual = mes_desde

        while anio_actual != anio_hasta or mes_actual != mes_hasta:
            tuplas.append((anio_actual, mes_actual))
            mes_actual = mes_actual + 1
            if mes_actual == 13:
                mes_actual = 1
                anio_actual = anio_actual + 1
        tuplas.append((anio_actual, mes_actual))

        return tuplas


class LogActividadManager(models.Manager):
    def por_mes(self, vendedor, anio, mes):
        return self.get(vendedor=vendedor, anio=anio, mes=mes)

    def ultimo_log_del_vendedor(self, vendedor):
        try:
            return self.filter(vendedor=vendedor).latest('ultima')
        except ObjectDoesNotExist:
            return None

    def actividad_del_mes(self, vendedor, anio, mes):
        try:
            actividad = self.get(vendedor=vendedor, anio=anio, mes=mes)
            return actividad.cantidad
        except ObjectDoesNotExist:
            return '0'

    def se_logueo_en_el_ultimo_mes(self, vendedor):
        ultimo_log = self.ultimo_log_del_vendedor(vendedor=vendedor)
        hoy = timezone.make_aware(datetime.datetime.now(), timezone.get_default_timezone())
        return ultimo_log and ultimo_log.ultima > hoy + datetime.timedelta(-30)


class LogDeActividadDiarioManager(models.Manager):

    def ultimo_log_del_vendedor(self, vendedor):
        try:
            return self.filter(vendedor=vendedor).latest('ultima')
        except ObjectDoesNotExist:
            return None

    def actividad_del_mes(self, vendedor, anio, mes):
        return self.filter(vendedor=vendedor, anio=anio, mes=mes).count()

    def actividad_entre(self, vendedor, fecha_inicio, fecha_fin):
        return self.filter(vendedor=vendedor,
                           anio__gte=fecha_inicio.year, mes__gte=fecha_inicio.month, dia__gte=fecha_inicio.day,
                           anio__lte=fecha_fin.year, mes__lte=fecha_fin.month, dia__lte=fecha_fin.day).count()