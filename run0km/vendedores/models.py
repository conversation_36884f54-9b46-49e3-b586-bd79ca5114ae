# coding=utf-8

import datetime
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db import models
from django.db.models import Min, Sum
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.template.defaultfilters import date
from django.template.defaultfilters import pluralize
from django.utils import timezone

from concesionarias.rango_laboral import CalendarioLaboral
from core.models import Sistema
from core.support import delete_all_sessions_for
from notificaciones import FormaDeEnvioDeNotificacion
from objetivos.models import Periodo
from prospectos.aplicacion.formas_de_ver import FormaDeVerExtendida
from prospectos.aplicacion.formas_de_ver import FormaDeVerParcial
from prospectos.models import Prospecto, Llamado, Venta, PedidoDeProspecto
from users.models import PermisoDePersonificacion, User
from users.personificacion import ControladorDePermisosDePersonificacion
from vendedores.detector_inactividad import DetectorDeInactividad
from vendedores.managers import (VendedorManager, VentasAprobadasPorMesManager, LogActividadManager,
                                 LogDeActividadDiarioManager, ConfiguracionDeServiciosManager)
from vendedores.permisos_de_vendedor import PermisosDeVendedor
from vendedores.validators import soloDigitosValidator

CARGOS = (('Vendedor', 'Vendedor'),
          ('Supervisor', 'Supervisor'),)


class ConfiguracionDeCanal(models.Model):
    _tipo = models.CharField(max_length=60, unique=True)
    _limite_de_envios = models.IntegerField(null=True, blank=True)

    @classmethod
    def nuevo(cls, forma_de_envio, limite):
        config = cls.objects.create(_tipo=forma_de_envio.nombre(), _limite_de_envios=limite)
        return config

    @classmethod
    def para(cls, forma_de_envio):
        return cls.objects.get(_tipo=forma_de_envio.nombre())

    def supera_limite(self, cantidad):
        return self._limite_de_envios < cantidad

    def es_para_forma_de_envio(self, forma_de_envio):
        return self._tipo == forma_de_envio.nombre()

    def forma_de_envio(self):
        return FormaDeEnvioDeNotificacion.de_nombre(self._tipo)

    def tipo(self):
        return self._tipo

    def limite_de_envios(self):
        return self._limite_de_envios

    class Meta:
        verbose_name = 'Configuración de Canal'
        verbose_name_plural = 'Configuraciones de Canal'
        ordering = ['_tipo', ]


class ConfiguracionDeServicios(models.Model):
    _vendedor = models.OneToOneField('Vendedor', related_name='configuracion_servicios')
    _permiso_para_chatear = models.BooleanField(default=True)
    _auto_tienda = models.BooleanField(default=True)
    _moto_autos = models.BooleanField(default=True)
    _chat_habilitado = models.BooleanField(default=True)
    _whatsapp_habilitado = models.BooleanField(default=False)
    _sms_habilitado = models.BooleanField(default=True)
    _servicio_incontactables_habilitado = models.BooleanField(default=True)
    _mensaje_para_incontactables = models.CharField(max_length=255, blank=True, default='')
    _redes_habilitado = models.BooleanField(default=False)
    _mensaje_bienvenida_habilitado = models.BooleanField(default=True)
    _mensaje_bienvenida = models.CharField(max_length=255, blank=True, default='')
    _notificaciones_habilitadas = models.BooleanField(default=True)
    _app_habilitada = models.BooleanField(default=True)
    _facebook_habilitado = models.BooleanField(default=False)
    _vendedores_pueden_crear_propuestas = models.BooleanField(default=True)
    _tiene_version_limitada = models.BooleanField(default=False)
    _llamadas_habilitadas = models.BooleanField(default=False)
    _pedir_prospecto_habilitado = models.BooleanField(default=True)
    _circular_prospectos_habilitado = models.BooleanField(default=True)

    objects = ConfiguracionDeServiciosManager()

    @classmethod
    def nuevo(cls, vendedor, permiso_para_chatear=True, chat_habilitado=True, whatsapp_habilitado=False,
              sms_habilitado=True,
              mensaje_bienvenida='', servicio_incontactables_habilitado=True, mensaje_para_incontactables='',
              redes_habilitado=False, mensaje_bienvenida_habilitado=True, _notificaciones_habilitadas=True,
              _app_habilitada=False, tiene_version_limitada=False, llamadas_habilitadas=False,
              pedir_prospecto_habilitado=True, circular_prospectos_habilitado=True):

        nueva_configuracion = cls(
            _vendedor=vendedor,
            _permiso_para_chatear=permiso_para_chatear,
            _chat_habilitado=chat_habilitado,
            _whatsapp_habilitado=whatsapp_habilitado,
            _sms_habilitado=sms_habilitado,
            _servicio_incontactables_habilitado=servicio_incontactables_habilitado,
            _mensaje_para_incontactables=mensaje_para_incontactables,
            _redes_habilitado=redes_habilitado,
            _mensaje_bienvenida_habilitado=mensaje_bienvenida_habilitado,
            _mensaje_bienvenida=mensaje_bienvenida,
            _notificaciones_habilitadas=_notificaciones_habilitadas,
            _app_habilitada=_app_habilitada,
            _tiene_version_limitada=tiene_version_limitada,
            _llamadas_habilitadas=llamadas_habilitadas,
            _pedir_prospecto_habilitado=pedir_prospecto_habilitado,
            _circular_prospectos_habilitado=circular_prospectos_habilitado
        )

        nueva_configuracion.full_clean()
        nueva_configuracion.save()

        return nueva_configuracion

    @classmethod
    def nuevo_desde_supervisor(cls, vendedor):
        supervisor = vendedor.responsable()
        return cls.nuevo(
            vendedor=vendedor,
            whatsapp_habilitado=supervisor.habilitado_para_mensajear_por_whatsapp(),
            sms_habilitado=supervisor.configuracion_servicios.sms_habilitado(),
            redes_habilitado=supervisor.configuracion_servicios.redes_habilitado(),
            llamadas_habilitadas=supervisor.configuracion_servicios.tiene_servicio_de_llamadas_habilitado())

    def clean(self):
        vendedor = self.vendedor()
        try:
            esta_activo = vendedor.esta_activo()
        except User.DoesNotExist:
            esta_activo = False
        esta_eliminado = vendedor.esta_eliminado()
        if self._facebook_habilitado and (not vendedor.es_supervisor() or not esta_activo or esta_eliminado):
            mensaje = self.mensaje_de_error_para_el_servicio_de_facebook()
            raise ValidationError(mensaje)

    @classmethod
    def mensaje_de_error_para_el_servicio_de_facebook(cls):
        return 'Solo los supervisores activos y no eliminados pueden tener habilitado el servicio de Facebook.'

    def vendedor(self):
        return self._vendedor

    def notificaciones_habilitadas(self):
        return self._notificaciones_habilitadas

    def actualizar_permisos_en_cascada(self, campos_modificados):
        if '_app_habilitada' in campos_modificados:
            self.configurar_habilitacion_de_la_app(app_habilitada=self._app_habilitada)
        if '_llamadas_habilitadas' in campos_modificados:
            self._configurar_permisos_para_llamadas_a_staff_a_cargo(habilitar=self._llamadas_habilitadas)

    def tiene_servicio_de_llamadas_habilitado(self):
        """
        Un vendedor tiene el servicio de llamadas habilitado si las configuraciones de servicios para el, su supervisor
        y su concesionaria estan habilitadas.

        Un supervisor tiene el servicio de llamadas habilitado si las configuraciones de servicios para el y su
        concesionaria estan habilitadas.
        """

        if self.vendedor().es_supervisor():
            supervisor_habilitado = self._llamadas_habilitadas
            configuracion_de_la_concesionaria = self.vendedor().obtener_concesionaria().configuracion_de_servicios()
            return supervisor_habilitado and configuracion_de_la_concesionaria.tiene_servicio_de_llamadas_habilitado()
        else:
            vendedor_habilitado = self._llamadas_habilitadas
            configuracion_del_supervisor = self.vendedor().responsable().configuracion_de_servicios()
            return vendedor_habilitado and configuracion_del_supervisor.tiene_servicio_de_llamadas_habilitado()

    # Servicio de Llamados VoIP
    def tiene_permiso_para_modificar_servicio_de_llamados(self):
        if self.vendedor().es_supervisor():
            supervisor_habilitado = self._llamadas_habilitadas
            configuracion_de_la_concesionaria = self.vendedor().obtener_concesionaria().configuracion_de_servicios()
            return supervisor_habilitado and configuracion_de_la_concesionaria.tiene_servicio_de_llamadas_habilitado()
        else:
            return False

    def staff_a_cargo_puede_utilizar_servicio_de_llamados(self):
        for staff in self.vendedor().staff_a_cargo():
            if not staff.configuracion_de_servicios().llamadas_habilitadas():
                return False
        return True

    def habilitar_servicio_de_llamados_a_staff_a_cargo(self):
        self._configurar_permisos_para_llamadas_a_staff_a_cargo(habilitar=True)

    def deshabilitar_llamados_de_llamados_a_staff_a_cargo(self):
        self._configurar_permisos_para_llamadas_a_staff_a_cargo(habilitar=False)

    def llamadas_habilitadas(self):
        return self._llamadas_habilitadas

    def pedir_prospecto_habilitado(self):
        return self._pedir_prospecto_habilitado

    def habilitar_pedir_prospecto(self):
        self._pedir_prospecto_habilitado = True
        self.save()

    def deshabilitar_pedir_prospecto(self):
        self._pedir_prospecto_habilitado = False
        self.save()

    def puede_pedir_prospecto(self):
        return self._pedir_prospecto_habilitado and self._el_supervisor_puede_pedir_prospecto() and self._la_concesionaria_puede_pedir_prospecto()

    def _la_concesionaria_puede_pedir_prospecto(self):
        return self.vendedor().obtener_concesionaria().configuracion_de_servicios().pedir_prospecto_habilitado()

    def _el_supervisor_puede_pedir_prospecto(self):
        return self.vendedor().responsable().configuracion_servicios.pedir_prospecto_habilitado()

    def app_habilitada(self):
        """
        Un vendedor tiene la app habilitada si las configuraciones de servicios para el, su supervisor y su
        concesionaria estan habilitadas.

        Un supervisor tiene la app habilitada si las configuraciones de servicios para el y su concesionaria estan
        habilitadas.
        """
        if self.vendedor().es_supervisor():
            supervisor_habilitado = self._app_habilitada
            return supervisor_habilitado and self._tiene_app_habilitada_concesionaria_de(supervisor=self.vendedor())
        else:
            vendedor_habilitado = self._app_habilitada
            return vendedor_habilitado and self.vendedor().responsable().configuracion_de_servicios().app_habilitada()

    def tiene_version_limitada(self):
        return self._tiene_version_limitada

    def facebook_habilitado(self):
        sistema = Sistema.instance()
        vendedor = self.vendedor()
        face_habilitado = self._facebook_habilitado
        es_supervisor = vendedor.es_supervisor()
        esta_activo = vendedor.esta_activo()
        no_esta_eliminado = not vendedor.esta_eliminado()
        return face_habilitado and es_supervisor and sistema.facebook_habilitado() and esta_activo and no_esta_eliminado

    def whatsapp_habilitado(self):
        return self._whatsapp_habilitado

    def sms_habilitado(self):
        return self._sms_habilitado

    def servicio_incontactables_habilitado(self):
        return self._servicio_incontactables_habilitado

    def mensaje_para_incontactables(self):
        return self._mensaje_para_incontactables

    def redes_habilitado(self):
        return self._redes_habilitado

    def mensaje_bienvenida_habilitado(self):
        return self._mensaje_bienvenida_habilitado

    def mensaje_bienvenida(self):
        return self._mensaje_bienvenida

    def auto_tienda_habilitado(self):
        return self._auto_tienda

    def moto_autos_habilitado(self):
        return self._moto_autos

    def habilitado_para_mensajear_por_whatsapp(self):
        """
            Si el vendedor es un Supervisor, el servicio WhatsApp esta habilitado cuando lo tiene habilitado la
            concesionaria.
            Sino, tiene que haber sido habilitado por su supervisor (whatsapp_habilitado).
            Tambien tiene que estar habilitado por el Sistema.
        :return: Boolean
        """
        return self._whatsapp_habilitado_en_conc_y_sistema() and (self.es_supervisor_o_tiene_whatsapp_habilitado())

    def _whatsapp_habilitado_en_conc_y_sistema(self):
        sistema = Sistema.instance()
        concesionaria = self.vendedor().obtener_concesionaria()
        config_servicios_concesionaria = concesionaria.configuracion_de_servicios()
        return sistema.whatsapp_habilitado() and concesionaria and config_servicios_concesionaria.whatsapp_habilitado()

    def es_supervisor_o_tiene_whatsapp_habilitado(self):
        return self.vendedor().es_supervisor() or self.whatsapp_habilitado()

    def tiene_sms_habilitado(self):
        """
            Si el vendedor es un Supervisor, el servicio SMS esta habilitado cuando lo tiene habilitado la concesionaria
            Sino, tiene que haber sido habilitado por su supervisor (sms_habilitado)
        :return: Boolean
        """
        concesionaria = self.vendedor().obtener_concesionaria()
        config_servicios = concesionaria.configuracion_de_servicios()
        return concesionaria and config_servicios.sms_habilitado() and (
            self.vendedor().es_supervisor() or self.sms_habilitado())

    # Servicio de Chat
    def habilitado_para_chatear(self):
        return self.chat_habilitado() and self.puede_modificar_servicio_de_chat()

    def habilitado_para_circular_prospectos(self):
        return self._circular_prospectos_habilitado

    def habilitar_circular_prospecto(self):
        self._circular_prospectos_habilitado = True
        self.save()

    def deshabilitar_circular_prospecto(self):
        self._circular_prospectos_habilitado = False
        self.save()

    def puede_modificar_servicio_de_chat(self):
        return self._tiene_chat_habilitado_concesionaria() and self.vendedor().tiene_cargo_vendedor() and \
               self._permiso_para_chatear

    def _tiene_chat_habilitado_concesionaria(self):
        concesionaria = self.vendedor().obtener_concesionaria()
        return concesionaria and concesionaria.configuracion_de_servicios().chat_habilitado()

    def chat_habilitado(self):
        return self._chat_habilitado

    # Conversaciones
    def tiene_conversaciones_habilitado(self):
        return self.habilitado_para_mensajear_por_whatsapp() or \
               self.tiene_sms_habilitado()

    def tipos_de_conversaciones_habilitados(self):
        from conversaciones.models import Conversacion
        tipos_habilitados = []

        if self.habilitado_para_chatear():
            tipos_habilitados.append(Conversacion.TIPO_CHAT)
        if self.habilitado_para_mensajear_por_whatsapp():
            tipos_habilitados.append(Conversacion.TIPO_WHATSAPP)
        if self.tiene_sms_habilitado():
            tipos_habilitados.append(Conversacion.TIPO_SMS)

        return tipos_habilitados

    def _validar_que_el_vendedor_sea_un_supervisor(self):
        if not self._vendedor.es_supervisor():
            raise ValidationError(message='No se puede habilitar Facebook para un vendedor que no sea supervisor')

    def configurar_habilitacion_de_las_redes(self, redes_habilitado=False):
        self._redes_habilitado = redes_habilitado
        self.full_clean()
        self.save()

    def _realizar_operaciones_extras_de_habilitacion_de_la_app(self):
        if self.vendedor().es_supervisor():
            ConfiguracionDeServicios.objects.configurar_permisos_para_app_mobile_a(
                vendedores=self.vendedor().vendedores_a_cargo(), habilitar=True)

    def _realizar_operacion_extras_de_deshabilitacion_de_la_app(self):
        if not self.vendedor().es_supervisor():
            self._vendedor.user.eliminar_token_para_api()
        else:
            ConfiguracionDeServicios.objects.configurar_permisos_para_app_mobile_a(
                vendedores=self.vendedor().vendedores_a_cargo(), habilitar=False)

    def _configurar_permisos_para_llamadas_a_staff_a_cargo(self, habilitar):
        if self.vendedor().es_supervisor():
            ConfiguracionDeServicios.objects.configurar_permisos_para_llamadas_a(
                vendedores=self.vendedor().vendedores_a_cargo(), habilitar=habilitar)

    def configurar_habilitacion_de_la_app(self, app_habilitada=False):
        self._app_habilitada = app_habilitada
        if app_habilitada:
            self._realizar_operaciones_extras_de_habilitacion_de_la_app()
        else:
            self._realizar_operacion_extras_de_deshabilitacion_de_la_app()
        self.full_clean()
        self.save()

    def configurar_habilitacion_de_notificaciones(self, notificaciones_habilitadas=False):
        self._notificaciones_habilitadas = notificaciones_habilitadas
        self.full_clean()
        self.save()

    def configurar_habilitacion_de_facebook(self, facebook_habilitado=False):
        self._validar_que_el_vendedor_sea_un_supervisor()
        self._facebook_habilitado = facebook_habilitado
        self.full_clean()
        self.save()

    def configurar_habilitacion_de_chat(self, chat_habilitado=False):
        self._chat_habilitado = chat_habilitado
        self.full_clean()
        self.save()

    def configurar_auto_tienda(self, habilitado=True):
        self._auto_tienda = habilitado
        self.full_clean()
        self.save()

    def configurar_moto_autos(self, habilitado=True):
        self._moto_autos = habilitado
        self.full_clean()
        self.save()

    def permitir_servicio_de_chat(self):
        self._permiso_para_chatear = True
        self.full_clean()
        self.save()

    def no_permitir_servicio_de_chat(self):
        self._permiso_para_chatear = False
        self.full_clean()
        self.save()

    def habilitar_whatsapp(self):
        self._whatsapp_habilitado = True
        self.full_clean()
        self.save()

    def deshabilitar_whatsapp(self):
        self._whatsapp_habilitado = False
        self.full_clean()
        self.save()

    def habilitar_sms(self):
        self._sms_habilitado = True
        self.full_clean()
        self.save()

    def deshabilitar_sms(self):
        self._sms_habilitado = False
        self.full_clean()
        self.save()

    def habilitar_llamadas(self):
        self._llamadas_habilitadas = True
        self.full_clean()
        self.save()

    def deshabilitar_llamadas(self):
        self._llamadas_habilitadas = False
        self.full_clean()
        self.save()

    def habilitar_mensaje_bienvenida(self):
        self._mensaje_bienvenida_habilitado = True
        self.full_clean()
        self.save()

    def deshabilitar_mensaje_bienvenida(self):
        self._mensaje_bienvenida_habilitado = False
        self.full_clean()
        self.save()

    def habilitar_mensaje_incontactables(self):
        self._servicio_incontactables_habilitado = True
        self.full_clean()
        self.save()

    def deshabilitar_mensaje_incontactables(self):
        self._servicio_incontactables_habilitado = False
        self.full_clean()
        self.save()

    def modificar_mensaje_para_incontactables(self, mensaje):
        self._validar_mensaje_para_incontactables(mensaje=mensaje)
        self._mensaje_para_incontactables = mensaje
        self.full_clean()
        self.save()

    def modificar_mensaje_bienvenida(self, mensaje):
        self._mensaje_bienvenida = mensaje
        self.full_clean()
        self.save()

    def puede_crear_propuestas(self):
        vendedor = self.vendedor()
        if vendedor.es_supervisor():
            return self.puede_modificar_servicio_de_propuestas()
        else:
            return self._pueden_crear_propuestas_vendedores_de(vendedor.supervisor)

    def puede_modificar_servicio_de_propuestas(self):
        return self.vendedor().es_supervisor() and \
               self._pueden_crear_propuestas_supervisores_de_concesionaria_de(self.vendedor())

    def habilitar_creacion_de_propuestas(self):
        self._vendedores_pueden_crear_propuestas = True
        self.full_clean()
        self.save()

    def vendedores_pueden_crear_propuestas(self):
        return self._vendedores_pueden_crear_propuestas

    def deshabilitar_creacion_de_propuestas(self):
        self._vendedores_pueden_crear_propuestas = False
        self.full_clean()
        self.save()

    def _validar_mensaje_para_incontactables(self, mensaje):
        if len(mensaje) > 160:
            raise ValidationError(
                message='El mensaje para incontactables no puede ser mayor a 160 caracteres (longitud actual: %s).' %
                        len(mensaje))

    def _tiene_app_habilitada_concesionaria_de(self, supervisor):
        return supervisor.obtener_concesionaria().configuracion_de_servicios().app_habilitada()

    def _pueden_crear_propuestas_supervisores_de_concesionaria_de(self, supervisor):
        concesionaria = supervisor.obtener_concesionaria()
        servicios = concesionaria.configuracion_de_servicios()
        return servicios.supervisores_pueden_crear_propuestas()

    def _pueden_crear_propuestas_vendedores_de(self, supervisor):
        return supervisor.configuracion_de_servicios().vendedores_pueden_crear_propuestas() and \
               self._pueden_crear_propuestas_supervisores_de_concesionaria_de(supervisor)


class Vendedor(models.Model):
    CARGO_VENDEDOR = 'Vendedor'
    CARGO_SUPERVISOR = 'Supervisor'

    # This field is required.
    user = models.OneToOneField('users.User', related_name='vendedor')
    # Other fields here
    supervisor = models.ForeignKey('Vendedor', related_name='vendedores', null=True, blank=True,
                                   on_delete=models.SET_NULL)
    prefijo_celular = models.CharField(max_length=5, blank=True, null=True, default='', validators=[
        soloDigitosValidator])
    celular = models.CharField(max_length=45, blank=True, null=True, default='', validators=[soloDigitosValidator])
    prefijo_telefono = models.CharField(max_length=5, blank=True, null=True, default='', validators=[
        soloDigitosValidator])
    telefono = models.CharField(max_length=45, blank=True, null=True, default='', validators=[soloDigitosValidator])
    cargo = models.CharField(max_length=10, choices=CARGOS, default='Vendedor')
    alerta_diaria = models.BooleanField(default=True, verbose_name='Alerta 24hs')
    alerta_a_supervisor = models.BooleanField(default=True, verbose_name='Notificar al supervisor',
                                              help_text='Alertar al supervisor cada 4 dias')
    equipo = models.ForeignKey('equipos.Equipo', related_name='integrantes', null=True, blank=True,
                               on_delete=models.SET_NULL)
    concesionaria = models.ForeignKey('concesionarias.Concesionaria', related_name='empleados', null=True, blank=True,
                                      on_delete=models.SET_NULL)
    factor_de_asignacion = models.IntegerField(default=10)  # Mantener sincronizado con default_factor_de_asignacion
    ajuste_factor_de_asignacion_administrador = models.IntegerField(default=0)
    eliminado = models.BooleanField(default=False, verbose_name='Vendedor Eliminado')
    ranking_habilitado = models.BooleanField(default=False)
    dias_para_atender_prospecto = models.IntegerField(null=True, blank=True, default=3,
                                                      help_text="Cantidad de días antes que el prospecto este en rojo")

    limite_de_datos_nuevos_en_pedidos = models.IntegerField(
        null=True, blank=True, help_text="Cantidad límite de prospectos nuevos a entregar en pedidos")

    # Indica el limite de prospectos que este vendedor puede recibir.
    # Si es None entonces se usa otro limite
    limite_de_datos_diarios_en_pedidos = models.IntegerField(
        null=True, blank=True, help_text="Cantidad máxima de prospectos diarios a entregar en pedidos")
    limite_de_datos_diarios_al_supervisor_en_pedidos = models.IntegerField(
        null=True, blank=True, help_text="Cantidad máxima de prospectos diarios a entregar al supervisor en pedidos")
    _ultima_actividad = models.DateTimeField(null=True, blank=True)
    puede_editar_limites_diarios = models.BooleanField(default=True)
    _ultimo_acceso_al_listado_de_prospectos = models.DateTimeField(null=True, blank=True, verbose_name='Último acceso al listado de prospectos')
    _minutos_de_inactividad_maximos_para_circular_sus_prospectos = models.IntegerField(default=20, verbose_name='Minutos de inactividad máximos para circular los prospectos de los vendedores')

    objects = VendedorManager()
    all_objects = models.Manager()

    class Meta:
        verbose_name = 'vendedor'
        verbose_name_plural = 'vendedores'
        ordering = ['user__first_name', ]

    @classmethod
    def default_factor_de_asignacion(cls):
        """
            Mantener sincronizado con la definicion de la variable factor_de_asignacion
        """

        return 10

    def fecha_de_creacion(self):
        return self.usuario().fecha_de_creacion()

    @classmethod
    def nuevo(cls, user, supervisor, concesionaria, equipo, factor_de_asignacion, limite_diario):
        if factor_de_asignacion is None:
            factor_de_asignacion_para_crear = cls.default_factor_de_asignacion()
        else:
            factor_de_asignacion_para_crear = factor_de_asignacion
        vendedor = cls(user=user, supervisor=supervisor, equipo=equipo, concesionaria=concesionaria,
                       factor_de_asignacion=factor_de_asignacion_para_crear,
                       limite_de_datos_diarios_en_pedidos=limite_diario)

        vendedor.full_clean()
        vendedor.save()

        ConfiguracionDeServicios.nuevo_desde_supervisor(vendedor=vendedor)
        PermisosDeVendedor.nuevo(vendedor=vendedor, _exportacion_de_prospectos=False)
        vendedor.asignar_creditos_para_sms_iniciales()
        return vendedor

    @classmethod
    def nuevo_supervisor(cls, user, concesionaria, limite_diario, puede_editar_limites_diarios=True):
        supervisor = Vendedor(
            user=user,
            concesionaria=concesionaria,
            limite_de_datos_diarios_al_supervisor_en_pedidos=limite_diario,
            cargo=Vendedor.CARGO_SUPERVISOR,
            puede_editar_limites_diarios=puede_editar_limites_diarios
        )
        supervisor.full_clean()
        supervisor.save()
        ConfiguracionDeServicios.nuevo(vendedor=supervisor)
        PermisosDeVendedor.nuevo(vendedor=supervisor, _exportacion_de_prospectos=False)
        supervisor.asignar_creditos_para_sms_iniciales()
        return supervisor

    def permisos(self):
        permisos = PermisosDeVendedor.para(vendedor=self)
        return permisos

    def tiene_siguiente_repetido_de(self, prospecto):
        if self.es_supervisor():
            siguiente = prospecto.siguiente_repetido_para_supervisor()
        else:
            siguiente = prospecto.siguiente_repetido_para_vendedor()
        return siguiente is not None

    def puede_acceder_a_los_proveedores_de_datos(self):
        return (self.es_supervisor() and
                self.concesionaria.configuracion_de_servicios().puede_acceder_a_los_proveedores_de_datos())

    def puede_transferir_prospectos_concesionaria(self):
        return self.concesionaria.configuracion_de_servicios().puede_transferir_prospectos()

    def tiene_version_limitada(self):
        return self.configuracion_de_servicios().tiene_version_limitada()

    def puede_crear_propuestas(self):
        return self.configuracion_de_servicios().puede_crear_propuestas()

    def forma_de_ver_para(self, prospecto):
        if self.obtener_concesionaria().puede_ver_forma_de_prospecto_extendida():
            return FormaDeVerExtendida.nueva(self, prospecto)
        else:
            return FormaDeVerParcial.nueva(self, prospecto)

    def puede_ver_forma_de_prospecto_extendida(self):
        return self.obtener_concesionaria().puede_ver_forma_de_prospecto_extendida()

    def cambiar_factor_de_asignacion_por(self, factor_de_asignacion):
        self.factor_de_asignacion = factor_de_asignacion
        self.save()

    def cambiar_limite_de_datos_diarios_para_supervisor_por(self, nuevo_limite):
        self.validar_cargo_supervisor()
        self.limite_de_datos_diarios_al_supervisor_en_pedidos = nuevo_limite
        self.save()

    def asignar_creditos_para_sms_iniciales(self):
        from occ.models import CreditoDeSMS

        credito = CreditoDeSMS.nuevo(vendedor=self)
        credito.asignar_credito_inicial()

    def tiene_credito_para_enviar_sms(self, cantidad_de_mensajes):
        return hasattr(self, 'credito') and self.credito.tiene_credito_para_enviar(cantidad_de_mensajes)

    def puede_tener_propuestas(self):
        return not self.es_supervisor() and not self.es_gerente()

    def ranking(self):
        # TODO - Definir calculo
        return '1/1'

    def clean(self):
        if self.es_supervisor():
            if not self.concesionaria:
                raise ValidationError('La concesionaria es obligatoria para los supervisores.')

    def nombre(self):
        return self.user.first_name

    def email(self):
        return self.user.email

    def esta_eliminado(self):
        return self.eliminado

    def usuario(self):
        return self.user

    def esta_activo(self):
        return self.usuario().esta_activo()

    def celular_completo(self):
        return self.prefijo_celular + self.celular

    def editar_datos_de_contacto(self, prefijo_celular, celular, email):
        self.editar_email_de_usuario(email)
        self.prefijo_celular = prefijo_celular
        self.celular = celular
        self.full_clean()
        self.save()

    def editar_email_de_usuario(self, email):
        user = self.user
        user.email = email
        # TODO: PARCHE: no hacemos el clen porque da error, tenemos usuaros con username invalido
        # user.full_clean()
        user.save()

    def __str__(self):
        return self.user.__str__()

    def full_name(self):
        return self.user.get_full_name()

    def username(self):
        return self.user.username

    username.short_description = 'Username'

    # ACTIVIDAD
    def tiempo_promedio_de_respuesta_del_mes(self):
        prospectos = Prospecto.objects.de_vendedor_en_los_ultimos_dias(vendedor=self, dias=30)
        return prospectos.tiempo_promedio_de_respuesta()

    def tiempo_promedio_de_respuesta_de_ultima_semana(self):
        prospectos = Prospecto.objects.de_vendedor_en_los_ultimos_dias(vendedor=self, dias=7)
        return prospectos.tiempo_promedio_de_respuesta()

    def tiempo_promedio_de_respuesta(self):
        # tiempo_promedio_respuesta = Prospecto.objects.tiempo_promedio_de_respuesta(self.prospectos.all())
        tiempo_promedio_respuesta = self.tiempo_promedio_de_respuesta_del_mes()
        if not tiempo_promedio_respuesta:
            return '---'
        else:
            tiempo_en_minutos = tiempo_promedio_respuesta / 60
            if tiempo_en_minutos < 60:
                return '%.1f min' % tiempo_en_minutos
            else:
                return '%.1f hs' % (tiempo_en_minutos / 60)

    def actualizar_ultimo_acceso_al_listado_de_prospectos(self):
        self._ultimo_acceso_al_listado_de_prospectos = timezone.now()
        self.save()

    def minutos_de_inactividad_maximos_para_circular_sus_prospectos_segun_supervisor(self):
        return self.responsable()._minutos_de_inactividad_maximos_para_circular_sus_prospectos

    def puede_circular_prospectos_si_su_ultima_actividad_es_mayor_a_minutos_de_inactividad_maximos_segun_supervisor(self):
        return (self.minutos_desde_su_ultima_actividad() is not None and
                self.minutos_desde_su_ultima_actividad() > self.minutos_de_inactividad_maximos_para_circular_sus_prospectos_segun_supervisor())

    def ultimo_log_actividad(self):
        return LogActividad.objects.ultimo_log_del_vendedor(self)

    def horas_desde_su_ultima_actividad(self):
        minutos = self.minutos_desde_su_ultima_actividad(redondear=False)
        if minutos:
            factor_horas = 60
            return minutos / factor_horas
        else:
            return None

    def minutos_desde_su_ultima_actividad(self, redondear=True):
        if self._ultima_actividad:
            ahora = timezone.now()
            factor_minutos = 60
            minutos = (ahora - self._ultima_actividad).total_seconds() / factor_minutos
            debe_ajustar_a_calendario_laboral = minutos > 5
            if debe_ajustar_a_calendario_laboral:
                segundos = self.calendario_laboral().horas_laborales_entre(
                    fecha_y_hora_final=ahora, fecha_y_hora_inicial=self._ultima_actividad).total_seconds()
                minutos = segundos / factor_minutos
            return int(minutos) if redondear else minutos
        else:
            return None

    def su_ultima_actividad_en_minutos_es_menor_a(self, minutos_de_inactividad):
        return (self.minutos_desde_su_ultima_actividad() is not None and
                self.minutos_desde_su_ultima_actividad() < minutos_de_inactividad)

    def obtener_ultima_actividad(self):
        return self._ultima_actividad

    def obtener_ultimo_acceso_al_listado_de_prospectos(self):
        return self._ultimo_acceso_al_listado_de_prospectos

    def sin_ultima_actividad_ni_acceso_listado(self):
        return self._ultimo_acceso_al_listado_de_prospectos is None and self._ultima_actividad is None

    def calendario_laboral(self):
        return CalendarioLaboral.default()

    def actividad_este_mes(self):
        hoy = timezone.now()
        return LogActividad.objects.actividad_del_mes(vendedor=self, anio=hoy.year, mes=hoy.month)

    actividad_este_mes.short_description = 'Actividad este mes'

    def actividad_diaria_entre(self, fecha_inicio, fecha_fin):
        return LogDeActividadDiario.objects.actividad_entre(self, fecha_inicio, fecha_fin)

    def llamados_pendientes(self):
        """
        Indica la cantidad de llamados pendientes para realizar hasta la hora actual.
        """
        return self.prospectos_con_llamados_pendientes().count()

    def llamados_asignados_entre_las_fechas(self, start_day, end_day):
        return Llamado.objects.cantidad_de_llamados_diarios_en_prospectos(self.prospectos,
                                                                          fecha_desde=start_day,
                                                                          fecha_hasta=end_day)

    # PROSPECTOS
    def tiene_asignado(self, prospecto):
        return (prospecto.obtener_vendedor() and self == prospecto.obtener_vendedor()) or \
               self == prospecto.obtener_responsable()

    def prospectos_con_llamados_pendientes(self):
        return self.prospectos.con_llamado_vencido()

    def cantidad_de_prospectos_nuevos(self):
        return self.prospectos.filter(estado='N').count()

    def prospectos_nuevos_llamables(self):
        return self.prospectos.filter(estado='N', esta_telefono_normalizado=True, telefono_bien_constituido=True,
                                      esta_spam_list=False).order_by("-fecha")

    def todos_los_prospectos(self):
        if self.es_supervisor():
            return self.prospectos_a_cargo.all()
        else:
            return self.obtener_prospectos_como_vendedor()

    def obtener_prospectos_como_vendedor(self):
        return self.prospectos.all()

    def prospectos_sin_revisar(self):
        return self.prospectos_sin_revisar_por(timezone.timedelta())

    def tiene_prospectos_sin_revisar_por(self, delta):
        """
            Por ahora duplica el metodo, no usa prospectos_sin_revisar_por para evitar la query prospectos costosa.
        """

        ultimo_log = LogActividad.objects.ultimo_log_del_vendedor(self)
        if ultimo_log:
            asignaciones = self._asignaciones_posteriores_a(fecha=ultimo_log.ultima, delta=delta)
            return asignaciones.exists()
        else:
            return self.prospectos.exists()

    def prospectos_sin_revisar_por(self, delta):
        """
            Prospectos asignados pero el vendedor no ingreso al sistema, con un delta de "changui"
        """
        ultimo_log = LogActividad.objects.ultimo_log_del_vendedor(self)
        if ultimo_log:
            asignaciones = self._asignaciones_posteriores_a(fecha=ultimo_log.ultima, delta=delta)
        else:
            asignaciones = self._asignaciones_posteriores_a(fecha=self.fecha_de_creacion(), delta=delta)
        return asignaciones.prospectos()

    def _asignaciones_posteriores_a(self, fecha, delta):
        from prospectos.models import AsignacionDeProspecto
        fecha_mas_delta = fecha + delta
        if fecha_mas_delta > timezone.now() + timezone.timedelta(days=1):
            # If para evitar consultas potencialmente costosas
            return AsignacionDeProspecto.objects.none()

        asignaciones = AsignacionDeProspecto.objects.a_vendedor(self)
        asignaciones = asignaciones.con_asignacion_a_vendedor_despues_de(fecha=fecha_mas_delta)
        return asignaciones

    def prospectos_sin_revisar_hace_demasiado(self):
        # Prospectos asignados despues de el ultimo loggeo del vendedor, con mas de 4 dias.
        cuatro_dias = timezone.timedelta(days=4)
        return self.prospectos_sin_revisar_por(cuatro_dias)

    def prospectos_sin_atender_a_tiempo(self):
        # Prospectos nuevos con cierta antiguedad
        limite = self.fecha_limite_para_atender_prospectos_en_tiempo()
        return self.prospectos.filter(estado='N', asignacion__fecha_de_asignacion_a_vendedor__lte=limite)

    def fecha_asignacion_sin_revisar_mas_antigua(self):
        prospectos = self.prospectos_sin_revisar()
        result = prospectos.aggregate(Min('asignacion__fecha_de_asignacion_a_vendedor'))
        return result['asignacion__fecha_de_asignacion_a_vendedor__min']

    # VENTAS
    def ventas_entre(self, fecha_inicio, fecha_fin):
        return self.prospectos.filter(ventas__isnull=False,
                                      ventas__estado=Venta.APROBADA,
                                      ventas__fecha_de_realizacion__gte=fecha_inicio,
                                      ventas__fecha_de_realizacion__lte=fecha_fin).count()

    def dinero_en_ventas_entre(self, fecha_inicio, fecha_fin):
        prospectos = self.prospectos.filter(ventas__isnull=False,
                                            ventas__estado=Venta.APROBADA,
                                            ventas__fecha_de_realizacion__gte=fecha_inicio,
                                            ventas__fecha_de_realizacion__lte=fecha_fin)
        dinero = prospectos.aggregate(Sum('ventas__precio'))['ventas__precio__sum']
        if dinero is None:
            return 0
        return dinero

    def dinero_en_ventas_historico(self):
        dinero = self.prospectos.filter(ventas__isnull=False,
                                        ventas__estado=Venta.APROBADA).aggregate(Sum('ventas__precio'))[
            'ventas__precio__sum']
        if dinero is None:
            return 0
        return dinero

    def vendedores_sin_equipo(self):
        self.validar_cargo_supervisor()
        return self.vendedores.filter(equipo__isnull=True)

    def tiene_ventas_pendientes(self, anio, mes):
        self.validar_cargo_supervisor()
        fecha = timezone.datetime(int(anio), int(mes), 1)
        fecha_de_inicio, fecha_de_fin = primer_y_ultima_fecha_del_mes(fecha)
        return self.vendedores.filter(ventas__estado=Venta.PENDIENTE, ventas__fecha_de_realizacion__gte=fecha_de_inicio,
                                      ventas__fecha_de_realizacion__lte=fecha_de_fin).exists()

    def ventas_pendientes_de(self, fecha_de_inicio, fecha_de_fin):
        return self.ventas.filter(fecha_de_realizacion__gte=fecha_de_inicio, fecha_de_realizacion__lte=fecha_de_fin)

    def ventas_en(self, periodo):
        return self.ventas_entre(periodo.fecha_inicio(), periodo.fecha_fin())

    def ventas_periodo_anterior(self):
        return self.ventas_en(Periodo.anterior_para(self.obtener_concesionaria()))

    def dinero_en_ventas_en(self, periodo):
        return self.dinero_en_ventas_entre(periodo.fecha_inicio(), periodo.fecha_fin())

    def dinero_en_ventas_periodo_anterior(self):
        return self.dinero_en_ventas_en(Periodo.anterior_para(self.obtener_concesionaria()))

    # OBJETIVOS
    def cumplio_objetivo_actual_en_periodo_anterior(self):
        periodo_actual = Periodo.actual_para(self.obtener_concesionaria())
        periodo_anterior = Periodo.anterior_para(self.obtener_concesionaria())

        objetivo_actual = self.objetivo_en(periodo_actual.fecha_fin())
        objetivo_anterior = self.objetivo_en(periodo_anterior.fecha_fin())

        if objetivo_actual != objetivo_anterior:
            return 'no se'

        if not objetivo_actual:
            return 'no se'

        if objetivo_actual.unidad == 'VENTA':
            return self.ventas_periodo_anterior() >= objetivo_actual.valor
        elif objetivo_actual.unidad == 'DINERO':
            return self.dinero_en_ventas_periodo_anterior() >= objetivo_actual.valor

        raise Exception("Unidad desconocida para el objetivo con pk %s." % objetivo_actual.pk)

    def valor_objetivo_en(self, periodo):
        objetivo = self.objetivo_en_periodo(periodo)
        if objetivo:
            if objetivo.unidad == 'VENTA':
                return str(objetivo.valor) + ' venta' + pluralize(objetivo.valor)
            elif objetivo.unidad == 'DINERO':
                return '$' + str(objetivo.valor)
        else:
            return "-"

    def valor_objetivo_actual(self):
        return self.valor_objetivo_en(Periodo.actual_para(self.obtener_concesionaria()))

    def mes_objetivo_actual(self):
        actual = self.objetivo_actual()
        if actual:
            periodo = Periodo.para(self.obtener_concesionaria(), actual.periodo.fin.month, actual.periodo.fin.year)
            return date(timezone.datetime(1, periodo.mes_de_periodo, 1), 'F') + ' ' + str(periodo.anio_de_periodo)
        else:
            return "Sin objetivo"

    def objetivo_en_periodo(self, periodo):
        return self.objetivo_en(periodo.fecha_fin())

    def objetivo_en(self, fecha):
        try:
            objetivo = self.objetivos.filter(periodo__inicio__lte=fecha).order_by('-periodo__fin')[0]
            return objetivo
        except IndexError:
            return None

    def objetivo_actual(self):
        return self.objetivo_en(timezone.now())

    def changui_actual_para(self, periodo, changui=settings.CHANGUI_OBJETIVO):
        changui = max(
            [changui - int(float(periodo.dias_transcurridos()) / (float(periodo.duracion()) / (changui + 1))), 0])
        return int(changui)

    def porcentaje_objetivo_cumplido_en_periodo(self, objetivo, periodo):
        # Objetivo x dia:   #VentasObjetivo / Duración Periodo       Ej:    10 Ventas / 30 Días = 0.3333 Ventas por día
        # Rendimiento x dia:	#VentasReales / Días Efectivos	   Ej:    8 Ventas / 24 días = 0.3333 Ventas por día
        # Días Efectivos : (Día actual - Día inicial periodo - Changui Proporcional)
        # Changui proporcional:
        # Periodo tiene 30 días y Changui es 5, entonces:
        # Changui:     5     4     3     2     1     0
        # #         |-----|-----|-----|-----|-----|-----|
        # Dias:     0     5     10    15    20    25    30
        # % Objetivo Cumplido:	Rendimiento x dia / Objetivo x dia	Ej: 	0.3333 / 0.3333 = 100%

        objetivo_por_dia = float(objetivo.valor) / periodo.duracion()

        changui_actual = self.changui_actual_para(periodo)
        dias_efectivos = max([periodo.dias_transcurridos() - changui_actual, 1])

        if objetivo.unidad == 'VENTA':
            rendimiento_por_dia = float(self.ventas_en(periodo)) / dias_efectivos
        elif objetivo.unidad == 'DINERO':
            rendimiento_por_dia = float(self.dinero_en_ventas_en(periodo)) / dias_efectivos
        else:
            raise Exception("Tipo de unidad para objetivo %s no conocido." % objetivo.unidad)

        return rendimiento_por_dia / objetivo_por_dia * 100

    def porcentaje_objetivo_cumplido_en_periodo_actual(self):
        periodo = Periodo.actual_para(self.obtener_concesionaria())
        objetivo = self.objetivo_en(periodo.fecha_fin())

        if objetivo:
            return self.porcentaje_objetivo_cumplido_en_periodo(objetivo, periodo)

    # STAFF
    def obtener_concesionaria(self):
        if self.es_supervisor():
            return self.concesionaria
        elif self.tiene_cargo_vendedor():
            if self.supervisor is not None:
                return self.supervisor.concesionaria
            else:
                return self.concesionaria
        raise Exception("Cargo desconocido para el vendedor %s al obtener concesionaria." % self.user.get_full_name())

    def vendedores_por_equipo(self):
        return self.vendedores.order_by('equipo')

    def vendedores_a_cargo(self):
        if not self.es_supervisor():
            return self.__class__.objects.none()
        return self.__class__.objects.activos_en(self.vendedores.all())

    def empleados(self):
        return self.vendedores_a_cargo()

    def staff_a_cargo(self):
        return self.vendedores_a_cargo()

    def mensajes_no_leidos(self):
        from conversaciones.models import Conversacion
        conversaciones_no_leidas = Conversacion.objects.cantidad_no_leidas_de_vendedor(self)
        from occ.models.eavisos import ConversacionDeEAvisos
        mensajes_de_e_avisos_no_leidos = ConversacionDeEAvisos.objects.para_vendedor(vendedor=self).no_leidas().count()
        return conversaciones_no_leidas + mensajes_de_e_avisos_no_leidos

    def tiene_ranking_habilitado(self):
        return self.ranking_habilitado

    def habilitar_ranking(self):
        self.ranking_habilitado = True
        self.save()

    def es_vendedor_con_pedidos_activos(self):
        return self.pedidos_activos().exists()

    def pedidos_activos(self):
        if self.es_supervisor():
            return PedidoDeProspecto.objects.activos_actuales().filter(supervisor=self)
        else:
            return self.supervisor.pedidos.para_vendedor(self)

    def marcas_pedidas(self):
        marcas = []
        for pedido in self.pedidos_activos():
            marcas.extend(pedido.marcas())
        return marcas

    def puede_ser_eliminado(self):
        if self.es_supervisor():
            vendedores_a_cargo = self.vendedores_a_cargo()
            if vendedores_a_cargo is not None and vendedores_a_cargo.count() > 0:
                return False, 'No puede eliminarse un Supervisor con Vendedores a cargo.'
            if self.prospectos_a_cargo.count() > 0:
                return False, 'No puede eliminarse un Supervisor con Prospectos a cargo.'
            if self.pedidos.activos().count() > 0:
                return False, 'No puede eliminarse un Supervisor con Pedidos activos.'
        return True, None

    def eliminar(self):
        """
            A usar UNICAMENTE por GestorDeVendedores
        """
        puede_ser_eliminado, motivo = self.puede_ser_eliminado()
        if not puede_ser_eliminado:
            raise AssertionError(motivo)
        self.equipo = None
        self.equipos.all().delete()
        self.eliminado = True
        self.save()
        self.deshabilitar()
        PermisoDePersonificacion.objects.filter(alias=self.user).delete()
        PermisoDePersonificacion.objects.filter(usuario=self.user).delete()

    @property
    def habilitado(self):
        return self.user.is_active

    def deshabilitar(self):
        self.user.is_active = False
        self.user.save()
        delete_all_sessions_for(a_user=self.user)

    def habilitar(self):
        self.user.is_active = True
        self.user.save()

    def reasignar_supervisor(self, nuevo_supervisor):
        self.equipo = None
        self.supervisor = nuevo_supervisor

        prospectos_asignados = self.prospectos
        for prospecto in prospectos_asignados.all():
            prospecto.reasignar_vendedor(nuevo_vendedor=self, nuevo_supervisor=nuevo_supervisor)

        self.save()

    def tiene_vendedores_con_prospectos_sin_trabajar(self):
        if self.es_supervisor():
            cuatro_dias = timezone.timedelta(days=4)
            detector = DetectorDeInactividad()
            return detector.tiene_vendedores_con_prospectos_sin_trabajar(self.vendedores.all(), cuatro_dias)
        return False

    def cantidad_de_prospectos_sin_asignar(self):
        if self.es_supervisor() and self.vendedores_a_cargo():
            detector = DetectorDeInactividad()
            sin_asignar = detector.prospectos_sin_asignar_de_un_supervisor_desde(self)
            return sin_asignar['cantidad']
        return 0

    def cantidad_de_ventas_aprobadas_del_mes(self, anio, mes):
        try:
            cantidad = VentasAprobadasPorMes.objects.get(vendedor=self, anio=anio, mes=mes).cantidad
        except ObjectDoesNotExist:
            cantidad = 0
        return cantidad

    def ventas_bloqueadas_del_mes(self, anio, mes, proveedor=None):
        return Vendedor.objects.ventas_bloqueadas_del_mes(self, anio, mes, proveedor)

    def ventas_canceladas_del_mes(self, anio, mes):
        return Vendedor.objects.ventas_canceladas_del_mes(self, anio, mes)

    def configuracion_de_servicios(self):
        if not hasattr(self, 'configuracion_servicios'):
            ConfiguracionDeServicios.nuevo(vendedor=self)
        return self.configuracion_servicios

    def validar_cargo_supervisor(self):
        if not self.es_supervisor():
            raise ValidationError('Este metodo es solo para los Supervisores.')

    def habilitar_whatsapp(self):
        self.configuracion_servicios.habilitar_whatsapp()

    def deshabilitar_whatsapp(self):
        self.configuracion_servicios.deshabilitar_whatsapp()

    def habilitar_sms(self):
        self.configuracion_servicios.habilitar_sms()

    def deshabilitar_sms(self):
        self.configuracion_servicios.deshabilitar_sms()

    def app_habilitada(self):
        return self.configuracion_servicios.app_habilitada()

    def habilitar_mensaje_bienvenida(self):
        self.configuracion_servicios.habilitar_mensaje_bienvenida()

    def deshabilitar_mensaje_bienvenida(self):
        self.configuracion_servicios.deshabilitar_mensaje_bienvenida()

    def habilitar_mensaje_incontactables(self):
        self.configuracion_servicios.habilitar_mensaje_incontactables()

    def deshabilitar_mensaje_incontactables(self):
        self.configuracion_servicios.deshabilitar_mensaje_incontactables()

    def tiene_sms_habilitado(self):
        return self.configuracion_servicios.tiene_sms_habilitado()

    def tiene_conversaciones_habilitado(self):
        return self.configuracion_servicios.tiene_conversaciones_habilitado()

    def tipos_de_conversaciones_habilitados(self):
        return self.configuracion_servicios.tipos_de_conversaciones_habilitados()

    def tiene_mensaje_bienvenida_habilitado(self):
        return self.configuracion_servicios.mensaje_bienvenida_habilitado()

    def habilitado_para_mensajear_por_whatsapp(self):
        return self.configuracion_servicios.habilitado_para_mensajear_por_whatsapp()

    def notificaciones_habilitadas(self):
        return self.configuracion_servicios.notificaciones_habilitadas()

    def responsable(self):
        if self.es_supervisor():
            return self
        else:
            return self.supervisor

    def tiene_cargo_vendedor(self):
        return self.cargo == 'Vendedor'

    def es_supervisor(self):
        return self.cargo == 'Supervisor'

    def es_vendedor(self):
        return True

    def es_gerente(self):
        return False

    def tiene_planilla_de_google_drive(self):
        return hasattr(self, "_gsuite_drive_spreadsheet")

    def planilla_de_google_drive(self):
        if self.tiene_planilla_de_google_drive():
            return self._gsuite_drive_spreadsheet

    def dias_como_vendedor_para_atender_prospecto(self):
        if self.supervisor:
            return self.supervisor.dias_para_atender_prospecto
        return self.dias_para_atender_prospecto

    def fecha_limite_para_atender_prospectos_en_tiempo(self):
        limite = self.dias_como_vendedor_para_atender_prospecto()
        delta = timezone.timedelta(days=limite)
        return timezone.now() - delta

    def as_json(self):
        return {
            'id': self.pk,
            'user': self.user.as_json()
        }

    def limite_de_datos_diarios(self):
        from prospectos.models import ConfiguracionDeEntrega
        configuracion = ConfiguracionDeEntrega.nuevo_para(self)
        return configuracion.limite_de_datos_diarios()

    def limite_de_datos_nuevos(self):
        from prospectos.models import ConfiguracionDeEntrega
        configuracion = ConfiguracionDeEntrega.nuevo_para(self)
        return configuracion.limite_de_datos_nuevos()

    def limite_de_datos_diarios_detallado(self):
        from prospectos.models import ConfiguracionDeEntrega
        calculadora = ConfiguracionDeEntrega.nuevo_para(self)
        return calculadora.limite_de_datos_diarios_detallado()

    # Supervisor
    def cancelar_venta(self, venta):
        # TODO: modelar el cargo
        self.validar_cargo_supervisor()
        venta.cancelar()
        prospecto = venta.prospecto
        prospecto.estado = Prospecto.EN_PROCESO
        prospecto.save()

    def tiene_a_cargo(self, vendedor):
        return self.es_supervisor() and vendedor in self.vendedores.all()

    def obtener_vendedores(self):
        self.validar_cargo_supervisor()
        return self.vendedores.all()

    def obtener_telefono(self):
        return self.telefono

    def obtener_equipos(self):
        return self.equipos.all()

    def obtener_factor_de_asignacion(self):
        return self.factor_de_asignacion

    def puede_pedir_prospecto(self):
        return self.configuracion_de_servicios().puede_pedir_prospecto()


class LogActividad(models.Model):
    vendedor = models.ForeignKey(Vendedor)
    anio = models.PositiveSmallIntegerField()
    mes = models.PositiveSmallIntegerField()
    ultima = models.DateTimeField()
    cantidad = models.PositiveSmallIntegerField()

    objects = LogActividadManager()

    class Meta:
        unique_together = ('vendedor', 'anio', 'mes')


class LogDeActividadDiario(models.Model):
    vendedor = models.ForeignKey(Vendedor)
    anio = models.PositiveSmallIntegerField()
    mes = models.PositiveSmallIntegerField()
    dia = models.PositiveSmallIntegerField()
    ultima = models.DateTimeField()

    objects = LogDeActividadDiarioManager()

    class Meta:
        unique_together = ('vendedor', 'anio', 'mes', 'dia')


# noinspection PyUnusedLocal
@receiver(post_save, sender=Vendedor)
def permiso_de_personificacion_default(sender, **kwargs):
    vendedor = kwargs['instance']
    creado = kwargs['created']
    if creado:
        controlador = ControladorDePermisosDePersonificacion()
        controlador.crear_permisos_default_para_vendedor(vendedor)


class VentasAprobadasPorMes(models.Model):
    vendedor = models.ForeignKey(Vendedor, related_name='ventas_aprobadas_por_mes')
    anio = models.PositiveSmallIntegerField()
    mes = models.PositiveSmallIntegerField()
    cantidad = models.PositiveIntegerField(default=0)

    objects = VentasAprobadasPorMesManager()

    class Meta:
        unique_together = ('vendedor', 'anio', 'mes')
        verbose_name_plural = 'Ventas aprobadas por mes'


def primer_y_ultima_fecha_del_mes(fecha):
    import calendar
    primer_fecha = fecha.replace(day=1)
    ultima_fecha = fecha.replace(day=calendar.monthrange(fecha.year, fecha.month)[1], hour=23, minute=59, second=59)
    return primer_fecha, ultima_fecha


# class ConfiguracionDeCuentaDeEmail(models.Model):
#     # Referenciar del vendedor como generic content type
#     # _duenio = models.OneToOneField('vendedores.Vendedor', related_name='configuracion_cuenta_de_email')
#
#     class Meta:
#         abstract = True
#         verbose_name = u'configuración de cuenta de email'
#         verbose_name_plural = u'configuraciones de cuenta de email'


class ConfiguracionDeCuentaDeEmailPropia(models.Model):
    _duenio = models.OneToOneField('vendedores.Vendedor', related_name='_configuracion_cuenta_de_email')
    _email = models.EmailField()
    _password = models.CharField(max_length=64, blank=True, default='')

    @classmethod
    def nueva(cls, email, passsword, vendedor):
        configuracion = cls.objects.create(_email=email, _password=passsword, _duenio=vendedor)
        return configuracion

    def email(self):
        return self._email

    def password(self):
        return self._password

class PermisoTransferencia(models.Model):
    vendedor = models.ForeignKey(Vendedor, on_delete=models.CASCADE, related_name="permisos_transferencia")
    vendedores_permitidos = models.ManyToManyField(Vendedor, related_name="puede_recibir_transferencia")

    class Meta:
        verbose_name = "Permiso de Transferencia"
        verbose_name_plural = "Permisos de Transferencia"

    def __str__(self):
        return (f"Permisos de {self.vendedor.user.first_name} {self.vendedor.user.last_name}"
                f" {self.vendedor.concesionaria.obtener_nombre()}")

    def obtener_permisos_de_vendedor_para_tags_completos(vendedor):
        permisos = []
        permisos_obj = PermisoTransferencia.objects.filter(vendedor=vendedor).first()
        if permisos_obj:
            for vendedor_permitido in permisos_obj.vendedores_permitidos.all():
                permisos.append({
                    "nombre": vendedor_permitido.user.first_name,
                    "apellido": vendedor_permitido.user.last_name,
                    "id_vendedor": vendedor_permitido.id
                })
        return permisos

    def obtener_permiso_transferencia(self, vendedor):
        return self.vendedores_permitidos.filter(vendedor=vendedor).values_list('id', flat=True)


