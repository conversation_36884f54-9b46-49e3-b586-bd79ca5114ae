from permisos.templatetags.permisos import tiene_permiso_para


class ControllerError(Exception):
    pass


class ObjetivosController(object):
    @classmethod
    def nuevo_para(cls, user):
        if user.is_gerente():
            return Objetivos<PERSON><PERSON>nte<PERSON><PERSON>roller(user)
        elif user.is_vendedor() and user.vendedor.es_supervisor():
            return ObjetivosSupervisorController(user)
        else:
            raise ControllerError('Usuario incorrecto')

    def _informacion_de_objetivos_de(self, periodo, supervisor):
        info_vendedores = []
        for vendedor in supervisor.vendedores_por_equipo().reverse():
            objetivo = vendedor.objetivo_en(periodo.fecha_fin())

            if objetivo:
                valor_objetivo_actual = objetivo.valor_en_unidad()
                info_vendedores.append({
                    'id': vendedor.id,
                    'nombre': vendedor.user.first_name,
                    'apellido': vendedor.user.last_name,
                    'equipo': vendedor.equipo.nombre if vendedor.equipo else '-',
                    'objetivo': objetivo,
                    'valor_objetivo_actual': valor_objetivo_actual,
                    'porcentaje_objetivo_cumplido': vendedor.porcentaje_objetivo_cumplido_en_periodo(objetivo,
                                                                                                     periodo) if objetivo else '-'
                })
        return info_vendedores

    def informacion_de_objetivos(self, periodo):
        raise NotImplementedError('Subclass Responsibility')

    def nombre_de_template(self):
        raise NotImplementedError('Subclass Responsibility')

    def concesionaria(self):
        raise NotImplementedError('Subclass Responsibility')


class ObjetivosGerenteController(ObjetivosController):
    def __init__(self, usuario):
        super(ObjetivosGerenteController, self).__init__()
        self._staff = usuario.gerente

    def informacion_de_objetivos(self, periodo):
        info_vendedores = {}
        for supervisor in self._staff.supervisores().reverse():
            info_vendedores.update({str(supervisor): self._informacion_de_objetivos_de(periodo=periodo,
                                                                                           supervisor=supervisor)})
        return info_vendedores

    def nombre_de_template(self):
        return 'objetivos_gerentes.html'

    def concesionaria(self):
        return self._staff.concesionaria


class ObjetivosSupervisorController(ObjetivosController):
    def __init__(self, usuario):
        super(ObjetivosSupervisorController, self).__init__()
        self._staff = usuario.vendedor

    def informacion_de_objetivos(self, periodo):
        info_vendedores = self._informacion_de_objetivos_de(periodo=periodo, supervisor=self._staff)
        return info_vendedores

    def nombre_de_template(self):
        return 'objetivos_supervisores.html'

    def concesionaria(self):
        return self._staff.obtener_concesionaria()


class AsignacionObjetivosController(object):
    @classmethod
    def nuevo_para(cls, user):
        if user.is_gerente():
            return AsignacionObjetivosGerenteController(user)
        elif tiene_permiso_para(user.cargo, 'administrar_objetivos'):
            return AsignacionObjetivosSupervisorController(user)
        else:
            raise ControllerError('Usuario incorrecto')

    def nombre_de_template(self):
        raise NotImplementedError('Subclass Responsibility')

    def lista_de_vendedores(self):
        raise NotImplementedError('Subclass Responsibility')

    def concesionaria(self):
        raise NotImplementedError('Subclass Responsibility')

    def lista_equipos(self, supervisor):
        vendedores_sin_equipo = supervisor.vendedores_sin_equipo()

        equipos = []
        if len(vendedores_sin_equipo) > 0:
            equipos = [{'nombre': 'Sin equipo', 'vendedores': vendedores_sin_equipo}]

        for equipo in supervisor.obtener_equipos().order_by('nombre'):
            vendedores = []
            for vendedor in equipo.integrantes.all().order_by('user__last_name'):
                vendedores.append(vendedor)
            equipos.append({'nombre': equipo.nombre, 'vendedores': vendedores})
        return equipos


class AsignacionObjetivosGerenteController(AsignacionObjetivosController):
    def __init__(self, usuario):
        super(AsignacionObjetivosGerenteController, self).__init__()
        self._staff = usuario.gerente

    def lista_de_vendedores(self):
        equipos_por_supervisor = {}
        for supervisor in reversed(self._staff.supervisores()):
            equipos_por_supervisor.update({supervisor.__str__(): self.lista_equipos(supervisor)})
        return equipos_por_supervisor

    def nombre_de_template(self):
        return "asignar_objetivos_gerentes.html"

    def concesionaria(self):
        return self._staff.concesionaria


class AsignacionObjetivosSupervisorController(AsignacionObjetivosController):
    def __init__(self, usuario):
        super(AsignacionObjetivosSupervisorController, self).__init__()
        self._staff = usuario.vendedor

    def lista_de_vendedores(self):
        return self.lista_equipos(self._staff)

    def nombre_de_template(self):
        return "asignar_objetivos_supervisores.html"

    def concesionaria(self):
        return self._staff.obtener_concesionaria()
