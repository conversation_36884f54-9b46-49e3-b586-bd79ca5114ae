import datetime

from objetivos.models import Objetivo, PeriodoPersistido
from testing.base import BaseFixturedTest
from testing.factories import ConcesionariasFactory


class TestObjetivos(BaseFixturedTest):
    def test_nuevo_objetivo_no_crea_dos_veces_con_los_mismos_datos(self):
        f = self.fixture
        hoy = datetime.datetime.now()
        concesionaria = ConcesionariasFactory(nombre='conce_1', dia_inicio_periodos=28, dia_fin_periodos=20)
        Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=12, anio=hoy.year + 1,
                                unidad='DINERO', valor=1)
        self.assertEqual(Objetivo.objects.count(), 1)
        Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=12, anio=hoy.year + 1,
                                unidad='DINERO', valor=1)
        self.assertEqual(Objetivo.objects.count(), 1)

    def test_nuevo_objetivo_crea_un_periodo_de_venta_por_concesionaria(self):
        f = self.fixture
        hoy = datetime.datetime.now()
        concesionaria = ConcesionariasFactory(nombre='conce_1', dia_inicio_periodos=28, dia_fin_periodos=20)

        Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=2, anio=hoy.year + 1,
                                unidad='DINERO', valor=1)
        self.assertEqual(PeriodoPersistido.objects.count(), 1)
        Objetivo.nuevo_o_editar(vendedor=f['vend_2'], concesionaria=concesionaria, mes=2, anio=hoy.year + 1,
                                unidad='DINERO', valor=1)
        self.assertEqual(PeriodoPersistido.objects.count(), 1)

    def test_objetivo_se_puede_crear_si_el_periodo_finalizo_y_tambien_se_puede_editar(self):
        f = self.fixture
        concesionaria = ConcesionariasFactory(nombre='conce_1', dia_inicio_periodos=28, dia_fin_periodos=20)
        Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=2,
                                           anio=2, unidad='VENTA', valor=3)
        objetivo = Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=2,
                                           anio=2, unidad='VENTA', valor=5)
        objetivo = Objetivo.objects.get(id=objetivo.id)
        self.assertEqual(objetivo.valor, 5)

    def test_objetivo_es_editable_durante_el_periodo_en_que_fue_creado(self):
        f = self.fixture
        hoy = datetime.datetime.today()

        concesionaria = ConcesionariasFactory(nombre='conce_1', dia_inicio_periodos=hoy.day,
                                              dia_fin_periodos=(hoy + datetime.timedelta(days=1)).day)
        Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=hoy.month, anio=hoy.year,
                                unidad='DINERO', valor=1)
        objetivo = Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=hoy.month,
                                           anio=hoy.year, unidad='VENTA', valor=3)
        self.assertNotEqual(objetivo, False)
        self.assertEqual(objetivo.valor, 3)
        self.assertEqual(objetivo.unidad, 'VENTA')

    def test_objetivo_es_editable_si_fue_fijado_en_el_futuro(self):
        f = self.fixture
        hoy = datetime.datetime.today()

        concesionaria = ConcesionariasFactory(nombre='conce_1', dia_inicio_periodos=1, dia_fin_periodos=30)
        Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=hoy.month, anio=hoy.year + 1,
                                unidad='DINERO', valor=1)
        objetivo = Objetivo.nuevo_o_editar(vendedor=f['vend_1'], concesionaria=concesionaria, mes=hoy.month,
                                           anio=hoy.year + 1, unidad='VENTA', valor=3)
        self.assertNotEqual(objetivo, False)
        self.assertEqual(objetivo.valor, 3)
        self.assertEqual(objetivo.unidad, 'VENTA')
