from freezegun import freeze_time

from objetivos.calculo_de_periodos import CalculadorDePeriodos
from testing.base import BaseFixturedTest


class CalculadorDePeriodosTest(BaseFixturedTest):

    @freeze_time("2016-07-10 13:21:34")
    def test_rango_cubre_el_mes_deberia_responder_mes_actual(self):
        # Periodo del 1 al 31, siempre mes actual
        calculador = CalculadorDePeriodos(dia_inicio=1, dia_fin=31)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (7, 2016))

    @freeze_time("2016-07-10 13:21:34")
    def test_rango_dentro_de_un_mes_fecha_en_rango_deberia_responder_mes_actual(self):
        # Periodo del 5 al 25, los dias 5 al 25 es del es actual
        calculador = CalculadorDePeriodos(dia_inicio=5, dia_fin=25)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (7, 2016))

    @freeze_time("2016-07-26 13:21:34")
    def test_rango_dentro_de_un_mes_fecha_posterior_deberia_responder_mes_siguiente(self):
        # Periodo del 5 al 25, los dias 26 al 31 es del es siguiente
        calculador = CalculadorDePeriodos(dia_inicio=5, dia_fin=25)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (8, 2016))

    @freeze_time("2016-07-04 13:21:34")
    def test_rango_dentro_de_un_mes_fecha_anterior_deberia_responder_mes_actual(self):
        # Periodo del 5 al 25, los dias 1 al 4 es del es actual
        calculador = CalculadorDePeriodos(dia_inicio=5, dia_fin=25)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (7, 2016))

    @freeze_time("2016-07-31 13:21:34")
    def test_rango_de_dos_meses_con_mas_dias_en_el_primer_mes_y_fecha_dentro_del_primer_mes_debe_responder_mes_actual(
            self):
        # Periodo del 15 al 10, los dias 15 al 31 es del es actual
        calculador = CalculadorDePeriodos(dia_inicio=15, dia_fin=10)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (7, 2016))

    @freeze_time("2016-07-31 13:21:34")
    def test_rango_de_dos_meses_con_mas_dias_en_el_segundo_mes_fecha_dentro_del_segundo_debe_responder_mes_siguiente(
            self):
        # Periodo del 25 al 10, los dias 15 al 31 es del es actual
        calculador = CalculadorDePeriodos(dia_inicio=25, dia_fin=10)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (8, 2016))

    @freeze_time("2016-07-09 13:21:34")
    def test_rango_de_dos_meses_con_mas_dias_en_el_primer_mes_y_fecha_en_el_segundo_mes_debe_responder_el_mes_anterior(
            self):
        # Periodo del 15 al 10, los dias 1 al 10 es el mes anterior
        calculador = CalculadorDePeriodos(dia_inicio=15, dia_fin=10)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (6, 2016))

    @freeze_time("2016-07-09 13:21:34")
    def test_rango_de_dos_meses_con_mas_dias_en_el_segundo_y_fecha_en_el_segundo_mes_debe_responder_el_mes_actual(
            self):
        # Periodo del 25 al 10, los dias 1 al 10 es del es actual
        calculador = CalculadorDePeriodos(dia_inicio=25, dia_fin=10)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (7, 2016))

    @freeze_time("2016-07-11 13:21:34")
    def test_rango_de_dos_meses_con_mas_dias_en_el_primer_mes_y_fecha_fuera_de_rango_debe_responder_el_mes_actual(self):
        # Periodo del 15 al 10, los dias 11 a 14 es del es actual
        calculador = CalculadorDePeriodos(dia_inicio=15, dia_fin=10)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (7, 2016))

    @freeze_time("2016-07-11 13:21:34")
    def test_rango_de_dos_meses_con_mas_dias_en_el_segundo_y_fecha_fuera_de_rango_debe_responder_el_mes_siguiente(self):
        # Periodo del 25 al 10, los dias 11 a 14 es del es actual
        calculador = CalculadorDePeriodos(dia_inicio=25, dia_fin=10)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (8, 2016))

    @freeze_time("2016-01-01 13:21:34")
    def test_de_dos_meses_con_mas_dias_en_el_primero_y_inicio_y_final_iguales_a_fecha_actual_responde_el_mes_actual(
            self):
        # Periodo del 1 al 1, el dia 1 es del mes pasado
        calculador = CalculadorDePeriodos(dia_inicio=1, dia_fin=1)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (1, 2016))

    @freeze_time("2016-01-25 13:21:34")
    def test_de_dos_meses_con_mas_dias_en_el_segundo_y_inicio_y_final_iguales_a_fecha_actual_responde_el_mes_actual(
            self):
        # Periodo del 25 al 25, el dia 25 es del mes siguiente
        calculador = CalculadorDePeriodos(dia_inicio=25, dia_fin=25)
        self.assertEqual(calculador.mes_y_anio_de_periodo_actual(), (2, 2016))
