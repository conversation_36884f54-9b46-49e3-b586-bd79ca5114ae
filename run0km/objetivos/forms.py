from django import forms
from objetivos.models import Objetivo
from datetime import datetime
from django.template.defaultfilters import date


def obtener_choices_de_anios(para_atras, para_adelante):
    anio_actual = datetime.today().year
    return [(anio, str(anio)) for anio in range(anio_actual - para_atras, anio_actual + para_adelante + 1)]


class AsignarObjetivoForm(forms.Form):
    mes = forms.TypedChoiceField(coerce=int, choices=[(index, date(datetime(1, index, 1), 'F')) for index in range(1, 13)])
    anio = forms.TypedChoiceField(coerce=int, choices=obtener_choices_de_anios(0, 1))
    valor = forms.IntegerField(min_value=1, initial=1)
    unidad = forms.ChoiceField(choices=Objetivo.UNIDAD_CHOICES)


class FiltrarObjetivosForm(forms.Form):
    mes = forms.TypedChoiceField(coerce=int, choices=[(index, date(datetime(1, index, 1), 'F')) for index in range(1, 13)])
    anio = forms.TypedChoiceField(coerce=int, choices=obtener_choices_de_anios(2, 2))