from django.conf.urls import url
from objetivos.views import AsignarObjetivoAjaxView, ObtenerFechasPeriodoAjaxView, ObjetivosView, AsignarObjetivoView, \
    BorrarObjetivosDeVendedorView

urlpatterns = [
    url(r'^borrar_objetivo$', BorrarObjetivosDeVendedorView.as_view(), name='borrar_objetivo'),
    url(r'^$', ObjetivosView.as_view(), name='objetivos'),
    url(r'^asignar_objetivo$', AsignarObjetivoView.as_view(), name='asignar_objetivo'),
    url(r'^asignar_objetivo_ajax$', AsignarObjetivoAjaxView.as_view(), name='asignar_objetivo_ajax'),
    url(r'^obtener_fechas_periodo$', ObtenerFechasPeriodoAjaxView.as_view(), name='obtener_fechas_periodo'),
]
