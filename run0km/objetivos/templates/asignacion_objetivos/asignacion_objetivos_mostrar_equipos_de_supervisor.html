{% for equipo in lista_de_vendedores %}
    {% with id_equipo=forloop.counter %}
            <tr style="background-color: #F0F0F0;font-weight: bold;height: 40px">
                <td colspan="2">{{ equipo.nombre }}</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <th style="text-align: center; width: 3%">
                    <input id="chkTodos" type="checkbox" class="check-equipo"
                           data-equipo="{{ id_equipo }}"/>
                </th>
                <th style="text-align: center; width: 17%;">Apellido</th>
                <th style="text-align: center; width: 16%;">Nombre</th>
                <th style="text-align: center; width: 16%;">Equipo</th>
                <th style="text-align: center; width: 16%;">Ventas Período Anterior</th>
                <th style="text-align: center; width: 16%;">Objetivo Actual</th>
                <th style="text-align: center; width: 16%;">Mes</th>
            </tr>
            {% for vendedor in equipo.vendedores %}
                <tr id="filas">
                    <td>
                        <input type="checkbox" class="check-vendedor" data-equipo="{{ id_equipo }}"
                               data-vendedor="{{ vendedor.id }}"/>
                    </td>
                    <td>
                        {{ vendedor.user.last_name }}
                    </td>
                    <td>
                        {{ vendedor.user.first_name }}
                    </td>
                    <td>
                        {% if vendedor.equipo %} {{ vendedor.equipo.nombre }} {% endif %}
                    </td>
                    <td>
                        {{ vendedor.ventas_periodo_anterior }}
                    </td>
                    <td id="vend_{{ vendedor.id }}_valor">
                        {% if vendedor.cumplio_objetivo_actual_en_periodo_anterior == 'no se' %}
                            {{ vendedor.valor_objetivo_actual }}
                        {% elif vendedor.cumplio_objetivo_actual_en_periodo_anterior %}
                            <span style="color: green;">{{ vendedor.valor_objetivo_actual }}</span>
                        {% else %}
                            <span style="color: red;">{{ vendedor.valor_objetivo_actual }}</span>
                        {% endif %}
                    </td>
                    <td id="vend_{{ vendedor.id }}_mes">
                        {{ vendedor.mes_objetivo_actual }}
                    </td>
                </tr>
            {% endfor %}
    {% endwith %}
{% endfor %}