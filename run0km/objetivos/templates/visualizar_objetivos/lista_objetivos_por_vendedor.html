<tr>
    <th style="text-align: left; padding: 10px; width: 20%;">Apellido</th>
    <th style="text-align: left; width: 19%;">Nombre</th>
    <th style="text-align: left; width: 19%;">Equipo</th>
    <th style="text-align: left; width: 13%;">Objetivo</th>
    <th style="text-align: left; width: 19%;">Desempeño</th>
    <th style="text-align: center; width: 10%;">Acciones</th>

</tr>

{% for vendedor in info_objetivos %}
    <tr id="filas">
        <td style="padding: 10px;">
            {{ vendedor.apellido }}
        </td>
        <td>
            {{ vendedor.nombre }}
        </td>
        <td>
            {{ vendedor.equipo }}
        </td>
        <td>
            {{ vendedor.valor_objetivo_actual }}
        </td>
        <td>
            {% if vendedor.porcentaje_objetivo_cumplido == '-' %}
                -
            {% elif vendedor.porcentaje_objetivo_cumplido < 50 %}
                <img src="{{ STATIC_URL }}img/bullet_red.png"/>
                ({{ vendedor.porcentaje_objetivo_cumplido|floatformat:0 }}%)
            {% elif vendedor.porcentaje_objetivo_cumplido < 75 %}
                <img src="{{ STATIC_URL }}img/bullet_yellow.png"/>
                ({{ vendedor.porcentaje_objetivo_cumplido|floatformat:0 }}%)
            {% elif vendedor.porcentaje_objetivo_cumplido < 125 %}
                <img src="{{ STATIC_URL }}img/bullet_green.png"/>
                ({{ vendedor.porcentaje_objetivo_cumplido|floatformat:0 }}%)
            {% else %}
                <img src="{{ STATIC_URL }}img/bullet_star.png"/>
                <img src="{{ STATIC_URL }}img/bullet_star.png"/>
                <img src="{{ STATIC_URL }}img/bullet_star.png"/>
                <img src="{{ STATIC_URL }}img/bullet_star.png"/>
                <img src="{{ STATIC_URL }}img/bullet_star.png"/>
                ({{ vendedor.porcentaje_objetivo_cumplido|floatformat:0 }}%)
            {% endif %}
        </td>
        <td>
            <a href="#" class="editar-objetivo-button" data-unidad="{{ vendedor.objetivo.unidad }}"
               data-valor="{{ vendedor.objetivo.valor }}" data-vendedor="{{ vendedor.id }}">
                <img src="{{ STATIC_URL }}img/book_edit.png" style="margin-left: 10px;"/>
            </a>
            <a href="#" class="borrar-objetivo-button" data-objetivo-id="{{ vendedor.objetivo.id }}">
               <img src="{{ STATIC_URL }}img/botones/boton-tacho-basura.png" style="margin-left: 10px;"/>
            </a>
        </td>
    </tr>
{% endfor %}
