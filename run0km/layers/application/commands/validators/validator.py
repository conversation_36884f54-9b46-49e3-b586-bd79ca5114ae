# coding=utf-8
from layers.domain import Result


class ParametersValidator(object):
    """
        I validate the command's parameters
    """

    def validate(self, parameters, result):
        raise NotImplementedError('subclass responsibility')


class NoneValidation(ParametersValidator):
    def validate(self, parameters, result):
        return result


class RequiredParameters(ParametersValidator):
    """
        I validate the required command's parameters
    """

    def __init__(self, required_parameters):
        """
        :param required_parameters: list of required parameters
        :type required_parameters: list of Parameter
        """
        super(RequiredParameters, self).__init__()
        self._required_parameters = required_parameters

    def validate(self, parameters, result):
        for parameter_name in self._required_parameters:
            if parameter_name not in parameters:
                result.add_error('The parameter %s is required' % parameter_name)
        return result

    @classmethod
    def new_with(cls, required_parameters):
        return cls(required_parameters)


class AtLeastOneParameterRequired(ParametersValidator):
    """
        I validate that the command has at least one of its required parameters.
    """

    def __init__(self, required_parameters):
        super(AtLeastOneParameterRequired, self).__init__()
        self._required_parameters = required_parameters

    def validate(self, parameters, result):
        for parameter_name in self._required_parameters:
            if parameter_name in parameters:
                return result
        result.add_error(
            'At least one of the following parameters is required: {0}.'.format(', '.join(self._required_parameters)))

    @classmethod
    def new_with(cls, required_parameters):
        return cls(required_parameters)


class ParameterValidator(object):
    """
        I validate a Parameter
    """

    def validate(self, argument, result=None):
        """
        :type argument: object
        :type result: Result
        :rtype: Result
        """
        if result is None:
            result = Result()
        if argument is None:
            return result.is_successful()
        return self.validate_with_result(argument, result)

    def validate_with_result(self, argument, result):
        """
        :type argument: object
        :type result: Result
        :rtype: bool
        """
        raise NotImplementedError('subclass responsibility')


class Parameter(object):
    def __init__(self, name, validators=None):
        """
        :type name: unicode
        :type validators: list of ParameterValidator
        """
        super(Parameter, self).__init__()
        self._name = name
        self._validators = validators if type(validators) is list else []

    def validate(self, argument, result=None):
        if result is None:
            result = Result()

        validators_result = Result()
        for validator in self._validators:
            validator.validate(argument, validators_result)

        converted_errors = self._convert_to_parameter_errors(validators_result)
        result.add_errors(converted_errors)
        return result

    def name(self):
        return self._name

    def _convert_to_parameter_errors(self, result):
        converted_errors = []
        for error in result.errors():
            converted_errors.append("{0}: {1}".format(self._name.upper(), error))
        return converted_errors
