# coding=utf-8
from layers.application.commands.validators.validator import ParameterValidator


class DebeSerVendedor(ParameterValidator):
    # noinspection PyUnresolvedReferences
    def validate_with_result(self, argument, result):
        condition = argument.is_vendedor()
        if not condition:
            result.add_error(self.error_message())
        return condition

    @classmethod
    def error_message(cls):
        return 'El usuario debe ser vendedor'
