# coding=utf-8
import math

from django.conf import settings
from django.urls import reverse

from conversaciones.administracion_de_conversaciones import AdministradorDeConversaciones
from conversaciones.administracion_de_conversaciones_de_e_avisos import AdministradorDeConversacionesDeEAvisos
from conversaciones.medios import MedioEAviso
from conversaciones.models import Conversacion
from layers.application import AppCommandLegacy, AppCommand
from layers.application.commands.validators.generic import NotNone
from layers.application.commands.validators.validator import Parameter
from occ.models.eavisos import ConversacionDeEAvisos, PreguntaDePublicacionEAvisos


class ListarConversacionesCommand(AppCommand):
    """
        Me encargo de listar las conversaciones de un lector aplicando filtros.
        Las conversaciones pueden ser muchas, por lo tanto devuelvo una pagina en particular.
        Las conversaciones las obtengo colaborando con mi administrador_de_conversaciones.
    """

    _FITRO_TODOS = ''

    def _initial_parameters(self):
        params = super(ListarConversacionesCommand, self)._initial_parameters()
        params.append(Parameter("lector", [NotNone()]))
        params.append(Parameter("filtros", []))
        params.append(Parameter("numero_de_pagina", [NotNone()]))
        params.append(Parameter("administrador_de_conversaciones", [NotNone()]))
        return params

    def _default_arguments(self):
        arguments = super(ListarConversacionesCommand, self)._default_arguments()
        arguments['lector'] = None
        arguments['filtros'] = {}
        arguments['numero_de_pagina'] = None
        arguments['administrador_de_conversaciones'] = None
        return arguments

    def medios_aceptados(self):
        from conversaciones.models import Conversacion
        return Conversacion.tipos() + ConversacionDeEAvisos.tipos()

    def _execute_from_successful_result(self, result):
        result = super(ListarConversacionesCommand, self)._execute_from_successful_result(result)
        filtros = self._filtros()
        conversaciones, paginado = self._obtener_conversaciones_y_pagindo_desde(filtros)

        result_object = {
            'filtros': filtros,
            'conversaciones': conversaciones,
            'paginado': paginado,
        }

        result.set_object(result_object)
        return result

    def _obtener_conversaciones_y_pagindo_desde(self, filtros):
        if self._filtrado_por_todos_los_medios(filtros):
            conversaciones, paginado = self._obtener_conversaciones_y_paginado_con_eavisos(filtros)
        elif self._filtrado_por_medio_eaviso(filtros):
            conversaciones, paginado = self._obtener_conversaciones_y_paginado_solo_de_eavisos(filtros)
        else:
            conversaciones, paginado = self._obtener_conversaciones_y_paginado_sin_eavisos(filtros)
        return conversaciones, paginado

    def _obtener_conversaciones_y_paginado_con_eavisos(self, filtros):
        conversaciones = self._administrador().conversaciones_filtradas_por(filtros)
        conversaciones = self._administrador().agregar_eavisos_a_la_lista_de_conversaciones(conversaciones, filtros)
        return self._paginar_conversaciones(conversaciones)

    def _obtener_conversaciones_y_paginado_solo_de_eavisos(self, filtros):
        conversaciones = self._administrador().obtener_eavisos(filtros)
        return self._paginar_conversaciones(conversaciones)

    def _paginar_conversaciones(self, conversaciones):
        """
            Responde una tupla que representa una pagina: conversaciones, infornacion de la pagina
            ({'num', 'max', 'total'})
            Queda pendiente responder una pagina utilizar Paginator.
        """
        paginado = self._obtener_datos_de_paginado(conversaciones, self._numero_de_pagina())
        conversaciones_filtradas_y_paginadas = self._obtener_pagina(conversaciones, paginado)
        conversaciones_filtradas_y_paginadas = self._administrador().obtener_conversaciones_y_eavisos(
            conversaciones_filtradas_y_paginadas)
        conversaciones = self._obtener_conversaciones_de_pagina(conversaciones_filtradas_y_paginadas)
        return conversaciones, paginado

    def _obtener_conversaciones_y_paginado_sin_eavisos(self, filtros):
        conversaciones = self._administrador().conversaciones_filtradas_por(filtros)
        paginado = self._obtener_datos_de_paginado(conversaciones, self._numero_de_pagina())
        conversaciones_filtradas_y_paginadas = self._obtener_pagina(conversaciones,
                                                                    paginado)
        conversaciones = self._obtener_conversaciones_de_pagina(conversaciones_filtradas_y_paginadas)
        return conversaciones, paginado

    def _obtener_datos_de_paginado(self, conversaciones, pagina_mostrada):
        if type(conversaciones) is list:
            total = len(conversaciones)
        else:
            total = conversaciones.count()
        ultima = int(math.ceil(float(total) / settings.CHAT_CONVERSACIONES_POR_PAGINA))
        ultima = max(ultima, 1)
        pagina_mostrada = max(pagina_mostrada, 1)
        pagina_mostrada = min(pagina_mostrada, ultima)

        paginado = {
            'num': pagina_mostrada,
            'max': ultima,
            'total': total
        }

        return paginado

    def _obtener_pagina(self, conversaciones, paginado):
        start = (paginado['num'] - 1) * settings.CHAT_CONVERSACIONES_POR_PAGINA
        end = paginado['num'] * settings.CHAT_CONVERSACIONES_POR_PAGINA
        pagina = conversaciones[start:end]
        return pagina

    def _obtener_conversaciones_de_pagina(self, conversaciones_filtradas_y_paginadas):
        conversaciones_de_pagina = []
        for conversacion in conversaciones_filtradas_y_paginadas:
            # fue_leida_o_es_vieja, id, nombre, es_de_whatsapp, prospecto, tiene_telefono_para_whatsapp, tipo
            serializer = ConversacionSerializer.para(conversacion)
            conversacion_serializada = serializer.serialize()
            conversaciones_de_pagina.append(conversacion_serializada)

        return conversaciones_de_pagina

    def set_filtros(self, filtros):
        self.set_argument_value('filtros', filtros)
        self.set_argument_value('administrador_de_conversaciones', None)

    def set_numero_de_pagina_a_listar(self, numero_de_pagina):
        self.set_argument_value('numero_de_pagina', numero_de_pagina)

    def set_lector(self, vendedor):
        self.set_argument_value('lector', vendedor)
        self.set_argument_value('administrador_de_conversaciones', None)

    def esta_autorizado_el_lector(self):
        return self._administrador().puede_acceder_a_conversaciones()

    def _administrador(self):
        if self.get_argument_named('administrador_de_conversaciones') is None:
            self._inicializar_administrador()
        return self.get_argument_named('administrador_de_conversaciones')

    def _inicializar_administrador(self):
        if self._lector() is not None:
            self._set_administrador(AdministradorDeConversaciones(self._lector()))
        if self._filtros() is not None and 'medio' in self._filtros():
            tipo_de_medio = self._filtros()['medio']
            if tipo_de_medio == 'A':
                self._set_administrador(AdministradorDeConversacionesDeEAvisos(self._lector()))

    def _set_administrador(self, administrador):
        self.set_argument_value('administrador_de_conversaciones', administrador)

    def _lector(self):
        return self.get_argument_named('lector')

    def _filtros(self):
        return self.get_argument_named('filtros')

    def _numero_de_pagina(self):
        return self.get_argument_named('numero_de_pagina')

    def _filtrado_por_todos_los_medios(self, filtros):
        return filtros.get('medio', self._FITRO_TODOS) == self._FITRO_TODOS

    def _filtrado_por_medio_eaviso(self, filtros):
        return filtros.get('medio', self._FITRO_TODOS) == MedioEAviso.tipo()


class ConversacionSerializer(object):
    """Convierte una conversacion en un JSON"""

    def __init__(self, conversacion):
        super(ConversacionSerializer, self).__init__()
        self._conversacion = conversacion

    def serialize(self):
        raise NotImplemented('Subclass responsibility')

    def mensajes_adaptados(self):
        return self._conversacion.mensajes()

    def _cantidad_de_mensajes(self):
        return settings.CHAT_MENSAJES_EN_VISTA_PREVIA

    @classmethod
    def para(cls, conversacion):
        for renderer in cls.__subclasses__():
            if renderer.acepta(conversacion):
                return renderer(conversacion)

    @classmethod
    def acepta(cls, conversacion):
        raise NotImplemented('Subclass responsibility')


class ConversacionConProspectoSerializer(ConversacionSerializer):
    def serialize(self):
        return {
            'id': self._conversacion.id,
            'object_id': self._conversacion.prospecto.id,
            'nombre': self._conversacion.nombre,
            'nombre_de_medio': self._conversacion.nombre_de_medio(),
            'puede_ser_eliminada': self._conversacion.es_de_whatsapp(),
            'prospecto': self._conversacion.prospecto,
            'fue_leida_o_es_vieja': self._conversacion.fue_leida_o_es_vieja(),
            'tipo': self._conversacion.tipo,
            'mensajes': self._conversacion.mensajes(self._cantidad_de_mensajes()),
            'tooltip_del_titulo': 'Click para ir al prospecto %s' % self._conversacion.nombre,
            'url': reverse('prospecto', kwargs={'pk': self._conversacion.prospecto.pk}),
        }

    @classmethod
    def acepta(cls, conversacion):
        return conversacion.tipo in Conversacion.tipos()


class ConversacionDeEAvisosSerializer(ConversacionSerializer):

    def mensajes_adaptados(self):
        mensajes = super(ConversacionDeEAvisosSerializer, self).mensajes_adaptados()
        return self._adaptar_mensajes(mensajes)

    def _adaptar_mensajes(self, mensajes):
        return [MensajeDeEAvisoAdapter(mensaje) for mensaje in mensajes]

    def serialize(self):
        return {
            'id': self._conversacion.id,
            'object_id': self._conversacion.id,
            'nombre': self._conversacion.nombre_de_medio(),
            'nombre_de_medio': self._conversacion.nombre_de_medio(),
            'puede_ser_eliminada': False,
            'prospecto': None,
            'fue_leida_o_es_vieja': self._conversacion.esta_leida(),
            'tipo': self._conversacion.tipo(),
            'mensajes': self._mensajes(),
            'tooltip_del_titulo': 'Click para ir a la publicación',
            'url': self._conversacion.publicacion().url(),
        }

    # --- metodos privados ---

    def _mensajes(self):
        mensajes = self._conversacion.mensajes(self._cantidad_de_mensajes())
        return self._adaptar_mensajes(mensajes)

    @classmethod
    def acepta(cls, conversacion):
        return conversacion.tipo() in ConversacionDeEAvisos.tipos()


class MensajeDeEAvisoAdapter(object):
    def __init__(self, mensaje_de_publicacion_e_aviso):
        self._mensaje = mensaje_de_publicacion_e_aviso

    def proveniente_de_cliente(self):
        return isinstance(self._mensaje, PreguntaDePublicacionEAvisos)

    def fue_enviado_por_api(self):
        return self._mensaje.fue_exitoso()

    def fue_enviado_al_telefono(self):
        return self._mensaje.fue_exitoso()

    def fecha(self):
        return self._mensaje.created_at()

    def nombre_de_emisor(self):
        return 'Posible comprador'

    def obtener_texto(self):
        return self._mensaje.contenido()

    def origen(self):
        from occ.models import OrigenDesconocido
        return OrigenDesconocido.nuevo()
