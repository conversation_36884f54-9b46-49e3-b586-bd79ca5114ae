# coding=utf-8
import logging

from django.core.exceptions import ValidationError

from conversaciones.gestor import GestorDeConversaciones
from conversaciones.medios import MedioDeConversacion, MedioWhatsapp, MedioEmail, MedioSMS
from conversaciones.meta.canal_de_comunicacion_via_meta import CanalDeComunicacionDeWhatsappViaMeta
from conversaciones.meta.notificador_de_conversaciones_meta_via_pusher import NotificadorDeConversacionesMetaViaPusher
from conversaciones.renderer_conversacion_multimedio import ContextoDeConversacionMultimedio
from layers.application import AppCommand
from layers.application.commands.eavisos import EnviarMensajeDePublicacionEAvisosComando
from layers.application.commands.validators.generic import NotNone, NotEmptyString
from layers.application.commands.validators.validator import Parameter
from prospectos.models import Prospecto
from whatsapp.meta.services.ws_meta_service import WsMetaService

logger = logging.getLogger('whatsapp-meta')

class EnviarMensajeMultimedio(AppCommand):
    """
    Esta clase modela el envio de un mensaje por algun tipo de medio.
    El mensaje puede ser a un prospecto o respondiendo un EAviso
    Sender: usuario que envía el mensaje
    id_de_objeto: id de objeto relevante para enviar el mensaje (ver mas abajo)
    tipo: tipo del objeto relevante para enviar el mensaje (ver mas abajo)

    Actualmente el comando acepta dos tipos de objetos relevantes para enviar un mensaje:
     - Prospecto, en cuyo caso envia un mensaje a la conversación asociada a éste
     - ConversacionDeEaviso, en cuyo caso envia el mensaje a ese medio

    Nota 1:
        Actualmente el envio a un EAviso es delegado al Command que se encarga de hacerlo
        Los mensajes enviados por el resto de los medios es delegado al gestor.
        Este último código fué extraido de la view y no sufrió mayor refactor que ponerlo en este Command.
    """
    # Estos textos/constantes son los valores que figuran en el HTML cuando nos mandan un mensaje.
    WHATSAPP = 'whatsapp'
    EMAIL = 'mail'
    SMS = 'sms'
    EAVISO = 'eaviso'
    # Estos tipos de medio son para manejar el caso en que no es un mensaje para un EAviso. Por eso EAviso no está.
    _TIPOS_DE_MEDIO = {WHATSAPP: MedioWhatsapp.tipo(), EMAIL: MedioEmail.tipo(),
                       SMS: MedioSMS.tipo()}

    def _initial_parameters(self):
        params = super(EnviarMensajeMultimedio, self)._initial_parameters()
        params.append(Parameter("sender", [NotNone()]))
        params.append(Parameter("id_objeto", [NotNone()]))
        params.append(Parameter("tipo", [NotNone()]))
        params.append(Parameter("contenido", [NotNone(), NotEmptyString()]))
        return params

    def _initial_required_parameter_names(self):
        return ['contenido', 'tipo', 'id_objeto', 'sender']

    def set_sender(self, user):
        self.set_argument_value('sender', user)

    def set_id_de_objeto(self, id_objeto):
        self.set_argument_value('id_objeto', id_objeto)

    def set_tipo(self, tipo):
        self.set_argument_value('tipo', tipo)

    def set_contenido(self, contenido):
        self.set_argument_value('contenido', contenido.strip())

    def get_contenido(self):
        return self.get_argument_named('contenido')

    def get_tipo_objeto(self):
        return self.get_argument_named('tipo')

    def get_id_objeto(self):
        return self.get_argument_named('id_objeto')

    def get_sender(self):
        return self.get_argument_named('sender')

    # --- metodos privados ---

    def _execute_from_successful_result(self, result):
        tipo_es_e_aviso = self.get_tipo_objeto() == self.EAVISO
        if tipo_es_e_aviso:
            return self._enviar_mensaje_de_e_aviso(result)
        return self._enviar_mensaje_multimedio(result)

    def _enviar_mensaje_de_e_aviso(self, result):
        send_result = self._enviar_mensaje_e_aviso(user=self.get_sender(),
                                                   id_objeto=self.get_id_objeto(),
                                                   texto=self.get_contenido())
        if send_result.is_successful():
            gestor = self._nuevo_gestor()
            respuesta = send_result.get_object()
            conversacion_eaviso = respuesta.conversacion()
            contexto = ContextoDeConversacionMultimedio.nuevo_para(gestor.rol(), conversacion_eaviso)
            result.set_object(contexto)
            return result
        else:
            result.add_errors(send_result.errors())
            return result

    def _enviar_mensaje_multimedio(self, result):
        gestor = self._nuevo_gestor()
        try:
            prospecto_id = self.get_id_objeto()
            prospecto = Prospecto.objects.get(id=prospecto_id)
        except Prospecto.DoesNotExist:
            result.add_error('No existe un prospecto con ese ID')
            return result
        try:
            medio = self._medio_para(self.get_tipo_objeto())
            gestor.enviar_a(prospecto, self.get_contenido(), medio)
            conversacion = gestor.conversacion_multimedia_para(prospecto)
            contexto = ContextoDeConversacionMultimedio.nuevo_para(gestor.rol(), conversacion)
            result.set_object(contexto)
        except ValidationError as exc:
            result.add_errors(exc.messages)
        except ValueError as exc:
            result.add_error(str(exc))
        return result

    def _enviar_mensaje_e_aviso(self, user, id_objeto, texto):
        comando = EnviarMensajeDePublicacionEAvisosComando()
        comando.set_arguments({
            'user': user,
            'id_mensaje_de_publicacion': id_objeto,
            'contenido': texto,
        })
        result = comando.execute()
        return result

    def _medio_para(self, codigo):
        tipo = self._TIPOS_DE_MEDIO.get(codigo)
        if not tipo:
            raise ValidationError('%s no es un tipo de medio valido' % codigo)
        medio = MedioDeConversacion.nuevo_para(tipo)
        return medio

    def _nuevo_gestor(self):
        canal_via_meta = CanalDeComunicacionDeWhatsappViaMeta.nuevo(
            servicio_de_meta=WsMetaService.nuevo(),
            notificador=NotificadorDeConversacionesMetaViaPusher.nuevo(), logger=logger)
        return GestorDeConversaciones.nuevo_para(rol=self.get_sender().vendedor, canal_via_meta=canal_via_meta)

    def _is_string_defined(self, string):
        if string is None:
            return False
        if string.strip() == '':
            return False
        return True
