# coding=utf-8
from django.core.exceptions import ObjectDoesNotExist

from conversaciones.models import Conversacion
from conversaciones.renderer_conversacion_multimedio import ConversacionMultimedioRender
from layers.application import AppCommand
from layers.application.commands.validators.delivery import DebeSerVendedor
from layers.application.commands.validators.generic import NotNone
from layers.application.commands.validators.validator import Parameter
from occ.models.eavisos import ConversacionDeEAvisos


class VerConversacionMultimedio(AppCommand):
    """
    Este comando devuelve un render en un HTML de una conversación unificada.
    Lector: usuario que va a consumir/leer/ver esta conversación
    id_de_objeto: id de objeto relevante para conseguir/construir esta conversación
    tipo: tipo del objeto relevante para conseguir/construir esta conversacion

    Actualmente el comando acepta dos tipos de objetos:
     - Prospecto, en cuyo caso devuelve el render de la conversacion asociada a ese prospecto
     - ConversacionDeEaviso, en cuyo caso devuelve el render de esa conversacion

    Nota:
        Este comando esta haciendo cosas que no le corresponden:
        - renderizar
        - determinar el contexto del renderer
        Estas responsabilidades hay que moverlas arriba, a un Presenter o a la View.
    """

    def _default_arguments(self):
        return {'lector': None, 'id_de_objeto': None, 'tipo': None}

    def _initial_required_parameter_names(self):
        return ['lector', 'id_de_objeto', 'tipo']

    def _initial_parameters(self):
        params = super(VerConversacionMultimedio, self)._initial_parameters()
        params.append(Parameter("lector", [NotNone(), DebeSerVendedor()]))
        params.append(Parameter("id_de_objeto", [NotNone()]))
        params.append(Parameter("tipo", [NotNone()]))
        return params

    def set_lector(self, user):
        self.set_argument_value('lector', user)

    def set_id_de_objeto(self, objeto_id):
        self.set_argument_value('id_de_objeto', objeto_id)

    def set_tipo(self, tipo):
        self.set_argument_value('tipo', tipo)

    def _execute_from_successful_result(self, result):
        user = self.get_argument_named('lector')
        objeto_id = self.get_argument_named('id_de_objeto')
        tipo = self.get_argument_named('tipo')

        es_una_conversacion_de_e_aviso = tipo == ConversacionDeEAvisos.TIPO
        renderer = ConversacionMultimedioRender()
        vendedor = user.vendedor
        if es_una_conversacion_de_e_aviso:
            conversacion_id = objeto_id
            try:
                conversacion = ConversacionDeEAvisos.objects.para_usuario(user).get(id=conversacion_id)
            except ObjectDoesNotExist:
                result.add_error('El ID de objeto no corresponde a una conversacion de EAviso.')
                return result
            self._marcar_conversacion_de_eavisos_como_leida(vendedor, conversacion_id)
            render = renderer.render_to_string_de_conversacion_e_aviso_element(vendedor, conversacion)
        else:
            try:
                prospecto = self._obtener_prospecto(objeto_id, vendedor)
            except ObjectDoesNotExist:
                result.add_error('El ID de objeto no corresponde a un prospecto.')
                return result
            self._marcar_como_leida(vendedor, prospecto)
            render = renderer.render_to_string_de_conversacion_element(vendedor, prospecto)

        result.set_object(render)
        return result

    def _obtener_prospecto(self, pk, vendedor):
        prospecto = vendedor.todos_los_prospectos().get(id=pk)
        return prospecto

    def _marcar_como_leida(self, vendedor, prospecto):
        self._marcar_como_leida_para_tipo(vendedor, prospecto, Conversacion.TIPO_WHATSAPP)
        self._marcar_como_leida_para_tipo(vendedor, prospecto, Conversacion.TIPO_SMS)
        self._marcar_como_leida_para_tipo(vendedor, prospecto, Conversacion.TIPO_EMAIL)

    def _marcar_como_leida_para_tipo(self, vendedor, prospecto, tipo):
        from conversaciones.administracion_de_conversaciones import AdministradorDeConversaciones
        if self._debe_marcar_como_leida(prospecto, vendedor):
            administrador = AdministradorDeConversaciones(vendedor)
            administrador.marcar_conversacion_como_leida(prospecto, tipo)

    def _debe_marcar_como_leida(self, prospecto, vendedor):
        return vendedor == prospecto.obtener_vendedor() or (
            not prospecto.esta_asignado() and prospecto.obtener_responsable() == vendedor)

    def _marcar_conversacion_de_eavisos_como_leida(self, vendedor, conversacion_id):
        from conversaciones.administracion_de_conversaciones import AdministradorDeConversacionesDeEAvisos
        administrador = AdministradorDeConversacionesDeEAvisos(vendedor)
        administrador.marcar_conversacion_como_leida(conversacion_id)
