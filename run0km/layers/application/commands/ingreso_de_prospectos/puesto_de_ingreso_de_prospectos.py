from django.conf import settings
from retrying import Retrying

from core.locker.errors import ResourceLockedError
from core.locker.mem_locker import Locker
from prospectos.models.exceptions import LoteDeProspectosRechazadoException


class PuestoDeIngresoDeProspectos(object):
    """
    Este objeto modela un "puesto de entrada" pero para prospectos entrando al sistema.
    Como tal su responsabilidad es la de dejar entrar, encolar o rechazar pedidos de ingreso.

    Agrupa los pedidos por sus datos de contacto. No deja pasar dos prospectos con mismos datos de contacto.
    """
    def __init__(self):
        super(PuestoDeIngresoDeProspectos, self).__init__()
        self._locker = Locker.new_for_group(group_name='ingreso-de-prospectos')

    @classmethod
    def nuevo(cls):
        return cls()

    def ingresar(self, datos_de_prospectos, metodo_constructor, argumentos):
        politica_de_ejecucion = Retrying(
            stop_max_attempt_number=settings.INGRESO_LOCKEO_DE_DATOS_DE_CONTACTOS_CANTIDAD_DE_REINTENTOS,
            wait_fixed=settings.INGRESO_LOCKEO_DE_DATOS_DE_CONTACTOS_MILISEGUNDOS_DE_ESPERA_ENTRE_REINTENTOS,
            retry_on_exception=self._es_resource_locked_error)
        try:
            resultado = politica_de_ejecucion.call(
                self._evaluar_lockeando, prospectos=datos_de_prospectos,
                funcion=metodo_constructor, argumentos=argumentos)
        except ResourceLockedError:
            raise LoteDeProspectosRechazadoException.datos_de_contecto_bloqueados()
        else:
            return resultado

    def _evaluar_lockeando(self, prospectos, funcion, argumentos):
        """

        Para deshabilitar el locker, descomentar la linea de abajo y comentar el resto de la funcion.

        """
        return funcion(*argumentos)
        # datos = self._obtener_datos_de_contacto_de(prospectos)
        # if datos:
        #     resultado = self._locker.do_locking_all(datos, funcion, argumentos)
        # else:
        #     return funcion(*argumentos)
        # return resultado

    def _obtener_datos_de_contacto_de(self, prospectos):
        datos = []
        for prospecto in prospectos:
            self._agregar_dato_si_existe('telefono', datos, prospecto)
            self._agregar_dato_si_existe('email', datos, prospecto)

        datos.sort(key=str.lower)
        return datos

    def _agregar_dato_si_existe(self, campo, datos, prospecto):
        dato_de_contacto = prospecto.get(campo)
        if dato_de_contacto is not None and str(dato_de_contacto) != '':
            datos.append(str(dato_de_contacto))

    def _es_resource_locked_error(self, exception):
        return isinstance(exception, ResourceLockedError)
