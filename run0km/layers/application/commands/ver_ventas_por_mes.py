# coding=utf-8
import json
from datetime import date
from operator import itemgetter

from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.utils import timezone

from core.date_helper import DatetimeHelper
from layers.application import AppCommand
from layers.application.commands.validators.validator import Parameter
from prospectos.models import Venta
from vendedores.forms import VentasPorMesForm
from vendedores.models import VentasAprobadasPorMes, Vendedor


class VerListadoDeVentasPorMesCommand(AppCommand):
    """
        Modelo la interacción de ver un listado de ventas.
        Al ejecutarme respondo información diferente segun el rol del que está pidiendo el listado y qué meses pidió,
        la respuesta contiene informacion de las ventas y algunas configuraciones de permisos.

        Pendientes: fue el primer movimiento de responsabilidades desde la capa de la view a la de aplicacion. Falta
        seguir iterando.
    """

    def _initial_parameters(self):
        params = super(VerListadoDeVentasPorMesCommand, self)._initial_parameters()
        # Nota: ¿No faltan validadores aca? Si es usuario, gerente, si son numeros, etc
        # Bueno, tambien faltan tests...
        params.append(Parameter("user", []))
        params.append(Parameter("anio", []))
        params.append(Parameter("mes", []))
        params.append(Parameter("proveedor_id", []))
        return params

    def _execute_from_successful_result(self, result):
        result = super(VerListadoDeVentasPorMesCommand, self)._execute_from_successful_result(result)
        datos = self._obtener_datos_de_ventas()
        result.set_object(datos)
        return result

    def _obtener_datos_de_ventas(self):
        user = self._user()
        meses = self._get_ultimos_meses(settings.CANTIDAD_MESES_RANKING)
        form, mes, anio = self._obtener_form_mes_y_anio()
        proveedor = self._proveedor_id()

        if user.is_gerente():
            listas_ventas = self._get_mayores_ventas_por_supervisor(user.gerente, meses, proveedor)
            datos = self._get_datos(mes, anio, supervisores=user.gerente.supervisores(), multiples_supervisores=False,
                                    proveedor=proveedor)
        elif user.is_vendedor() and user.vendedor.es_supervisor():
            listas_ventas = self._get_mayores_ventas_por_vendedor_y_proveedor(user.vendedor, meses, proveedor)
            datos = self._get_datos(mes, anio, supervisores=[user.vendedor], multiples_supervisores=False,
                                    proveedor=proveedor)
        else:
            listas_ventas = []
            datos = self._get_datos_de_vendedor(mes, anio, user.vendedor)

        meses_en_grafico = [x.month for x in meses]
        datos.update({
            'meses_en_grafico': meses_en_grafico,
            'listas_ventas': json.dumps(listas_ventas),
            'form': form,
            'anio': anio,
            'mes': int(mes),
            'nombre_mes': self._nombre_de_mes(mes),
            'proveedor': proveedor
        })

        return datos

    def _nombre_de_meses(self):
        nombres_mes = dict(enumerate(DatetimeHelper().months_names(), 1))
        return nombres_mes

    def _obtener_form_mes_y_anio(self):
        anio = self._anio()
        mes = self._mes()
        form = VentasPorMesForm(data={'anio': anio, 'mes': mes, 'proveedor': self._proveedor_id(),
                                      'concesionaria': self._user().concesionaria()})
        if not form.is_valid():
            localnow = timezone.localtime(timezone.now())
            anio = localnow.year
            mes = localnow.month
        return form, mes, anio

    def _get_mayores_ventas_por_supervisor(self, gerente, meses, proveedor):
        ventas_por_supervisor = []
        for supervisor in gerente.supervisores():
            ventas_de_supervisor = {"info": {"id": int(supervisor.id), "nombre": supervisor.full_name()},
                                    'total': 0}
            for mes in meses:
                #TODO Agregar logica de proveedor. Misma solucion para el supervisor
                cantidad_de_ventas = self.cantidad_de_ventas_por_supervisor_por_mes(mes, supervisor, proveedor)
                ventas_de_supervisor[mes.month] = cantidad_de_ventas
                ventas_de_supervisor['total'] += cantidad_de_ventas
            ventas_por_supervisor.append(ventas_de_supervisor)
        ventas_por_supervisor.sort(key=lambda k: -k['total'])
        ventas_por_mes = []
        for ventas_del_supervisor in ventas_por_supervisor:
            ventas_del_supervisor.pop('total')
            ventas_por_mes.append(ventas_del_supervisor)
        return ventas_por_supervisor[:6]

    def cantidad_de_ventas_por_supervisor_por_mes(self, mes, supervisor, proveedor):
        if proveedor:
            ventas_de_supervisor = Venta.objects.de_supervisor(supervisor).aprobadas().de_proveedor(proveedor).en_mes(mes.year, mes.month).count()
            return ventas_de_supervisor
        else:
            return VentasAprobadasPorMes.objects.ventas_por_supervisor_por_mes(mes.year, mes.month, supervisor)

    def _get_mayores_ventas_por_vendedor_y_proveedor(self, supervisor, meses, proveedor):
        ventas_por_vendedor = []

        vendedores_y_su_supervisor = Vendedor.all_objects.filter(id=supervisor.id) | supervisor.vendedores.all()

        for vendedor in vendedores_y_su_supervisor:
            ventas_de_vendedor = {
                "info": {
                    "id": int(vendedor.id),
                    "nombre": vendedor.full_name()
                },
                'total': 0
            }

            for mes in meses:
                ventas = self._cantidad_de_ventas_de_vendedor_y_proveedor_para(mes, proveedor, vendedor)
                ventas_de_vendedor[mes.month] = ventas
                ventas_de_vendedor['total'] += ventas

            ventas_por_vendedor.append(ventas_de_vendedor)

        ventas_por_vendedor.sort(key=lambda k: -k['total'])

        ventas_por_mes = []
        for ventas in ventas_por_vendedor:
            ventas.pop('total')
            ventas_por_mes.append(ventas)

        return ventas_por_vendedor[:3]

    def _cantidad_de_ventas_de_vendedor_y_proveedor_para(self, mes, proveedor, vendedor):
        if proveedor:
            return Venta.objects.de_vendedor(vendedor).aprobadas().de_proveedor(proveedor).en_mes(mes.year,
                                                                                              mes.month).count()
        else:
            try:
                ventas = VentasAprobadasPorMes.objects.get(vendedor=vendedor, anio=mes.year, mes=mes.month).cantidad
            except ObjectDoesNotExist:
                ventas = 0
            return int(ventas)

    def _get_datos(self, mes, anio, supervisores, multiples_supervisores, proveedor=None):
        """
            Responde un diccionario de la pinta: {
                'es_mes_actual': True/False,
                'ids_ventas_form': list de Ids,
                'ventas_por_supervisor': list({
                        'supervisor': supervisor,
                        'informacion_de_ventas': list({
                                'vendedor': vendedor,
                                'ventas_aprobadas': Number,
                                'cantidad_de_ventas_aprobadas': Number,
                                'ventas_bloqueadas': Number,
                                'ventas_pendientes': Number,
                                'ventas_canceladas': Number
                            }),
                        'cantidad_de_aprobadas': Number,
                        'cantidad_de_pendientes': Number,
                        'cantidad_de_bloqueadas': Number,
                        'cantidad_de_canceladas': Number
                    }),
                'ventas_aprobadas_totales': Number,
                'ventas_pendientes_totales': Number,
                'ventas_bloqueadas_totales': Number,
                'ventas_canceladas_totales': Number,
            }
        """

        ventas_aprobadas_totales = 0
        ventas_canceladas_totales = 0
        ventas_pendientes_totales = 0
        ventas_bloqueadas_totales = 0
        ventas_por_supervisor = []
        ids_ventas = []

        for supervisor in supervisores:
            informacion_ventas_supervisor = self._informacion_de_ventas_para_supervisor(anio, mes, supervisor, proveedor)
            ventas_por_supervisor.append(informacion_ventas_supervisor)
            ventas_aprobadas_totales += informacion_ventas_supervisor['cantidad_de_aprobadas']
            ventas_pendientes_totales += informacion_ventas_supervisor['cantidad_de_pendientes']
            ventas_bloqueadas_totales += informacion_ventas_supervisor['cantidad_de_bloqueadas']
            ventas_canceladas_totales += informacion_ventas_supervisor['cantidad_de_canceladas']

            for ventas_de_vendedor in informacion_ventas_supervisor['informacion_de_ventas']:
                ids_ventas += [int(venta.id) for venta in ventas_de_vendedor['ventas_aprobadas']]
                ids_ventas += [int(venta.id) for venta in ventas_de_vendedor['ventas_pendientes']]
                ids_ventas += [int(venta.id) for venta in ventas_de_vendedor['ventas_canceladas']]

        ventas_por_supervisor = sorted(ventas_por_supervisor, key=itemgetter('cantidad_de_aprobadas'), reverse=True)

        return {
            'es_mes_actual': self._es_mes_actual(mes, anio),
            'ids_ventas_form': ids_ventas,
            'ventas_por_supervisor': ventas_por_supervisor,
            'ventas_aprobadas_totales': ventas_aprobadas_totales,
            'ventas_pendientes_totales': ventas_pendientes_totales,
            'ventas_bloqueadas_totales': ventas_bloqueadas_totales,
            'ventas_canceladas_totales': ventas_canceladas_totales,
            'multiples_supervisores': multiples_supervisores,
            'puede_exportar_ventas': True,
            'puede_editar_ventas': True
        }

    def _get_datos_de_vendedor(self, mes, anio, vendedor):
        inicio, fin = DatetimeHelper().range_for(mes, anio)
        informacion_de_ventas = Venta.objects.informacion_de_ventas_para_vendedor(inicio, fin, vendedor)
        return {
            'informacion_de_ventas': informacion_de_ventas,
            'ventas_aprobadas_totales': len(informacion_de_ventas['ventas_aprobadas']),
            'ventas_bloqueadas_totales': len(informacion_de_ventas['ventas_bloqueadas']),
            'ventas_pendientes_totales': len(informacion_de_ventas['ventas_pendientes']),
            'ventas_canceladas_totales': len(informacion_de_ventas['ventas_canceladas']),
            'multiples_supervisores': False,
            'puede_exportar_ventas': False,
            'puede_editar_ventas': False
        }

    def _informacion_de_ventas_para_supervisor(self, anio, mes, supervisor, proveedor=None):
        ventas_por_vendedor = Venta.objects.ventas_por_vendedor_y_proveedor(supervisor, anio, mes, proveedor)

        cantidad_de_aprobadas = sum([ventas['cantidad_de_ventas_aprobadas'] for ventas in ventas_por_vendedor])
        cantidad_de_pendientes = sum([len(ventas['ventas_pendientes']) for ventas in ventas_por_vendedor])
        cantidad_de_bloqueadas = sum([len(ventas['ventas_bloqueadas']) for ventas in ventas_por_vendedor])
        cantidad_de_canceladas = sum([len(ventas['ventas_canceladas']) for ventas in ventas_por_vendedor])

        return {
            'supervisor': supervisor,
            'informacion_de_ventas': ventas_por_vendedor,
            'cantidad_de_aprobadas': cantidad_de_aprobadas,
            'cantidad_de_pendientes': cantidad_de_pendientes,
            'cantidad_de_bloqueadas': cantidad_de_bloqueadas,
            'cantidad_de_canceladas': cantidad_de_canceladas
        }

    def _es_mes_actual(self, mes, anio):
        hoy = timezone.localtime(timezone.now())
        return hoy.month == int(mes) and hoy.year == int(anio)

    def _get_ultimos_meses(self, cantidad):
        ultimo = timezone.localtime(timezone.now()).date()
        meses = []
        anterior = date(ultimo.year, ultimo.month, 1)
        for i in range(cantidad):
            meses.insert(0, anterior)
            anterior = anterior - timezone.timedelta(days=1)
            anterior = date(anterior.year, anterior.month, 1)

        return meses

    def _nombre_de_mes(self, mes):
        meses = self._nombre_de_meses()
        return meses[int(mes)]

    def _user(self):
        return self.get_argument_named('user')

    def _anio(self):
        return self.get_argument_named('anio')

    def _mes(self):
        return self.get_argument_named('mes')

    def set_user(self, user):
        self.set_argument_value('user', user)

    def set_anio(self, anio):
        self.set_argument_value('anio', anio)

    def set_mes(self, mes):
        self.set_argument_value('mes', mes)

    def _proveedor_id(self):
        return self.get_argument_named(u'proveedor_id')

    def set_proveedor_id(self, proveedor_id):
        self.set_argument_value('proveedor_id', proveedor_id)