# coding=utf-8


class ProtoAppCommand(object):
    """
    Esta clase modela un comando que no esta terminado de modelar.
    Se encarga de contener logica de operacion disparada desde un unico metodo: execute.

    La idea de esta clase es que cada vez que extraigamos logica de una View u otro lado
    que la pongamos acá y que de alguna forma esta clase nos sirva de ToDo de comandos para hacer.
    Los parametros pueden ser pasados directamente en el constructor
    o ser definidos con distintos setters (como para ir acercandose a la implementacion final).
    """

    def execute(self):
        raise NotImplementedError('Subclass responsibility')
