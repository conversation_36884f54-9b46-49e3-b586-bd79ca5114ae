# coding=utf-8
from layers.application.commands.base.command_parameters import CommandParameters
from layers.application.commands.validators.validator import NoneValidation
from layers.domain import Result


class AppCommand(object):
    """
    I model an interaction between an external actor and the system.
    I usually model also the operation that is being performed.
    """
    def __init__(self):
        super(AppCommand, self).__init__()
        self._parameters = CommandParameters(self,
                                             self._initial_parameters(),
                                             self._initial_required_parameter_names(),
                                             self._initial_at_least_one_required_sets(),
                                             self._default_arguments())

    def set_arguments(self, arguments):
        """
        Set the values that each parameter will have.
        Arguments is a dictionary with the names of the parameters as keys and the value of the argument as value.

        :param arguments: dict of str: object
        """
        self._parameters.set_arguments(arguments)

    def can_execute(self):
        """
        Answers the result of trying to execute.

        If result has no errors, it means the command can be executed (although it may fail if further problems arise).
        If result has errors, then the command can not be executed (and the errors explain why)"""
        result = Result()
        result = self._parameters.validate_parameters_and_arguments(result)
        return result

    def execute(self):
        """
        Performs the operation of the receiver and answers the result of the execution.

        :return: result of execution
        """
        result = self.can_execute()
        if result.has_errors():
            return result
        return self._execute_from_successful_result(result)

    def get_parameters(self):
        return self._parameters

    def add_parameter(self, parameter):
        self._parameters.add_parameter(parameter)

    def set_parameters(self, parameters):
        self._parameters.clear_parameters()
        self.add_parameters(parameters)

    def add_parameters(self, parameters):
        for parameter in parameters:
            self.add_parameter(parameter)

    def set_argument_value(self, argument_name, value):
        return self.get_parameters().set_argument_value(argument_name, value)

    def get_argument_named(self, argument_name, default_value=None):
        return self.get_parameters().get_argument_named(argument_name, default_value)

    def get_parameter_named(self, name):
        return self._parameters.get_parameter_named(name)

    def add_required_parameter_named(self, parameter_name):
        self._parameters.add_required_parameter_named(parameter_name)

    def remove_required_parameter_named(self, parameter_name):
        self._parameters.remove_required_parameter_named(parameter_name)

    def required_parameter_names(self):
        return self._parameters.required_parameter_names()

    def add_at_least_one_constraint(self, parameter_names):
        self._parameters.add_at_least_one_constraint(parameter_names)

    def handle_unexpected_arguments(self, extra_arguments, result):
        """Handle the existence of more arguments than those supported by parameters.

        Note: Currently, we don't raise warnings or errors for this case.
        If you want to do something about extra arguments, override this method.

        :param extra_arguments: the names of the arguments that has no matching parameter
        :param result: result object in which to add the error or warning about this issue
        """
        pass

    # --- private methods ---

    def _initial_required_parameter_names(self):
        """
        Answer the names of the required parameters.
        If my subclass has one or more required parameters,
        it should override me answering the names of that/those parameter/s.

        :return: set of str
        """
        return set()

    def _execute_from_successful_result(self, result):
        """
        My subclass should override me. Performs the operation and answers the result of the execution

        :return: result of execution
        """
        return result

    def _initial_parameters(self):
        """
        If I have more than 0 parameters, I should answer the definition of that/those parameter/s.

        :rtype: list of Parameter
        """
        return []

    def _initial_at_least_one_required_sets(self):
        """Answer a list of sets. Each set with the names of parameters from which al least one is required"""
        return []

    def _default_arguments(self):
        """Answer a dictionary with the default command arguments"""
        return {}


class AppCommandLegacy(object):
    def __init__(self, parameters=None):
        super(AppCommandLegacy, self).__init__()
        self._parameters = parameters or {}

    def can_execute(self):
        """
        Answers the result of trying to execute.

        If result has no errors, it means the command can be executed (although it may fail if further problems arise).
        If result has errors, then the command can not be executed (and the errors explain why)"""
        result = Result()
        for validator in self._parameters_validator():
            result = validator.validate(self._parameters, result)
        return result

    def execute(self):
        """
        Performs the operation of the receiver and answers the result of the execution.

        :return: result of execution
        """
        result = self.can_execute()
        if result.has_errors():
            return result
        return self._execute_from_successful_result(result)

    def _execute_from_successful_result(self, result):
        """
        Execute the command assuming can_execute is successful.
        My subclasses should override me.
        Performs the operation and answers the result of the execution

        :return: result of execution
        """
        return result

    def set_parameters(self, parameters):
        self._parameters = parameters

    def _parameters_validator(self):
        """
        My subclass should override me. Configures how my parameters must be validated.

        :return: a list of parameter names
        """
        return [NoneValidation()]
