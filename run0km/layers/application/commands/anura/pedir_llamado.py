# coding=utf-8
from django.core.exceptions import ValidationError, ObjectDoesNotExist

from layers.application import AppCommand
from layers.application.commands.validators.generic import NotNone, IsTelephoneNumber
from layers.application.commands.validators.validator import Parameter
from layers.domain.gestor_de_creditos_de_anura import GestorDeCreditosDeAnura
from occ.models.anura import IntentoDeLlamado


class PedirLlamadoPorAnuraComando(AppCommand):
    def _initial_parameters(self):
        return [
            Parameter("llamador", [NotNone()]),
            Parameter("telefono", [NotNone(), IsTelephoneNumber()]),
            Parameter("id_prospecto", [NotNone()])
        ]

    def _initial_required_parameter_names(self):
        return {"llamador", "id_prospecto", "telefono"}

    def _execute_from_successful_result(self, result):
        try:
            vendedor = self._llamador()
            prospecto = vendedor.todos_los_prospectos().get(id=self._id_prospecto())
            telefono = self._telefono()

            self._validar_que_el_vendedor_tenga_permisos(result, vendedor)
            self._validar_que_el_vendedor_tenga_creditos(result, vendedor)
            self._validar_telefono(result, telefono, prospecto)

            if not result.has_errors():
                intento_de_llamado = IntentoDeLlamado.nuevo(vendedor, prospecto, telefono)
                result.set_object(intento_de_llamado)

        except ValidationError as error:
            result.add_error(error.message)
        except ObjectDoesNotExist as error:
            result.add_error(str(error))
        return result

    def _validar_que_el_vendedor_tenga_permisos(self, result, vendedor):
        configuracion = vendedor.configuracion_de_servicios()
        tiene_servicio_de_llamadas_habilitado = configuracion.tiene_servicio_de_llamadas_habilitado()
        if not tiene_servicio_de_llamadas_habilitado:
            result.add_error(self.mensaje_de_error_falta_de_permisos())

    def _validar_que_el_vendedor_tenga_creditos(self, result, vendedor):
        gestor_de_creditos = GestorDeCreditosDeAnura()
        if not gestor_de_creditos.tiene_creditos_disponibles(vendedor):
            result.add_error(self.mensaje_de_error_falta_de_creditos())

    def _validar_telefono(self, result, telefono, prospecto):
        # TODO: agregarlo a las validaciones del telefono?
        if telefono not in prospecto.telefonos_activos():
            result.add_error(self.mensaje_de_error_telefono_inactivo())

    def set_llamador(self, llamador):
        self.set_argument_value('llamador', llamador)

    def set_prospecto(self, prospecto):
        self.set_id_prospecto(prospecto.id)

    def set_id_prospecto(self, id_prospecto):
        self.set_argument_value('id_prospecto', id_prospecto)

    def set_telefono(self, telefono):
        self.set_argument_value('telefono', telefono)

    def _id_prospecto(self):
        return self.get_argument_named('id_prospecto')

    def _llamador(self):
        return self.get_argument_named('llamador')

    def _telefono(self):
        return self.get_argument_named('telefono')

    @classmethod
    def mensaje_de_error_falta_de_creditos(cls):
        return "No tiene créditos suficientes para realizar este llamado"

    @classmethod
    def mensaje_de_error_falta_de_permisos(cls):
        return "No tiene permisos para realizar este llamado"

    @classmethod
    def mensaje_de_error_telefono_inactivo(cls):
        return "El teléfono no es un teléfono activo del prospecto"

    @classmethod
    def mensaje_de_error_telefono_invalido(cls):
        return "El teléfono no es válido"
