# coding=utf-8
from django.core.exceptions import ValidationError, ObjectDoesNotExist

from layers.application import AppCommand
from layers.application.commands.validators.delivery import DebeSerVendedor
from layers.application.commands.validators.generic import NotNone, NotEmptyString
from layers.application.commands.validators.validator import Parameter
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from prospectos.templatetags.prospectos_utils import clase_por_estado


class AgregarComentarioConSemanticaComando(AppCommand):
    def _initial_parameters(self):
        return [
            Parameter("user", [NotNone(), DebeSerVendedor()]),
            Parameter("id_prospecto", [NotNone()]),
            Parameter("etiqueta_semantica", [NotNone(), NotEmptyString()])
        ]

    def _initial_required_parameter_names(self):
        return {"user", "id_prospecto", "etiqueta_semantica"}

    def _execute_from_successful_result(self, result):
        try:
            vendedor = self._user().vendedor
            prospecto = vendedor.todos_los_prospectos().get(id=self._id_prospecto())
            gestor = GestorDeProspecto.nuevo_para(rol=vendedor)
            try:
                comentario = gestor.agregar_comentario_con_semantica(
                    prospecto=prospecto, etiqueta_semantica=self._etiqueta_semantica())
            except ValidationError as error:
                result.add_error(' | '.join(error.messages))
            else:
                result_obj = {
                    'prospecto_estado': clase_por_estado(prospecto),
                    'comentario': comentario
                }
                result.set_object(result_obj)
        except ValidationError as error:
            result.add_error(error.message)
        except ObjectDoesNotExist as error:
            result.add_error(str(error))
        return result

    def set_user(self, user):
        self.set_argument_value('user', user)

    def set_etiqueta_semantica(self, comentario):
        self.set_argument_value('etiqueta_semantica', comentario)

    def set_prospecto(self, prospecto):
        self.set_id_prospecto(prospecto.id)

    def set_id_prospecto(self, id_prospecto):
        self.set_argument_value('id_prospecto', id_prospecto)

    def _id_prospecto(self):
        return self.get_argument_named('id_prospecto')

    def _user(self):
        return self.get_argument_named('user')

    def _etiqueta_semantica(self):
        return self.get_argument_named('etiqueta_semantica')
