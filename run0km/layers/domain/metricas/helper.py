# coding=utf-8
from fractions import Fraction
from unittest import TestCase


class MetricaHelper(object):
    """Este objeto contiene metodos comunes a varios cálculos"""

    def calcular_tasa(self, numerador, denominador):
        if denominador != 0:
            return Fraction(numerador, denominador)
        return None

    def tasa_a_porcentaje(self, tasa):
        if tasa is None:
            porcentaje = None
        else:
            porcentaje = int(round(tasa * 100))
        return porcentaje

    def porcentaje(self, numerador, denominador):
        return self.tasa_a_porcentaje(self.calcular_tasa(numerador, denominador))


class MetricaHelperTest(TestCase):
    def test_calculo_de_una_tasa_tipica(self):
        un_medio = MetricaHelper().calcular_tasa(1, 2)
        self.assertEqual(un_medio, 0.5)

    def test_tasa_indefinida(self):
        tasa_indefinida = MetricaHelper().calcular_tasa(12, 0)
        self.assertIsNone(tasa_indefinida)

    def test_un_medio_es_50_porciento(self):
        un_medio = MetricaHelper().calcular_tasa(1, 2)
        cincuenta_porciento = MetricaHelper().tasa_a_porcentaje(un_medio)
        self.assertEqual(cincuenta_porciento, 50)

    def test_una_tasa_indefinida_es_un_porcentaje_indefinido(self):
        porcentaje_indefinido = MetricaHelper().tasa_a_porcentaje(None)
        self.assertIsNone(porcentaje_indefinido)

    def test_un_medio_es_cincuenta_porciento(self):
        cincuenta_porciento = MetricaHelper().porcentaje(1, 2)
        self.assertEqual(cincuenta_porciento, 50)

    def test_porcentaje_de_denominador_cero_da_indefinido(self):
        porcentaje_indefinido = MetricaHelper().porcentaje(1, 0)
        self.assertIsNone(porcentaje_indefinido)
