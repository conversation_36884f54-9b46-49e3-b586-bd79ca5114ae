class MetricaDeDelivery(object):
    """Esta clase abstracta modela las metricas de Delivery.

    Una metrica devuelve el valor que midio.
    Tambien puede devolver un detalle, o un desglose, o una explicacion de ese valor.
    Donde da cuenta de mas datos o detalles, o de como se calculo o llego a ese valor.

    Nota:
    En realidad este objeto son dos objetos acoplados:
    el Calculo/Medicion y el ComplexValue/CalculatedValue/MeasuredValue.
    El primer objeto tiene la responsabilidad de crear al segundo.
    El segundo tiene la responsabilidad de expresarse.
    De forma sucinta (solo un valor) o explayado (su detalle, descirpcion o explicacion)
    """

    def valor(self):
        raise NotImplementedError('Subclass responsibility')

    def descripcion(self):
        raise NotImplementedError('Subclass responsibility')

