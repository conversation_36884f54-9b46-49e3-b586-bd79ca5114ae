class Result(object):
    """This class models the result from the trying to execute or execution of a Command or Operation

    It has an object, which is set by the command/operation. It is the product of the execution.
    Example:
        1) Adding a new user would result in the result of that operation to have the user in the result object
        2) Closing a poll may return an object containing the booleam indicating success or failure of the poll,
        and a list of positive voters and negative voters. All of these in a dictionary.
    It has errors, which contain a list of problems in a human readable way. It is empty if the execution was successful

    Note that objects that return a Result should not throw exceptions.
    Because any errors should be contained and explained in the Result.
    """

    def __init__(self):
        self._errors = []
        self._object = None

    def get_object(self):
        return self._object

    def set_object(self, object_to_set):
        self._object = object_to_set

    def is_successful(self):
        return len(self._errors) == 0

    def errors(self):
        return self._errors

    def add_error(self, error):
        self._errors.append(error)

    def add_errors(self, errors):
        for error in errors:
            self.add_error(error)

    def has_errors(self):
        return not self.is_successful()

    # Convenience method
    def errors_as_string(self):
        return ', '.join(map(str, self.errors()))

    @classmethod
    def with_error(cls, error):
        result = cls()
        result.add_error(error)
        return result
