import os
from fabric import Connection
from fabric.tasks import task

# Set the correct environment variables for each server
os.environ.setdefault("AWS_PROD_KEY_PATH", "/home/<USER>/.ssh/delivery-new.pem")
os.environ.setdefault("AWS_STAGING_KEY_PATH", "/home/<USER>/.ssh/delivery-staging-2.pem")



HOSTS = {
    "prod": {
        "host": "ec2-3-213-213-124.compute-1.amazonaws.com",
        "user": "ubuntu",
        "connect_kwargs": {
            "key_filename": os.environ.get("AWS_PROD_KEY_PATH"),
            "disabled_algorithms": dict(pubkeys=["rsa-sha2-512", "rsa-sha2-256"])
        }
    },
    "staging": {
        "host": "<EMAIL>",
        "connect_kwargs": {
            "key_filename": os.environ.get("AWS_STAGING_KEY_PATH"),
            "disabled_algorithms": dict(pubkeys=["rsa-sha2-512", "rsa-sha2-256"])
        }
    }
}

HOSTS_FOLDERS = {
    "prod": {
        "proj_home": "/var/www/master/deliveryrun",
        "django_home": "/var/www/master/deliveryrun/run0km",
        "venv": "/var/www/master/deliveryrun/venv3.8/bin/activate"
    },
    "staging": {
        "proj_home": "/var/www/master/deliveryrun",
        "django_home": "/var/www/master/deliveryrun/run0km",
        "venv": "/var/www/master/deliveryrun/venv3.8/bin/activate"
    }
}


def virtualenv(folders, connection, command):
    source = "source {} && export LD_LIBRARY_PATH=/usr/local/ssl/lib:$LD_LIBRARY_PATH && ".format(folders["venv"])
    connection.run(source + command)


def apply_migrate(c, folders):
    print("*** Applying migrations...")
    with c.cd(folders["django_home"]):
        virtualenv(folders, c, "python manage.py migrate")


def apply_dependencies(c, folders):
    print("*** Applying dependencies...")
    with c.cd(folders["django_home"]):
        virtualenv(folders, c, "pip install -r requirements/master.txt")


def apply_collectstatic(c, folders):
    print("*** Applying collectstatic...")
    with c.cd(folders["django_home"]):
        virtualenv(folders, c, "python manage.py collectstatic <<<yes")


def restart_server(connection):
    connection.run("sudo service gunicorn restart")
    connection.run("sudo service celery-delivery restart")
    connection.run("sudo service celery-delivery-chat restart")
    connection.run("sudo service nginx restart")


@task
def deploy(c, server, migrate=False, dependencies=False, collectstatic=False, branch="master"):
    """
    Deploy the project to the remote server with an optional branch selection.

    Usage:
        fab deploy --server staging --branch develop
        fab deploy --server prod --migrate
        fab deploy --server prod --dependencies
        fab deploy --server prod --collectstatic
        fab deploy --server prod --branch feature-xyz

        or combine them:
        fab deploy --server prod --migrate --dependencies --collectstatic --branch develop
    """
    if server not in ["staging", "production", "prod"]:
        raise ValueError("Invalid server name")

    server_name = "prod" if server == "production" else server

    if server_name == "prod" and branch != "master":
        raise ValueError("❌ Solo la rama 'master' puede desplegarse en producción.")

    folders = HOSTS_FOLDERS[server_name]

    # Conectar al servidor remoto
    c = Connection(**HOSTS[server_name])

    print(f"Deploying the project to the remote server using branch '{branch}'...")

    # Hacer pull de la rama seleccionada
    c.run("cd {proj_home} && git fetch origin && git checkout {branch} && git pull origin {branch}".format(**folders,
                                                                                                           branch=branch))

    if dependencies:
        apply_dependencies(c, folders)
    if migrate:
        apply_migrate(c, folders)
    if collectstatic:
        apply_collectstatic(c, folders)

    restart_server(c)


@task
def restart(c, server):
    """
    Usage:
        fab restart --server prod
    """
    if server not in ["staging", "production", "prod"]:
        raise ValueError("Invalid server name")
    server_name = "prod" if server == "production" else server
    c = Connection(**HOSTS[server_name])
    restart_server(c)

