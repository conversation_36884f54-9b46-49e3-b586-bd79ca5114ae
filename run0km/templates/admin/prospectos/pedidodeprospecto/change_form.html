{% extends "admin/change_form.html" %}
{% load i18n admin_urls %}
{% load prospectos_utils %}
{% block extrahead %}{{ block.super }}
<link rel="stylesheet" href="{{STATIC_URL}}css/jquery-ui.min.css" />
<script type="text/javascript" src="{{STATIC_URL}}js/jquery.min.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}js/jquery-ui.min.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}js/system_unavailable.js"></script>
<script type="text/javascript" src="{{STATIC_URL}}js/admin_pedidos.js"></script>
<script type="text/javascript">

    var es_para_agregar = window.location.pathname == '/admin/prospectos/pedidodeprospecto/add/';
    var categorias= {% categorias_de_campania_no_externas_json %};
    var campanias= {% campanias_no_externas_json %};
    var vendedores = {% vendedores_json %};
    var supervisores = {% supervisores_json %};
    var campos_de_prospecto = {% campos_de_prospecto_json %};
    var configuraciones_crm_por_tipo = {% configuraciones_crm_por_tipo %};

    $(InicializarForm);
    $(InicializarAutocompletarFiltros);

    $(ActualizarVendedores);
    $(configurarEventosCRMS)

</script>

<style>
.field-crm ul
{
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.field-crm ul li { display: inline; }
</style>
{% endblock %}

{% block inline_field_sets %}
<br>
<p><b>Informacion sobre los filtros de pedidos:</b>
<ul>
<li>El pedido no incluirá los prospectos que tengan un campo que coincida con el criterio definido en <b>al menos un</b> filtro con acción "Excluir".</li>
<li>El pedido solo incluirá los prospectos que coincidan con el criterio de inclusión de <b>TODOS</b> sus subgrupos de inclusion.</li>
<li>Un subgrupo de inclusión es un conjunto de filtros de acción "Incluir" que comparten el mismo campo.</li>
<li>Un prospecto coincide con el criterio de inclusión de un subgrupo de inclusión si coincide con el criterio definido por <b>al menos uno</b> de sus filtros.</li>
</ul>
</p>
{{ block.super }}
{% endblock %}