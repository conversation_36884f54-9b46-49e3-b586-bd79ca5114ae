{% extends "admin/change_form.html" %}
{% load i18n admin_urls %}
{% load prospectos_utils %}
{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{{STATIC_URL}}admin/css/forms.css" />
    <link rel="stylesheet" type="text/css" href="{{STATIC_URL}}css/runkm-admin.css" />
    <link href="{{ STATIC_URL }}css/jquery-ui.min.css" type="text/css" rel="stylesheet"/>
    <link href="{{ STATIC_URL }}css/jquery.growl.css" type="text/css" rel="stylesheet">

    <script type="text/javascript" src="{{STATIC_URL}}js/jquery.min.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/jquery-ui.min.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/jquery-ui-timepicker-addon.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/jquery.ui.datepicker-es.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/system_unavailable.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/csrf_token.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/jquery.growl.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/notificador.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}lib/moment/moment-with-locales.min.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/descriptor_de_reporte_de_distribucion.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/spin.min.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/spinners.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/jquery.redirect.js"></script>

    <script type="text/javascript">
        $(function(){
            var json_responsables_por_concesionaria = {{ json_responsables_por_concesionaria|safe }};
            var reporteDeDistribucion = new DescriptorDeDistribucionDeProspectosController(
                json_responsables_por_concesionaria);
            reporteDeDistribucion.configurar()
        });
    </script>
{% endblock %}