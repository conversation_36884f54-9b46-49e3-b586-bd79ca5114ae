class WhatsAppCheckerApiError(RuntimeError):
    @classmethod
    def error_type(cls):
        raise NotImplementedError('Subclass responsibility')

    def __init__(self, e, request=None, response=None, *args, **kwargs):
        self.request = request
        self.response = response
        super(WhatsAppCheckerApiError, self).__init__(e, *args, **kwargs)


class WhatsAppCheckerInvalidUrlError(WhatsAppCheckerApiError):
    @classmethod
    def error_type(cls):
        return 'La url de serivicio no es correcta'


class WhatsAppCheckerConnectionError(WhatsAppCheckerApiError):
    @classmethod
    def error_type(cls):
        return 'El serivicio no responde'


class WhatsAppCheckerParseResponseError(WhatsAppCheckerApiError):
    @classmethod
    def error_type(cls):
        return 'Respuesta inesperada'
