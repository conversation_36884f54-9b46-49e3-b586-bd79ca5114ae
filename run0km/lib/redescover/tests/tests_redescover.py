from unittest import TestCase
from lib.redescover import SocialNetworksCover
from lib.redescover.errors import SocialNetworksConnectionError, SocialNetworksParseResponseError, \
    SocialNetworksTokenResponseError, SocialNetworkInvalidRequestError
from lib.redescover.tests import SocialNetworksConnectionFailMock, SocialNetworksTokenMock, SocialNetworksMock


class SocialNetworksTokenServiceTest(TestCase):
    def setUp(self):
        self.title = 'TokenTest'
        self.queries = self.generate_json()

    def generate_json(self):
        json_queries = []
        json_auto = {"Modelo": "golf", "Marca": "volkswagen"}
        json_querie = {'Ref': "11", 'Telefono': "1164462329", "Email": "<EMAIL>",
                       "Nombre": "<PERSON>", "Provincia": "Capital Federal", "Auto": json_auto}
        json_queries.append(json_querie)
        json_auto = {"Modelo": "tida", "Marca": "nissan"}
        json_querie = {'Ref': "12", 'Telefono': "111111111", "Email": "<EMAIL>",
                       "Nombre": "ivana curchmar", "Provincia": "buenos aires", "Auto": json_auto}
        json_queries.append(json_querie)
        return json_queries

    def test_should_fails_communication(self):
        mock_service = SocialNetworksConnectionFailMock()
        checker = SocialNetworksCover(mock_service)
        self.assertRaises(SocialNetworksConnectionError, checker.evaluate, title=self.title, queries=self.queries)

    def test_should_raises_parsing_error_by_format_unexpected(self):
        mock_service = SocialNetworksMock.response_format_unexpected()
        checker = SocialNetworksCover(mock_service)
        self.assertRaises(SocialNetworksParseResponseError, checker.evaluate, title=self.title, queries=self.queries)

    def test_should_raises_parsing_error_by_empty_response(self):
        mock_service = SocialNetworksMock.response_empty_json()
        checker = SocialNetworksCover(mock_service)
        self.assertRaises(SocialNetworksParseResponseError, checker.evaluate, title=self.title, queries=self.queries)

    def test_should_checks_token_correctly(self):
        mock_service = SocialNetworksTokenMock.response_valid_token('cd82e0a6-b204-46ba-9785-9f016bf0be')
        checker = SocialNetworksCover(mock_service)
        token_response = checker.evaluate(self.title, self.queries)
        self.assertFalse(token_response.has_error)
        self.assertEqual(token_response.request_id, 253)
        self.assertEqual(token_response.error_message, None)

    def test_should_checks_invalid_token_correctly(self):
        mock_service = SocialNetworksTokenMock.response_invalid_token('cd82e0a6-b204-46ba-9785-9f016bf0be')
        checker = SocialNetworksCover(mock_service)
        self.assertRaises(SocialNetworksTokenResponseError, checker.evaluate, title=self.title, queries=self.queries)

    def test_should_raise_json_error_by_not_serializable_queries(self):
        queries = {'xx'}
        mock_service = SocialNetworksTokenMock.response_valid_token('cd82e0a6-b204-46ba-9785-9f016bf0be')
        checker = SocialNetworksCover(mock_service)
        self.assertRaises(SocialNetworkInvalidRequestError, checker.evaluate, title=self.title, queries=queries)