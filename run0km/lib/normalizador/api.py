# -*- coding: utf-8 -*-
import re
import xml.etree.ElementTree as ElementTree
from suds.client import Client

from lib.normalizador.errors import ServicioNormalizarRespuestaInesperadaError, ServicioNormalizarComunicacionError, \
    NormalizacionTelefonoError

# TODO: quitar esto cuando modele correctamente la entidad telefono
ID = 0
NUMERO = 1
PREFIJO = 2
MOVIL = 3
TELCO = 4
BIEN_CONSTITUIDO = 5
SPAM = 6
LOCALIDAD = 7
PROVINCIA = 8


class ClienteNormalizar(object):

    def __init__(self, wsdl):
        self.wsdl = wsdl

    def llamar(self, mensaje):
        try:
            cliente = Client(self.wsdl)
            response_xml = cliente.service.Normalizar(mensaje)
        except Exception as e:
            raise ServicioNormalizarComunicacionError(e, request=mensaje)
        else:
            return response_xml


class NormalizadorDeTelefonos(object):
    _mensaje_xml = '<xml><telefonos>%s</telefonos></xml>'
    _telefono_tag = '<telefono codigo="%s">%s</telefono>%s'

    @classmethod
    def nuevo_con_url(cls, wsdl_url):
        cliente = ClienteNormalizar(wsdl_url)
        return cls(cliente)

    def __init__(self, cliente):
        self.cliente = cliente
        self._mensaje_formado = ''

    def normalizar(self, numeros_telefonicos):
        """
            Responde un objeto que representa la respuesta obtenida desde el normalizador a partir de una
            lista de <id, numeros telefonicos>. Por ahora no modelo el objeto request.

            En caso de no poder comunicarse (cliente) lanza la excepcion ServicioNormalizarComunicacionError. Si
            el normalizador no responde un string con el xml se lanza la excepcion ServicioNormalizarRespuestaInesperadaError

        :param numeros_telefonicos: List(<(String,String)>)
        :return: RespuestaNormalizador
        """
        self._mensaje_formado = self._formar_mensaje_para(numeros_telefonicos)
        response_xml = self.cliente.llamar(self._mensaje_formado)
        try:
            response = RespuestaNormalizador(response_xml)
        except Exception as e:
            raise ServicioNormalizarRespuestaInesperadaError(e, request=self._mensaje_formado, response=response_xml)
        return response

    def _formar_mensaje_para(self, numeros_telefonicos):
        """
            Responde el xml para la consulta de todos los telefonos, agregando el tag <telefono> con
            el indeice como codigo.
        """
        mensaje = self._mensaje_xml
        for codigo, numero in numeros_telefonicos:
            mensaje_telefono = self._telefono_tag % (codigo, numero, '%s')
            mensaje %= mensaje_telefono

        return mensaje % ''  # elimino el ultimo %s


class RespuestaNormalizador(object):
    def __init__(self, xml):
        self.xml = self._normalize_xml_format(xml)
        root = ElementTree.fromstring(self.xml.encode('utf-8'))
        self.tag_telefonos = root.find('telefonos').findall('telefono')

    def _normalize_xml_format(self, xml_string):
        return re.sub('&(?!amp;)', '&amp;', xml_string)

    def get_telefono(self, codigo, default=None):
        """
            Responde el telefono normalizado o un el objeto default si no lo contiene en la respuesta. En caso que la
            normalizacion haya producido un error lanza una excepcion NormalizacionTelefonoError. Si el normalizador
            no responde el xml esperado lanza la excepcion ServicioNormalizarRespuestaInesperadaError
            (por ahora no tenemos modelado el consepto telefono asi que responde una asociacion)
        :param codigo: String
        :param default: object
        :return: Tuple representing a phone or default object
        """
        telefono_tag = self._get_tag_telefono(codigo)
        if telefono_tag is None:
            return default
        else:
            return self._parsear_telefono(telefono_tag)

    def cantidad_de_telefonos(self):
        return len(self.tag_telefonos)

    def _get_tag_telefono(self, codigo):
        tags = [tel_tag for tel_tag in self.tag_telefonos if tel_tag.get('codigo').lower() == codigo.lower()]
        if tags:
            # TODO: el serivicio respondia respuestas con mas de un tag para el mismo codigo!!!!
            #  if len(tags) > 1:
            #     raise ServicioNormalizarRespuestaInesperadaError(
            #         'Error al normalizar %s - Se obtuvo mas de una respuesta' % codigo,
            #         request='',
            #         response=self.xml)
            # else:
            return tags[0]
        else:
            return None

    def _parsear_telefono(self, telefono_tag):
        codigo = telefono_tag.get('codigo')
        mensaje_de_error = telefono_tag.get('error', '')
        if not mensaje_de_error:
            telefono = list(range(9))
            telefono[ID] = codigo
            telefono[TELCO] = telefono_tag.get('telco')
            telefono[MOVIL] = self._parse_bool(telefono_tag.get('movil'))
            telefono[SPAM] = self._parse_bool(telefono_tag.get('spam'))
            telefono[PREFIJO] = telefono_tag.get('Prefijo')
            telefono[NUMERO] = telefono_tag.text
            telefono[BIEN_CONSTITUIDO] = self._parse_bool(telefono_tag.get('IsOk'), default=False)

            telefono[LOCALIDAD] = telefono_tag.get('Localidad')
            telefono[PROVINCIA] = telefono_tag.get('Provincia')
            #telefono[ESTA_NORMALIZADO] = True
            # telefono = Telefono(numero=numero, es_movil=movil, telco=telco, esta_normalizado=True, esta_spam_list=spam)
            return telefono
        else:
            raise NormalizacionTelefonoError(
                'Error al parsear la respuesta del normalizador %s - %s' % (codigo, mensaje_de_error),
                request='',
                response=telefono_tag)

    def _parse_bool(self, attribute, default=None):
        if attribute is not None:
            return attribute == 'True'
        else:
            return default
