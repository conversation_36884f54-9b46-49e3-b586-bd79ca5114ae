import unittest

from lib.normalizador import NormalizadorDeTelefonos, NUMERO, TELCO, MOVIL, BIEN_CONSTITUIDO, SPAM, ID, \
    PREFIJO, LOCALIDAD, PROVINCIA
from lib.normalizador.errors import NormalizacionTelefonoError, ServicioNormalizarComunicacionError
from lib.normalizador.tests.mocks import RespuestaNormalizadorMock, ClienteNormalizarMockRespuestaPredeterminada, \
    ClienteNormalizarMockErrorComunicacion


class NormalizacionTest(unittest.TestCase):
    def test_debe_fallar_normalizacion_por_datos_incorrectos(self):
        # Dado
        error = RespuestaNormalizadorMock.error()
        cliente = ClienteNormalizarMockRespuestaPredeterminada.nuevo_con_respuesta('4545', error)
        normalizador = NormalizadorDeTelefonos(cliente)

        # Cuando
        response = normalizador.normalizar([('1', '4545')])

        # Entonces
        self.assertRaises(NormalizacionTelefonoError, response.get_telefono, codigo='1')

    def test_debe_responder_telefono_normalizado(self):
        # Dado
        cliente = ClienteNormalizarMockRespuestaPredeterminada()
        respuesta = cliente.respuesta_default
        normalizador = NormalizadorDeTelefonos(cliente)

        # Cuando
        response = normalizador.normalizar([('1', '4823783')])

        # Entonces
        self.assertEqual(response.cantidad_de_telefonos(), 1)
        self._assert_respuesta_contiene_informacion_de_telefono_identificado_como(response, respuesta, codigo='1')

    def test_debe_responder_telefono_normalizado_con_caracter_ampersand(self):
        respuesta = RespuestaNormalizadorMock(numero='4823783', telco='AT&T ARGENTINA S.A')
        cliente = ClienteNormalizarMockRespuestaPredeterminada.nuevo_con_respuesta('4823783', respuesta)
        normalizador = NormalizadorDeTelefonos(cliente)
        response = normalizador.normalizar([('1', '4823783')])

        self.assertEqual(response.cantidad_de_telefonos(), 1)
        self._assert_respuesta_contiene_informacion_de_telefono_identificado_como(response, respuesta, codigo='1')

    def test_debe_fallar_comununicacion_soap(self):
        normalizador = NormalizadorDeTelefonos(ClienteNormalizarMockErrorComunicacion())
        self.assertRaises(
            ServicioNormalizarComunicacionError, normalizador.normalizar, numeros_telefonicos=[('1', '2214823783')])

    def _assert_respuesta_contiene_informacion_de_telefono_identificado_como(
            self, response, respuesta_esperada, codigo):
        try:
            telefono = response.get_telefono(codigo)
        except ValueError:
            self.fail("Deberia encontrarse el telefono normalizado")
        else:
            self.assertIsNotNone(telefono)
            self.assertTrue(telefono[BIEN_CONSTITUIDO])
            self.assertEqual(telefono[ID], '1')
            self.assertEqual(telefono[NUMERO], '4823783')
            self.assertEqual(telefono[PREFIJO], respuesta_esperada.prefijo)
            self.assertEqual(telefono[TELCO], respuesta_esperada.telco)
            self.assertEqual(telefono[MOVIL], respuesta_esperada.movil)
            self.assertEqual(telefono[SPAM], respuesta_esperada.spam)
            self.assertEqual(telefono[LOCALIDAD], respuesta_esperada.localidad)
            self.assertEqual(telefono[PROVINCIA], respuesta_esperada.provincia)
