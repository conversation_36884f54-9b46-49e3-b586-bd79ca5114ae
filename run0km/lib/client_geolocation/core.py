import logging

import requests
from IPy import IP
from django.core.exceptions import ValidationError

from lib.api_client.api import ApiClient
from lib.api_client.errors import ClientError

logger = logging.getLogger('prospectos')


class GeolocationClient(ApiClient):
    _HEADERS = {'Content-type': 'application/json', 'Accept': 'application/json, text/javascript'}

    def name(self):
        return 'Geolocalizacion'

    def headers(self):
        return self._HEADERS

    def __init__(self, url, request_type=None):
        super(GeolocationClient, self).__init__(url, request_type)
        self.key_suburl = "?key=TxqGkeU4NhhozUK"
        self.ip_address = None

    @classmethod
    def new(cls):
        # plugin = cls(url='http://pro.ip-api.com/json/', request_type=cls.GET)
        plugin = cls(url='http://ip-api.com/json/', request_type=cls.GET)
        return plugin

    def _get(self, url, headers, data, **kwargs):
        try:
            return requests.get(url=url, params=data, headers=headers, timeout=22.5, **kwargs)
        except requests.Timeout as exc:
            logger.debug(
                f"Timeout producido por geoplugin para Prospecto c/IP {self.ip_address}. Excepcion: {str(exc)}")
            raise ClientError

    def call(self, ip):
        self._validate_ip_format(ip=ip)
        self.ip_address = ip
        response = self._get_response(ip=ip)
        json_response = response.json()
        self._validate_response(json_response)
        return json_response

    def _get_response(self, ip):
        # TODO: no es una buena practica llamar a un super de otro metodo
        old_url = "".join([char for char in self.url])
        self.url = self._add_ip_and_key_to_url(ip)
        response = super(GeolocationClient, self).call()
        self.url = old_url
        self.ip = None
        return response

    def _add_ip_and_key_to_url(self, ip):
        return self.url + ip + self.key_suburl

    def get_geolocation_data_from(self, ip):
        location = self.call(ip=ip)
        province = location['regionName']
        locality = location['city']
        latitude = location['lat']
        longitude = location['lon']
        return {'province': province, 'locality': locality, 'latitude': latitude, 'longitude': longitude}

    def _validate_ip_format(self, ip):
        if ip is None:
            raise ValidationError('Ip is None')
        try:
            IP(ip)
        except ValueError as exc:
            raise ValidationError(message=str(exc))

    def _validate_response(self, json_response):
        if json_response['status'] != 'success':
            raise ValidationError(message='Could not find localization for IP %s' % json_response['query'])
