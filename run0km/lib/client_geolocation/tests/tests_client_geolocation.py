import mock
from django.core.exceptions import ValidationError
from django.test import TestCase

from lib.client_geolocation.core import Geolocation<PERSON>lient
from lib.client_geolocation.tests.request_geolocation_mock import RequestGeolocationMock


class TestClientGeolocation(TestCase):
    def setUp(self):
        super(TestClientGeolocation, self).setUp()
        self.plugin = GeolocationClient.new()

    def test_cant_obtain_location_from_invalid_ip(self):
        self.assertRaises(ValidationError, self.plugin.call, 'totally_an_ip_address')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.http_404_not_found())
    def test_exception_raised_if_status_code_is_not_200(self, send_request_mock):
        self.assertRaises(ValidationError, self.plugin.call, ip='127.0.0.1')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_information_is_answered_when_the_request_is_successful(self, send_request_mock):
        response = self.plugin.call(ip='***********')
        self.assertEqual(response["as"], "AS22927 Telefonica de Argentina")
        self.assertEqual(response["city"], "Quilmes")
        self.assertEqual(response["country"], "Argentina")
        self.assertEqual(response["countryCode"], "AR")
        self.assertEqual(response["isp"], "Telefonica de Argentina")
        self.assertEqual(response["lat"], "-34.7203")
        self.assertEqual(response["lon"], "-58.2694")
        self.assertEqual(response["org"], "Telefonica de Argentina")
        self.assertEqual(response["query"], "***********")
        self.assertEqual(response["region"], "B")
        self.assertEqual(response["regionName"], "Buenos Aires")
        self.assertEqual(response["status"], 'success')
        self.assertEqual(response["timezone"], 'America/Argentina/Buenos_Aires')
        self.assertEqual(response["zip"], '1878')
