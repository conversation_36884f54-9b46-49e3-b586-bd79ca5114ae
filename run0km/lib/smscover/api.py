from copy import copy
from xml.etree import ElementTree
import itertools
import pytz
from datetime import datetime

from lib.smscover.errors import SMSPushServiceResponseError, SMSCoverCommunicationError, SMSCoverAPIError, SMSPushResponseError, \
    SMSPushParseResponseError
from lib.smscover.helpers import SMSTemplateTextEvaluation, SMSText, SMSListSerializer, SMSParser, SMSSendingSettings, \
    SMSSystem, SMSObject

from lib.smscover.sslcontext import HTTPSTransport, create_ssl_context
from suds.client import Client


class SMSPushService(object):
    _default_wsdl_url = 'https://www.smscover.com/stringway/push.asmx?WSDL'

    def __init__(self, wsdl=_default_wsdl_url):
        self.wsdl = wsdl

    def call(self, request):
        try:
            response = self._call_xml_request(request.serialize_as_xml())
        except Exception as e:
            import logging
            logger = logging.getLogger('sales_chat_service')
            logger.info('SMSPushService Error (%s): %s', type(e), str(e))
            raise SMSCoverCommunicationError(e)
        else:
            encoded_response = response.encode('utf-8')
            return encoded_response

    def _call_xml_request(self, request_as_xmls):
        ssl_context = create_ssl_context(verify=False, cafile=None, capath=None)  # ssl patch for suds
        client = Client(self.wsdl, transport=HTTPSTransport(ssl_context))
        response = client.service.smsPush(request_as_xmls)
        return response


class SMSSender(object):
    def __init__(self, client, default_settings=SMSSendingSettings(), sms_system=SMSSystem(),
                 service_caller=SMSPushService()):
        self.client = client
        self.default_settings = default_settings
        self.sms_system = sms_system
        self.service_caller = service_caller

    def _send(self, request):
        try:
            xml_response = self.service_caller.call(request)
            return xml_response
        except SMSCoverCommunicationError as exc:
            raise SMSCoverAPIError(exc, str_request=request.serialize_as_xml())

    def send_with(self, configuration, messages):
        self.sms_system.config_as_sending_request()
        request = self._generate_request(configuration, messages)
        xml_response = self._send(request)

        try:
            response = SMSPushResponse(xml_response)
            return response
        except SMSPushResponseError as exc:
            raise SMSCoverAPIError(exc, str_request=request.serialize_as_xml(), str_response=xml_response)

    def send_messages(self, messages):
        return self.send_with(self.default_settings, messages)

    def send_lot(self, lot_name, messages):
        settings = copy(self.default_settings)
        settings.lot_request = lot_name
        return self.send_with(settings, messages)

    def query_status_with(self, configuration):
        self.sms_system.config_as_sending_query()
        request = self._generate_request(configuration)
        xml_response = self._send(request)
        try:
            response = SMSQueryResponse(xml_response)
            return response
        except SMSPushResponseError as exc:
            raise SMSCoverAPIError(exc, str_request=request.serialize_as_xml(), str_response=xml_response)

    def query_status(self):
        return self.query_status_with(self.default_settings)

    def _generate_request(self, configuration, messages=None):
        message_list = SMSMessageList(messages)
        return SMSCoverRequest(self.client, configuration, self.sms_system, message_list)


class SMSMockSender(object):
    def __init__(self, client, default_settings=None, sms_system=None, service_caller=None):
        pass

    def send_lot(self, lot_name, messages):
        # raise SMSCoverAPIError('prueba')
        pass


class SMSMessage(SMSObject):
    def __init__(self, sending_id, sms_text, phone_number, country_code=''):
        self.sending_id = sending_id
        self.country_code = country_code
        self.phone_number = phone_number
        self.sms_text = sms_text

    def get_message(self):
        if isinstance(self.sms_text, SMSTemplateTextEvaluation):
            return self.sms_text.get_message()
        else:
            return self.sms_text.text

    def serialize_as_xml(self):
        id_xml = '<idTran>%s</idTran>' % self.sending_id
        phone_xml = '<numeroTelefono pais="%s">%s</numeroTelefono>' % (self.country_code, self.phone_number)
        text_xml = self.sms_text.serialize_as_xml()

        return '<destinatario>{0}{1}{2}</destinatario>'.format(id_xml, phone_xml, text_xml)

    @classmethod
    def new_from_template(cls, sending_id, template, template_kwargs, phone_number, country_code=''):
        template_evaluation = SMSTemplateTextEvaluation(template, template_kwargs)
        return cls(sending_id, template_evaluation, phone_number, country_code)

    @classmethod
    def new_from_text(cls, sending_id, string, phone_number, country_code=''):
        text = SMSText(string)
        return cls(sending_id, text, phone_number, country_code)


class SMSCoverClient(SMSObject):
    _client_xml = '<user>%s</user><key>%s</key>'

    def __init__(self, id, key):
        self.id = id
        self.key = key

    def serialize_as_xml(self):
        return self._client_xml % (self.id, self.key)


class SMSMessageList(SMSObject):
    def __init__(self, list_of_messages=None):
        if not list_of_messages:
            list_of_messages = []
        if isinstance(list_of_messages, SMSMessageList):
            list_of_messages = list_of_messages.messages
        self.messages = list_of_messages

    def add_message_from_text(self, sending_id, string, phone_number, country_code=''):
        message = SMSMessage.new_from_text(sending_id, string, phone_number, country_code)
        self.add_message(message)

    def add_message_from_template(self, sending_id, template, template_kwargs, phone_number, country_code=''):
        message = SMSMessage.new_from_template(sending_id, template, template_kwargs, phone_number, country_code)
        self.add_message(message)

    def add_message(self, message):
        self.messages.append(message)

    def serialize_as_xml(self):
        return SMSListSerializer.serialize('destinatarios', self.messages)

    def __len__(self):
        return len(self.messages)


class SMSPushResponse(object):
    def __init__(self, xml):
        self.xml = xml
        self._parse_response(xml)

    def _parse_response(self, xml):
        try:
            root = ElementTree.fromstring(xml)
        except Exception as exc:
            self._raise_error(str(exc))
        else:
            self._parse_answer(root)

    def _parse_answer(self, root):
        header = self._find_element_from_xpath(root, "./parametros/cabecera")
        self._parse_header(header)
        output = self._find_element_from_xpath(root, "./parametros/output")
        self._parse_output(output)

    def _parse_output(self, output):
        self.errors = SMSErrorResponse.read_errors_from(output)
        self.is_successfully = SMSParser.parse_number_as_boolean('successfully', output)
        if not self.is_successfully:
            raise SMSPushServiceResponseError(self.errors)

    def _parse_header(self, header):
        self.lot_response = header.findtext('lote-response', '')
        self.queue = header.findtext('cola', '')

    def _find_element_from_xpath(self, root, xpath):
        element = root.find(xpath)
        if element is None:
            self._raise_error(xpath)
        else:
            return element

    def _raise_error(self, message):
        SMSParser.raise_parse_error(message)


class SMSQueryResponse(SMSPushResponse):

    def __init__(self, xml):
        self._lot_sending_list = None
        self._answer_list = None
        super(SMSQueryResponse, self).__init__(xml)

    def _parse_output(self, output):
        super(SMSQueryResponse, self)._parse_output(output)
        self._parse_query_lot_list(output)
        self._parse_answer_list(output)

    def _parse_query_lot_list(self, output):
        """
            Parse all lots,  /output/lotes/.../lotes
        """
        lots_tags = self._find_element_from_xpath(output, "./lotes")
        lot_sending_list = []
        for lots_element in lots_tags.iterfind('xml-push-sms/lotes'):
            for lot_sending_element in lots_element.getchildren():
                lot_sending = SMSLotSending.read_from(lot_sending_element)
                lot_sending_list.append(lot_sending)
        self._lot_sending_list = lot_sending_list

    def _parse_answer_list(self, output):
        """
            Parse all answers,  /output/respuestas
        """
        answer_tags = self._find_element_from_xpath(output, "./respuestas")
        answer_list = []
        for answer_element in answer_tags.iterfind('respuesta'):
            answer = SMSMessageAnswer.read_from(answer_element)
            answer_list.append(answer)
        self._answer_list = answer_list

    def lot_sending_list(self):
        return self._lot_sending_list

    def get_all_sending(self):
        list_of_sending_list = [lot_sending.sending_list() for lot_sending in self._lot_sending_list]
        sending_list_flatten = list(itertools.chain.from_iterable(list_of_sending_list))
        return sending_list_flatten

    def get_all_answers(self):
        return self._answer_list


class SMSErrorResponse(object):
    def __init__(self, error_id, message):
        self.error_id = error_id
        self.message = message

    @classmethod
    def read_errors_from(cls, element_tree):
        errors = []
        error_tags = element_tree.find('errores')
        if error_tags is None:
            return errors
        for error_tag in error_tags.iter('error'):
            error = cls._read_error_from(error_tag)
            errors.append(error)
        return errors

    @classmethod
    def _read_error_from(cls, error_tag):
        error_id = error_tag.get('id')
        message = error_tag.text
        return cls(error_id, message)


class SMSLotSending(object):
    def __init__(self, lot_request, lot_response, sending_list=None):
        self.lot_request = lot_request
        self.lot_response = lot_response
        if not sending_list:
            sending_list = []
        self._sending_list = sending_list

    def sending_list(self):
        return self._sending_list

    @classmethod
    def read_from(cls, lot_query_element):
        lot_request = lot_query_element.get('cod-cli')
        lot_response = lot_query_element.get('lote-response')
        sending_list = cls._read_sending_list_from(lot_query_element)
        return cls(lot_request, lot_response, sending_list)

    @classmethod
    def _read_sending_list_from(cls, lot_query_element):
        sending_list = []
        for message_status_tag in lot_query_element.iter('destinatario'):
            sending = SMSMessageSending.read_from(message_status_tag)
            sending_list.append(sending)
        return sending_list


class SMSMessageSending(object):
    def __init__(self, sending_id, phone_number):
        self.sending_id = sending_id
        self.phone_number = phone_number

    def was_successful(self):
        raise NotImplementedError('Subclass Responsibility')

    @classmethod
    def successful(cls, sending_id, phone_number, sending_date):
        return SMSSuccessfulMessageSending(sending_id, phone_number, sending_date)

    @classmethod
    def failed(cls, sending_id, phone_number):
        return SMSFailedMessageSending(sending_id, phone_number)

    @classmethod
    def read_from(cls, sending_query_element):
        state = SMSParser.parse_number_as_boolean('estado', sending_query_element)
        sending_id = sending_query_element.findtext('idTran')
        phone_number = sending_query_element.findtext('numero')
        if state:
            sending_date = SMSParser.parse_text_as_date('fecha-envio', sending_query_element)
            return SMSSuccessfulMessageSending(sending_date, sending_id, phone_number)
        else:
            return SMSFailedMessageSending(sending_id, phone_number)


class SMSFailedMessageSending(SMSMessageSending):
    def was_successful(self):
        return False


class SMSSuccessfulMessageSending(SMSMessageSending):
    def __init__(self, sending_date, *args, **kwargs):
        self.sending_date = sending_date
        super(SMSSuccessfulMessageSending, self).__init__(*args, **kwargs)

    def was_successful(self):
        return True


class SMSMessageAnswer(object):
    def __init__(self, sending_id, answer_date, phone_number, message):
        self.sending_id = sending_id
        self.answer_date = answer_date
        self.phone_number = phone_number
        self.message = message

    @classmethod
    def read_from(cls, answer_element):
        sending_id = SMSParser.parse_required_attribute(answer_element, 'idTran')
        try:
            answer_date = SMSParser.parse_attribute_as_datetime(answer_element, 'hora',
                                                            '%d/%m/%Y %H:%M:%S')  # 01/01/2008 22:14:14 p.m. cambio a 22/10/2015 16:02:00
        except SMSPushParseResponseError:
            answer_date = datetime.now(pytz.utc)

        phone_number = answer_element.get('numero-telefono', '')
        message = answer_element.text
        return cls(sending_id, answer_date, phone_number, message)


class SMSCoverRequest(SMSObject):
    _format_request = '<?xml version="%(version)s" encoding="%(encoding)s"?><xml-push-sms>' \
                      '%(sms_system)s<parametros><cabecera>%(client)s%(configuration)s</cabecera>' \
                      '<detalle>%(messages)s</detalle></parametros></xml-push-sms>'

    def __init__(self, client, configuration, sms_system, messages=None, version='1.0', encoding='UTF-8'):
        self.client = client
        self.configuration = configuration
        self.sms_system = sms_system
        if messages is None:
            messages = SMSMessageList()
        self.messages = messages
        self.version = version
        self.encoding = encoding

    def is_sending_query(self):
        return self.sms_system.is_sending_query()

    def serialize_as_xml(self):
        return self._format_request % {'version': self.version, 'encoding': self.encoding,
                                       'sms_system': self.sms_system.serialize_as_xml(),
                                       'client': self.client.serialize_as_xml(),
                                       'configuration': self.configuration.serialize_as_xml(),
                                       'messages': self.messages.serialize_as_xml()}
