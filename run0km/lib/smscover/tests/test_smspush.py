# coding=utf-8
import unittest
from datetime import datetime, time

from lib.smscover.api import <PERSON><PERSON><PERSON>, SMSMessageList, SMSCoverClient, SMSMessage
from lib.smscover import SMSCoverCommunicationError, SMSPushServiceResponseError, SMSCoverAPIError
from lib.smscover import SMSSystem, SMSSendingSettings, TimeRangeList, SMSTemplateText
from lib.smscover.tests.mocks import SMSCommunicationFailMock, SMSServiceAnswerMock, SMSOutputQueryLotListMock, \
    SMSAnswerListMock
import pytz

class SMSPushTest(unittest.TestCase):

    def _get_default_sms_sender(self, caller):
        client = SMSCoverClient('helper', 'pass12')
        sms_system = SMSSystem()
        sms_settings = SMSSendingSettings('codigo-lote-cliente', datetime(2015, 1, 6), datetime(2015, 1, 8),
                                          time_range_list=TimeRangeList.new_with('1', time(8), time(22)))
        sms_sender = SMSSender(client, sms_settings, sms_system, caller)
        return sms_sender

    def assert_sending(self, query_sending, sending_id, phone_number, was_successfully, sending_date=None):
        self.assertEqual(query_sending.sending_id, sending_id)
        self.assertEqual(query_sending.phone_number, phone_number)
        self.assertEqual(query_sending.was_successful(), was_successfully)
        if query_sending.was_successful():
            self.assertEqual(query_sending.sending_date, datetime.strptime(sending_date, '%Y/%m/%d'))

    def assert_answer(self, answer, sending_id, answer_date, phone_number, message):
        self.assertEqual(answer.sending_id, sending_id)
        answer_date_cover_tz = pytz.timezone('America/Argentina/Buenos_Aires').localize(datetime.strptime(answer_date, '%d/%m/%Y %H:%M:%S'))

        self.assertEqual(answer.answer_date, answer_date_cover_tz)
        self.assertEqual(answer.phone_number, phone_number)
        self.assertEqual(answer.message, message)

    def test_should_fails_communication(self):
        mock_caller = SMSCommunicationFailMock()
        sms_sender = self._get_default_sms_sender(mock_caller)
        message_list = SMSMessageList()
        try:
            sms_sender.send_messages(message_list)
        except SMSCoverAPIError as err:
            sms_error = err.sms_error
            self.assertTrue(isinstance(sms_error, SMSCoverCommunicationError))
        else:
            self.fail('A SMSCoverAPIError is expected')

    def test_should_fails_authentication(self):
        """
            Por ahora no distingo el error de autenticacion, lanza un SMSPushServiceResponseError
        """
        mock_caller = SMSServiceAnswerMock.authentication_error()
        sms_sender = self._get_default_sms_sender(mock_caller)
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '123123123', '')

        try:
            sms_sender.send_messages(message_list)
        except SMSCoverAPIError as err:
            sms_error = err.sms_error
            self.assertTrue(isinstance(sms_error, SMSPushServiceResponseError))
            self.assertEqual(len(sms_error.response_errors()), 1)
            error = sms_error.response_errors()[0]
            self.assertEqual(error.error_id, 'ERROR_SEGURIDAD')
            self.assertEqual(error.message,
                             'No dispone de los privilegios para consumir el Web Service. Cod:>l63 key:12')
            return
        else:
            self.fail('The authentication should fail')

    def test_should_response_request_errors(self):
        mock_caller = SMSServiceAnswerMock.request_errors()
        sms_sender = self._get_default_sms_sender(mock_caller)
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1164462659', '+54')
        try:
            sms_sender.send_messages(message_list)

        except SMSCoverAPIError as err:
            sms_error = err.sms_error
            self.assertTrue(isinstance(sms_error, SMSPushServiceResponseError))

            self.assertEqual(len(sms_error.response_errors()), 2)
            error = sms_error.response_errors()[0]
            self.assertEqual(error.error_id, 'ERROR_FORMATO')
            self.assertEqual(error.message, 'El xml enviado no tiene el formato correcto')
            error = sms_error.response_errors()[1]
            self.assertEqual(error.error_id, 'ERROR_FORMATO')
            self.assertEqual(error.message,
                             'The \'numeroTelefono\' element is invalid - The value \'1525\' is invalid according to its datatype \'String\' - The Pattern constraint failed.')
            return
        else:
            self.fail('The sending should fail')

    def test_should_response_errors_but_exception_is_not_raised(self):
        mock_caller = SMSServiceAnswerMock.response_errors_but_successful()
        sms_sender = self._get_default_sms_sender(mock_caller)
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1164462659', '+54')
        response = sms_sender.send_messages(message_list)

        self.assertEqual(len(response.errors), 2)
        error = response.errors[0]
        self.assertEqual(error.error_id, 'ERROR_FORMATO')
        self.assertEqual(error.message, 'El xml enviado no tiene el formato correcto')
        error = response.errors[1]
        self.assertEqual(error.error_id, 'ERROR_FORMATO')
        self.assertEqual(error.message,
                         'The \'numeroTelefono\' element is invalid - The value \'1525\' is invalid according to its datatype \'String\' - The Pattern constraint failed.')

    def test_should_successfully_send_message(self):
        mock_caller = SMSServiceAnswerMock.successfully(lot_response='10', queue='6')
        sms_sender = self._get_default_sms_sender(mock_caller)
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1164462659', '+54')
        response = sms_sender.send_messages(message_list)
        self.assertTrue(response.is_successfully)
        self.assertEqual(response.lot_response, '10')
        self.assertEqual(response.queue, '6')

    def test_should_successfully_send_message_with_especial_characters(self):
        mock_caller = SMSServiceAnswerMock.successfully_with_special_characters(lot_response='10', queue='6')
        sms_sender = self._get_default_sms_sender(mock_caller)
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1164462659', '+54')
        response = sms_sender.send_messages(message_list)
        self.assertTrue(response.is_successfully)
        self.assertEqual(response.lot_response, '10')
        self.assertEqual(response.queue, '6')
        self.assertEqual(len(response.errors), 1)
        error = response.errors[0]
        self.assertEqual(error.error_id, 'ERROR')
        self.assertEqual(error.message, 'No ñáéíó')

    def test_should_successfully_send_lot(self):
        mock_caller = SMSServiceAnswerMock.successfully(lot_response='10', queue='6')
        sms_sender = self._get_default_sms_sender(mock_caller)
        template = SMSTemplateText('Hello {name}!!')
        message = SMSMessage.new_from_template('1', template, {'name': 'roman'}, '1164462659')
        response = sms_sender.send_lot('12', [message])
        self.assertTrue(response.is_successfully)
        self.assertEqual(response.lot_response, '10')
        self.assertEqual(response.queue, '6')

    def test_query_expects_empty_sending_status_and_answers(self):
        mock_caller = SMSServiceAnswerMock.successfully_query()
        sms_sender = self._get_default_sms_sender(mock_caller)

        query_response = sms_sender.query_status()
        self.assertEqual(len(query_response.get_all_sending()), 0)
        self.assertEqual(len(query_response.get_all_answers()), 0)

    def test_query_all_sending_expects_one_message(self):
        sending_date = '2015/8/22'
        mock_caller = SMSServiceAnswerMock.query_with_sending('lot1', '000001', '1522222222', 1, sending_date)
        sms_sender = self._get_default_sms_sender(mock_caller)
        query_response = sms_sender.query_status()
        sending_list = query_response.get_all_sending()
        self.assertEqual(len(sending_list), 1)

        self.assert_sending(sending_list[0], '000001', '1522222222', True, sending_date)

    def test_query_all_sending_expects_three_messages(self):
        query_output = SMSOutputQueryLotListMock()
        sending_date = '2015/8/22'
        query_output.add_query_push_with_sending('lot1', '000001', '1522222222', 1, sending_date)
        query_output.add_query_push_with_sending('lot1', '000002', '1533333333', 0)
        query_output.add_query_push_with_sending('lot1', '000003', '1544444444', 1, sending_date)

        mock_caller = SMSServiceAnswerMock.successfully_query(lot_status=query_output)
        sms_sender = self._get_default_sms_sender(mock_caller)
        query_response = sms_sender.query_status()
        sending_list = query_response.get_all_sending()
        self.assertEqual(len(sending_list), 3)

        sending_list = sorted(sending_list, key=lambda query: query.sending_id)
        self.assert_sending(sending_list[0], '000001', '1522222222', True, sending_date)
        self.assert_sending(sending_list[1], '000002', '1533333333', False)
        self.assert_sending(sending_list[2], '000003', '1544444444', True, sending_date)

    def test_query_response(self):
        #->COVER
        sending_date = '2015/8/22'
        query_output = SMSOutputQueryLotListMock.with_sending('lot1', '000001', '1522222222', 1, sending_date)
        #<-COVER
        answer_date = '22/10/2015 16:02:00' # Parece que cambio: '25/08/2015 12:15:05 p.m.'
        message = 'Ack!!'
        answer_output = SMSAnswerListMock.with_answer('000001', answer_date, '1522222222', message)
        mock_caller = SMSServiceAnswerMock.successfully_query(lot_status=query_output, answer_list=answer_output)
        sms_sender = self._get_default_sms_sender(mock_caller)

        query_response = sms_sender.query_status()

        sending_list = query_response.get_all_sending()
        self.assertEqual(len(sending_list), 1)
        self.assert_sending(sending_list[0], '000001', '1522222222', True, sending_date)

        answer_list = query_response.get_all_answers()
        self.assertEqual(len(answer_list), 1)
        self.assert_answer(answer_list[0], '000001', answer_date, '1522222222', message)
