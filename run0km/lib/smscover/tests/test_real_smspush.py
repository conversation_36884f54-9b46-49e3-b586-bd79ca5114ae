from unittest import TestCase, skipIf
from datetime import datetime, time

from django.conf import settings
from suds.client import Client

from lib.smscover.api import SMSS<PERSON>, SMSMessageList, SMSCoverClient, SMSPushService, SMSSystem
from lib.smscover import SMSCoverCommunicationError, SMSPushServiceResponseError, SMSCoverAPIError
from lib.smscover import TimeRangeList, SMSSendingSettings


@skipIf(not hasattr(settings, 'EJECUTAR_SMSPUSH_TEST') or not settings.EJECUTAR_SMSPUSH_TEST,
        'Se han anulado los tests que envian pedidos reales a SMSPush - Configurar EJECUTAR_SMSPUSH_TEST')
class SMSPushRealServiceTest(TestCase):
    # no prueba nada, es solo un ejemplo de uso
    def example_suds_pelado(self):
        wsdlUrl = 'https://www.smscover.com/stringway/push.asmx?WSDL'

        client = Client(wsdlUrl)
        query = '<?xml version="1.0" encoding="UTF-8"?><xml-push-sms>' \
                '<metadata>' \
                '<server>stringway.smscover.com</server><version>Gateway_V3</version><accion>0</accion>' \
                '</metadata>' \
                '<parametros><cabecera><user>run0km</user><key>87629369823742</key>' \
                '<lote-request>lote-20150811</lote-request><formato>NoClass_7Bit</formato>' \
                '<bocas></bocas><prioridad>6</prioridad><fecha-comienzo></fecha-comienzo>' \
                '<fecha-fin></fecha-fin><continuacion>0</continuacion><rangos-horarios>' \
                '<rango-horario id=""><hora-desde am-pm="am">8:00</hora-desde>' \
                '<hora-hasta am-pm="pm">10:00</hora-hasta></rango-horario></rangos-horarios>' \
                '</cabecera><detalle><destinatarios>' \
                '<destinatario><idTran>20150811-000001</idTran>' \
                '<numeroTelefono pais="+54">1522368515</numeroTelefono><mensaje>Hola!!</mensaje></destinatario>' \
                '<destinatario><idTran>20150811-000002</idTran>' \
                '<numeroTelefono pais="+54">1522368515</numeroTelefono><mensaje>Hola Hola!!</mensaje></destinatario>' \
                '</destinatarios></detalle></parametros></xml-push-sms>'

        # try:
        response = client.service.smsPush(query)
        # except:
        #     print('error')
        #     return None
        print(response)

    def example_suds_query_sending(self):
        wsdlUrl = 'https://www.smscover.com/stringway/push.asmx?WSDL'

        client = Client(wsdlUrl)
        query = '<?xml version="1.0" encoding="UTF-8"?><xml-push-sms>' \
                '<metadata>' \
                '<server>stringway.smscover.com</server><version>Gateway_V3</version><accion>1</accion>' \
                '</metadata>' \
                '<parametros><cabecera><user>run0km</user><key>87629369823742</key>' \
                '<lote-request></lote-request><formato>NoClass_7Bit</formato>' \
                '<bocas></bocas><prioridad></prioridad><fecha-comienzo></fecha-comienzo>' \
                '<fecha-fin></fecha-fin><continuacion>0</continuacion><rangos-horarios>' \
                '</rangos-horarios>' \
                '</cabecera><detalle><destinatarios></destinatarios></detalle></parametros></xml-push-sms>'

        # try:
        response = client.service.smsPush(query)
        # except Exception as e:
        #     print 'error'
        #     return None
        print(response)

    def _default_client(self):
        return SMSCoverClient('run0km', '87629369823742')

    def _get_default_sms_sender(self, caller=SMSPushService(), client=None):
        if client is None:
            client = self._default_client()
        sms_system = SMSSystem()
        sms_settings = SMSSendingSettings('codigo-lote-cliente', datetime(2015, 1, 6), datetime(2015, 1, 8),
                                          time_range_list=TimeRangeList.new_with('1', time(8), time(22)))
        sms_sender = SMSSender(client, sms_settings, sms_system, caller)
        return sms_sender

    def test_should_fails_communication(self):
        mock_caller = SMSPushService('https://www.example.com/push.asmx?WSDL')
        sms_sender = self._get_default_sms_sender(mock_caller)
        message_list = SMSMessageList()
        try:
            sms_sender.send_messages(message_list)
        except SMSCoverAPIError as err:
            sms_error = err.sms_error
            self.assertTrue(isinstance(sms_error, SMSCoverCommunicationError))
        else:
            self.fail('A SMSCoverAPIError is expected')

    def test_should_fails_authentication(self):
        client = SMSCoverClient('helper', 'pass12')
        sms_sender = self._get_default_sms_sender(client=client)
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1522368515', '')

        try:
            sms_sender.send_messages(message_list)
        except SMSCoverAPIError as err:
            sms_error = err.sms_error
            self.assertTrue(isinstance(sms_error, SMSPushServiceResponseError))
            self.assertEqual(len(sms_error.response_errors()), 1)
            error = sms_error.response_errors()[0]
            self.assertEqual(error.error_id, 'ERROR_SEGURIDAD')
            self.assertEqual(error.message,
                             'No dispone de los privilegios para consumir el Web Service. Cod:>l63 key:pass12')

        else:
            self.fail('A SMSCoverAPIError is expected')

    def test_should_response_request_errors(self):
        sms_sender = self._get_default_sms_sender()
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1234', '')

        sms_sender.send_messages(message_list)
        response = sms_sender.send_messages(message_list)
        self.assertTrue(len(response.errors)>0)


    def test_should_successfully_send_message(self):
        sms_sender = self._get_default_sms_sender()
        message_list = SMSMessageList()
        message_list.add_message_from_text('1', 'Hello World!!', '1522368515', '+54')
        response = sms_sender.send_messages(message_list)
        self.assertTrue(response.is_successfully)
        self.assertIsNotNone(response.lot_response)
        self.assertIsNotNone(response.queue)

    def test_query_status(self):
        """
            No test. It is an example of use
        """
        sms_sender = SMSSender(self._default_client())
        query_response = sms_sender.query_status()
        sending_list = query_response.get_all_sending()
        self.assertIsNotNone(sending_list)
        answer_list = query_response.get_all_answers()
        self.assertIsNotNone(answer_list)
