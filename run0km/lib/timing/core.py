import time


def timing(func):
    def wrapper(*args, **kwargs):
        answer, milliseconds = measure_time_to_evaluate(func, *args, **kwargs)
        print('%s function took %0.2f ms' % (func.__name__, milliseconds))
        return answer
    return wrapper


def measure_time_to_evaluate(func, *args, **kwargs):
    start = time.time()
    answer = func(*args, **kwargs)
    end = time.time()
    milliseconds = (end - start) * 1000.0
    return answer, milliseconds