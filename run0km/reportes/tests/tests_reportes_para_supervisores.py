# -*- coding: utf-8 -*-


from datetime import date

from django.urls import reverse
from django.utils.timezone import datetime, timedelta, make_aware, get_current_timezone

from reportes.forms import ReporteParaSupervisoresForm
from reportes.generacion_de_reportes import CalculosPreviosParaReportesDePedidos, ConfiguracionDeReporteParaSupervisor
from reportes.models import TipoDeStaff, TipoDeReporte
from testing.base import BaseFixturedTest, BaseLoggedTest, BaseLoggedSupervisorTest
from testing.factories import PedidosDeProspectoFactory, ProspectosFactory


class BaseReporteParaSupervisoresTest(BaseFixturedTest):
    def setUp(self):
        super(BaseReporteParaSupervisoresTest, self).setUp()
        f = self.fixture

        self.desde = date(day=22, month=12, year=2009)  # 10 dias de Diciembre
        self.hasta = date(day=10, month=1, year=2010)  # 10 dias de Enero
        self.configuracion = ConfiguracionDeReporteParaSupervisor(supervisor=f['sup_1'],
                                                                  desde=self.desde,
                                                                  hasta=self.hasta,
                                                                  tipo_de_staff=TipoDeStaff.SUPERVISORES,
                                                                  staff=[f['vend_1'], ],
                                                                  tipos_de_reportes=TipoDeReporte.objects.all())

        diciembre = date(day=1, month=12, year=2009)
        enero = date(day=22, month=1, year=2010)
        febrero = date(day=1, month=2, year=2010)

        # Pedidos que entran
        p1 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=2790, fecha=diciembre, asignar_a='T')
        p1.origenes = [f['tipo_s'], ]
        p1.categorias = [f['cat_s']]
        p2 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=2790, fecha=diciembre, asignar_a='T')
        p2.origenes = [f['tipo_s'], ]
        p2.categorias = [f['cat_s'], ]
        p3 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=2790, fecha=enero, asignar_a='T')
        p3.origenes = [f['tipo_s'], ]
        p3.categorias = [f['cat_s'], ]

        # Pedidos que no entran
        p4 = PedidosDeProspectoFactory(supervisor=f['sup_2'], credito=10, fecha=diciembre, asignar_a='T')
        p4.origenes = [f['tipo_s'], ]
        p5 = PedidosDeProspectoFactory(supervisor=f['sup_3'], credito=10, fecha=diciembre, asignar_a='T')
        p5.origenes = [f['tipo_s'], ]
        p6 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=10, fecha=febrero, asignar_a='E',
                                       equipo=f['equipo_1'])
        p6.origenes = [f['tipo_m'], ]
        p6.categorias = [f['cat_m'], ]
        p7 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=10, fecha=febrero, asignar_a='E',
                                       equipo=f['equipo_1'])
        p7.origenes = [f['tipo_w'], ]
        p7.categorias = [f['cat_w'], ]
        p8 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=10, fecha=febrero, asignar_a='T')
        p8.origenes = [f['tipo_s'], ]
        p8.categorias = [f['cat_s'], ]

        self.pedidos = [p1, p2, p3, p4, p5, p6, p7, p8, ]

        desde = make_aware(datetime(day=22, month=12, year=2009), get_current_timezone())
        hasta = make_aware(datetime(day=10, month=1, year=2010), get_current_timezone())
        antes1 = desde - timedelta(days=1)
        antes2 = desde - timedelta(minutes=3)
        self.mientras_1 = mientras1 = desde + timedelta(minutes=3)
        self.mientras_2 = mientras2 = desde + timedelta(days=3)
        despues = hasta + timedelta(days=1)

        # Prospectos q entran
        e1 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p1,
                               fecha_de_asignacion_a_vendedor=mientras1, fecha=mientras1, fecha_creacion=mientras1)
        e2 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p3,
                               fecha_de_asignacion_a_vendedor=mientras2, fecha=mientras1, fecha_creacion=mientras1)
        e3 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p2,
                               fecha_de_asignacion_a_vendedor=mientras1, fecha=mientras1, fecha_creacion=mientras1)
        e4 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p1,
                               fecha_de_asignacion_a_vendedor=mientras1, fecha=mientras1, fecha_creacion=mientras1)
        e5 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_2'], responsable=f['sup_1'], pedido=p1,
                               fecha_de_asignacion_a_vendedor=mientras1, fecha=mientras1)
        e6 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_2'], responsable=f['sup_1'], pedido=p2,
                               fecha_de_asignacion_a_vendedor=mientras2, fecha=mientras2)
        e7 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_3'], responsable=f['sup_1'], pedido=p6,
                               fecha_de_asignacion_a_vendedor=mientras1, fecha=mientras1)

        self.prospectos = [e1, e2, e3, e4, e5, e6, e7, ]

        # Prospectos q no entran
        n1 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p1,
                               fecha_de_asignacion_a_vendedor=antes1)
        n1.fecha = antes1
        n1.save()
        n2 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p1,
                               fecha_de_asignacion_a_vendedor=despues)
        n2.fecha = despues
        n2.save()
        n2 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p1,
                               fecha_de_asignacion_a_vendedor=antes2)
        n2.fecha = antes2
        n2.save()
        n3 = ProspectosFactory(campania=f['camp_2'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p4,
                               fecha_de_asignacion_a_vendedor=despues)
        n3.fecha = despues
        n3.save()
        n4 = ProspectosFactory(campania=f['camp_3'], vendedor=f['vend_1'], responsable=f['sup_1'], pedido=p5,
                               fecha_de_asignacion_a_vendedor=mientras1)
        n4.fecha = antes1
        n4.save()
        n5 = ProspectosFactory(campania=f['camp_1'], vendedor=f['vend_6'], responsable=f['sup_3'], pedido=p6,
                               fecha_de_asignacion_a_vendedor=mientras1)
        n5.fecha = antes2
        n5.save()

        f['cat_s'].valor = 10
        f['cat_s'].save()
        f['cat_m'].valor = 10
        f['cat_m'].save()
        f['cat_w'].valor = 10
        f['cat_w'].save()


class CalculosPreviosParaReportesDePedidosTest(BaseReporteParaSupervisoresTest):

    def test_prospectos_afectados(self):
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        supervisor = self.configuracion.supervisor
        prospectos_entregados = calculos_previos.prospectos_entregados[supervisor.id]
        self.assertEqual(prospectos_entregados.count(), 7)
        for prospecto in prospectos_entregados:
            self.assertEqual(prospecto.responsable, supervisor)

    def test_definir_pedidos_afectados(self):
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)

        supervisor = self.configuracion.supervisor
        pedidos_afectados = calculos_previos.pedidos_afectados[supervisor.id]
        self.assertEqual(pedidos_afectados.count(), 3)
        for pedido in pedidos_afectados:
            self.assertEqual(pedido.supervisor, self.configuracion.supervisor)
            self.assertTrue(pedido.fecha.year == self.desde.year and pedido.fecha.month == self.desde.month or
                            pedido.fecha.year == self.hasta.year and pedido.fecha.month == self.hasta.month)
        self.assertTrue(self.pedidos[0] in pedidos_afectados)
        self.assertTrue(self.pedidos[1] in pedidos_afectados)
        self.assertTrue(self.pedidos[2] in pedidos_afectados)

        p1 = self.pedidos[0]
        p1.fecha = date(day=1, month=2, year=2010)  # febrero
        p1.save()
        p2 = self.pedidos[1]
        p2.supervisor = self.fixture['sup_2']
        p2.save()
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        pedidos_afectados = calculos_previos.pedidos_afectados[supervisor.id]
        self.assertEqual(pedidos_afectados.count(), 1)
        self.assertTrue(self.pedidos[2] in pedidos_afectados)


class PermisosDenegadoViewReportesTest(BaseLoggedTest):
    def test_permiso_denegado_para_vendedores(self):
        reportes_url = reverse('reportes-online')
        response = self.client.get(reportes_url)
        self.assertRedirects(response, reverse("resumen"))
        response = self.client.post(reportes_url, {})
        self.assertRedirects(response, reverse("resumen"))


class ReporteParaGerentesFormTest(BaseFixturedTest):
    def test_validez_de_datos(self):
        data = {}
        supervisor = self.fixture['sup_1']
        form = ReporteParaSupervisoresForm(supervisor=supervisor, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('desde', form.errors)
        self.assertIn('hasta', form.errors)
        self.assertIn('tipo_de_staff', form.errors)
        self.assertIn('tipos_de_reportes', form.errors)

        data['desde'] = date(2010, 1, 2)
        data['hasta'] = date(2010, 1, 1)
        data['tipo_de_staff'] = TipoDeStaff.VENDEDORES
        id_tipos = [str(x.id) for x in TipoDeReporte.objects.all()]
        data['tipos_de_reportes'] = id_tipos

        form = ReporteParaSupervisoresForm(supervisor=supervisor, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('staff_a_cargo', form.errors)
        self.assertEqual(form.errors['staff_a_cargo'], ['Debe seleccionar al menos un vendedor'])
        data['staff_a_cargo'] = [supervisor.vendedores.all()[0].id, ]


        form = ReporteParaSupervisoresForm(supervisor=supervisor, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('hasta', form.errors)
        self.assertEqual(form.errors['hasta'], ['La fecha de finalización debe ser menor a la de inicio.'])

        data['hasta'] = date(2010, 2, 2)
        form = ReporteParaSupervisoresForm(supervisor=supervisor, data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('hasta', form.errors)
        self.assertEqual(form.errors['hasta'], ['El mes de finalización debe ser el mismo que el de inicio.'])

        data['hasta'] = date(2010, 1, 22)
        form = ReporteParaSupervisoresForm(supervisor=supervisor, data=data)
        self.assertTrue(form.is_valid())


class ReportesParaGerentesViewTest(BaseLoggedSupervisorTest):
    def test_opciones_de_formulario(self):
        reportes_url = reverse('reportes-online')
        response = self.client.get(reportes_url)
        self.assertEqual(response.status_code, 200)
