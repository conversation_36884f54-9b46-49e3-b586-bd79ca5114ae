# -*- coding: utf-8 -*-


from datetime import date

from prospectos.models import PedidoDeProspecto
from prospectos.models.entrega_de_datos.opciones import MetodosDeAsignacionChoices
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from reportes.generacion_de_reportes import (ConfiguracionDeReporteParaGerente, CalculosPreviosParaReportesDePedidos,
                                             ReporteDeEntrega)
from reportes.models import TipoDeReporte, TipoDeStaff
from reportes.tests.tests_reportes_para_gerentes import BaseReporteParaGerentesTest
from testing.factories import PedidosDeProspectoFactory, EquiposFactory


class ReporteDeEntregaTest(BaseReporteParaGerentesTest):
    def test_cantidad_de_dias_en_pedido(self):
        f = self.fixture
        self.configuracion.tipos_de_reportes = TipoDeReporte.objects.get(nombre='Entrega')
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)
        p1 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=124, fecha=self.desde)
        p1.cambiar_calidades_por(calidades=[self.fixture['tipo_s'], ])
        p1.categorias = [f['cat_s'], ]

        self.assertEqual(reporte._cantidad_de_dias_en_pedido(p1), 10)
        p2 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=186, fecha=self.hasta)
        p2.cambiar_calidades_por(calidades=[self.fixture['tipo_s'], ])
        p2.categorias = [f['cat_s'], ]
        self.assertEqual(reporte._cantidad_de_dias_en_pedido(p2), 10)

        diciembre = self.configuracion.desde
        enero = self.configuracion.hasta
        self.configuracion.desde = date(day=1, month=11, year=2009)
        self.configuracion.hasta = date(day=1, month=2, year=2010)
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)
        p1 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=diciembre)
        p1.cambiar_calidades_por(calidades=[self.fixture['tipo_s'], ])
        p1.categorias = [f['cat_s'], ]
        p2 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=enero)
        p2.cambiar_calidades_por(calidades=[self.fixture['tipo_s'], ])
        p2.categorias = [f['cat_s'], ]
        self.assertEqual(reporte._cantidad_de_dias_en_pedido(p1), 31)
        self.assertEqual(reporte._cantidad_de_dias_en_pedido(p2), 31)

    def test_fraccion_por_dias_por_pedido(self):
        f = self.fixture
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)
        for id_pedido in reporte.fraccion_por_dias_por_pedido:
            self.assertEqual(10 / 31, reporte.fraccion_por_dias_por_pedido[id_pedido])

        self.configuracion.desde = date(day=16, month=11, year=2009)  # 15 dias en noviembre
        self.configuracion.hasta = date(day=10, month=2, year=2010)  # 10 dias en febrero
        p1 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=date(2009, 11, 1), asignar_a='T')
        p1.origenes = [f['tipo_s'], ]
        p1.categorias = [f['cat_s'], ]
        p2 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=date(2009, 12, 1), asignar_a='T')
        p2.origenes = [f['tipo_s'], ]
        p2.categorias = [f['cat_s'], ]
        p3 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=date(2010, 1, 1), asignar_a='T')
        p3.origenes = [f['tipo_s'], ]
        p3.categorias = [f['cat_s'], ]
        p4 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=date(2010, 2, 1), asignar_a='T')
        p4.origenes = [f['tipo_s'], ]
        p4.categorias = [f['cat_s'], ]
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)

        self.assertEqual(15 / 30, reporte.fraccion_por_dias_por_pedido[p1.id])
        self.assertEqual(31 / 31, reporte.fraccion_por_dias_por_pedido[p2.id])
        self.assertEqual(31 / 31, reporte.fraccion_por_dias_por_pedido[p3.id])
        self.assertEqual(10 / 28, reporte.fraccion_por_dias_por_pedido[p4.id])

    def test_fraccion_por_vendedor_por_pedido(self):
        # Pedidos a repartir entre todos
        self.configuracion.tipos_de_reportes = [TipoDeReporte.objects.get(nombre='Entrega')]
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)
        fraccion_por_vendedor_por_pedido = reporte._calcular_fraccion_por_vendedor_por_pedido()
        self.assertEqual(len(list(fraccion_por_vendedor_por_pedido.keys())), 3)
        for fracciones_por_pedido in list(fraccion_por_vendedor_por_pedido.values()):
            for fraccion_por_vendedor in list(fracciones_por_pedido.values()):
                self.assertEqual(fraccion_por_vendedor, 1 / 3)

        f = self.fixture

        p1 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=date(2009, 12, 1),
                                       asignar_a='V', vendedor=f['vend_1'])
        p1.origenes = [f['tipo_s'], ]
        p1.categorias = [f['cat_s'], ]
        PedidoDeProspecto.objects.filter(asignar_a='E').delete()
        p2 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=date(2009, 12, 1),
                                       asignar_a='E', equipo=f['equipo_1'])
        p2.origenes = [f['tipo_s'], ]
        p2.categorias = [f['cat_s'], ]
        p3 = PedidosDeProspectoFactory(supervisor=f['sup_1'], credito=122, fecha=date(2009, 12, 1),
                                       asignar_a='E', equipo=f['equipo_1'],
                                       metodo_de_asignacion=MetodosDeAsignacionChoices.FACTOR_MANUAL)
        p3.origenes = [f['tipo_s'], ]
        p3.categorias = [f['cat_s'], ]

        f['equipo_1'].integrantes.add(f['vend_5'])
        f['equipo_1'].integrantes.add(f['vend_1'])
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=f['vend_5'], factor_de_asignacion=20)

        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)
        fraccion_por_vendedor_por_pedido = reporte._calcular_fraccion_por_vendedor_por_pedido()

        # Si el pedido es para un vendedor en particular, ese tiene el 100/100
        for vendedor in f['sup_1'].vendedores.all():
            fraccion = fraccion_por_vendedor_por_pedido[p1.id][vendedor.id]
            self.assertEqual(fraccion, 1 if vendedor == f['vend_1'] else 0)

        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)
        fraccion_por_vendedor_por_pedido = reporte._calcular_fraccion_por_vendedor_por_pedido()
        # Si el pedido se reparte uniformemente para un equipo
        self.assertEqual(fraccion_por_vendedor_por_pedido[p2.id][f['vend_1'].id], 1 / 2)
        self.assertEqual(fraccion_por_vendedor_por_pedido[p2.id][f['vend_2'].id], 0)
        self.assertEqual(fraccion_por_vendedor_por_pedido[p2.id][f['vend_5'].id], 1 / 2)
        # Si el pedido se reparte por factor de asignacion para un equipo
        self.assertEqual(fraccion_por_vendedor_por_pedido[p3.id][f['vend_1'].id], 1 / 3)
        self.assertEqual(fraccion_por_vendedor_por_pedido[p3.id][f['vend_2'].id], 0)
        self.assertEqual(fraccion_por_vendedor_por_pedido[p3.id][f['vend_5'].id], 2 / 3)

    def test_pedido_por_vendedor(self):
        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)

        for vendedor in self.configuracion.staff[0].vendedores.all():
            calidad = self.fixture['cat_s'].tipo_de_origen
            self.assertEqual(reporte.pedido_por_vendedor_por_calidad[calidad.id][vendedor.id], 279 * 10 / 31)

    def test_cantidad_pedida_ponderada(self):
        p1 = self.pedidos[0]
        cat_s = self.fixture['cat_s']
        cat_s.valor = 20
        cat_s.save()
        cat_m = self.fixture['cat_m']
        cat_m.valor = 10
        cat_m.save()
        p1.categorias = [cat_m, cat_s]

        calculos_previos = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, calculos_previos)
        promedio = (20 + 10) / 2
        self.assertEqual(2790 / promedio, reporte.cantidad_pedida_por_pedido[p1.id])

    def test_valor_pedido_por_vendedor(self):
        datos_precalculados = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, datos_precalculados)
        datos_de_reporte = reporte.datos_de_reporte()
        self.assertEqual(len(datos_de_reporte), 1)
        datos_sms = datos_de_reporte[0]
        self.assertEqual(datos_sms['calidad'], self.fixture['tipo_s'])
        self.assertEqual(len(datos_sms['datos']), 1)
        datos_de_supervisor = datos_sms['datos'][0]
        self.assertEqual(datos_de_supervisor['supervisor'], self.fixture['sup_1'])
        for dato in datos_de_supervisor['datos']:
            self.assertEqual(dato['pedido'], 279 * 10 / 31)
            if dato['vendedor'] == self.fixture['vend_1']:
                self.assertEqual(dato['entregado'], 4)
                self.assertEqual(dato['faltante'], 279 * 10 / 31 - 4)
            if dato['vendedor'] == self.fixture['vend_2']:
                self.assertEqual(dato['entregado'], 2)
                self.assertEqual(dato['faltante'], 279 * 10 / 31 - 2)
            if dato['vendedor'] == self.fixture['vend_5']:
                self.assertEqual(dato['entregado'], 0)
                self.assertEqual(dato['faltante'], 279 * 10 / 31)

    def test_valor_pedido_por_vendedor_por_calidad(self):
        f = self.fixture
        ped_1 = self.pedidos[0]
        ped_1.cambiar_calidades_por(calidades=[f['tipo_s'], f['tipo_w']])
        pro_1 = self.prospectos[0]
        pro_1.campania = f['camp_3']
        pro_1.save()

        datos_precalculados = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, datos_precalculados)

        datos_de_reporte = reporte.datos_de_reporte()
        self.assertEqual(len(datos_de_reporte), 2)
        datos_sms = datos_de_reporte[0] if datos_de_reporte[0]['calidad'] == f['tipo_s'] else datos_de_reporte[1]
        self.assertEqual(len(datos_sms['datos']), 1)
        datos_web = datos_de_reporte[0] if datos_de_reporte[0]['calidad'] == f['tipo_w'] else datos_de_reporte[1]
        self.assertEqual(len(datos_web['datos']), 1)

        datos_sms_de_supervisor = datos_sms['datos'][0]
        self.assertEqual(datos_sms_de_supervisor['supervisor'], f['sup_1'])
        fraccion_sms = 5 / 6
        for dato in datos_sms_de_supervisor['datos']:
            pedido = 279 * 10 / 31 * fraccion_sms
            self.assertEqual(dato['pedido'], pedido)
            if dato['vendedor'] == f['vend_1']:
                self.assertEqual(dato['entregado'], 3)
                self.assertEqual(dato['faltante'], pedido - 3)
            if dato['vendedor'] == f['vend_2']:
                self.assertEqual(dato['entregado'], 2)
                self.assertEqual(dato['faltante'], pedido - 2)
            if dato['vendedor'] == f['vend_5']:
                self.assertEqual(dato['entregado'], 0)
                self.assertEqual(dato['faltante'], pedido)

        datos_web_de_supervisor = datos_web['datos'][0]
        self.assertEqual(datos_web_de_supervisor['supervisor'], f['sup_1'])
        fraccion_web = 1 / 6
        for dato in datos_web_de_supervisor['datos']:
            pedido = 279 * 10 / 31 * fraccion_web
            self.assertEqual(dato['pedido'], pedido)
            if dato['vendedor'] == f['vend_1']:
                self.assertEqual(dato['entregado'], 1)
                self.assertEqual(dato['faltante'], pedido - 1)
            if dato['vendedor'] == f['vend_2']:
                self.assertEqual(dato['entregado'], 0)
                self.assertEqual(dato['faltante'], pedido - 0)
            if dato['vendedor'] == f['vend_5']:
                self.assertEqual(dato['entregado'], 0)
                self.assertEqual(dato['faltante'], pedido)

    def test_reporte_de_entrega_con_un_vendedor_con_factor_de_asignacion_cero(self):
        pedidos = PedidoDeProspecto.objects.all()
        f = self.fixture
        eq1 = EquiposFactory(supervisor=f['sup_1'], nombre='Eq 1')
        eq2 = EquiposFactory(supervisor=f['sup_1'], nombre='Eq 2')
        vendedor_1 = f['vend_1']
        vendedor_2 = f['vend_2']
        vendedor_3 = f['vend_5']
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=vendedor_1, factor_de_asignacion=0)

        eq1.integrantes = [vendedor_1, vendedor_2]
        eq2.integrantes = [vendedor_3, ]
        conf = ConfiguracionDeReporteParaGerente(desde=self.desde,
                                                 hasta=self.hasta,
                                                 tipo_de_staff=TipoDeStaff.EQUIPOS,
                                                 staff=[eq1, eq2],
                                                 tipos_de_reportes=['T', ])
        datos_precalculados = CalculosPreviosParaReportesDePedidos(conf)
        reporte = ReporteDeEntrega(conf, datos_precalculados)
        datos_de_reporte = reporte.datos_de_reporte()

        datos_sms = datos_de_reporte[0]['datos']
        datos_supervisor = datos_sms[0]['datos']

        if datos_supervisor[0]['equipo'] == eq1:
            datos_eq1 = datos_supervisor[0]['datos']
            datos_eq2 = datos_supervisor[1]['datos']
        else:
            datos_eq1 = datos_supervisor[1]['datos']
            datos_eq2 = datos_supervisor[0]['datos']

        pedido = 279 * 10 / 31
        self.assertEqual(len(datos_eq1), 2)
        self.assertEqual(datos_eq1[0]['vendedor'], f['vend_1'])
        self.assertEqual(datos_eq1[0]['pedido'], '?')
        self.assertEqual(datos_eq1[0]['entregado'], 4)
        self.assertEqual(datos_eq1[0]['faltante'], '?')

    def test_reporte_de_entrega_no_muestra_vendedores_deshabilitados_sin_entregados(self):
        pedidos = PedidoDeProspecto.objects.all()
        f = self.fixture
        eq1 = EquiposFactory(supervisor=f['sup_1'], nombre='Eq 1')
        eq2 = EquiposFactory(supervisor=f['sup_1'], nombre='Eq 2')
        vendedor_3 = f['vend_5']

        vendedor_3.deshabilitar()

        eq2.integrantes = [vendedor_3, ]
        conf = ConfiguracionDeReporteParaGerente(desde=self.desde,
                                                 hasta=self.hasta,
                                                 tipo_de_staff=TipoDeStaff.EQUIPOS,
                                                 staff=[eq1, eq2],
                                                 tipos_de_reportes=['T', ])
        datos_precalculados = CalculosPreviosParaReportesDePedidos(conf)
        reporte = ReporteDeEntrega(conf, datos_precalculados)
        datos_de_reporte = reporte.datos_de_reporte()

        datos_sms = datos_de_reporte[0]['datos']
        datos_supervisor = datos_sms[0]['datos']

        if datos_supervisor[0]['equipo'] == eq1:
            datos_eq1 = datos_supervisor[0]['datos']
            datos_eq2 = datos_supervisor[1]['datos']
        else:
            datos_eq1 = datos_supervisor[1]['datos']
            datos_eq2 = datos_supervisor[0]['datos']

        self.assertEqual(len(datos_eq2), 0)

    def test_reporte_de_entrega_por_equipos(self):
        pedidos = PedidoDeProspecto.objects.all()
        f = self.fixture
        eq1 = EquiposFactory(supervisor=f['sup_1'], nombre='Eq 1')
        eq2 = EquiposFactory(supervisor=f['sup_1'], nombre='Eq 2')
        eq1.integrantes = [f['vend_1'], f['vend_2']]
        eq2.integrantes = [f['vend_5'], ]
        conf = ConfiguracionDeReporteParaGerente(desde=self.desde,
                                                 hasta=self.hasta,
                                                 tipo_de_staff=TipoDeStaff.EQUIPOS,
                                                 staff=[eq1, eq2],
                                                 tipos_de_reportes=['T', ])
        datos_precalculados = CalculosPreviosParaReportesDePedidos(conf)
        reporte = ReporteDeEntrega(conf, datos_precalculados)
        datos_de_reporte = reporte.datos_de_reporte()

        self.assertEqual(len(datos_de_reporte), 1)
        self.assertEqual(datos_de_reporte[0]['calidad'], self.fixture['tipo_s'])
        datos_sms = datos_de_reporte[0]['datos']
        self.assertEqual(len(datos_sms), 1)
        self.assertEqual(datos_sms[0]['supervisor'], f['sup_1'])
        datos_supervisor = datos_sms[0]['datos']
        self.assertEqual(len(datos_supervisor), 2)
        self.assertTrue(datos_supervisor[0]['equipo'] == eq1 or datos_supervisor[1]['equipo'] == eq1)
        self.assertTrue(datos_supervisor[0]['equipo'] == eq2 or datos_supervisor[1]['equipo'] == eq2)
        if datos_supervisor[0]['equipo'] == eq1:
            datos_eq1 = datos_supervisor[0]['datos']
            datos_eq2 = datos_supervisor[1]['datos']
        else:
            datos_eq1 = datos_supervisor[1]['datos']
            datos_eq2 = datos_supervisor[0]['datos']

        pedido = 279 * 10 / 31
        self.assertEqual(len(datos_eq1), 2)
        self.assertEqual(datos_eq1[0]['vendedor'], f['vend_1'])
        self.assertEqual(datos_eq1[0]['pedido'], pedido)
        self.assertEqual(datos_eq1[0]['entregado'], 4)
        self.assertEqual(datos_eq1[0]['faltante'], pedido - 4)
        self.assertEqual(datos_eq1[1]['vendedor'], f['vend_2'])
        self.assertEqual(datos_eq1[1]['pedido'], pedido)
        self.assertEqual(datos_eq1[1]['entregado'], 2)
        self.assertEqual(datos_eq1[1]['faltante'], pedido - 2)

        self.assertEqual(len(datos_eq2), 1)
        self.assertEqual(datos_eq2[0]['vendedor'], f['vend_5'])
        self.assertEqual(datos_eq2[0]['pedido'], pedido)
        self.assertEqual(datos_eq2[0]['entregado'], 0)
        self.assertEqual(datos_eq2[0]['faltante'], pedido)

    def test_sin_asignar(self):
        repartidor = RepartidorDeProspectos()
        repartidor.quitar_asignacion_a_prospectos(prospectos=self.fixture['vend_1'].prospectos.all())
        datos_precalculados = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeEntrega(self.configuracion, datos_precalculados)
        datos_de_reporte = reporte.datos_de_reporte()
        self.assertEqual(datos_de_reporte[0]['datos'][0]['sin_asignar'], 4)
