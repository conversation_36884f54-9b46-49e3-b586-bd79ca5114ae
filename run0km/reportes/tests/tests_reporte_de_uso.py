# -*- coding: utf-8 -*-


import mock
from datetime import date
from django.utils.timezone import datetime, timedelta, make_aware
from django.utils import timezone
from concesionarias.rango_laboral import CalendarioLaboral
from prospectos.models import Prospecto, PedidoDeProspecto, Comentario

from reportes.tests.tests_reportes_para_gerentes import BaseReporteParaGerentesTest
from reportes.generacion_de_reportes import CalculosPreviosParaReportesDePedidos, ReporteDeUso
from reportes.models import TipoDeReporte
from testing.factories import LogActividadFactory


class ReporteDeUsoTest(BaseReporteParaGerentesTest):
    def tiempo_promedio_de_respuesta_en_el_reporte_para_vendedor(self, vendedor):
        horario_laboral = CalendarioLaboral.default()

        desde = datetime(self.configuracion.desde.year, self.configuracion.desde.month, self.configuracion.desde.day)
        hasta = datetime(self.configuracion.hasta.year, self.configuracion.hasta.month,
                         self.configuracion.hasta.day) + timedelta(days=1)

        prospectos_de_vendedor = Prospecto.objects.filter(fecha__gte=make_aware(desde),
                                                          fecha__lt=make_aware(hasta),
                                                          responsable=vendedor.supervisor,
                                                          vendedor=vendedor)

        promedio_respuesta_vendedor = sum([horario_laboral.horas_laborales_entre(
            fecha_y_hora_final=prospecto.comentarios.first().datetime if prospecto.comentarios.first() else timezone.now(),
            fecha_y_hora_inicial=prospecto.fecha_de_asignacion_a_vendedor()).total_seconds() for prospecto in
                                             prospectos_de_vendedor]) / prospectos_de_vendedor.count()

        return promedio_respuesta_vendedor

    @mock.patch('django.utils.timezone.now', return_value=timezone.now())
    def test_datos(self, mock_now):
        f = self.fixture

        hace_dias = timezone.now() + timedelta(days=-10)
        f['log_1'] = LogActividadFactory(vendedor=f['vend_1'], anio=hace_dias.year, mes=hace_dias.month,
                                         ultima=hace_dias, cantidad=0)
        f['log_2'] = LogActividadFactory(vendedor=f['vend_2'], anio=hace_dias.year, mes=hace_dias.month,
                                         ultima=hace_dias, cantidad=0)
        f['log_3'] = LogActividadFactory(vendedor=f['vend_3'], anio=hace_dias.year, mes=hace_dias.month,
                                         ultima=hace_dias, cantidad=0)
        f['log_4'] = LogActividadFactory(vendedor=f['vend_4'], anio=hace_dias.year, mes=hace_dias.month,
                                         ultima=hace_dias, cantidad=0)
        f['log_5'] = LogActividadFactory(vendedor=f['vend_5'], anio=hace_dias.year, mes=hace_dias.month,
                                         ultima=hace_dias, cantidad=0)
        f['log_6'] = LogActividadFactory(vendedor=f['vend_6'], anio=hace_dias.year, mes=hace_dias.month,
                                         ultima=hace_dias, cantidad=0)

        self.configuracion.tipos_de_reportes = [TipoDeReporte.objects.get(nombre='Uso'), ]
        datos_precalculados = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeUso(self.configuracion, datos_precalculados)

        datos_de_reporte = reporte.datos_de_reporte()
        self.assertEqual(len(datos_de_reporte), 1)  # 1 supervisor
        self.assertEqual(len(datos_de_reporte[0]['datos']), 3)  # 3 vendedores

        promedio_respuesta_vendedor_1 = self.tiempo_promedio_de_respuesta_en_el_reporte_para_vendedor(f['vend_1'])
        promedio_respuesta_vendedor_2 = self.tiempo_promedio_de_respuesta_en_el_reporte_para_vendedor(f['vend_2'])

        for datos_de_vendedor in datos_de_reporte[0]['datos']:
            if datos_de_vendedor['vendedor'] == f['vend_1']:
                self.assertEqual(datos_de_vendedor['datos_nuevos'], 4)
                self.assertEqual(datos_de_vendedor['ingresos_al_panel'], 0)
                self.assertEqual(datos_de_vendedor['tiempo_de_respuesta'], promedio_respuesta_vendedor_1)
            if datos_de_vendedor['vendedor'] == f['vend_2']:
                self.assertEqual(datos_de_vendedor['datos_nuevos'], 2)
                self.assertEqual(datos_de_vendedor['ingresos_al_panel'], 0)
                self.assertEqual(datos_de_vendedor['tiempo_de_respuesta'], promedio_respuesta_vendedor_2)
            if datos_de_vendedor['vendedor'] == f['vend_5']:
                self.assertEqual(datos_de_vendedor['datos_nuevos'], 0)
                self.assertEqual(datos_de_vendedor['ingresos_al_panel'], 0)
                self.assertEqual(datos_de_vendedor['tiempo_de_respuesta'], 0)

        self._crear_comentario(prospecto=self.prospectos[0], vendedor=f['vend_1'], texto='Comentario 1',
                               fecha=self.prospectos[0].fecha_de_asignacion_a_vendedor() + timedelta(minutes=3))
        self._crear_comentario(prospecto=self.prospectos[1], vendedor=f['vend_1'], texto='Comentario 2',
                               fecha=self.prospectos[1].fecha_de_asignacion_a_vendedor() + timedelta(minutes=4))
        self._crear_comentario(prospecto=self.prospectos[2], vendedor=f['vend_1'], texto='Comentario 3',
                               fecha=self.prospectos[2].fecha_de_asignacion_a_vendedor() + timedelta(minutes=3))

        self._crear_comentario(prospecto=self.prospectos[4], vendedor=f['vend_2'], texto='Comentario 1',
                               fecha=self.prospectos[4].fecha_de_asignacion_a_vendedor() + timedelta(days=3))
        self._crear_comentario(prospecto=self.prospectos[5], vendedor=f['vend_2'], texto='Comentario 2',
                               fecha=self.prospectos[5].fecha_de_asignacion_a_vendedor() + timedelta(days=2))

        promedio_respuesta_vendedor_1 = self.tiempo_promedio_de_respuesta_en_el_reporte_para_vendedor(f['vend_1'])
        promedio_respuesta_vendedor_2 = self.tiempo_promedio_de_respuesta_en_el_reporte_para_vendedor(f['vend_2'])

        datos_precalculados = CalculosPreviosParaReportesDePedidos(self.configuracion)
        reporte = ReporteDeUso(self.configuracion, datos_precalculados)
        datos_de_reporte = reporte.datos_de_reporte()
        self.assertEqual(len(datos_de_reporte), 1)  # 1 supervisor
        self.assertEqual(len(datos_de_reporte[0]['datos']), 3)  # 3 vendedores
        for datos_de_vendedor in datos_de_reporte[0]['datos']:
            if datos_de_vendedor['vendedor'] == f['vend_1']:
                self.assertEqual(datos_de_vendedor['datos_nuevos'], 1)
                self.assertEqual(datos_de_vendedor['ingresos_al_panel'], 0)
                self.assertEqual(datos_de_vendedor['tiempo_de_respuesta'], promedio_respuesta_vendedor_1)
            if datos_de_vendedor['vendedor'] == f['vend_2']:
                self.assertEqual(datos_de_vendedor['datos_nuevos'], 0)
                self.assertEqual(datos_de_vendedor['ingresos_al_panel'], 0)
                self.assertEqual(datos_de_vendedor['tiempo_de_respuesta'], promedio_respuesta_vendedor_2)
            if datos_de_vendedor['vendedor'] == f['vend_5']:
                self.assertEqual(datos_de_vendedor['datos_nuevos'], 0)
                self.assertEqual(datos_de_vendedor['ingresos_al_panel'], 0)
                self.assertEqual(datos_de_vendedor['tiempo_de_respuesta'], 0)

    def _crear_comentario(self, prospecto, vendedor, texto, fecha):
        patcher = mock.patch('django.utils.timezone.now', return_value=fecha)
        patcher.start()
        Comentario.nuevo(prospecto=prospecto, vendedor=vendedor, comentario=texto)
        patcher.stop()
