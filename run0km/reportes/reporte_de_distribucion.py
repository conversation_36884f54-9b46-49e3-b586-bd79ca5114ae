# coding=utf-8
import calendar
from datetime import datetime

from dateutil.relativedelta import relativedelta
from django.contrib.sites.models import Site
from django.urls import reverse
from django.db.models import Count, Q
from django.db.models import F
from django.utils import timezone
from django.utils.timezone import make_aware

from campanias.models import CategoriaDeCampania, Campania
from core.support import make_aware_when_is_naive
from lib.timing import timing
from prospectos.models import AsignacionDeProspecto, Marca
from prospectos.models import PedidoDeProspecto
from prospectos.models import Prospecto
from prospectos.models import Proveedor
from reportes.pedidos import EstimadorDePedidos
from vendedores.models import Vendedor


class GeneradorDeReporteDeDistribucion(object):

    @classmethod
    def nuevo(cls):
        manejador = cls()
        return manejador

    def url_para(self, descriptor):
        dominio = Site.objects.get_current().domain
        view = reverse('reporte-de-distribucion', kwargs={'hash_de_descriptor': descriptor.hash()})
        url = 'http://%s%s' % (dominio, view)
        return url

    def generar_reporte_desde(self, descriptor, proveedores, marcas,
                              filtro_de_prospecto_por_fecha, fecha_desde, fecha_hasta):
        calculos = CalculosDeReporte.nuevo_desde_descriptor(descriptor, proveedores)
        datos_por_categoria = self.reporte_de_entregas_por_categoria(
            calculos, marcas, filtro_de_prospecto_por_fecha, fecha_desde, fecha_hasta)
        cantidades_pedidas = self.reporte_cantidad_pedida_y_entregada_para(calculos, marcas, fecha_desde, fecha_hasta)

        datos = {'cantidades_pedidas': cantidades_pedidas, 'datos_de_categorias': datos_por_categoria}
        if descriptor.reporte_de_prospectos_no_asignados():
            datos_sin_asignar = self.reporte_de_prospectos_sin_asignar(calculos)
            datos['sin_asignar'] = datos_sin_asignar

        return {
            'datos': datos,
            'fecha_desde': fecha_desde,
            'fecha_hasta': fecha_hasta,
            'marcas': calculos.marcas_encontradas()
        }

    @timing
    def reporte_de_entregas_por_categoria(
            self, calculos, marcas, filtro_de_prospecto_por_fecha, fecha_desde, fecha_hasta):
        # {'categoria_id': { nombre: '...', total: {{ dia: cantidad, ...}, ...},
        # 'marcas': { 'marca': { dia: cantidad, ...}, ... }}, }
        prospectos = calculos.prospectos_con_fecha_entre(filtro_de_prospecto_por_fecha, fecha_desde, fecha_hasta)
        prospectos = self._filtrar_por_marcas(prospectos, marcas)
        datos_por_categoria = {}
        datos = filtro_de_prospecto_por_fecha.agrupar_cantidades_desde(prospectos)
        for dato_por_marca in datos:
            datos_de_categoria = self._datos_de_categoria(datos_por_categoria, dato_por_marca)
            self._agregar_asignacion_para_marca(dato_por_marca, datos_de_categoria)
        return datos_por_categoria

    def reporte_cantidad_pedida_y_entregada_para(self, calculos, marcas, fecha_desde, fecha_hasta):
        prospectos = calculos.prospectos_con_fecha_creacion_entre(fecha_desde, fecha_hasta)
        prospectos = self._filtrar_por_marcas(prospectos, marcas)
        # print(prospectos.query)
        datos = self._datos_de_entrega_para(prospectos)
        self._datos_cantidad_pedida_para(
            calculos.categorias(), calculos.responsables(), marcas, fecha_desde, fecha_hasta, datos)
        return datos

    def reporte_de_prospectos_sin_asignar(self, calculos):
        # {'categoria_id': { nombre: '...', total: 45, datos: {'Volkswagen': 15, ...}}

        sin_asignar = calculos.prospectos_sin_asignar().annotate(marca=F('_marca___nombre'))
        consulta = sin_asignar.values('campania__categoria', 'marca').annotate(cantidad=Count('marca'))
        consulta = consulta.values('campania__categoria', 'campania__categoria__nombre', 'marca', 'cantidad')
        datos = {}
        for dato_por_categoria_y_marca in consulta.all():
            default = self._datos_default_para_categoria(dato_por_categoria_y_marca['campania__categoria__nombre'])
            categoria = dato_por_categoria_y_marca['campania__categoria']
            dato_categoria = datos.setdefault(categoria, default)
            datos_marcas = dato_categoria.get('datos')
            cantidad = dato_por_categoria_y_marca['cantidad']
            dato_categoria['total'] = dato_categoria['total'] + cantidad
            datos_marcas[dato_por_categoria_y_marca['marca']] = cantidad
        return datos

    def _agregar_asignacion_para_marca(self, dato_por_marca, datos_de_categoria):
        dia = dato_por_marca['dia']
        cantidad = dato_por_marca['cantidad']
        totales = datos_de_categoria['total']
        total = totales.get(dia, 0)
        totales[dia] = total + cantidad
        marca = dato_por_marca['marca'].title()
        datos_marca = datos_de_categoria['marcas'].setdefault(marca, {})
        cantidad_asignada = datos_marca.get(dia, 0)
        datos_marca[dia] = cantidad_asignada + cantidad

    def _datos_de_categoria(self, datos, dato_por_marca):
        categoria_id = dato_por_marca['categoria']
        nombre = dato_por_marca['nombre_categoria']
        datos_de_categoria = datos.setdefault(categoria_id, {'nombre': nombre, 'total': {}, 'marcas': {}})
        return datos_de_categoria

    @timing
    def _obtener_cantidades_por_categoria_por_dia(self, categorias, asignaciones, fecha_desde, fecha_hasta):
        # {{'categoria': {{ 'fecha': cantidada}, ...}}, ...}
        cantidades_totales_entregadas_por_dia = {}
        for categoria in categorias:
            cantidad_entregada_de_categoria = asignaciones.de_categoria(categoria=categoria)
            cantidad_de_categoria_entregadas_por_dia = self._separar_cantidades_entregadas_por_dia(
                cantidad_entregada_de_categoria=cantidad_entregada_de_categoria, fecha_desde=fecha_desde,
                fecha_hasta=fecha_hasta)
            cantidades_totales_entregadas_por_dia.update({categoria.nombre: cantidad_de_categoria_entregadas_por_dia})
        return cantidades_totales_entregadas_por_dia

    @timing
    def _separar_cantidades_entregadas_por_dia(self, cantidad_entregada_de_categoria, fecha_desde, fecha_hasta):
        # { 'fecha': cantidada}
        cantidades_entregadas_por_dia = {}
        ids = cantidad_entregada_de_categoria.values_list('id', flat=True)
        # print(ids.query)
        asignaciones = AsignacionDeProspecto.objects.filter(id__in=list(ids))
        cada_fecha = fecha_desde
        timedelta_un_dia = timezone.timedelta(days=1)
        while cada_fecha <= fecha_hasta:
            cantidad = asignaciones.con_asignacion_a_vendedor_del_dia(fecha=cada_fecha).count()
            cantidades_entregadas_por_dia.update({cada_fecha.strftime('%d-%m-%Y'): cantidad})
            cada_fecha += timedelta_un_dia
        return cantidades_entregadas_por_dia

    def _filtrar_por_marcas(self, prospectos, nombres_de_marcas):
        if nombres_de_marcas:
            ids_marcas = list(Marca.objects.con_nombres(nombres_de_marcas).ids())
            prospectos = prospectos.filter(_marca__in=ids_marcas)
        return prospectos

    @timing
    def _datos_cantidad_pedida_para(self, categorias, responsables, marcas, fecha_desde, fecha_hasta, datos):
        pedidos = self._pedidos_que_aplican_para(categorias, responsables, fecha_desde, fecha_hasta)
        pedidos = pedidos.prefetch_related('categorias', 'filtros')
        estimador_de_pedidos = EstimadorDePedidos()
        for pedido in pedidos.all():
            marcas_pedidas = self._marcas_pedidas_para(pedido, marcas)
            if marcas_pedidas:
                self._datos_para_pedido(
                    pedido, categorias, marcas_pedidas, fecha_desde, fecha_hasta, datos, estimador_de_pedidos)
        return datos

    def _datos_para_pedido(self, pedido, categorias, marcas, fecha_desde, fecha_hasta, datos, estimador_de_pedidos):
        cantidad = estimador_de_pedidos.cantidad_pedida_por_marca_y_categorias_entre(pedido, fecha_desde, fecha_hasta)
        region = CalculosDeRegion.de_pedido(pedido)
        for marca in marcas:
            for categoria in categorias:
                self._dato_por_categoria_y_marca(categoria, marca, region, cantidad, datos)
        return datos

    def _dato_por_categoria_y_marca(self, categoria, marca, region, cantidad, datos):
        dato = [dato for dato in datos if dato['categoria'] == categoria.nombre and dato['marca'] == marca.title()]
        assert (len(dato) < 2)
        if dato:
            dato_de_la_marca = dato[0]
            dato_de_la_marca['cantidad_pedida'] += cantidad
            porcentaje = CalculosDePorcentaje().porcentaje_de(
                numerador=dato_de_la_marca['cantidad_entregada'], denominador=dato_de_la_marca['cantidad_pedida'])
            dato_de_la_marca['porcentaje_entregado'] = porcentaje
        else:
            dato_de_la_marca = self._nuevo_dato_para(categoria.nombre, marca, cantidad_pedida=cantidad)
            datos.append(dato_de_la_marca)
        if cantidad > 0:
            region.sumar_cantidad_pedida(dato_de_la_marca, cantidad)

    def _nuevo_dato_para(self, nombre_de_categoria, marca, cantidad_pedida=0,
                         cantidad_entregada=0,
                         amba_cantidad_entregada=0, resto_cantidad_entregada=0):
        return {
            'categoria': nombre_de_categoria,
            'marca': marca.title(),
            'cantidad_pedida': cantidad_pedida,
            'cantidad_entregada': cantidad_entregada,
            'porcentaje_entregado': 0,
            'detalle': {
                'amba': {'cantidad_pedida': 0, 'cantidad_entregada': amba_cantidad_entregada,
                         'porcentaje_entregado': 0},
                'resto': {'cantidad_pedida': 0, 'cantidad_entregada': resto_cantidad_entregada,
                          'porcentaje_entregado': 0}
            }
        }

    def _pedidos_que_aplican_para(self, categorias, responsables, fecha_desde, fecha_hasta):
        pedidos = PedidoDeProspecto.objects.filter(
            fecha__gte=self._mes_desde(fecha_desde), fecha__lte=self._mes_hasta(fecha_hasta))
        pedidos_que_aplican = pedidos.filter(categorias__in=categorias, supervisor__in=responsables)
        return pedidos_que_aplican

    def _mes_desde(self, fecha):
        return fecha.replace(day=1)

    def _mes_hasta(self, fecha):
        monthrange = calendar.monthrange(fecha.year, fecha.month)
        return fecha.replace(day=monthrange[1])

    def _marcas_pedidas_para(self, pedido, marcas):
        marcas = marcas or []
        marcas_del_pedido = [marca.lower() for marca in pedido.marcas()]
        if marcas:
            return [marca for marca in marcas if marca.lower() in marcas_del_pedido]
        else:
            return marcas_del_pedido

    @timing
    def _datos_de_entrega_para(self, prospectos):
        consulta = prospectos.annotate(categoria=F('campania__categoria__nombre'), marca=F('_marca___nombre')).values(
            'categoria', 'marca', 'localidad')
        consulta = consulta.annotate(cantidad_entregada=Count('marca'))
        datos = {}
        for dato_de_consulta in consulta:
            cantidad_entregada = dato_de_consulta['cantidad_entregada']
            categoria = dato_de_consulta['categoria']
            marca = dato_de_consulta['marca'].title()
            dato = datos.get((categoria, marca))
            if dato is None:
                dato = self._nuevo_dato_para(
                    nombre_de_categoria=categoria, marca=marca, cantidad_entregada=cantidad_entregada)
                datos[(categoria, marca)] = dato
            else:
                dato['cantidad_entregada'] += cantidad_entregada
            region = CalculosDeRegion.de_localidad(localidad=dato_de_consulta['localidad'])
            region.sumar_cantidad_entregada(dato, cantidad=cantidad_entregada)
        return list(datos.values())

    def _datos_default_para_categoria(self, nombre):
        return {'nombre': nombre, 'datos': {}, 'total': 0}


class CalculosDePorcentaje(object):
    def porcentaje_de(self, numerador, denominador):
        if denominador > 0:
            porcentaje = float(numerador) / float(denominador)
        else:
            porcentaje = 0
        return round(porcentaje * 100, 2)


class CalculosDeReporte(object):
    def __init__(self, categorias, responsables, proveedores):
        super(CalculosDeReporte, self).__init__()
        self._categorias = categorias
        self._responsables = responsables
        self._proveedores = proveedores
        self._categorias_ids = list(self._categorias.values_list('id', flat=True))
        self._responsables_ids = list(self._responsables.values_list('id', flat=True))
        self._proveedores_ids = list(self._proveedores.values_list('id', flat=True))
        self._prospectos_por_fecha_de_asignacion = None
        self._prospectos_por_fecha_de_creacion = None
        self._prospectos_sin_asignar = None

    @classmethod
    def nuevo_desde_ids(cls, ids_categorias=None, ids_responsables=None, ids_proveedores=None):
        # Metodo de conveniencia para debug
        if ids_categorias:
            categorias = CategoriaDeCampania.objects.filter(id__in=ids_categorias)
        else:
            categorias = CategoriaDeCampania.objects.all()
        if ids_responsables:
            responsables = Vendedor.objects.filter(id__in=ids_responsables)
        else:
            responsables = Vendedor.objects.all()
        if ids_proveedores:
            proveedores = Proveedor.objects.filter(id__in=ids_proveedores)
        else:
            proveedores = Proveedor.objects.all()
        return cls.nuevo(categorias=categorias, responsables=responsables, proveedores=proveedores)

    @classmethod
    def nuevo(cls, categorias, responsables, proveedores):
        return cls(categorias, responsables, proveedores)

    @classmethod
    def nuevo_desde_descriptor(cls, descriptor, proveedores):
        return cls.nuevo(descriptor.categorias(), descriptor.responsables(), proveedores)

    def categorias(self):
        return self._categorias

    def responsables(self):
        return self._responsables

    def prospectos_con_fecha_entre(self, filtro_de_prospecto_por_fecha, fecha_desde, fecha_hasta):
        return filtro_de_prospecto_por_fecha.filtrar_entre(self, fecha_desde, fecha_hasta)

    def prospectos_con_fecha_asignaciones_entre(self, fecha_desde, fecha_hasta):
        if self._prospectos_por_fecha_de_asignacion is None:
            self._inicializar_prospectos_por_fecha_de_asignacion_entre(fecha_desde, fecha_hasta)
        return self._prospectos_por_fecha_de_asignacion

    def prospectos_con_fecha_creacion_entre(self, fecha_desde, fecha_hasta):
        if self._prospectos_por_fecha_de_creacion is None:
            self._inicializar_prospectos_por_fecha_de_creacion_entre(fecha_desde, fecha_hasta)
        return self._prospectos_por_fecha_de_creacion

    def prospectos_sin_asignar(self):
        if self._prospectos_sin_asignar is None:
            self._inicializar_prospectos_sin_asignar()
        return self._prospectos_sin_asignar

    def _inicializar_prospectos_sin_asignar(self):
        sin_asignar = Prospecto.objects.filter(
            responsable__isnull=True, vendedor__isnull=True, campania__categoria__in=self._categorias_ids)
        sin_asignar = self._filtrar_por_proveedores(sin_asignar)
        self._prospectos_sin_asignar = sin_asignar

    def _filtrar_por_proveedores(self, prospectos):
        query = Q(proveedor__in=self._proveedores_ids)
        filtrados = prospectos.filter(query)
        return filtrados

    def _inicializar_prospectos_por_fecha_de_creacion_entre(self, fecha_desde, fecha_hasta):
        desde_datetime = make_aware_when_is_naive(datetime.combine(fecha_desde, datetime.min.time()))
        hasta_datetime = make_aware_when_is_naive(datetime.combine(fecha_hasta, datetime.max.time()))

        prospectos = Prospecto.objects.filter(
            fecha_creacion__gte=desde_datetime,
            fecha_creacion__lte=hasta_datetime,
            campania__categoria__in=self._categorias_ids,
            responsable__in=self._responsables_ids)
        prospectos = self._filtrar_por_proveedores(prospectos)
        ids = list(prospectos.values_list('id', flat=True))
        self._prospectos_por_fecha_de_creacion = Prospecto.objects.filter(id__in=ids)

    @timing
    def _inicializar_prospectos_por_fecha_de_asignacion_entre(self, fecha_desde, fecha_hasta):
        asignaciones = AsignacionDeProspecto.objects.con_asignacion_a_responsable_entre_fechas(
            desde=fecha_desde, hasta=fecha_hasta)
        ids_de_prospectos = list(asignaciones.values_list('prospecto_id', flat=True))
        prospectos = Prospecto.objects.filter(
            id__in=ids_de_prospectos, responsable__in=self._responsables_ids,
            campania__categoria__in=self._categorias_ids)
        prospectos = self._filtrar_por_proveedores(prospectos)
        self._prospectos_por_fecha_de_asignacion = prospectos

    def marcas_encontradas(self):
        marcas = []
        if self._prospectos_por_fecha_de_asignacion is not None:
            nombres_de_marcas = self._prospectos_por_fecha_de_asignacion.values_list('_marca___nombre', flat=True)
            marcas_por_asignacion = nombres_de_marcas.distinct()
            marcas = [marca.title() for marca in marcas_por_asignacion.all()]
        if self._prospectos_por_fecha_de_creacion is not None:
            self._incluir_marcas_de(self._prospectos_por_fecha_de_creacion, marcas)
        if self._prospectos_sin_asignar is not None:
            self._incluir_marcas_de(self._prospectos_sin_asignar, marcas)
        return marcas

    def _incluir_marcas_de(self, prospectos, marcas):
        marcas_de_prospectos = prospectos.marcas()
        for marca in marcas_de_prospectos:
            nombre = marca.nombre().title()
            if nombre not in marcas:
                marcas.append(nombre)
        return marcas


class CalculosDeRegion(object):
    def sumar_cantidad_pedida(self, datos, cantidad):
        raise NotImplementedError('subclass responsibility')

    def sumar_cantidad_entregada(self, datos, cantidad):
        raise NotImplementedError('subclass responsibility')

    def es_amba(self):
        return False

    def no_es_amba(self):
        return False

    def es_region_indefinida(self):
        return not self.es_amba() and not self.no_es_amba()

    def _sumar_cantidad_pedida_a_region(self, region, datos, cantidad):
        datos_region = datos['detalle'][region]
        datos_region['cantidad_pedida'] += cantidad
        porcentaje = CalculosDePorcentaje().porcentaje_de(
            numerador=datos_region['cantidad_entregada'], denominador=datos_region['cantidad_pedida'])
        datos_region['porcentaje_entregado'] = porcentaje

    def _sumar_cantidad_entregada_a_region(self, region, datos, cantidad):
        datos_region = datos['detalle'][region]
        datos_region['cantidad_entregada'] += cantidad

    @classmethod
    def de_pedido(cls, pedido):
        filtros = [filtro for filtro in pedido.filtros.all() if cls._filtro_refiere_a_amba(filtro)]
        son_de_amba = False
        son_de_resto = False
        for filtro in filtros:
            if cls._es_filtro_de_amba(filtro):
                son_de_amba = True
            else:
                son_de_resto = True
            if son_de_amba and son_de_resto:
                return cls.no_definida()
        if son_de_amba:
            return cls.amba()
        elif son_de_resto:
            return cls.resto()
        else:
            return cls.no_definida()

    @classmethod
    def de_localidad(cls, localidad):
        if cls._es_localidad_de_amba(localidad):
            return cls.amba()
        else:
            return cls.resto()

    @classmethod
    def amba(cls):
        return CalculosDeRegionAMBA()

    @classmethod
    def resto(cls):
        return CalculosDeRegionResto()

    @classmethod
    def no_definida(cls):
        return CalculosDeRegionNoDefinida()

    @classmethod
    def _filtro_refiere_a_amba(cls, filtro):
        return filtro.refiere_a('localidad') or filtro.refiere_a('prefijo') or filtro.refiere_a('telefono')

    @classmethod
    def _es_filtro_de_amba(cls, filtro):
        return cls._es_filtro_localidad_de_amba(filtro) or cls._es_filtro_prefijo_de_amba(filtro)

    @classmethod
    def _es_filtro_localidad_de_amba(cls, filtro):
        return filtro.refiere_a('localidad') and any(
            [filtro.aplica(localidad_de_amba) for localidad_de_amba in cls._valores_de_filtros_para_amba()])

    @classmethod
    def _es_filtro_prefijo_de_amba(cls, filtro):
        return (filtro.refiere_a('prefijo') or filtro.refiere_a('telefono'))and any(
            [filtro.aplica(prefijo) for prefijo in cls._valores_prefijos_de_amba()])

    @classmethod
    def _es_localidad_de_amba(cls, localidad):
        return any([localidad.lower() in localidad_de_amba.lower() for localidad_de_amba in cls._localidades_de_amba()])

    @classmethod
    def _valores_de_filtros_para_amba(cls):
        # Por ahora no tengo en cuenta las localidades que definen la region AMBA, validar con Nico
        return ['amba', 'caba', 'capital']

    @classmethod
    def _valores_prefijos_de_amba(cls):
        return ['11', '011']

    @classmethod
    def _localidades_de_amba(cls):
        return ['caba', 'Almirante Brown', 'Avellaneda', 'Berazatagui', 'Berisso', 'Brandsen', 'Campana',
                'Cañuelas', 'Ensenada', 'Escobar', 'Esteban Echeverría', 'Exaltación de la Cruz', 'Ezeiza',
                'Florencio Varela', 'General Las Heras', 'General Rodríguez', 'General San Martín', 'Hurlingham',
                'Ituzaingó', 'José C. Paz', 'La Matanza', 'Lanús', 'La Plata', 'Lomas de Zamora', 'Luján',
                'Marcos Paz', 'Malvinas Argentinas', 'Moreno', 'Merlo', 'Morón', 'Pilar', 'Presidente Perón',
                'Quilmes', 'San Fernando', 'San Isidro', 'San Miguel', 'San Vicente', 'Tigre', 'Tres de Febrero',
                'Vicente López', 'Zárate']


class CalculosDeRegionAMBA(CalculosDeRegion):
    def sumar_cantidad_pedida(self, datos, cantidad):
        self._sumar_cantidad_pedida_a_region('amba', datos, cantidad)

    def sumar_cantidad_entregada(self, datos, cantidad):
        self._sumar_cantidad_entregada_a_region('amba', datos, cantidad)

    def es_amba(self):
        return True


class CalculosDeRegionResto(CalculosDeRegion):
    def sumar_cantidad_pedida(self, datos, cantidad):
        self._sumar_cantidad_pedida_a_region('resto', datos, cantidad)

    def sumar_cantidad_entregada(self, datos, cantidad):
        self._sumar_cantidad_entregada_a_region('resto', datos, cantidad)

    def no_es_amba(self):
        return True


class CalculosDeRegionNoDefinida(CalculosDeRegion):
    def sumar_cantidad_pedida(self, datos, cantidad):
        self._repartir_entre_regiones(
            metodo_suma_cantidad=self._sumar_cantidad_pedida_a_region, datos=datos, cantidad=cantidad)

    def sumar_cantidad_entregada(self, datos, cantidad):
        self._repartir_entre_regiones(
            metodo_suma_cantidad=self._sumar_cantidad_entregada_a_region, datos=datos, cantidad=cantidad)

    def _repartir_entre_regiones(self, metodo_suma_cantidad, datos, cantidad):
        metodo_suma_cantidad('amba', datos, (cantidad / 2) + (cantidad % 2))
        metodo_suma_cantidad('resto', datos, cantidad / 2)


class RangoDeFechas(object):
    def __init__(self, identificador, label, fecha_desde, fecha_hasta):
        super(RangoDeFechas, self).__init__()
        self._identificador = identificador
        self._label = label
        self._fecha_desde = fecha_desde
        self._fecha_hasta = fecha_hasta

    def identificador(self):
        return self._identificador

    def label(self):
        return self._label

    def fecha_desde(self):
        return self._fecha_desde

    def fecha_hasta(self):
        return self._fecha_hasta

    @classmethod
    def ultimos_siete_dias(cls):
        return cls._nuevo_desde_dias_anteriores(
            identificador='utlimos-siete-dias',
            label='Ultimos siete dias',
            dias_anteriores=6)

    @classmethod
    def semana_en_curso(cls):
        hoy = timezone.now()
        dias_de_la_semana = calendar.weekday(year=hoy.year, month=hoy.month, day=hoy.day)
        return cls._nuevo_desde_dias_anteriores(
            identificador='semana-en-curso',
            label='Semana en curso',
            dias_anteriores=dias_de_la_semana,
            fecha_hasta=hoy
        )

    @classmethod
    def ultimos_treinta_dias(cls):
        return cls._nuevo_desde_dias_anteriores(
            identificador='utlimos-treinta-dias',
            label='Ultimos treinta dias',
            dias_anteriores=29)

    @classmethod
    def mes_en_curso(cls):
        hoy = timezone.now()
        return cls._nuevo_desde_dias_anteriores(
            identificador='mes-en-curso',
            label='Mes en curso',
            dias_anteriores=hoy.day - 1,
            fecha_hasta=hoy
        )

    @classmethod
    def mes_pasado(cls):
        fin_de_mes_pasado = timezone.now().replace(day=1) - timezone.timedelta(days=1)
        return cls._nuevo_desde_dias_anteriores(
            identificador='mes-pasado',
            label='Mes pasado',
            dias_anteriores=fin_de_mes_pasado.day - 1,
            fecha_hasta=fin_de_mes_pasado
        )

    @classmethod
    def _nuevo_desde_dias_anteriores(cls, identificador, label, dias_anteriores, fecha_hasta=None):
        fecha_hasta = fecha_hasta or timezone.now()
        return cls(identificador=identificador,
                   label=label,
                   fecha_desde=fecha_hasta.date() - timezone.timedelta(days=dias_anteriores),
                   fecha_hasta=fecha_hasta.date())

    @classmethod
    def opciones(cls):
        return [cls.ultimos_siete_dias(), cls.semana_en_curso(),
                cls.ultimos_treinta_dias(), cls.mes_en_curso(),
                cls.mes_pasado()]

    @classmethod
    def nuevo_desde_identificador(cls, identificador):
        opciones = cls.opciones()
        for opcion in opciones:
            if opcion.identificador() == identificador:
                return opcion
        return None


class FiltroDeProspectosPorFechas(object):
    def filtrar_entre(self, calculos_de_reportes, fecha_desde, fecha_hasta):
        raise NotImplementedError('subclass responsibility')

    def agrupar_cantidades_desde(self, prospectos):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def por_fecha_de_asginacion(cls):
        return FiltroPorFechasDeAsignacion()

    @classmethod
    def por_fecha_de_creacion(cls):
        return FiltroPorFechasDeCreacion()


class FiltroPorFechasDeAsignacion(FiltroDeProspectosPorFechas):
    def filtrar_entre(self, calculos_de_reportes, fecha_desde, fecha_hasta):
        return calculos_de_reportes.prospectos_con_fecha_asignaciones_entre(fecha_desde, fecha_hasta)

    @timing
    def agrupar_cantidades_desde(self, prospectos):
        # Responde un dict {{'dia', 'categoria', 'nombre', 'marca', 'cantidad'}, }
        # TODO: ver como extender las expresiones de django, quitar hardcodeo del TZ
        asignaciones = AsignacionDeProspecto.objects.filter(prospecto__in=prospectos)
        select_data = {
            "dia": """DATE_FORMAT(CONVERT_TZ(fecha_de_asignacion_a_supervisor, 'UTC',
            'America/Argentina/Buenos_Aires'), '%%d-%%m-%%Y')"""}
        asignaciones = asignaciones.extra(select=select_data)
        asignaciones = asignaciones.annotate(
            categoria=F('prospecto__campania__categoria__id'),
            nombre_categoria=F('prospecto__campania__categoria__nombre'),
            marca=F('prospecto___marca___nombre')
        ).values(
            'dia', 'categoria', 'nombre_categoria', 'marca')
        asignaciones = asignaciones.annotate(cantidad=Count('categoria'))
        return asignaciones.all()


class FiltroPorFechasDeCreacion(FiltroDeProspectosPorFechas):
    def filtrar_entre(self, calculos_de_reportes, fecha_desde, fecha_hasta):
        return calculos_de_reportes.prospectos_con_fecha_creacion_entre(fecha_desde, fecha_hasta)

    @timing
    def agrupar_cantidades_desde(self, prospectos):
        # Responde un dict {{'dia', 'categoria', 'nombre_categoria', 'marca', 'cantidad'}, }
        select_data = {
            "dia": """DATE_FORMAT(CONVERT_TZ(fecha_creacion, 'UTC',
            'America/Argentina/Buenos_Aires'), '%%d-%%m-%%Y')"""}
        consulta = prospectos.extra(select=select_data)
        consulta = consulta.annotate(
            categoria=F('campania__categoria__id'),
            nombre_categoria=F('campania__categoria__nombre'),
            marca=F('_marca___nombre')
        ).values(
            'dia', 'categoria', 'nombre_categoria', 'marca')
        consulta = consulta.annotate(cantidad=Count('categoria'))
        return consulta.all()


class GeneradorDeReporteSimplificado(object):
    """
        Genera reporte: un json {'mes': 5, 'anio', 'ingresos': 5000, 'asignaciones': 4900}
    """

    def __init__(self, mes, anio, categorias):
        super(GeneradorDeReporteSimplificado, self).__init__()
        self._mes = mes
        self._anio = anio
        self._categorias = categorias

    def generar(self):
        reporte = {'mes': self._mes, 'anio': self._anio}
        fecha_desde = make_aware(timezone.datetime(day=1, month=self._mes, year=self._anio))
        fecha_hasta = fecha_desde + relativedelta(months=1)
        categorias = list(self._categorias.values_list('id', flat=True))
        campanias = list(Campania.objects.filter(categoria__in=categorias).values_list('id', flat=True))
        prospectos = Prospecto.objects.filter(
            fecha_creacion__gte=fecha_desde,
            fecha_creacion__lt=fecha_hasta,
            campania__in=campanias).values_list('id', flat=True)
        ingresos = prospectos.count()
        reporte['ingresos'] = ingresos

        asignados = AsignacionDeProspecto.objects.filter(
            fecha_de_asignacion_a_supervisor__gte=fecha_desde,
            fecha_de_asignacion_a_supervisor__lt=fecha_hasta,
            prospecto__in=prospectos

        )

        asignaciones = asignados.count()
        reporte['asignaciones'] = asignaciones
        return reporte
