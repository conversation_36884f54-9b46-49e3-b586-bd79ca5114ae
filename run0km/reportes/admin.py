import json

from django.contrib import admin
from django.db.models import F
from django.utils.safestring import mark_safe

from concesionarias.models import Concesionaria
from prospectos.models import Proveedor
from reportes.admin_forms import DescriptorDeReporteDeDistribucionForm
from reportes.models import DescriptorDeReporteDeDistribucion
from reportes.reporte_de_distribucion import GeneradorDeReporteDeDistribucion
from vendedores.models import Vendedor


class DescriptorDeReporteDeDistribucionAdmin(admin.ModelAdmin):
    model = DescriptorDeReporteDeDistribucion
    form = DescriptorDeReporteDeDistribucionForm
    list_display = ('_nombre', 'obtener_url', '_fecha_de_creacion')
    date_hierarchy = '_fecha_de_creacion'
    actions_on_top = False
    search_fields = ['_nombre']
    exclude = ('_todos_los_proveedores', '_todas_las_concesionarias', '_todos_los_responsables', '_hash')

    fieldsets = (
        (None, {'fields': ['_nombre', '_categorias', '_proveedores', '_reporte_de_prospectos_no_asignados',
                           '_reporte_deshabilitado']}),
        ('Filtros', {
            'fields': ['_concesionarias', '_solo_responsables_activos', '_responsables'],
        }),
    )

    def get_form(self, request, obj=None, **kwargs):  # Just added this override
        form = super(DescriptorDeReporteDeDistribucionAdmin, self).get_form(request, obj, **kwargs)
        form.base_fields['_categorias'].widget.can_add_related = False
        form.base_fields['_proveedores'].widget.can_add_related = False
        form.base_fields['_concesionarias'].widget.can_add_related = False
        form.base_fields['_responsables'].widget.can_add_related = False
        return form

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        extra_context = extra_context or {}
        responsables_por_concesionaria = self._responsables_por_concesionaria()
        extra_context['json_responsables_por_concesionaria'] = mark_safe(json.dumps(responsables_por_concesionaria))
        return super(DescriptorDeReporteDeDistribucionAdmin, self).changeform_view(request, object_id, form_url, extra_context)

    def save_model(self, request, obj, form, change):
        """
        Given a model instance save it to the database.
        OVERRIDE: Revisamos si marco todos los concesionarias/responsables, y actualizamos acorde.
        """
        todos_los_proveedores = self._selecciono_todos_los_proveedores(form)
        todas_las_concesionarias, todos_los_responsables = self._selecciono_todas_las_concesionarias_y_responsables(
            form)
        obj.actualizar_campos_dinamicos_con(todos_los_proveedores, todas_las_concesionarias, todos_los_responsables)
        obj.save()

    def _selecciono_todos_los_proveedores(self, form):
        """ Los proveedores son dinamicos si se seleccionaron todos """
        proveedores = form.cleaned_data['_proveedores']
        return proveedores.count() == Proveedor.objects.count()

    def _selecciono_todas_las_concesionarias_y_responsables(self, form):
        """ Las concesionarias son dinamicas si se seleccionaron todas ademas de todos sus responsables.
            Los supervisores son dinamicos si se seleccionaron todos los supervisores de las concesionarias elegidas. """
        concesionarias = form.cleaned_data['_concesionarias']
        responsables = form.cleaned_data['_responsables']
        todos_los_responsables = responsables.count() == Vendedor.objects.supervisores_de_concesionarias(concesionarias).count()
        todas_las_concesionarias = concesionarias.count() == Concesionaria.objects.count()
        return todas_las_concesionarias, todos_los_responsables

    def obtener_url(self, descriptor):
        manejador_de_reporte = GeneradorDeReporteDeDistribucion.nuevo()
        return '<a id="url-reference-%(id)s" href="%(url)s">%(url)s</a>' \
               '<button type="button" class="url-button" title="Copiar Url" ' \
               'onclick="copyUrlToClipboard(\'url-reference-%(id)s\');"/>' % {
                    'url': manejador_de_reporte.url_para(descriptor),
                    'id': descriptor.id}
    obtener_url.allow_tags = True
    obtener_url.short_description = "Url"

    def _responsables_por_concesionaria(self):
        responsables_por_concesionaria = {}
        concesionarias = Concesionaria.objects.all().prefetch_related('empleados')
        for concesionaria in concesionarias:
            concesionaria_dict = {
                str(concesionaria.id): list(concesionaria.supervisores_activos_y_no_activos().annotate(
                    es_activo=F('user__is_active')).values('id', 'es_activo'))}
            responsables_por_concesionaria.update(concesionaria_dict)
        return responsables_por_concesionaria


admin.site.register(DescriptorDeReporteDeDistribucion, DescriptorDeReporteDeDistribucionAdmin)