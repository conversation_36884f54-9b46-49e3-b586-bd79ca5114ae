
import mock
from django.core.management.base import BaseCommand
from django.utils import timezone

from reportes.generacion_de_reportes import AdministradorDeProgramacionesDeReportes
#from reportes.models import ProgramacionDeReporteParaGerentes

PROGRAMADO = False


# def side_effect_esta_programado_para(self, fecha):
#     return isinstance(self, ProgramacionDeReporteParaGerentes) and self.pk == 1
def side_effect_esta_programado_para(self, fecha):
    global PROGRAMADO
    if not PROGRAMADO:
        PROGRAMADO = True
        return True
    return False


def side_effect_destinatarios(self):
    return ['<EMAIL>']


class Command(BaseCommand):
    help = 'Envia los reportes programados para el dia de hoy'

    def handle(self, *args, **options):
        global PROGRAMADO
        PROGRAMADO = False
        with mock.patch("reportes.models.UltimoEnvioDeReporte.existe_envio_para",
                        return_value=False) as mock_existe_envio_para:
            with mock.patch("reportes.models.ProgramacionDeReporte.esta_programado_para", autospec=True) as mock_esta_programado:
                with mock.patch("reportes.models.ProgramacionDeReporte.destinatarios", autospec=True) as mock_destinatarios:
                    mock_esta_programado.side_effect = side_effect_esta_programado_para
                    mock_destinatarios.side_effect = side_effect_destinatarios
                    hoy = timezone.now().date()
                    hace_30_dias = hoy + timezone.timedelta(days=-30)
                    ultimo_lunes = hace_30_dias - timezone.timedelta(days=hace_30_dias.weekday())
                    cantidad, fallidos = AdministradorDeProgramacionesDeReportes().enviar_reportes_programados_para(ultimo_lunes)
                    print("Se enviaron %s reportes." % cantidad)
                    print("Fallidos %s." % fallidos)
