{% load reportes_utils %}

<div class="titulo-lista-rvReportes">
    <h3>Reporte de Uso</h3>
</div>
<div class="lista">
    {%  if not reporte_de_uso.datos %}
        <p style="text-align:center">Sin datos</p>
    {% else %}
        <table class="resumen">
            {% for datos_de_supervisor in reporte_de_uso.datos %}
              {% if es_reporte_de_gerente %}
                <tr class="supervisor">
                  <td colspan="4">
                      <a href="{{DOMAIN}}{% url 'editar-vendedor' datos_de_supervisor.supervisor.id %}">
                      Supervisor: {{ datos_de_supervisor.supervisor }}
                  </a></td>
                </tr>
              {% endif %}

              {% if agrupado_por_equipos %}
                {% for datos_de_equipo in datos_de_supervisor.datos %}
                  <tr class="equipo">
                    <td colspan="4">Equipo: {{ datos_de_equipo.equipo }}</td>
                  </tr>
                  <tr class="titulo">
                    <td>&nbsp;&nbsp;VENDEDOR</td>
                    <td>Datos nuevos sin usar</td>
                    <td>Ingresos al panel</td>
                    <td>Tiempo promedio de respuesta</td>
                  </tr>
                  {% for datos_de_vendedor in datos_de_equipo.datos%}
                    <tr>
                      <td><a href="{{DOMAIN}}{% url 'editar-vendedor' datos_de_vendedor.vendedor.id %}"> {{ datos_de_vendedor.vendedor }}</a></td>
                      <td>{{ datos_de_vendedor.datos_nuevos }}</td>
                      <td>{{ datos_de_vendedor.ingresos_al_panel }}</td>
                      <td>{{ datos_de_vendedor.tiempo_de_respuesta|formato_tiempo }}</td>
                    </tr>
                  {% endfor %}
                {% endfor %}
              {% else %}
                <tr class="titulo">
                    <td>&nbsp;&nbsp;VENDEDOR</td>
                    <td>Datos nuevos sin usar</td>
                    <td>Ingresos al panel</td>
                    <td>Tiempo promedio de respuesta</td>
                </tr>

                {% for datos_de_vendedor in datos_de_supervisor.datos %}
                        <tr>
                          <td><a href="{{DOMAIN}}{% url 'editar-vendedor' datos_de_vendedor.vendedor.id %}"> {{ datos_de_vendedor.vendedor }}</a></td>
                          <td>{{ datos_de_vendedor.datos_nuevos }}</td>
                          <td>{{ datos_de_vendedor.ingresos_al_panel }}</td>
                          <td>{{ datos_de_vendedor.tiempo_de_respuesta|formato_tiempo }}</td>
                        </tr>
                {% endfor %}
                <tr><td colspan="4"></td></tr>
              {% endif %}
              <tr><td colspan="4" style="background-color: lightblue"></td></tr>
            {% endfor %}
        </table>
    {% endif %}
</div>
