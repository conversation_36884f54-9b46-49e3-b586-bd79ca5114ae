{% extends "reportes_layout.html" %}
{% block css %}
    {{ block.super }}
    <link href="{{ STATIC_URL }}css/grilla_desde_lista.css" type="text/css" rel="stylesheet"/>
{% endblock %}

{% block js %}
    {{ block.super }}
    <script type="text/javascript">
        var eliminar_programacion_reporte_url = "{% url 'programacion-borrar' %}";
    </script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/csrf_token.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/system_unavailable.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/reportes_generador_automatico.js"></script>
{% endblock %}


{% block reportes_content %}

    <nav class="solapas">
        <a href="{% url 'reportes-online' %}">Reportes</a>
        <a href="{% url 'programacion-lista' %}" class="activo">Programación</a>
    </nav>

    <div id="mails_overlay" style="display:none"></div>

    <div id="confirmar_eliminar" title="Atención" style="display:none;">
        ¿Desea eliminar el generador de reportes?
    </div>
    <div id="listaReportes">
        <div id="botonesTop">
            <a href="{% url 'programacion-nueva' %}">Crear nueva programación</a>
        </div>
        <div class="contenedor-lista">
            <div class="titulo-lista">
                <h3>Resumen de programación</h3>
            </div>
            <div class="lista">
                <table>
                    <tr class="titulo">
                        <td>Tipo
                            <button></button>
                        </td>
                        <td>Frecuencia
                            <button></button>
                        </td>
                        <td>Tipo de Staff
                            <button></button>
                        </td>
                        <td>{{opcion_de_staff}}/Equipos
                            <button></button>
                        </td>
                        <td>Mails
                            <button></button>
                        </td>
                        <td>Editar/Eliminar
                            <button></button>
                        </td>
                    </tr>
                    {% for programacion in programaciones %}
                        <tr>
                            <td>
                                {{ programacion.tipos_de_reportes.all|join:", "}}
                            </td>
                            <td>{{ programacion.frecuencia.descripcion }}</td>
                            <td>{{ programacion.descripcion_de_tipo_de_staff }}</td>

                            {% with reporte_pk=programacion.pk|stringformat:"s" %}
                                <td>
                                    {% with "lista_de_staff_"|add:reporte_pk as id_div_coleccion_completa %}
                                        {% if programacion.selecciono_equipos %}
                                            {% include "popup_lista_staff.html" with coleccion=programacion.staff id_selector=id_div_coleccion_completa  selecciono_equipos=True%}
                                            {% include "visualizador_de_coleccion.html" with tipo_de_elementos=programacion.descripcion_de_tipo_de_staff primer_elemento=programacion.equipos.first.nombre coleccion=programacion.equipos id_div_coleccion_completa=id_div_coleccion_completa %}
                                        {% else %}
                                            {% include "popup_lista_staff.html" with coleccion=programacion.staff id_selector=id_div_coleccion_completa  selecciono_equipos=False%}
                                            {% include "visualizador_de_coleccion.html" with tipo_de_elementos=programacion.descripcion_de_tipo_de_staff primer_elemento=programacion.staff.first.full_name coleccion=programacion.staff id_div_coleccion_completa=id_div_coleccion_completa %}
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    {% with "lista_de_mails_"|add:reporte_pk as id_div_coleccion_completa %}
                                        {% include "popup_mails.html" with mails=programacion.mails.all id_selector=id_div_coleccion_completa %}
                                        {% include "visualizador_de_coleccion.html" with tipo_de_elementos="Mails" primer_elemento=programacion.mails.first.mail coleccion=programacion.mails id_div_coleccion_completa=id_div_coleccion_completa %}
                                    {% endwith %}
                                </td>
                            {% endwith %}

                            <td>
                                <a id="editar-reporte" href="{% url 'programacion-editar' programacion.pk %}">
                                    Editar
                                </a>
                                <span>/</span>
                                <a id="borrar-reporte" href="javascript:void"
                                   onclick="eliminar_programacion_de_reporte('{{ programacion.pk }}')">Eliminar</a>
                            </td>
                        </tr>
                    {% endfor %}
                </table>
            </div>
        </div>
    </div>

{% endblock %}
