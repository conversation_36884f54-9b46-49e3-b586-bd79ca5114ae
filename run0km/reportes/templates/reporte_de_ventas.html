
<div class="titulo-lista-rvReportes">
    <h3>Reporte de Ventas</h3>
</div>
<div class="lista">
    {%  if not reporte_de_ventas.datos %}
        <p style="text-align:center">Sin datos</p>
    {% else %}
        <table>
            {% for datos_de_supervisor in reporte_de_ventas.datos %}
              {% if es_reporte_de_gerente %}
                <tr class="supervisor">
                    <td colspan="5"><a href="{{DOMAIN}}{% url 'editar-vendedor' datos_de_supervisor.supervisor.id %}">
                        Supervisor: {{ datos_de_supervisor.supervisor }}
                    </a></td>
                </tr>
              {% endif %}

              {% if agrupado_por_equipos %}
                {% for datos_de_equipo in datos_de_supervisor.datos %}
                  <tr class="equipo">
                    <td colspan="5">Equipo: {{ datos_de_equipo.equipo }}
                      </a></td>
                  </tr>
                  {% for datos_de_ciclo in datos_de_equipo.datos%}
                    {% include "reporte_de_ventas_datos_de_ciclos.html" %}
                  {% endfor %}
                {% endfor %}
              {% else %}
                  {% for datos_de_ciclo in datos_de_supervisor.datos%}
                    {% include "reporte_de_ventas_datos_de_ciclos.html" %}
                      <tr><td colspan="5"></td></tr>
                  {% endfor %}
              {% endif %}
              <tr><td colspan="5" style="background-color: lightblue"></td></tr>
            {% endfor %}
        </table>
    {% endif %}
</div>
