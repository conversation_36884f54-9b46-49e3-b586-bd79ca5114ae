{% extends "reportes_layout.html" %}

{% block css %}
    {{ block.super }}
    <link href="{{ STATIC_URL }}css/reportes.css" type="text/css" rel="stylesheet"/>
    <link href="{{ STATIC_URL }}css/multiselect.css" type="text/css" rel="stylesheet"/>
{% endblock %}

{% block js %}
    {{ block.super }}
    <title> Resultados de ventas | Reportes</title>
    <script type="text/javascript" src="{{ STATIC_URL }}js/reportes.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/jquery.multi-select.js"></script>

    <script type="text/javascript">
        var opcion_de_staff = '{{opcion_de_staff}}';
        $(function () {
            var inputsDesdeHasta = $("input[name=desde], input[name=hasta]");
            inputsDesdeHasta.datepicker({dateFormat: "yy-mm-dd"});
            $('#equipos').multiSelect();
            $('#' + opcion_de_staff).multiSelect();
            inicializarOpcionSeleccionStaff();
            $('#reporte_de_datos_entregados_del_dia_actual').click(function () {
               inputsDesdeHasta.datepicker("setDate", moment().toDate());

            });
        });
    </script>
{% endblock %}

{% block reportes_content %}
    {% csrf_token %}

    <nav class="solapas">
        <a href="{% url 'reportes-online' %}" class="activo">Reportes</a>
        <a href="{% url 'programacion-lista' %}">Programación</a>
    </nav>

    <div id="contRVProgramacion">
        <form class="filtro-reportes" action="#" method="POST">
            {% csrf_token %}

            <div class="rvProgramacion">
                <h3>Tipo de Reporte</h3>
            </div>
            <div class="filtro-programacion">
                {{ form.tipos_de_reportes.errors }}
                {% for choice in form.tipos_de_reportes %}
                    <label>
                        {{ choice.tag }}
                        {{ choice.choice_label }}
                    </label>
                {% endfor %}
            </div>
            <div class="rvProgramacion">
                <h3>Período:</h3>
            </div>
            <div>
                <div class="container mb-0 px-5 py-2" style=" background-color: #acdaf9;">
                    <div class="align-items-center mb-0 row">
                        <div class="col-sm mb-0">
                            <div class="align-items-center mb-0 row">
                                <label for="id_desde" class="col-form-label col-sm-2"
                                       style="font-size: 11px;">Desde</label>
                                <div class="col-sm-10 mb-0">
                                    <input autocomplete="off" type="text" class="form-control" id="id_desde"
                                           placeholder="Desde" name="desde" value="{{ form.desde.value }}">

                                </div>
                            </div>

                        </div>
                        <div class="col-sm mb-0">
                            <div class="align-items-center mb-0 row">
                                <label for="id_hasta" class="col-form-label col-sm-2"
                                       style=" font-size: 11px;">Hasta</label>
                                <div class="col-sm-10 mb-0">
                                    <input autocomplete="off" type="text" class="form-control"
                                           id="id_hasta"
                                           placeholder="Hasta" name="hasta" value="{{ form.hasta.value }}"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm mb-0">
                            <button id="reporte_de_datos_entregados_del_dia_actual" type="button"
                                    class="btn btn-dark btn-xs" style="background-color: grey;">Dia de Hoy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
                {{ form.hasta.errors }}
                {{ form.desde.errors }}
            <div class="vendedores">
                <h3>Seleccionar {{ opcion_de_staff }}/equipos</h3>
            </div>
            <div class="filtro-vendedores">
                {{ form.tipo_de_staff.errors }}
                {% for radio in form.tipo_de_staff %}
                    <label>
                        {{ radio.tag }}
                        {{ radio.choice_label }}
                    </label>
                {% endfor %}
            </div>

            <div id="contVendedoresDisponibles">
                <div class="vendedoresDisponibles"><h4>Disponibles</h4></div>
                <div class="vendedoresDisponibles"><h4>Seleccionados</h4></div>
            </div>

            <div id="multiple_equipos">
                {{ form.equipos.errors }}
                {{ form.equipos }}
            </div>

            <div id="multiple_{{ opcion_de_staff }}">
                {{ form.staff_a_cargo.errors }}
                {{ form.staff_a_cargo }}
            </div>
            {{ form.non_field_errors }}
            <button id="generar-reporte-button" class="reporte-button center">Generar Reportes</button>
        </form>
        <div class="contenedor-lista-rvReportes">

            {% if reporte_de_entrega %}
                {% include "reporte_de_entrega.html" %}
            {% endif %}

            {% if reporte_de_ventas %}
                {% include "reporte_de_ventas.html" %}
            {% endif %}

            {% if reporte_de_uso %}
                {% include "reporte_de_uso.html" %}
            {% endif %}

        </div>
    </div>
{% endblock %}
