# coding=utf-8

from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone

from equipos.models import Equipo
from core.date_helper import DatetimeHelper
from prospectos.models import Proveedor
from reportes.models import TipoDeReporte, TipoDeStaff, FrecuenciaDeProgramacion, \
    ProgramacionDeReporteParaGerentes, ProgramacionDeReporteParaSupervisores
from reportes.reporte_de_distribucion import RangoDeFechas, FiltroDeProspectosPorFechas
from vendedores.models import Vendedor


class BaseReportesForm(forms.Form):
    desde = forms.DateField(widget=forms.TextInput(attrs={'autocomplete': 'off'}),
                            input_formats=('%Y-%m-%d',),
                            initial=DatetimeHelper().first_day_of_current_month().strftime('%Y-%m-%d')
                            )
    hasta = forms.DateField(widget=forms.TextInput(attrs={'autocomplete': 'off'}),
                            input_formats=('%Y-%m-%d',),
                            initial=DatetimeHelper().last_day_of_current_month().strftime('%Y-%m-%d')
                            )
    tipo_de_staff = forms.ChoiceField(widget=forms.RadioSelect(),
                                      choices=list())
    equipos = forms.ModelMultipleChoiceField(widget=forms.SelectMultiple(attrs={'id': 'equipos'}),
                                             queryset=Equipo.objects.none(), required=False)
    tipos_de_reportes = forms.ModelMultipleChoiceField(widget=forms.CheckboxSelectMultiple(),
                                                       queryset=TipoDeReporte.tipos_de_reporte_validos(),
                                                       label='Tipo de Reporte',
                                                       initial=TipoDeReporte.tipos_de_reporte_validos())

    def __init__(self, *args, **kwargs):
        super(BaseReportesForm, self).__init__(*args, **kwargs)
        self._configurar_tipo_de_staff()

    def clean_equipos(self):
        tipo_de_staff = self.cleaned_data.get('tipo_de_staff')
        equipos = self.cleaned_data.get('equipos')
        if tipo_de_staff == TipoDeStaff.EQUIPOS and not equipos:
            raise ValidationError('Debe seleccionar al menos un equipo')
        return equipos

    def clean_tipo_de_staff(self):
        tipo_de_staff = self.cleaned_data.get('tipo_de_staff')
        return int(tipo_de_staff)

    def clean_hasta(self):
        desde = self.cleaned_data.get('desde', None)
        hasta = self.cleaned_data.get('hasta', None)
        if hasta and desde:
            if not (desde.month == hasta.month and desde.year == hasta.year):
                raise ValidationError('El mes de finalización debe ser el mismo que el de inicio.')
            if desde <= hasta:
                return hasta
        raise ValidationError('La fecha de finalización debe ser menor a la de inicio.')

    def _configurar_tipo_de_staff(self):
        raise NotImplementedError("Subclass Responsibility")


class ReporteParaGerentesForm(BaseReportesForm):
    staff_a_cargo = forms.ModelMultipleChoiceField(widget=forms.SelectMultiple(attrs={'id': 'supervisores'}),
                                                   queryset=Vendedor.objects.none(), required=False,
                                                   initial=Vendedor.objects.all())

    def __init__(self, gerente, *args, **kwargs):
        super(ReporteParaGerentesForm, self).__init__(*args, **kwargs)
        self.fields['staff_a_cargo'].queryset = gerente.supervisores()
        self.fields['equipos'].queryset = gerente.obtener_equipos()

    def clean_staff_a_cargo(self):
        tipo_de_staff = self.cleaned_data.get('tipo_de_staff')
        supervisores = self.cleaned_data.get('staff_a_cargo')
        if tipo_de_staff == TipoDeStaff.SUPERVISORES and not supervisores:
            raise ValidationError('Debe seleccionar al menos un supervisor')
        return supervisores

    def _configurar_tipo_de_staff(self):
        self.fields['tipo_de_staff'].choices = TipoDeStaff.OPCIONES_PARA_GERENTES
        self.fields['tipo_de_staff'].initial = TipoDeStaff.SUPERVISORES


class ReporteParaSupervisoresForm(BaseReportesForm):
    staff_a_cargo = forms.ModelMultipleChoiceField(widget=forms.SelectMultiple(attrs={'id': 'vendedores'}),
                                                   queryset=Vendedor.objects.none(), required=False,
                                                   initial=Vendedor.objects.all())

    def __init__(self, supervisor, *args, **kwargs):
        super(ReporteParaSupervisoresForm, self).__init__(*args, **kwargs)
        self.fields['staff_a_cargo'].queryset = supervisor.vendedores.all()
        self.fields['equipos'].queryset = supervisor.obtener_equipos()

    def clean_staff_a_cargo(self):
        tipo_de_staff = self.cleaned_data.get('tipo_de_staff')
        vendedores = self.cleaned_data.get('staff_a_cargo')
        if tipo_de_staff == TipoDeStaff.VENDEDORES and not vendedores:
            raise ValidationError('Debe seleccionar al menos un vendedor')
        return vendedores

    def _configurar_tipo_de_staff(self):
        self.fields['tipo_de_staff'].choices = TipoDeStaff.OPCIONES_PARA_SUPERVISORES
        self.fields['tipo_de_staff'].initial = TipoDeStaff.VENDEDORES


class BaseProgramacionDeReporteForm(forms.Form):
    _SEPARADOR_DE_MAIL = ";"

    tipos_de_reportes = forms.ModelMultipleChoiceField(widget=forms.CheckboxSelectMultiple(),
                                                       queryset=TipoDeReporte.tipos_de_reporte_validos(),
                                                       label='Tipo de Reporte',
                                                       initial=TipoDeReporte.tipos_de_reporte_validos())
    tipo_de_frecuencia = forms.ChoiceField(widget=forms.RadioSelect(),
                                           choices=list(FrecuenciaDeProgramacion.OPCIONES.items()),
                                           initial=FrecuenciaDeProgramacion.SEMANAL)
    tipo_de_staff = forms.ChoiceField(widget=forms.RadioSelect(),
                                      choices=TipoDeStaff.OPCIONES_PARA_GERENTES,
                                      initial=TipoDeStaff.EQUIPOS)
    equipos = forms.ModelMultipleChoiceField(widget=forms.SelectMultiple(attrs={'id': 'equipos'}),
                                             queryset=Equipo.objects.none(), required=False)
    mails = forms.CharField(max_length=256, widget=forms.TextInput(attrs={'placeholder': '<EMAIL>;'}))

    def __init__(self, programador, programacion=None, *args, **kwargs):
        super(BaseProgramacionDeReporteForm, self).__init__(*args, **kwargs)
        self.fields['staff_a_cargo'].queryset = programador.staff_a_cargo()
        self.fields['equipos'].queryset = programador.obtener_equipos()
        self.fields['tipo_de_staff'].choices = self._tipo_de_reporte().opciones_de_tipo_de_staff()
        if programacion:
            self._inicializar_con(programacion)

    def _inicializar_con(self, programacion):
        self.fields['tipos_de_reportes'].initial = programacion.tipos_de_reportes.all()
        self.fields['tipo_de_frecuencia'].initial = programacion.tipo_de_frecuencia
        self.fields['tipo_de_staff'].initial = programacion.tipo_de_staff
        self.fields['equipos'].initial = programacion.equipos.all()
        self.fields['mails'].initial = self._formatear_mails(programacion.mails)
        self.fields['staff_a_cargo'].initial = programacion.staff_a_cargo.all()

    def _formatear_mails(self, mails):
        mails_string = [mail.mail for mail in mails.all()]
        return self._SEPARADOR_DE_MAIL.join(mails_string)

    @classmethod
    def _get_queryset_staff_a_cargo(cls, programador):
        raise NotImplementedError('Subclass Responsibility')

    @classmethod
    def _tipo_de_reporte(cls):
        raise NotImplementedError('Subclass Responsibility')

    @classmethod
    def _tipo_de_staff_a_cargo(cls):
        raise NotImplementedError('Subclass Responsibility')

    def clean_mails(self):
        from prospectos.aplicacion.operaciones_oportunidades import CrearNuevaProgramacionParaSupervisor
        mails_string = self.cleaned_data.get('mails')
        mails = CrearNuevaProgramacionParaSupervisor().mails_string_a_modelos(mails_string)
        if not mails:
            raise ValidationError('Debe ingresar al menos un correo electronico')
        else:
            return mails

    def clean_equipos(self):
        tipo_de_staff = self.cleaned_data.get('tipo_de_staff')
        equipos = self.cleaned_data.get('equipos')
        if tipo_de_staff == TipoDeStaff.EQUIPOS and not equipos:
            raise ValidationError('Debe seleccionar al menos un equipo')
        return equipos

    def clean_tipo_de_staff(self):
        tipo_de_staff = self.cleaned_data.get('tipo_de_staff')
        return int(tipo_de_staff)

    def clean_staff_a_cargo(self):
        tipo_de_staff = self.cleaned_data.get('tipo_de_staff')
        staff_a_cargo = self.cleaned_data.get('staff_a_cargo')
        if tipo_de_staff == self._tipo_de_reporte().tipo_staff_seleccionado() and not staff_a_cargo:
            raise ValidationError('Debe seleccionar al menos un %s' % self._tipo_de_staff_a_cargo())

        return staff_a_cargo


class ProgramacionDeReporteParaGerenteForm(BaseProgramacionDeReporteForm):
    staff_a_cargo = forms.ModelMultipleChoiceField(widget=forms.SelectMultiple(attrs={'id': 'supervisores'}),
                                                   queryset=Vendedor.objects.none(), required=False)

    @classmethod
    def _get_queryset_staff_a_cargo(cls, programador):
        return programador.supervisores()

    @classmethod
    def _tipo_de_reporte(cls):
        return ProgramacionDeReporteParaGerentes

    @classmethod
    def _tipo_de_staff_a_cargo(cls):
        return 'supervisor'


class ProgramacionDeReporteParaSupervisorForm(BaseProgramacionDeReporteForm):
    staff_a_cargo = forms.ModelMultipleChoiceField(widget=forms.SelectMultiple(attrs={'id': 'vendedores'}),
                                                   queryset=Vendedor.objects.none(), required=False)

    @classmethod
    def _get_queryset_staff_a_cargo(cls, programador):
        return programador.vendedores.all()

    @classmethod
    def _tipo_de_reporte(cls):
        return ProgramacionDeReporteParaSupervisores

    @classmethod
    def _tipo_de_staff_a_cargo(cls):
        return 'vendedor'


class FiltrosDeReporteDeDistribucionForm(forms.Form):
    rango_de_fechas = forms.TypedChoiceField(
        choices=[(rango.identificador(), rango.label()) for rango in RangoDeFechas.opciones()],
        widget=forms.RadioSelect(),
        initial=RangoDeFechas.ultimos_siete_dias().identificador(),
        coerce=RangoDeFechas.nuevo_desde_identificador
    )
    proveedores = forms.ModelMultipleChoiceField(
        queryset=Proveedor.objects.none(), required=False,
        widget=forms.SelectMultiple(attrs={'size': '8', 'class': 'form-control'}))
    marcas = forms.MultipleChoiceField(required=False,
                                       widget=forms.SelectMultiple(attrs={'size': '8', 'class': 'form-control'}))
    filtro_de_prospecto_por_fecha = forms.BooleanField(initial=True, required=False)

    def __init__(self, proveedores, marcas=None, *args, **kwargs):
        super(FiltrosDeReporteDeDistribucionForm, self).__init__(*args, **kwargs)
        field_proveedores = self.fields['proveedores']
        field_proveedores.queryset = proveedores
        field_proveedores.initial = proveedores
        if marcas is None:
            data = kwargs.get('data')
            marcas = data.getlist('marcas')
        marca_field = self.fields['marcas']
        marca_field.choices = [self._opcion_de_marca(marca) for marca in marcas]
        marca_field.initial = [marca.title() for marca in marcas]

    def clean_filtro_de_prospecto_por_fecha(self):
        filtro = self.cleaned_data.get('filtro_de_prospecto_por_fecha', True)
        if filtro:
            return FiltroDeProspectosPorFechas.por_fecha_de_creacion()
        else:
            return FiltroDeProspectosPorFechas.por_fecha_de_asginacion()

    def tipo_de_reporte_por_categoria(self):
        filtro = self.data.get('filtro_de_prospecto_por_fecha', True)
        if filtro:
            return {'titulo': 'Ingreso de datos', 'detalle': 'datos que ingresaron al sistema'}
        else:
            return {'titulo': 'Asignación de datos', 'detalle': 'datos asignados'}

    def actualizar_marcas(self, marcas):
        marca_field = self.fields['marcas']
        marca_field.choices = [self._opcion_de_marca(marca) for marca in marcas]

    def _opcion_de_marca(self, marca):
        if marca == '':
            return '', 'Marca Blanca'
        else:
            return marca.title(), marca.title()


class FiltrosDeReporteDeLlamadasForm(forms.Form):
    FORMATO_FECHA = "%Y-%m-%d"
    NO_FILTRAR_POR_DURACION = 0
    DURACION_MAYOR_A_UN_MINUTO = 60
    DURACION_MAYOR_A_CINCO_MINUTOS = 300
    DURACION_MAYOR_A_DIEZ_MINUTOS = 600

    fecha_desde = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))
    fecha_hasta = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))
    por_tiempo = forms.BooleanField(required=False)
    solo_vendidos = forms.BooleanField(required=False)
    vendedores = forms.ModelMultipleChoiceField(
        queryset=Vendedor.objects.none(), required=False,
        widget=forms.SelectMultiple(attrs={'size': '8', 'class': 'form-control'}))
    duracion_mayor_a = forms.ChoiceField(
        choices=[(NO_FILTRAR_POR_DURACION, 'No filtrar por duración'),
                 (DURACION_MAYOR_A_UN_MINUTO, 'Duración mayor a 1 minuto'),
                 (DURACION_MAYOR_A_CINCO_MINUTOS, 'Duración mayor a 5 minutos'),
                 (DURACION_MAYOR_A_DIEZ_MINUTOS, 'Duración mayor a 10 minutos')],
        required=True, widget=forms.RadioSelect())

    def __init__(self, vendedores, *args, **kwargs):
        super(FiltrosDeReporteDeLlamadasForm, self).__init__(*args, **kwargs)
        field_vendedores = self.fields['vendedores']
        field_vendedores.queryset = vendedores
        field_vendedores.initial = vendedores

    def clean_fecha_hasta(self):
        fecha_hasta = self.cleaned_data.get('fecha_hasta')
        fecha_desde = self.cleaned_data.get('fecha_desde')
        if fecha_desde > fecha_hasta:
            raise ValidationError('La fecha de inicio debe ser anterior a la fecha de finalización')
        return fecha_hasta

    def clean_solo_vendidos(self):
        solo_vendidos = self.cleaned_data.get('solo_vendidos', False)
        return solo_vendidos

    def clean_por_tiempo(self):
        por_tiempo = self.cleaned_data.get('por_tiempo', False)
        return por_tiempo
