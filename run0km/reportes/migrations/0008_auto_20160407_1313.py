# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('reportes', '0007_auto_20160314_1951'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='programaciondereporte',
            name='equipos',
            field=models.ManyToManyField(default=None, to='equipos.Equipo', blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='programaciondereporte',
            name='supervisores',
            field=models.ManyToManyField(default=None, related_name='programaciones_de_gerente', to='vendedores.Vendedor', blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='programaciondereporte',
            name='vendedores',
            field=models.ManyToManyField(default=None, related_name='programaciones_de_supervisor', to='vendedores.Vendedor', blank=True),
            preserve_default=True,
        ),
    ]
