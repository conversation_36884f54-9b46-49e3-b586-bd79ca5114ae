# -*- coding: utf-8 -*-


from django.db import models, migrations


def crear_tipo_de_reportes(apps, schema_editor):
    TipoDeReporte = apps.get_model("reportes", "TipoDeReporte")
    TipoDeReporte(nombre='Entrega').save()
    TipoDeReporte(nombre='Ventas').save()
    TipoDeReporte(nombre='Uso').save()


class Migration(migrations.Migration):

    dependencies = [
        ('reportes', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(crear_tipo_de_reportes),
    ]
