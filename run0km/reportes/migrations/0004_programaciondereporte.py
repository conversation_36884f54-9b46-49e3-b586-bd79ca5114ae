# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0016_auto_20160217_1425'),
        ('equipos', '0001_initial'),
        ('concesionarias', '0007_concesionaria_sms_habilitado'),
        ('reportes', '0003_auto_20160301_1912'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProgramacionDeReporte',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('tipo_de_frecuencia', models.CharField(default=b'S', max_length=1, choices=[(b'S', b'Semanal'), (b'M', b'Mensual'), (b'C', b'Cantidad')])),
                ('frecuencia_en_dias', models.PositiveIntegerField(default=None, null=True, blank=True)),
                ('fecha_de_envio', models.DateField(null=True, blank=True)),
                ('selecciono_equipos', models.BooleanField(default=False)),
                ('concesionaria', models.ForeignKey(related_name='programaciones', to='concesionarias.Concesionaria')),
                ('equipos', models.ManyToManyField(default=None, to='equipos.Equipo', null=True, blank=True)),
                ('mails', models.ManyToManyField(to='reportes.Mail')),
                ('supervisores', models.ManyToManyField(default=None, to='vendedores.Vendedor', null=True, blank=True)),
                ('tipos_de_reportes', models.ManyToManyField(to='reportes.TipoDeReporte')),
            ],
            options={
                'verbose_name': 'generador automatico de reporte',
                'verbose_name_plural': 'generador automatico de reportes',
            },
            bases=(models.Model,),
        ),
    ]
