# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='HttpConnectionsLog',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('when', models.DateTimeField(auto_now_add=True, verbose_name='fecha y hora')),
                ('provider_name', models.CharField(max_length=100, verbose_name='nombre del proveedor')),
                ('request_url', models.URLField(verbose_name=b'URL')),
                ('request_method', models.CharField(max_length=20, verbose_name='m\xe9todo')),
                ('request_body', models.TextField(verbose_name='cuerpo de la petici\xf3n')),
                ('request_error', models.TextField(verbose_name='error de la petici\xf3n', blank=True)),
                ('response_status_code', models.PositiveIntegerField(null=True, verbose_name='c\xf3digo de estado de respuesta', blank=True)),
                ('response_headers', models.TextField(verbose_name='encabezado de la respuesta', blank=True)),
                ('response_body', models.TextField(verbose_name='cuerpo de la respuesta', blank=True)),
            ],
            options={
                'verbose_name': 'Log de conexiones HTTP',
                'verbose_name_plural': 'Logs de conexiones HTTP',
            },
            bases=(models.Model,),
        ),
    ]
