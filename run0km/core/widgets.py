from django.forms.widgets import NumberInput
from django.utils.safestring import mark_safe


class PorcentajeWidget(NumberInput):
    def render(self, name, value, attrs=None):
        number_rendered = super(PorcentajeWidget, self).render(name, value, attrs)
        rendered = self._porcentaje(number_rendered)
        return mark_safe(rendered)

    def _porcentaje(self, widget_rendered):
        return '<span class ="widget-percentage">%s</span>' % widget_rendered

    class Media:
        css = {
            'all': ('css/porcentaje.css',)
        }