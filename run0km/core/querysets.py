from django.db import models


class DeliveryQuerySet(models.QuerySet):

    def para_id(self, identificador):
        return self.get(id=identificador)

    def para_id_o_none(self, identificador):
        try:
            return self.para_id(identificador)
        except self.model.DoesNotExist:
            return None

    def ids(self):
        return self.values_list('id', flat=True)

    def con_ids(self, ids):
        return self.filter(id__in=ids)

    def consulta_limpia(self):
        ids = list(self.ids())
        return self.model.objects.con_ids(ids)
