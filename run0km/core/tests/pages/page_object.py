class PageObject(object):
    def __init__(self, client, last_response=None):
        self._client = client
        self._last_response = last_response

    def last_response(self):
        if self._last_response is None:
            self.load()
        return self._last_response

    def load(self):
        self._last_response = self._client.get(self.url())
        if self._last_response.status_code != 200:
            raise RuntimeError('Page not available: tried to load page and failed')
        if not self.is_page_content_correct():
            raise RuntimeError('Page is not the {0} page'.format(self.name()))

    def is_page_content_correct(self):
        return True

    def content(self):
        return self.last_response().content.decode('utf-8')

    def has_content(self, text):
        return text in self.content()

    def url(self):
        raise NotImplementedError('Subclass responsibility')

    def name(self):
        return self.__class__.__name__

    def _post(self, parameters):
        self._last_response = self._client.post(self.url(), parameters)
        return self._last_response
