

class Validator(object):
    def __init__(self, testcase):
        super(Validator, self).__init__()
        self._testcase = testcase

    @classmethod
    def new_for(cls, testcase):
        return cls(testcase)

    def assertEqual(self, first, second, msg=None):
        self._testcase.assertEqual(first, second, msg=msg)

    def assertNotEqual(self, first, second, msg=None):
        self._testcase.assertNotEqual(first, second, msg=msg)

    def assertTrue(self, expr, msg=None):
        self._testcase.assertTrue(expr, msg=msg)

    def assertFalse(self, expr, msg=None):
        self._testcase.assertFalse(expr, msg=msg)

    def assertIn(self, member, container, msg=None):
        self._testcase.assertIn(member, container, msg)
