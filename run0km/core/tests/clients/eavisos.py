from core.tests.clients.proveedor_externo import ProveedorExterno
from layers.application.commands.eavisos import RecibirMensajeDePublicacionEAvisosComando


class ProveedorEAvisos(ProveedorExterno):

    def nuevo_mensaje_para_datos(self, datos):
        response = self._post_json(datos)
        return response

    def nuevo_mensaje(self, id_propuesta, user_crm, id_tran, id_pregunta, msg, fecha,
                      id_aviso, link, id_canal="mercadolibre"):
        fecha_string = fecha.strftime(RecibirMensajeDePublicacionEAvisosComando.FORMATO_FECHA_HORA)
        datos = self.datos(id_propuesta, user_crm, id_tran, id_pregunta, msg, fecha_string, id_aviso, link, id_canal)
        response = self.nuevo_mensaje_para_datos(datos)
        return response

    def datos(self, id_propuesta, user_crm, id_tran, id_pregunta, msg, fecha_string, id_aviso, link, id_canal="mercadolibre"):
        return {
            "id_canal": id_canal,
            "id_campania": "",
            "fecha": fecha_string,
            "user_crm": user_crm,
            "id_tran": id_tran,
            "msg": msg,
            "direccion": "PREGUNTA",
            "tiene_respuesta": True,
            "id_pregunta": id_pregunta,
            "id_propuesta": id_propuesta,
            "id_padre": None,
            "id_aviso": id_aviso,
            "link": link,
            "phone": "",
            "whatsapp": "",
            "email": "",
            "name": "",
            "allowed_answers": 1,
        }

    def _nombre_de_endpoint(self):
        return 'eavisos_recibir_mensaje'

    # def _post(self, datos):
    #     response = self._http_client.post(
    #         self._url(), datos, HTTP_AUTHORIZATION='Token %s' % self._token_key, content_type='application/json')
    #     return response
