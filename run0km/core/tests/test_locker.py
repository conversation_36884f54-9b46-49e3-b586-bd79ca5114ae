from django.test import TestCase

from core.locker.errors import ResourceLockedError, ResourcesError
from core.locker.mem_locker import Locker


class TestLocker(TestCase):
    def setUp(self):
        self._locker = Locker.new_for_group('test')
        self._resource_one = 'a-resource-one'
        self._resource_two = 'a-resource-two'
        self._the_method_was_called = False

    def tearDown(self):
        self._locker.unlock_resource(self._resource_one)
        self._locker.unlock_resource(self._resource_two)
        self._locker.unlock_resource([self._resource_one, self._resource_two])

    def test_do_locking_when_a_resource_is_locked_raises_an_exception(self):
        # Given
        self._locker.lock_resource(self._resource_one)

        # When / Then
        self.assertRaises(
            ResourceLockedError,
            self._locker.do_locking, resource=self._resource_one, a_function=self._a_method, arguments=[])
        self.assertTrue(self._locker.is_locked(self._resource_one))
        self.assertFalse(self._the_method_was_called)

    def test_do_locking_when_a_resource_is_unlocked_the_method_is_evaluated(self):
        # When
        self._locker.do_locking(resource=self._resource_one, a_function=self._a_method, arguments=[])

        # Then
        self.assertTrue(self._the_method_was_called)
        self.assertFalse(self._locker.is_locked(self._resource_one))

    def test_do_locking_when_the_resource_is_a_collection_and_it_is_locked_raises_an_exception(self):
        # Given
        self._locker.lock_resource([self._resource_one, self._resource_two])

        # When / Then
        self.assertRaises(
            ResourceLockedError,
            self._locker.do_locking,
            resource=[self._resource_one, self._resource_two],
            a_function=self._a_method, arguments=[])
        self.assertTrue(self._locker.is_locked([self._resource_one, self._resource_two]))
        self.assertFalse(self._locker.is_locked(self._resource_one))
        self.assertFalse(self._locker.is_locked(self._resource_two))
        self.assertFalse(self._the_method_was_called)

    def test_do_locking_when_the_resource_is_a_collection_and_it_is_unlocked_the_method_is_evaluated(self):
        # When
        self._locker.do_locking(
            resource=[self._resource_one, self._resource_two], a_function=self._a_method, arguments=[])

        # Then
        self.assertTrue(self._the_method_was_called)
        self.assertFalse(self._locker.is_locked([self._resource_one, self._resource_two]))
        self.assertFalse(self._locker.is_locked(self._resource_one))
        self.assertFalse(self._locker.is_locked(self._resource_two))

    def test_do_locking_when_the_resource_is_a_collection_and_not_all_its_items_are_locked_the_method_is_evaluated(
            self):
        # Given
        self._locker.lock_resource(self._resource_two)

        # When
        self._locker.do_locking(
            resource=[self._resource_one, self._resource_two], a_function=self._a_method, arguments=[])

        # Then
        self.assertFalse(self._locker.is_locked(self._resource_one))
        self.assertTrue(self._locker.is_locked(self._resource_two))
        self.assertFalse(self._locker.is_locked([self._resource_one, self._resource_two]))
        self.assertTrue(self._the_method_was_called)

    def test_do_locking_each_when_any_resource_is_locked_raises_an_exception(self):
        # Given
        self._locker.lock_resource(self._resource_two)

        # When / Then
        self.assertRaises(
            ResourceLockedError,
            self._locker.do_locking_each,
            resources=[self._resource_one, self._resource_two],
            a_function=self._a_method, arguments=[])

        self.assertFalse(self._locker.is_locked(self._resource_one))
        self.assertTrue(self._locker.is_locked(self._resource_two))
        self.assertFalse(self._locker.is_locked([self._resource_one, self._resource_two]))
        self.assertFalse(self._the_method_was_called)

    def test_do_locking_each_when_all_resources_are_unlocked_the_method_is_evaluated(self):
        # When
        self._locker.do_locking_each(
            resources=[self._resource_one, self._resource_two], a_function=self._a_method, arguments=[])

        # Then
        self.assertFalse(self._locker.is_locked(self._resource_one))
        self.assertFalse(self._locker.is_locked(self._resource_two))
        self.assertFalse(self._locker.is_locked([self._resource_one, self._resource_two]))
        self.assertTrue(self._the_method_was_called)

    def test_do_locking_each_when_resources_is_empty_raises_an_exception(self):
        self.assertRaisesMessage(
            ResourcesError, 'The resources cannot be empty',
            self._locker.do_locking_each, resources=[], a_function=self._a_method, arguments=[])

    def _a_method(self):
        self._the_method_was_called = True