# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-06-26 23:31


from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Sistema',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=50, unique=True)),
                ('cantidad_maxima_de_horas_sin_actividad', models.IntegerField(default=24, help_text='Cantidad m\xe1xima de horas sin actividad a tener en cuenta en pedidos.')),
                ('cantidad_maxima_de_prospectos_nuevos', models.IntegerField(default=5, help_text='Cantidad l\xedmite de prospectos nuevos a entregar en pedidos')),
                ('cantidad_maxima_de_prospectos_diarios', models.IntegerField(default=24, help_text='Cantidad m\xe1xima de prospectos diarios a entregar en pedidos')),
            ],
        ),
    ]
