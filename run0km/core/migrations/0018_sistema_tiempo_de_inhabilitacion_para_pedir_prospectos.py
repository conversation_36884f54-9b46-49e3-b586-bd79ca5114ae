# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2024-12-18 15:45
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0017_sistema_tiempo_de_antiguedad_minimo_para_prospectos_disponibles'),
    ]

    operations = [
        migrations.AddField(
            model_name='sistema',
            name='tiempo_de_inhabilitacion_para_pedir_prospectos',
            field=models.PositiveSmallIntegerField(default=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Cantidad de minutos de inhabilitación para pedir otro prospecto'),
        ),
    ]
