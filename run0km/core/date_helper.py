import calendar
import datetime
from datetime import time

from dateutil.relativedelta import relativedelta
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from core.support import make_aware_when_is_naive


class DatetimeHelper(object):

    def now(self):
        return self.to_localtime(timezone.now())

    def to_localtime(self, a_datetime):
        an_aware_datetime = make_aware_when_is_naive(a_datetime)
        return timezone.localtime(an_aware_datetime)

    def tomorrow(self):
        return self.now() + datetime.timedelta(days=1)

    def yesterday(self):
        return self.now() - datetime.timedelta(days=1)

    def last_day_of_current_month(self):
        first_day_of_next_month = (self.now() + relativedelta(months=1)).replace(day=1)
        last_day_of_current_month = first_day_of_next_month - datetime.timedelta(days=1)
        return last_day_of_current_month

    def last_day_of_month_in_date(self, a_datetime):
        monthrange = calendar.monthrange(a_datetime.year, a_datetime.month)
        return a_datetime.replace(day=monthrange[1])

    def first_day_of_current_month(self):
        return self.now().replace(day=1)

    def as_start_of_day(self, a_datetime):
        return timezone.make_aware(datetime.datetime.combine(a_datetime, time.min))

    def as_end_of_day(self, a_datetime):
        return timezone.make_aware(datetime.datetime.combine(a_datetime, time.max))

    def range_for(self, month, year):
        start = datetime.datetime(year=int(year), month=int(month), day=1, tzinfo=timezone.get_current_timezone())
        end = start + relativedelta(months=1)
        return start, end

    def months_names(self):
        return [_(calendar.month_name[month_number]) for month_number in range(1, 13)]

    def combine(self, a_date, a_time):
        a_datetime = datetime.datetime.combine(a_date, a_time)
        return make_aware_when_is_naive(a_datetime)

    def today_at(self, hour, minute, second):
        return self.combine(self.now(), datetime.time(hour, minute, second))

    def datetime(self, year, month, day):
        a_datetime = datetime.datetime(year, month, day)
        return make_aware_when_is_naive(a_datetime)

    def from_timestamp(self, timestamp, timezone):
        a_datetime = datetime.datetime.fromtimestamp(timestamp, timezone)
        return make_aware_when_is_naive(a_datetime, timezone)
