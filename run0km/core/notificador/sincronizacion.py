# coding=utf-8
import inspect

from django.conf import settings
from django.db import transaction
from django.utils.module_loading import import_string

from prospectos.models import Prospecto


class Sincronizacion(object):
    """
        Los dependientes deben ser polimorficos con respecto a los mensajes de ReceptorDeNotificaciones
    """

    def __init__(self):
        self._dependientes = []
        self._inicializar_dependientes()

    def evaluar(self, metodo_decorado, args, kwargs):
        debe_sincronizar = kwargs.pop('debe_sincronizar', True)
        if debe_sincronizar:
            respuesta = self._evaluar_y_sincronizar(metodo_decorado, args, kwargs)
        else:
            respuesta = self._evaluar_metodo(metodo_decorado, args, kwargs)
        return respuesta

    def _evaluar_y_sincronizar(self, metodo_decorado, args, kwargs):
        raise NotImplementedError('Subclass responsibility')

    def _evaluar_metodo(self, metodo_decorado, args, kwargs):
        return metodo_decorado(*args, **kwargs)

    def _obtener_parametro(self, nombre_argumento, metodo_decorado, args, kwargs, si_no_existe_hacer=lambda: None):
        if nombre_argumento in kwargs:
            argumento = kwargs.get(nombre_argumento, None)
        else:
            argumento = self._parametro_desde_args(nombre_argumento=nombre_argumento,
                                                   metodo_decorado=metodo_decorado,
                                                   args=args)

        if argumento is None:
            return si_no_existe_hacer()
        else:
            return argumento

    def _parametro_desde_args(self, nombre_argumento, metodo_decorado, args):
        arguments_spec = inspect.getfullargspec(metodo_decorado)
        try:
            indice = arguments_spec.args.index(nombre_argumento)
        except ValueError:
            return None
        else:
            return args[indice]

    def tiene_parametro_de_nombre(self, nombre_argumento, metodo_decorado):
        arguments_spec = inspect.getfullargspec(metodo_decorado)
        return nombre_argumento in arguments_spec.args

    def _obtener_prospectos_desde(self, metodo_decorado, args, kwargs):
        es_en_masa = self.tiene_parametro_de_nombre('prospectos', metodo_decorado)
        if es_en_masa:
            prospectos = self._obtener_parametro(nombre_argumento='prospectos', metodo_decorado=metodo_decorado,
                                                 args=args, kwargs=kwargs)
            ids_prospectos = list(prospectos.values_list('id', flat=True))
            prospectos = Prospecto.objects.filter(id__in=ids_prospectos)
        else:
            prospecto = self._obtener_parametro(nombre_argumento='prospecto', metodo_decorado=metodo_decorado,
                                                args=args, kwargs=kwargs)
            prospectos = [prospecto]
        return prospectos

    def _obtener_conversacion_desde(self, metodo_decorado, args, kwargs):
        return self._obtener_parametro(nombre_argumento='self', metodo_decorado=metodo_decorado,
                                       args=args, kwargs=kwargs)

    def _inicializar_dependientes(self):
        for dependiente_path in settings.SINCRONIZACIONES:
            dependiente = self._crear_dependiente_desde(dependiente_path)
            self._dependientes.append(dependiente)

    def _crear_dependiente_desde(self, dependiente_path):
        try:
            dependiente_class = import_string(dependiente_path)
        except ImportError as exc:
            raise ValueError('La configuracion del dependiente %s no es correcta. %s' % (dependiente_path, str(exc)))
        return dependiente_class()

    def _con_dependientes_hacer(self, funcion_lambda):
        for dependiente in self._dependientes:
            funcion_lambda(dependiente)


class SincronizacionProspectoModificado(Sincronizacion):
    def _evaluar_y_sincronizar(self, metodo_decorado, args, kwargs):
        prospectos = self._obtener_prospectos_desde(metodo_decorado=metodo_decorado, args=args, kwargs=kwargs)
        if len(prospectos) == 1:
            self._notificar_modificacion_de_prospecto(prospectos[0])
        else:
            self._notificar_modificaciones_de_prospectos(prospectos)
        respuesta = metodo_decorado(*args, **kwargs)
        return respuesta

    def _notificar_modificaciones_de_prospectos(self, prospectos):
        for prospecto in prospectos:
            self._notificar_modificacion_de_prospecto(prospecto)

    def _notificar_modificacion_de_prospecto(self, prospecto):
        if prospecto:
            transaction.on_commit(
                lambda: self._con_dependientes_hacer(lambda dependiente: dependiente.prospecto_modificado(prospecto)))


class SincronizacionConversacionModificada(Sincronizacion):
    def _evaluar_y_sincronizar(self, metodo_decorado, args, kwargs):
        conversacion = self._obtener_conversacion_desde(metodo_decorado=metodo_decorado, args=args, kwargs=kwargs)

        if conversacion is not None and not conversacion.es_de_chat():
            self._notificar_modificacion_de_conversacion(conversacion)

        respuesta = metodo_decorado(*args, **kwargs)
        return respuesta

    def _notificar_modificaciones_de_conversaciones(self, conversaciones):
        for conversacion in conversaciones:
            self._notificar_modificacion_de_conversacion(conversacion)

    def _notificar_modificacion_de_conversacion(self, conversacion):
        if conversacion:
            transaction.on_commit(
                lambda: self._con_dependientes_hacer(
                    lambda dependiente: dependiente.conversacion_modificada(conversacion)))


class SincronizacionCambioDeAsignacion(Sincronizacion):
    def _evaluar_y_sincronizar(self, metodo_decorado, args, kwargs):
        prospectos = self._obtener_prospectos_desde(metodo_decorado=metodo_decorado, args=args, kwargs=kwargs)
        vendedor = self._obtener_parametro(nombre_argumento='vendedor', metodo_decorado=metodo_decorado,
                                           args=args, kwargs=kwargs)
        prospectos_removidos = self._capturar_pre_operacion(prospectos)
        respuesta = metodo_decorado(*args, **kwargs)
        transaction.on_commit(lambda:
                              self._notificar_cambio_de_asignacion(prospectos, vendedor, prospectos_removidos))
        return respuesta

    def _capturar_pre_operacion(self, prospectos):
        prospectos_removidos = {prospecto: prospecto.vendedor for prospecto in prospectos if
                                prospecto.tiene_vendedor()}
        return prospectos_removidos

    def _notificar_cambio_de_asignacion(self, prospectos, vendedor, prospectos_removidos):
        self._notificar_prospectos_removidos(prospectos_removidos)
        self._notificar_prospectos_asignados(prospectos, vendedor)

    def _notificar_prospectos_removidos(self, prospectos_removidos):
        for prospecto, vendedor in list(prospectos_removidos.items()):
            self._con_dependientes_hacer(
                lambda dependiente: dependiente.vendedor_removido_de(
                    prospecto=prospecto, vendedor=vendedor))

    def _notificar_prospectos_asignados(self, prospectos, vendedor_a_reasignar):
        if vendedor_a_reasignar:
            self._con_dependientes_hacer(
                lambda dependiente: dependiente.vendedor_asignado_a_prospectos(
                    prospectos=prospectos, vendedor=vendedor_a_reasignar))


class SincronizacionCreacionDeProspecto(Sincronizacion):
    def _evaluar_y_sincronizar(self, metodo_decorado, args, kwargs):
        resultado = metodo_decorado(*args, **kwargs)
        self._notificar(resultado)
        return resultado

    def _notificar(self, resultado):
        if not resultado.es_erroneo() and not resultado.estaba_asignado():
            self._notificar_vendedor_asignado_a(resultado.prospecto())

    def _notificar_vendedor_asignado_a(self, prospecto):
        vendedor = prospecto.vendedor
        if vendedor:
            transaction.on_commit(
                lambda: self._con_dependientes_hacer(lambda dependiente: dependiente.vendedor_asignado_a_prospectos(
                    prospectos=[prospecto], vendedor=vendedor)))


class SincronizacionCreacionDeConversacion(Sincronizacion):
    def _evaluar_y_sincronizar(self, metodo_decorado, args, kwargs):
        conversacion = metodo_decorado(*args, **kwargs)
        if not conversacion.es_de_chat():
            self._notificar_creacion_de_conversacion(conversacion)
        return conversacion

    def _notificar_creacion_de_conversacion(self, conversacion):
        prospecto = conversacion.prospecto
        if prospecto:
            vendedor = prospecto.vendedor
            if vendedor:
                transaction.on_commit(lambda: self._con_dependientes_hacer(
                    lambda dependiente: dependiente.vendedor_asignado_a_conversaciones(conversaciones=[conversacion],
                                                                                       vendedor=vendedor)))
