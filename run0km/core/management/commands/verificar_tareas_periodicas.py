# coding=utf-8
from django.core.management.base import BaseCommand
from celery.schedules import current_app
from celery.beat import Service
from django.utils import timezone


class Command(BaseCommand):
    help = 'Información de las tareas perdiodicas registradas con Celery. Alternativa a celery -A project-name report'

    def handle(self, *args, **options):
        informer = CeleryStatusInformer()
        print(informer.periodic_tasks())


class CeleryStatusInformer(object):
    def periodic_tasks(self):
        schedule = Service(current_app).get_scheduler().get_schedule()
        tasks = []
        for key, entry in list(schedule.items()):
            is_due_tpl = entry.is_due()

            next_execution = timezone.now() + timezone.timedelta(seconds=is_due_tpl[1])
            next_execution = next_execution.replace(microsecond=0)

            tasks.append({
                'name': key,
                'task': entry.task,
                'args': self._print_args(entry.args),
                'kwargs': entry.kwargs,
                'is_due': is_due_tpl[0],
                'next_execution': next_execution
            })
        return tasks

    def _print_args(self, args):
        return '(' + ', '.join([arg for arg in args]) + ')'
