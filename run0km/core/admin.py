# coding=utf-8
from functools import update_wrapper

from django.conf.urls import url
from django.contrib import admin
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.utils.encoding import force_text

from core.admin_forms import SistemaDeliveryAdminForm
from core.models import Sistema
from lib.model_admin_logger import ModelAdminClassProxy


class SistemaDeliveryAdmin(admin.ModelAdmin):
    verbose_name = "Deliveryrun"
    verbose_name_plural = "Deliveryrun"
    form = SistemaDeliveryAdminForm
    exclude = ('nombre',)

    fieldsets = [
        ('Condiciones de Entrega para Pedidos de Prospectos', {
            "classes": [""],
            'fields': ['limite_de_horas_sin_actividad_en_pedidos', 'limite_de_datos_nuevos_en_pedidos',
                       'limite_de_datos_diarios_en_pedidos', 'factor_de_priorizacion_para_usuarios_mobile'],
        }),
        ('Configuración Entrega por Productividad', {
            'fields': ['constante_distribucion_asistida_en_pedido']}),
        ('Regla Ventas Semanales', {
            "classes": ["configuracion-titulo-2", "collapse"],
            'fields': ['regla_ventas_semanales_maximo', 'regla_ventas_semanales_minimo'],
        }),
        ('Regla Ventas Mensuales', {
            "classes": ["configuracion-titulo-2", "collapse"],
            'fields': ['regla_ventas_mensuales_maximo', 'regla_ventas_mensuales_minimo'],
        }),
        ('Regla Tiempo de Respuesta Semanal', {
            "classes": ["configuracion-titulo-2", "collapse"],
            'fields': ['regla_tiempo_de_respuesta_semanal_maximo', 'regla_tiempo_de_respuesta_semanal_minimo'],
        }),
        ('Regla Tiempo de Respuesta Mensual', {
            "classes": ["configuracion-titulo-2", "collapse"],
            'fields': ['regla_tiempo_de_respuesta_mensual_maximo', 'regla_tiempo_de_respuesta_mensual_minimo'],
        }),
        ('Configuración de Compulsa', {
            'fields': ['cantidad_de_participantes_compulsa',
                       'cantidad_de_reintentos_compulsa',
                       'timeout_compulsa',
                       'tiempo_de_inactividad_para_filtrar_vendedor_en_compulsa'
                       ],
        }),
        ('Factor para los criterios de selección de participantes', {
            "classes": ["configuracion-titulo-2", "collapse"],
            'fields': ['factor_criterio_ultima_actividad_para_seleccion_de_participantes',
                       'factor_criterio_conversiones_para_seleccion_de_participantes',
                       'factor_criterio_ventas_para_seleccion_de_participantes'],
        }),
        ('Deshabilitación Global de Servicios', {
            'fields': ['deshabilitar_facebook', 'deshabilitar_whatsapp'],
        }),
        ('Campos Adicionales Default para las Concesionarias',{
           'fields': ['campos_default_para_concesionarias'],
        }),
        ('Configuraciones para el mostrado de Novedades', {
            'fields': ['cantidad_de_veces_a_mostrar_novedades', 'frecuencia_para_mostrar_novedades'],
        }),
        ('Configuraciones para Anura', {
            'fields': ['max_minutos_mensuales_para_llamados_voip_por_vendedor'],
        }),
        ('Configuraciones para peticiones de prospectos', {
            'fields': ['tiempo_de_antiguedad_minimo_para_prospectos_disponibles',
                       'tiempo_de_inhabilitacion_para_pedir_prospectos',
                       'tamanio_maximo_de_cola_de_peticiones'],
        }),
        ('Configuraciones de tiempo máximo de inactividad para cada estado', {
            'fields': ['tiempo_maximo_de_inactividad_en_linea',
                       'tiempo_maximo_de_inactividad_actividad_hace_minutos',
                       'tiempo_maximo_de_inactividad_actividad_reciente'],
        }),
    ]

    def __init__(self, model, admin_site):
        super(SistemaDeliveryAdmin, self).__init__(model, admin_site)
        self.opts.verbose_name = self.verbose_name
        self.opts.verbose_name_plural = self.verbose_name_plural

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_urls(self):
        urls = super(SistemaDeliveryAdmin, self).get_urls()

        def wrap(view):
            def wrapper(*args, **kwargs):
                return self.admin_site.admin_view(view)(*args, **kwargs)

            wrapper.model_admin = self
            return update_wrapper(wrapper, view)

        sistema = self.sistema()
        info = self.model._meta.app_label, sistema.instance_nombre()

        custom_urls = [
            url(r'^$', wrap(self.change_view), {'object_id': str(sistema.instance_id())}, name='%s_%s_change' % info),
            url(r'^(.+)/history/$', wrap(self.history_view), name='%s_%s_history' % info),
        ]
        return custom_urls + urls

    def sistema(self):
        return Sistema

    def response_change(self, request, obj):
        msg_dict = {'name': force_text(self.opts.verbose_name)}
        msg = 'La configuración de %(name)s fué modificada exitosamente.' % msg_dict
        if '_continue' in request.POST:
            self.message_user(request, msg, messages.SUCCESS)
            return HttpResponseRedirect(request.path)
        else:
            self.message_user(request, msg)
            return HttpResponseRedirect("../../")

    class Media:
        css = {
            'all': ('css/configuracion-sitema.css',)
        }

admin.site.register(Sistema, ModelAdminClassProxy(SistemaDeliveryAdmin,
                                                  ['limite_de_horas_sin_actividad_en_pedidos',
                                                   'limite_de_datos_nuevos_en_pedidos',
                                                   'limite_de_datos_diarios_en_pedidos']))