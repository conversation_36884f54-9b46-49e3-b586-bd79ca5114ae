# coding=utf-8
from collections.abc import Iterable
from contextlib import contextmanager
from datetime import timedelta

from django.core.cache import caches, InvalidCacheBackendError
from django.utils import timezone

from core.locker.errors import ResourcesError, ResourceLockedError


class Locker(object):
    """
        Mis instancias permiten evaluar una llamada a un metodo con arguments si los resources son adquiridos. Es
        decir al evaluar dicho metodo se lockea un conjunto de resources y son liberados cuando la evaluacion finaliza
        o por timeout.


        Importante:

         - debe tener instalado memcache para que funcione correctamente:
            - sudo apt install memcached
         - en los settings debe tener configurada la cache lock con memcache:
            - CACHES['locker'] = {
                    'BACKEND': 'django.core.cache.backends.memcached.MemcachedCache',
                    'LOCATION': '127.0.0.1:11211',
            }



        Example:
            import time
            import threading
            def worker():
                # thread worker function
                print 'Worker'
                locker = Locker.new_for_default_group()
                locker.do_locking([1], lambda x: time.sleep(25); print('fin'), (1,))
                return 1
            worker()
            t = threading.Thread(target=worker)

            t.start()

            Prueba manual con prints:

            Agregar prints para saber el estado de la variable en la cache y que thread la modifico.

            import threading
            thread_name = threading.current_thread().getName()
            print('lock_id', thread_name, lock_id)

            def funcion_de_prueba(x):
                import threading
                thread_name = threading.current_thread().getName()
                print('inicio', thread_name)
                time.sleep(25)
                print('fin', thread_name)

            import time
            import threading


            def worker():
                # thread worker function
                locker = Locker.new_for_default_group()
                locker.do_locking([1], funcion_de_prueba, (1,))
                return 1

            for _ in range(5):
                threading.Thread(target=worker).start()
                time.sleep(1)


            Esto deberia imprimir: ('inicio', 'Thread-X') ('fin', 'Thread-X') y 4 ResourceLockedError
    """
    _LOCK_EXPIRE = 60 * 5  # Lock expires in 5 minutes
    _DEFAULT_GROUP_NAME = 'default'

    def __init__(self, group_name, expire_time, cache):
        super(Locker, self).__init__()
        self._expire_time = expire_time or self._LOCK_EXPIRE
        self._group_name = group_name or self._DEFAULT_GROUP_NAME
        self._cache = cache

    @classmethod
    def new_for_default_group(cls, expire_time=None):
        return cls.new_for_group(cls._DEFAULT_GROUP_NAME, expire_time)

    @classmethod
    def new_for_group(cls, group_name, expire_time=None):
        return cls(group_name, expire_time, cls._default_cache())

    @classmethod
    def _default_cache(cls):
        try:
            return caches['locker']
        except InvalidCacheBackendError:
            raise ValueError('Debe configurar la cache locker')

    def do_locking_each(self, resources, a_function, arguments):
        """
            Evalua a_function con arguments si cada uno de los resources son adquiridos. Por ejemplo
            si para evaluar la funcion se piden los resources [1, 2, 3] y estan lockeado los resources [1, 2], entonces
            lanzará ResourceLockedError y a_function no será evaluada.

            Tener en cuenta el orden en que se mandan los resources, esto puede generar deadlocks
        """

        if len(resources) == 0:
            raise ResourcesError.empty_resources()

        with memcache_lock(locker=self, resources=resources, expire_time=self._expire_time) as acquired:
            if acquired:
                return a_function(*arguments)
            else:
                raise ResourceLockedError('Any resource of %s is locked' % resources)

    def do_locking(self, resource, a_function, arguments):
        """
            Evalua a_function con arguments si los resources son adquiridos. Los resources son interpretados como
            unidad. Por ejemplo si para evaluar la funcion se piden los resources [1, 2, 3] y estan lockeado los
            resources [1, 2], entonces a_function será evaluada.

            Es decir, que este lockeado [1, 2] pero no implica que este lockeado [1, 2, 3]
        """
        return self.do_locking_each([resource], a_function, arguments)

    def is_locked(self, resource_id):
        return self._cache.has_key(self._resource_id_for(resource_id))

    def lock_resource(self, resource_id):
        # cache.add fails if the key already exists
        return self._cache.add(self._resource_id_for(resource_id), True, self._expire_time)

    def lock_each_resource_of(self, resources):
        return all([self.lock_resource(resource) for resource in resources])

    def unlock_resource(self, resource_id):
        self._cache.delete(self._resource_id_for(resource_id))

    def unlock_each_resource_of(self, resources):
        for resource in resources:
            self.unlock_resource(resource)

    def _resource_id_for(self, resources):
        if isinstance(resources, Iterable) and not isinstance(resources, str):
            resources_string = '-'.join(map(str, resources))
        else:
            resources_string = str(resources)
        resource_id = '%s-%s' % (self._group_name, resources_string)
        resource_id = resource_id.replace(' ', '-')
        return resource_id


@contextmanager
def memcache_lock(locker, resources, expire_time):
    timeout_at = timezone.now() + timedelta(seconds=expire_time)
    # lock_each_resource_of is no used because we need to know what resources were locked to unlock them
    locked_resources = [resource for resource in resources if locker.lock_resource(resource)]
    are_all_resources_locked = len(locked_resources) == len(resources)
    try:
        yield are_all_resources_locked
    finally:
        # memcache delete is very slow, but we have to use it to take
        # advantage of using add() for atomic locking
        if timezone.now() < timeout_at:
            # don't release the lock if we exceeded the timeout
            # to lessen the chance of releasing an expired lock
            # owned by someone else.
            for resource in locked_resources:
                locker.unlock_resource(resource)