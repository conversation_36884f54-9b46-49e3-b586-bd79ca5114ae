from django.core.mail import EmailMultiAlternatives
from django.template.loader import get_template


class EmailTemplateMessage(object):

    def __init__(self, template_name, context, subject='', from_email=None, to=None, bcc=None):
        super(EmailTemplateMessage, self).__init__()
        self._template_name = template_name
        self._context = context
        self._subject = subject
        self._from_email = from_email
        self._to = to
        self._bcc = bcc
        self._attachments = []

    @classmethod
    def new_for(cls, template_name, context, subject='', from_email=None, to=None, bcc=None):
        return cls(template_name, context,  subject, from_email, to, bcc)

    def add_all_attachments(self, attachments):
        self._attachments.extend(attachments)

    def send(self):
        template = get_template(self._template_name)
        html_msg = template.render(self._context)
        email = EmailMultiAlternatives(
            subject=self._subject, body=html_msg,
            from_email=self._from_email, to=self._to, bcc=self._bcc,
            attachments=self._attachments)
        email.content_subtype = 'html'
        email.attach_alternative(html_msg, "text/html")
        email.send()
