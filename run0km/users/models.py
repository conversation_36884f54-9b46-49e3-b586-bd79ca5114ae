# -*- coding: utf-8 -*-
from django.contrib.auth.models import BaseUserManager, AbstractBaseUser, PermissionsMixin
from django.contrib.sessions.models import Session
from django.core import validators
from django.core.mail import send_mail
from django.db import models
from django.utils import timezone
from django.utils.translation import ugettext_lazy as _


class CustomUserManager(BaseUserManager):

    def _create_user(self, username, email, password,
                     first_name, last_name,
                     is_staff, is_superuser, **extra_fields):
        """
        Creates and saves a User with the given username, email, password, first_name and last_name.
        """
        now = timezone.now()
        if not username:
            raise ValueError('The given username must be set')
        if not first_name:
            raise ValueError('The given first_name must be set')
        if not last_name:
            raise ValueError('The given last_name must be set')
        email = self.normalize_email(email)
        user = self.model(username=username, email=email,
                          is_staff=is_staff, is_active=True,
                          first_name=first_name, last_name=last_name,
                          is_superuser=is_superuser, last_login=now,
                          date_joined=now, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, username, email=None, password=None, first_name=None, last_name=None, **extra_fields):
        return self._create_user(username, email, password, first_name, last_name, False, False,
                                 **extra_fields)

    def create_superuser(self, username, email, password, first_name=None, last_name=None, **extra_fields):
        return self._create_user(username, email, password, first_name, last_name, True, True,
                                 **extra_fields)


class AbstractUser(AbstractBaseUser, PermissionsMixin):
    """
    Username, password and email are required. Other fields are optional.
    """
    username = models.CharField(_('username'), max_length=30, unique=True,
        help_text=_('Required. 30 characters or fewer. Letters, digits and '
                    '@/./+/-/_ only.'),
        validators=[
            validators.RegexValidator(r'^[\w.@+-]+$', _('Enter a valid username.'), 'invalid')
        ])
    first_name = models.CharField(_('first name'), max_length=30)
    last_name = models.CharField(_('last name'), max_length=30)
    email = models.EmailField(_('email address'), blank=True)
    is_staff = models.BooleanField(_('staff status'), default=False,
        help_text=_('Designates whether the user can log into this admin '
                    'site.'))
    is_active = models.BooleanField(_('active'), default=True,
        help_text=_('Designates whether this user should be treated as '
                    'active. Unselect this instead of deleting vendedores.'))
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)

    objects = CustomUserManager()

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email', 'first_name', 'last_name']

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        abstract = True

    def __str__(self):
        return self.get_full_name()

    def get_username(self):
        return self.username

    def get_full_name(self):
        """
        Returns the first_name plus the last_name, with a space in between.
        """
        full_name = '%s %s' % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        "Returns the short name for the user."
        return self.first_name

    def email_user(self, subject, message, from_email=None, **kwargs):
        """
        Sends an email to this User.
        """
        send_mail(subject, message, from_email, [self.email], **kwargs)

    def esta_activo(self):
        return self.is_active

    def role(self):
        if self.is_gerente():
            return self.gerente
        if self.is_vendedor():
            return self.vendedor
        raise ValueError('Not role defined to the user %s' % self.get_full_name())

    def is_vendedor(self):
        """
        Determines if the user is a Vendedor/Supervisor.
        """
        return hasattr(self, 'vendedor') and self.vendedor.__class__.__name__ == 'Vendedor'

    def is_supervisor(self):
        """
        Determines if the user is a vendedor with Supervisor position
        """
        return self.is_vendedor() and self.vendedor.es_supervisor()

    def is_gerente(self):
        """
        Determines if the user is a Gerente.
        """
        return hasattr(self, 'gerente') and self.gerente.__class__.__name__ == 'Gerente'

    def concesionaria(self):
        """ Returns concesionaria assiciated to User or None """
        if self.is_gerente():
            return self.gerente.concesionaria
        elif self.is_vendedor():
            return self.vendedor.obtener_concesionaria()

    def es_superusuario(self):
        return self.is_superuser

    def puede_exportar_prospectos(self):
        if self.is_supervisor():
            return self.role().permisos().exportacion_de_prospectos_habilitada()
        elif self.es_superusuario():
            return True
        return False

    def eliminar_token_para_api(self):
        from rest_framework.authtoken.models import Token
        Token.objects.filter(user=self).delete()

    def fecha_de_creacion(self):
        return self.date_joined

    def as_json(self):
        cargo = ''
        if self.is_gerente():
            cargo = '(G)'
        elif self.is_vendedor():
            if self.vendedor.es_supervisor():
                cargo = '(S)'
            else:
                cargo = '(V)'

        return {
            'id': self.pk,
            'nombre': self.get_full_name(),
            'cargo': cargo
        }


class User(AbstractUser):
    """
    Users within the Django authentication system are represented by this
    model.

    Username, password and email are required. Other fields are optional.
    """
    class Meta(AbstractUser.Meta):
        swappable = 'AUTH_USER_MODEL'

    def pertenece_a_concesionaria_con_sitio_propio(self):
        """
            Verifica si el usuario es de una concesionarias de marca blanca es decir con sitio propio
        """
        try:
            rol = self.role()
        except ValueError:
            return False
        else:
            concesionaria = rol.obtener_concesionaria()
            return concesionaria.run_sitio().es_sitio_propio()

    @classmethod
    def nuevo(cls, username, first_name, last_name, password, email):
        user = cls(username=username, first_name=first_name, last_name=last_name, password=password, email=email)
        user.full_clean()
        user.save()
        return user


class PermisoDePersonificacion(models.Model):
    usuario = models.ForeignKey('users.User', related_name='permisos_de_personificacion')
    alias = models.ForeignKey('users.User', related_name='permisos_de_personificadores')

    class Meta:
        unique_together = ('usuario', 'alias')
        verbose_name = "Permiso de Personificación"
        verbose_name_plural = "Permisos de Personificación"

    def __str__(self):
        return "%s -> %s" % (self.usuario.username, self.alias.username)

    def concesionaria(self):
        return self.usuario.concesionaria()
