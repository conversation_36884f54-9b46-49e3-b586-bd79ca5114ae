from django.http import JsonResponse
from django.shortcuts import render
from django.views.generic import View
from concesionarias.models import Concesionaria
from users.models import User

from vendedores.models import Vendedor
from gerentes.models import Gerente


class PersonificacionView(View):
    def get(self, request):
        vendedores = Vendedor.objects.con_cargo_vendedor()
        supervisores = Vendedor.objects.con_cargo_supervisor()
        gerentes = Gerente.objects.filter(concesionaria__isnull=False)
        return render(
            request,
            'admin/users/personificar.html',
            {
                'vendedores': vendedores.select_related('user', 'concesionaria'),
                'supervisores': supervisores.select_related('user', 'concesionaria'),
                'gerentes': gerentes.select_related('user', 'concesionaria'),
            },
        )


class FiltroConcesionariaView(View):
    def get(self, request):
        concesionaria_id = request.GET.get('concesionaria', None)
        usuarios = self.usuarios_de(concesionaria_id)
        usuarios_json = self.as_json(usuarios.order_by('username'))
        response = {'usuarios': usuarios_json}

        return JsonResponse(response)

    def as_json(self, usuarios_query_set):
        query_set_as_json = []
        for usuario in usuarios_query_set:
            query_set_as_json.append(usuario.as_json())

        return query_set_as_json

    def usuarios_de(self, concesionaria_id):
        if concesionaria_id:
            try:
                concesionaria = Concesionaria.objects.get(pk=concesionaria_id)
            except Concesionaria.DoesNotExist:
                concesionaria = None
        else:
            concesionaria = None
        usuarios = self.__class__.usuarios_de_concesionaria(concesionaria)
        return usuarios

    @classmethod
    def usuarios_de_concesionaria(cls, concesionaria):
        if concesionaria:
            usuarios = concesionaria.users_staff()
        else:
            usuarios = User.objects.all()
        return usuarios.order_by('username')
