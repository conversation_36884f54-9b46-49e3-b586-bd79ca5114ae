# -*- coding: utf-8 -*-


from django.db import migrations


def crear_usuario_distribucion(apps, _):
        permission_model_klass = apps.get_model("auth", "Permission")
        perms = permission_model_klass.objects.filter(codename__in=["add_prospecto", "change_prospecto",
                                                                    "delete_prospecto"]).values_list('id', flat=True)
        campos = {
            "username": "distribucion",
            "first_name": "Administrador",
            "last_name": "Distribucion",
            "is_active": True,
            "is_superuser": False,
            "is_staff": True,
            "password": "pbkdf2_sha256$10000$BIb68xGBzFXH$qKOMPfvWfDj1whkRxQanz/YLoXfDIJxcpJRkGUBCf6U=",
            "email": "",
        }
        user_model_klass = apps.get_model("users", "User")
        distribuidor = user_model_klass(**campos)
        distribuidor.save()
        distribuidor.user_permissions = perms


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(crear_usuario_distribucion),
    ]
