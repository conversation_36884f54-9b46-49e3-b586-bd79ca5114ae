from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.conf.urls import url
from django.db import models
from django.utils.timezone import localtime

from django.utils.translation import ugettext_lazy as _
from users.admin_views import FiltroConcesionariaView
from users import admin_views

from users.forms import UserChangeForm, UserCreationForm, PermisoDePersonificacionForm
from users.models import PermisoDePersonificacion, User


class CustomUserAdmin(UserAdmin):
    # The fields to be used in displaying the User model.
    # These override the definitions on the base UserAdmin
    # that reference the removed 'username' field
    fieldsets = (
        (None, {'fields': ('username', 'email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name')}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser')}),  # ,'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')}
         ),
    )
    form = UserChangeForm
    add_form = UserCreationForm
    list_display = ('username', 'email', 'is_staff', '_ultimo_logueo')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('username', 'email',)

    def get_urls(self):
        urls = super(CustomUserAdmin, self).get_urls()
        pull_urls = [url(r'^personificar/$', admin_views.PersonificacionView.as_view(), name='personificar-admin')]
        return pull_urls + urls

    def get_queryset(self, request):
        qs = self.model.objects.get_queryset()
        qs = qs.annotate(ultimo_logueo=models.Max('vendedor__logactividad__ultima'))
        ordering = self.get_ordering(request)
        if ordering:
            qs = qs.order_by(*ordering)
        return qs

    def _ultimo_logueo(self, obj):
        ultimo_logueo = obj.ultimo_logueo
        if ultimo_logueo:
            return localtime(ultimo_logueo).strftime('%d/%m/%Y %H:%M')
        else:
            return '-'
    _ultimo_logueo.short_description = "Ultimo Logueo"
    _ultimo_logueo.admin_order_field = 'ultimo_logueo'


class PermisoDePersonificacionAdmin(admin.ModelAdmin):
    search_fields = ('usuario__first_name', 'usuario__last_name', 'alias__first_name', 'alias__last_name')

    def get_urls(self):
        urls = super(PermisoDePersonificacionAdmin, self).get_urls()
        my_urls = [url(r'^admin_filtro_concesionaria/$', FiltroConcesionariaView.as_view()), ]
        return my_urls + urls

    form = PermisoDePersonificacionForm

    fieldsets = (
        (None, {
            'fields': ('concesionaria', 'usuario', 'alias',),
        }),
    )

    ordering = ('usuario',)
    list_filter = ('usuario', 'alias',)


admin.site.unregister(Group)
admin.site.register(get_user_model(), CustomUserAdmin)
admin.site.register(PermisoDePersonificacion, PermisoDePersonificacionAdmin)
