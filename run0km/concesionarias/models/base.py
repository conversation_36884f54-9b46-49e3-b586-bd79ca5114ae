import datetime

import tldextract
from django.conf import settings
from django.db import models
from django.utils.timezone import localtime

from concesionarias.models.configuracion_de_servicios_de_concesionaria import ConfiguracionDeServiciosDeConcesionaria
from concesionarias.querysets import ConcesionariaQuerySet
from concesionarias.sitios import RunSitio
from core.models import Sistema
from objetivos.models import Periodo
from users.models import User


class Concesionaria(models.Model):
    nombre = models.CharField(max_length=100, blank=False, null=False)
    logo = models.ImageField(upload_to='concesionarias', null=True, blank=True,
                             help_text='El nombre del archivo no debe contener caracteres especiales')
    descripcion = models.TextField(blank=True)
    web = models.URLField(blank=True, null=True)
    dia_inicio_periodos = models.IntegerField()
    dia_fin_periodos = models.IntegerField()
    token_whatsapp = models.Char<PERSON>ield(max_length=256, blank=True, null=True)
    sitio = models.ForeignKey('Sitio', related_name='concesionarias', null=True, blank=True, on_delete=models.SET_NULL)
    subdominio = models.CharField(max_length=64, blank=True, null=False)
    rubro = models.ForeignKey('Rubro', related_name='concesionarias')
    hora_apertura = models.TimeField(default=datetime.time(9, 0))
    hora_cierre = models.TimeField(default=datetime.time(21, 0))
    intervalo_de_tiempo_entre_llamados = models.TimeField(default=datetime.time(0, 0))


    objects = ConcesionariaQuerySet.as_manager()

    class Meta:
        ordering = ['nombre', ]

    def __str__(self):
        return self.nombre

    def users_staff(self):
        empleados_ids = [empleado.user.pk for empleado in self.empleados.all()]
        gerentes_ids = [gerente.user.pk for gerente in self.gerentes.all()]
        users = User.objects.filter(pk__in=gerentes_ids + empleados_ids)
        return users

    def email_de_origen_para_notificaciones(self, asunto=''):
        asunto = " - %s" % asunto if asunto else ''
        dominio = self.url_concesionaria()
        if dominio:
            extracted = tldextract.extract(dominio)
            email = "%(dominio)s%(asunto)s <no-reply@%(dominio)s.%(sufijo)s>" % {'dominio': extracted.domain,
                                                                                 'asunto': asunto,
                                                                                 'sufijo': extracted.suffix}
        else:
            email = "Run0km%(asunto)s <%(email)s>" % {'asunto': asunto, 'email': settings.DEFAULT_FROM_EMAIL}
        return email

    def supervisores(self):
        # TODO: llamar al manager
        return self.supervisores_activos_y_no_activos().filter(user__is_active=True)

    def supervisores_activos_y_no_activos(self):
        return self.empleados.filter(cargo='Supervisor')

    def url_concesionaria(self):
        # URL Propia de la concesionaria. Solamente para concesionarias con Sitio configurado
        if self.sitio:
            if self.subdominio:
                return '%s.%s' % (self.subdominio, self.sitio.url)
            else:
                return self.sitio.url
        return None

    def run_sitio(self):
        return RunSitio.nuevo_para_concesionaria(concesionaria=self)

    """

        METODOS DE CONVENIENCIA

    """

    def obtener_gerentes(self):
        return self.gerentes.all()

    def configuracion_de_servicios(self):
        if not hasattr(self, 'configuracion_servicios'):
            ConfiguracionDeServiciosDeConcesionaria.nuevo(concesionaria=self)
        return self.configuracion_servicios

    def obtener_sitio(self):
        return self.sitio

    def obtener_subdominio(self):
        return self.subdominio

    def obtener_token_whatsapp(self):
        return self.token_whatsapp

    def obtener_nombre(self):
        return self.nombre

    def obtener_descripcion(self):
        return self.descripcion

    def periodo_actual(self):
        return Periodo.actual_para(concesionaria=self)

    def dia_final_del_periodo(self):
        return self.dia_fin_periodos

    def dia_inicial_del_periodo(self):
        return self.dia_inicio_periodos

    def esta_horario_laboral(self, fecha):
        fecha_local = localtime(fecha)
        return self.hora_apertura <= fecha_local.time() <= self.hora_cierre

    def puede_ver_forma_de_prospecto_extendida(self):
        return self.obtener_nombre() in ['Dietrich']

    def campos_adicionales(self):
        return Sistema.instance().campos_adicionales_para_concesionaria()
        # if self.puede_ver_forma_de_prospecto_extendida():
        #     from prospectos.models import InformacionAdicionalDeProspecto
        #     campos = InformacionAdicionalDeProspecto._meta.get_fields()
        #     return
        # else:
        #     sistema = Sistema.instance()
        #     return [campo.nombre() for campo in sistema.campos_adicionales_para_concesionaria()]

    def obtener_rubro(self):
        return self.rubro

    def configurar_intervalo_entre_llamados(self, intervalo_de_tiempo_entre_llamados):
        self.intervalo_de_tiempo_entre_llamados = intervalo_de_tiempo_entre_llamados

    def minutos_entre_llamados(self):
        intervalo_de_tiempo_entre_llamados = self.intervalo_de_tiempo_entre_llamados.hour * 60 + self.intervalo_de_tiempo_entre_llamados.minute
        return intervalo_de_tiempo_entre_llamados

