from datetime import timedelta, datetime, date

from django.conf import settings
from django.utils import timezone
from django.utils.timezone import make_aware, make_naive


class CalendarioLaboral(object):
    def __init__(self, entrada_dia_de_semana, salida_dia_de_semana, entrada_sabado, salida_sabado):
        self._entrada_dia_de_semana = entrada_dia_de_semana
        self._salida_dia_de_semana = salida_dia_de_semana
        self._entrada_sabado = entrada_sabado
        self._salida_sabado = salida_sabado
        self._feriados = [datetime.strptime(s, '%d/%m') for s in settings.FERIADOS]

    @classmethod
    def mensaje_error_fechas_invalidas(cls):
        return 'Las fechas inicial del rango laboral debe ser anterior a la fecha final.'

    @classmethod
    def default(cls):
        return cls(entrada_dia_de_semana=settings.HORARIOS_LABORALES['ENTRADA_DIA_SEMANA'],
                   salida_dia_de_semana=settings.HORARIOS_LABORALES['SALIDA_DIA_SEMANA'],
                   entrada_sabado=settings.HORARIOS_LABORALES['ENTRADA_SABADO'],
                   salida_sabado=settings.HORARIOS_LABORALES['SALIDA_SABADO'])

    def horas_laborales_entre(self, fecha_y_hora_inicial, fecha_y_hora_final, timezone=None):
        fecha_y_hora_inicial = make_aware(make_naive(fecha_y_hora_inicial), timezone)
        fecha_y_hora_final = make_aware(make_naive(fecha_y_hora_final), timezone)

        if fecha_y_hora_inicial > fecha_y_hora_final:
            raise ValueError(self.__class__.mensaje_error_fechas_invalidas())
        if fecha_y_hora_inicial.date() == fecha_y_hora_final.date():
            return self._horas_laborales_entre_dos_horarios_del_mismo_dia(fecha=fecha_y_hora_final.date(),
                                                                          hora_final=fecha_y_hora_final.time(),
                                                                          hora_inicial=fecha_y_hora_inicial.time())
        else:
            return self._horas_laborales_entre_dos_dias_diferentes(fecha_y_hora_final, fecha_y_hora_inicial)

    def feriados(self):
        return self._feriados

    def es_feriado(self, fecha):
        return [dia for dia in self._feriados if dia.day == fecha.day and dia.month == fecha.month]

    def siguiente_fecha_y_hora_laboral_a(self, fecha_y_hora):
        siguiente_dia_laboral = self.sumar_dias_laborales_a(fecha_y_hora, 1)
        horario_de_entrada = self.horario_de_entrada(fecha_y_hora)
        siguiente_dia_y_hora = timezone.datetime.combine(
            siguiente_dia_laboral, horario_de_entrada).replace(tzinfo=siguiente_dia_laboral.tzinfo)
        return siguiente_dia_y_hora

    def sumar_dias_laborales_a(self, fecha, n):
        count = 0
        while count < n:
            fecha += timedelta(days=1)
            if self.es_dia_laboral(fecha):
                count += 1
        return fecha

    def es_dia_laboral(self, fecha):
        return not self.es_feriado(fecha) and not self._es_domingo(fecha)

    def esta_dentro_del_horario_laboral(self, fecha):
        time = fecha.time()
        return self.es_dia_laboral(fecha) and self.horario_de_entrada(fecha) <= time <= self.horario_de_salida(fecha)

    def horario_de_entrada(self, una_fecha):
        if self._es_sabado(una_fecha):
            return self._entrada_sabado
        else:
            return self._entrada_dia_de_semana

    def horario_de_salida(self, una_fecha):
        if self._es_sabado(una_fecha):
            return self._salida_sabado
        else:
            return self._salida_dia_de_semana

    def duracion_de_jornada_laboral_en_dia_de_semana(self):
        return datetime.combine(date.today(), self._salida_dia_de_semana) - \
            datetime.combine(date.today(), self._entrada_dia_de_semana)

    def duracion_de_jornada_laboral_en_dia_sabado(self):
        return datetime.combine(date.today(), self._salida_sabado) - \
            datetime.combine(date.today(), self._entrada_sabado)

    def horas_laborales_de(self, fecha):
        return self.horas_laborables_del_dia_a_partir_de_hora(fecha=fecha, hora=self.horario_de_entrada(fecha))

    def horas_laborables_del_dia_a_partir_de_hora(self, fecha, hora):
        if not self.es_dia_laboral(fecha):
            return timedelta()
        return self._tiempo_entre_dos_horas(hora_final=max(self.horario_de_salida(fecha), hora),
                                            hora_inicial=max(self.horario_de_entrada(fecha), hora))

    def horas_laborables_del_dia_hasta_hora(self, fecha, hora):
        if not self.es_dia_laboral(fecha):
            return timedelta()
        return self._tiempo_entre_dos_horas(hora_final=min(self.horario_de_salida(fecha), hora),
                                            hora_inicial=min(self.horario_de_entrada(fecha), hora))

    def _horas_laborales_entre_dos_horarios_del_mismo_dia(self, fecha, hora_final, hora_inicial):
        hora_efectiva_inicial = max(hora_inicial, self.horario_de_entrada(fecha))
        hora_efectiva_final = max(hora_efectiva_inicial, min(hora_final, self.horario_de_salida(fecha)))
        return self._tiempo_entre_dos_horas(hora_final=hora_efectiva_final, hora_inicial=hora_efectiva_inicial)

    def _horas_laborales_entre_dos_dias_diferentes(self, fecha_y_hora_final, fecha_y_hora_inicial):
        horas_efectivas_primer_dia = self.horas_laborables_del_dia_a_partir_de_hora(
            fecha=fecha_y_hora_inicial.date(), hora=fecha_y_hora_inicial.time())

        horas_efectivas_ultimo_dia = self.horas_laborables_del_dia_hasta_hora(
            fecha=fecha_y_hora_final.date(), hora=fecha_y_hora_final.time())

        resultado = horas_efectivas_primer_dia + horas_efectivas_ultimo_dia
        day_count = (fecha_y_hora_final.date() - fecha_y_hora_inicial.date()).days + 1
        for fecha in (fecha_y_hora_inicial.date() + timedelta(days=n) for n in range(1, day_count - 1)):
            resultado += self.horas_laborales_de(fecha)
        return resultado

    def _es_sabado(self, fecha):
        return fecha.strftime("%A") == 'Saturday'

    def _es_domingo(self, fecha):
        return fecha.strftime("%A") == 'Sunday'

    def _tiempo_entre_dos_horas(self, hora_final, hora_inicial):
        return datetime.combine(date.today(), hora_final) - datetime.combine(date.today(), hora_inicial)
