from django.conf import settings


class RunSitio(object):
    def es_sitio_propio(self):
        raise NotImplementedError('Subclass responsibility')

    def dominio(self):
        raise NotImplementedError('Subclass responsibility')

    def titulo(self):
        raise NotImplementedError('Subclass responsibility')

    def logo_url(self):
        raise NotImplementedError('Subclass responsibility')

    def derechos(self):
        raise NotImplementedError('Subclass responsibility')

    def mostrar_slider_en_login(self):
        raise NotImplementedError('Subclass responsibility')

    def tiene_acceso_a_terminos_legales_oficiales(self):
        raise NotImplementedError('Subclass responsibility')

    def url_absoluta_dominio(self):
        return 'http://%s' % self.dominio()

    def get_contexto(self):
        datos = {'mostrar_slider_en_login': self.mostrar_slider_en_login(), 'imagen_url': self.logo_url(),
                 'title': self.titulo(), 'derechos': self.derechos(),
                 'tiene_acceso_a_terminos_legales_oficiales': self.tiene_acceso_a_terminos_legales_oficiales()}
        return datos

    def get_contexto_con_urls_absolutas(self):
        contexto = self.get_contexto()
        contexto['imagen_url'] = self.dominio() + self.logo_url() if self.logo_url() else ''
        return contexto

    @classmethod
    def nuevo_default(cls):
        return DefaultSitio()

    @classmethod
    def nuevo_para_concesionaria(cls, concesionaria):
        if concesionaria and concesionaria.sitio:
            return ConcesionariaSitioPropio(concesionaria)
        else:
            return DefaultSitio(concesionaria=concesionaria)

    @classmethod
    def nuevo_para_sitio(cls, sitio):
            return SitioPropio(sitio)


class DefaultSitio(RunSitio):
    def __init__(self, concesionaria=None):
        self.concesionaria = concesionaria
    def es_sitio_propio(self):
        return False

    def dominio(self):
        return settings.HOST

    def titulo(self):
        return settings.TITULO

    def logo_url(self):
        if self.concesionaria and self.concesionaria.logo:
            return self.concesionaria.logo.url
        return settings.STATIC_URL + 'img/LogoRunOne.png'

    def derechos(self):
        return 'Todos los derechos reservados Run0km.com'

    def tiene_acceso_a_terminos_legales_oficiales(self):
        return True

    def mostrar_slider_en_login(self):
        return True

    def banner_ranking_url(self):
        return settings.STATIC_URL + 'img/banner-run-premia-superior.jpg'

    def get_contexto(self):
        contexto = super(DefaultSitio, self).get_contexto()
        contexto['banner_ranking'] = self.banner_ranking_url()
        return contexto

    def get_contexto_con_urls_absolutas(self):
        contexto = super(DefaultSitio, self).get_contexto_con_urls_absolutas()
        contexto['banner_ranking'] = self.dominio() + self.banner_ranking_url()
        return contexto


class ConcesionariaSitioPropio(RunSitio):
    """
        Sitio propio para una unica concesionaria
    """
    def __init__(self, concesionaria):
        self._concesionaria = concesionaria
        super(ConcesionariaSitioPropio, self).__init__()

    def es_sitio_propio(self):
        return True

    def dominio(self):
        return self._concesionaria.url_concesionaria()

    def tiene_acceso_a_terminos_legales_oficiales(self):
        return False

    def titulo(self):
        return self._concesionaria.nombre

    def logo_url(self):
        if self._concesionaria.logo:
            return self._concesionaria.logo.url
        if self._concesionaria.sitio and self._concesionaria.sitio.logo:
            return self._concesionaria.sitio.logo.url
        return ''

    def derechos(self):
        return 'Todos los derechos reservados'

    def mostrar_slider_en_login(self):
        return False


class SitioPropio(RunSitio):
    """
        Sitio propio para un conjunto de concesionarias
    """
    def __init__(self, sitio):
        self._sitio = sitio
        super(SitioPropio, self).__init__()

    def es_sitio_propio(self):
        return True

    def dominio(self):
        return self._sitio.url

    def titulo(self):
        return self._sitio.url

    def logo_url(self):
        return self._sitio.logo.url if self._sitio.logo else ''

    def derechos(self):
        return 'Todos los derechos reservados'

    def mostrar_slider_en_login(self):
        return False

    def tiene_acceso_a_terminos_legales_oficiales(self):
        return False
