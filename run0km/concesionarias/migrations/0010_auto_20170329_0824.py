# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-03-29 11:24


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('concesionarias', '0009_auto_20160728_0934'),
    ]

    operations = [
        migrations.CreateModel(
            name='Sitio',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('logo', models.ImageField(blank=True, null=True, upload_to=b'concesionarias')),
                ('descripcion', models.TextField(blank=True)),
                ('web', models.URLField(blank=True, null=True)),
                ('url', models.CharField(help_text=b'sin "www" ni "/". Ej: "delivery.run0km.com"', max_length=64)),
            ],
        ),
        migrations.AddField(
            model_name='concesionaria',
            name='subdominio',
            field=models.CharField(blank=True, max_length=64),
        ),
        migrations.AddField(
            model_name='concesionaria',
            name='sitio',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='concesionarias', to='concesionarias.Sitio'),
        ),
    ]
