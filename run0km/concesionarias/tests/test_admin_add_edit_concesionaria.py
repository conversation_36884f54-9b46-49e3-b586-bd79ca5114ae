# coding=utf-8
from concesionarias.models import Concesion<PERSON>, <PERSON><PERSON><PERSON>
from testing.base import BaseLoggedAdminTest
from testing.test_utils import reload_model


class AddEditConcesionariaAdminContext(object):
    @classmethod
    def nuevo(cls):
        contexto = cls()
        return contexto

    def concesionarias_url(self):
        return '/admin/concesionarias/concesionaria/'

    def editar_concesionaria_url(self, concesionaria_id):
        return '/admin/concesionarias/concesionaria/%s/change/' % concesionaria_id

    def agregar_concesionaria_url(self):
        return '/admin/concesionarias/concesionaria/add/'

    def _dicc_servicios_para_post_data(self, app_habilitada, sms_habilitado, whatsapp_habilitado,
                                       chat_habilitado):
        dicc_servicios = {}
        if app_habilitada:
            dicc_servicios.update({'configuracion_servicios-0-_app_habilitada': 'on'})
        if chat_habilitado:
            dicc_servicios.update({'configuracion_servicios-0-_chat_habilitado': 'on'})
        if sms_habilitado:
            dicc_servicios.update({'configuracion_servicios-0-_sms_habilitado': 'on'})
        if whatsapp_habilitado:
            dicc_servicios.update({'configuracion_servicios-0-_whatsapp_habilitado': 'on'})
        return dicc_servicios

    def agregar_concesionaria_initial_post_data(self, nombre, logo='', descripcion='descripcion_test',
                                                dia_inicio_periodos=1, dia_fin_periodos=28, token_whatsapp='asd',
                                                sitio='', subdominio='', app_habilitada=True, sms_habilitado=True,
                                                chat_habilitado=True, whatsapp_habilitado=False):
        dicc_servicios = self._dicc_servicios_para_post_data(app_habilitada=app_habilitada,
                                                             sms_habilitado=sms_habilitado,
                                                             whatsapp_habilitado=whatsapp_habilitado,
                                                             chat_habilitado=chat_habilitado)
        rubro, _ = Rubro.objects.get_or_create(_nombre='Automotriz')
        initial_post_data = {'_encoding': 'utf-8',
                             '_mutable': True,
                             'encoding': 'utf-8',
                             '_save': 'Guardar',
                             'configuracion_servicios-0-_concesionaria': '',
                             'configuracion_servicios-0-id': '',
                             'configuracion_servicios-INITIAL_FORMS': str(0),
                             'configuracion_servicios-MAX_NUM_FORMS': str(1),
                             'configuracion_servicios-MIN_NUM_FORMS': str(0),
                             'configuracion_servicios-TOTAL_FORMS': str(1),
                             'configuracion_servicios-__prefix__-_chat_habilitado': chat_habilitado,
                             'configuracion_servicios-__prefix__-_concesionaria': '',
                             'configuracion_servicios-__prefix__-_sms_habilitado': sms_habilitado,
                             'configuracion_servicios-__prefix__-id': '',
                             'descripcion': descripcion,
                             'dia_fin_periodos': dia_fin_periodos,
                             'dia_inicio_periodos': dia_inicio_periodos,
                             'logo': logo,
                             'nombre': nombre,
                             'sitio': sitio,
                             'rubro': rubro.id,
                             'subdominio': subdominio,
                             'token_whatsapp': token_whatsapp,
                             'hora_apertura': '09:00:00',
                             'hora_cierre': '21:00:00',
                             'intervalo_de_tiempo_entre_llamados' : '00:00:00'
                             }
        initial_post_data.update(dicc_servicios)
        return initial_post_data

    def editar_concesionaria_initial_post_data(self, concesionaria_id, sms_habilitado=False, whatsapp_habilitado=False,
                                               chat_habilitado=False, descripcion='', dia_final_del_periodo='',
                                               dia_inicial_del_periodo='', nombre='', sitio=None, subdominio='',
                                               token_whatsapp='', app_habilitada=None):
        concesionaria = Concesionaria.objects.get(id=concesionaria_id)
        config_de_servicios = concesionaria.configuracion_de_servicios()
        app_habilitada = app_habilitada if app_habilitada is not None else config_de_servicios.app_habilitada()
        sms_habilitado = sms_habilitado or config_de_servicios.sms_habilitado()
        whatsapp_habilitado = whatsapp_habilitado or config_de_servicios.whatsapp_habilitado()
        chat_habilitado = chat_habilitado or config_de_servicios.chat_habilitado()
        dicc_servicios = self._dicc_servicios_para_post_data(app_habilitada=app_habilitada,
                                                             sms_habilitado=sms_habilitado,
                                                             whatsapp_habilitado=whatsapp_habilitado,
                                                             chat_habilitado=chat_habilitado)
        descripcion = descripcion or concesionaria.obtener_descripcion()
        dia_final_del_periodo = dia_final_del_periodo or concesionaria.dia_final_del_periodo()
        dia_inicial_del_periodo = dia_inicial_del_periodo or concesionaria.dia_inicial_del_periodo()
        nombre = nombre or concesionaria.obtener_nombre()
        sitio = sitio or concesionaria.obtener_sitio()
        rubro, _ = Rubro.objects.get_or_create(_nombre='Automotriz')
        if sitio is None:
            sitio = ''
        subdominio = subdominio or concesionaria.obtener_subdominio()
        token_whatsapp = token_whatsapp or concesionaria.obtener_token_whatsapp()
        initial_post_data = {'_encoding': 'utf-8',
                             '_mutable': True,
                             'encoding': 'utf-8',
                             '_save': 'Guardar',
                             'configuracion_servicios-0-_concesionaria': str(concesionaria_id),
                             'configuracion_servicios-0-id': str(config_de_servicios.id),
                             'configuracion_servicios-INITIAL_FORMS': str(1),
                             'configuracion_servicios-MAX_NUM_FORMS': str(1),
                             'configuracion_servicios-MIN_NUM_FORMS': str(0),
                             'configuracion_servicios-TOTAL_FORMS': str(1),
                             'configuracion_servicios-__prefix__-_chat_habilitado': chat_habilitado,
                             'configuracion_servicios-__prefix__-_concesionaria': concesionaria_id,
                             'configuracion_servicios-__prefix__-_sms_habilitado': sms_habilitado,
                             'configuracion_servicios-__prefix__-id': '',
                             'descripcion': descripcion,
                             'dia_fin_periodos': dia_final_del_periodo,
                             'dia_inicio_periodos': dia_inicial_del_periodo,
                             'logo': '',
                             'nombre': nombre,
                             'rubro': rubro.id,
                             'sitio': sitio,
                             'subdominio': subdominio,
                             'token_whatsapp': token_whatsapp,
                             'hora_apertura': '09:00:00',
                             'hora_cierre': '21:00:00',
                             'intervalo_de_tiempo_entre_llamados': '00:00:00'
                             }
        initial_post_data.update(dicc_servicios)
        return initial_post_data


class AddConcesionariaAdminTest(BaseLoggedAdminTest):
    def setUp(self):
        super(AddConcesionariaAdminTest, self).setUp()
        self.contexto_test = AddEditConcesionariaAdminContext.nuevo()
        self.concesionaria = self.fixture['conce_1']
        self._default_data = self.contexto_test.agregar_concesionaria_initial_post_data(nombre='default_conc')

    def test_agregar_concesionaria_habilita_su_app_por_defecto(self):
        response = self.client.post(self.contexto_test.agregar_concesionaria_url(), self._default_data, follow=True)
        self.assertRedirects(response, self.contexto_test.concesionarias_url(), status_code=302, target_status_code=200,
                             msg_prefix='')
        self.assertContains(response, 'Se agregó con éxito concesionaria')
        concesionaria_agregada = Concesionaria.objects.get(nombre='default_conc')
        self.assertTrue(concesionaria_agregada.configuracion_de_servicios().app_habilitada())


class EditConcesionariaAdminTest(BaseLoggedAdminTest):
    def setUp(self):
        super(EditConcesionariaAdminTest, self).setUp()
        self.contexto_test = AddEditConcesionariaAdminContext.nuevo()
        self.concesionaria = self.fixture['conce_1']
        self.supervisor_del_vendedor = self.fixture['sup_1']
        self.otro_supervisor = self.fixture['sup_2']

    def _assert_habilitacion_de_app_para_supervisores_y_vendedores_de(self, concesionaria, habilitada):
        supervisores = Concesionaria.objects.supervisores(concesionaria)
        vendedores = Concesionaria.objects.vendedores(concesionaria)
        for supervisor in supervisores:
            config_servicios = supervisor.configuracion_de_servicios()
            self.assertEqual(config_servicios.app_habilitada(), habilitada)
        for vendedor in vendedores:
            config_servicios = vendedor.configuracion_de_servicios()
            self.assertEqual(config_servicios.app_habilitada(), habilitada)

    def test_habilitar_app_desde_concesionaria_la_habilita_para_todos_sus_supervisores_y_vendedores(self):
        configuracion_de_servicios = self.concesionaria.configuracion_de_servicios()
        configuracion_de_servicios.deshabilitar_app()
        editar_concesionaria_url = self.contexto_test.editar_concesionaria_url(concesionaria_id=self.concesionaria.id)
        edit_data = self.contexto_test.editar_concesionaria_initial_post_data(
            concesionaria_id=self.concesionaria.id, app_habilitada=True)
        response = self.client.post(editar_concesionaria_url, edit_data, follow=True)
        self.assertEqual(response.status_code, 200)
        concesionaria = reload_model(self.concesionaria)
        self.assertTrue(concesionaria.configuracion_de_servicios().app_habilitada())
        self._assert_habilitacion_de_app_para_supervisores_y_vendedores_de(concesionaria=concesionaria,
                                                                           habilitada=True)

    def test_deshabilitar_app_desde_concesionaria_la_deshabilita_para_todos_sus_supervisores_y_vendedores(self):
        configuracion_de_servicios = self.concesionaria.configuracion_de_servicios()
        configuracion_de_servicios.habilitar_app()
        editar_concesionaria_url = self.contexto_test.editar_concesionaria_url(concesionaria_id=self.concesionaria.id)
        edit_data = self.contexto_test.editar_concesionaria_initial_post_data(
            concesionaria_id=self.concesionaria.id, app_habilitada=False)
        response = self.client.post(editar_concesionaria_url, edit_data, follow=True)
        self.assertEqual(response.status_code, 200)
        concesionaria = reload_model(self.concesionaria)
        self.assertFalse(concesionaria.configuracion_de_servicios().app_habilitada())
        self._assert_habilitacion_de_app_para_supervisores_y_vendedores_de(concesionaria=concesionaria,
                                                                           habilitada=False)



