from datetime import time, datetime, timedelta, date

from django.test import TestCase
from django.utils.timezone import make_aware

from concesionarias.rango_laboral import CalendarioLaboral
from concesionarias.tests.factories import una_hora_laboral


class CalendarioLaboralTest(TestCase):
    def setUp(self):
        self.lunes = datetime(year=2016, month=5, day=30)
        self.martes = self.lunes + timedelta(days=1)
        self.miercoles = self.lunes + timedelta(days=2)
        self.jueves = self.lunes + timedelta(days=3)
        self.viernes = self.lunes + timedelta(days=4)
        self.sabado = self.lunes + timedelta(days=5)
        self.domingo = self.lunes + timedelta(days=6)
        self.otro_lunes = self.lunes + timedelta(days=7)

        self.horario_laboral = CalendarioLaboral.default()

    def test_entre_dos_horarios_laborales_del_mismo_dia_es_la_diferencia_entre_ambos(self):
        """
            Entre horarios laborables en la misma fecha el resultado es la resta entre ambas fechas
        """
        primer_hora_laboral = self.horario_laboral._entrada_dia_de_semana
        otra_hora_laboral = una_hora_laboral()

        lunes_en_el_horario_de_entrada = make_aware(datetime.combine(self.lunes, primer_hora_laboral))
        lunes_en_otra_hora_laboral = make_aware(datetime.combine(self.lunes, otra_hora_laboral))

        self.assertEqualRangoLaboral(fecha_inicial=lunes_en_el_horario_de_entrada,
                                     fecha_final=lunes_en_otra_hora_laboral,
                                     resultado=lunes_en_otra_hora_laboral - lunes_en_el_horario_de_entrada)

    def test_ambas_fechas_en_el_mismo_dia_fuera_de_rango(self):
        """
            Entre horarios no laborables (uno anterior a la entrada y otro posterior a la salida)
            en la misma fecha el resultado es la resta entre el horario de salida y el horario de entrada
        """
        hora_anterior_a_primer_hora_laboral = time(self.horario_laboral._entrada_dia_de_semana.hour - 1, 0, 0, 0)
        hora_posterior_a_primer_hora_laboral = time(self.horario_laboral._salida_dia_de_semana.hour + 1, 0, 0, 0)

        lunes_una_hora_antes_de_la_entrada = make_aware(datetime.combine(self.lunes, hora_anterior_a_primer_hora_laboral))
        lunes_una_hora_despues_de_la_salida = make_aware(datetime.combine(self.lunes, hora_posterior_a_primer_hora_laboral))

        resultado_en_horas = self.horario_laboral._salida_dia_de_semana.hour - self.horario_laboral._entrada_dia_de_semana.hour
        self.assertEqualRangoLaboral(
            fecha_inicial=lunes_una_hora_antes_de_la_entrada,
            fecha_final=lunes_una_hora_despues_de_la_salida,
            resultado=timedelta(hours=resultado_en_horas))

    def test_ambas_fechas_en_el_mismo_dia_posteriores_al_horario_laboral(self):
        """
            Entre horarios no laborables (ambos posteriores a la salida)
            en la misma fecha el resultado es 0
        """
        hora_posterior_a_ultima_hora_laboral = time(self.horario_laboral._salida_dia_de_semana.hour + 1, 0, 0, 0)
        otra_hora_posterior_a_ultima_hora_laboral = time(self.horario_laboral._salida_dia_de_semana.hour + 2, 0, 0, 0)

        lunes_una_hora_despues_de_la_salida = make_aware(datetime.combine(self.lunes, hora_posterior_a_ultima_hora_laboral))
        lunes_dos_horas_despues_de_la_salida = make_aware(datetime.combine(self.lunes, otra_hora_posterior_a_ultima_hora_laboral))

        self.assertEqualRangoLaboral(
            fecha_inicial=lunes_una_hora_despues_de_la_salida,
            fecha_final=lunes_dos_horas_despues_de_la_salida,
            resultado=timedelta())

    def test_ambas_fechas_en_el_mismo_dia_anteriores_al_horario_laboral(self):
        """
            Entre horarios no laborables (ambos anteriores a la entrada)
            en la misma fecha el resultado es 0
        """
        hora_anterior_a_primer_hora_laboral = time(self.horario_laboral._entrada_dia_de_semana.hour - 2, 0, 0, 0)
        otra_hora_anterior_a_primer_hora_laboral = time(self.horario_laboral._entrada_dia_de_semana.hour - 1, 0, 0, 0)

        lunes_dos_horas_antes_de_la_entrada = make_aware(datetime.combine(self.lunes, hora_anterior_a_primer_hora_laboral))
        lunes_una_hora_antes_de_la_entrada = make_aware(datetime.combine(self.lunes, otra_hora_anterior_a_primer_hora_laboral))

        self.assertEqualRangoLaboral(
            fecha_inicial=lunes_dos_horas_antes_de_la_entrada,
            fecha_final=lunes_una_hora_antes_de_la_entrada,
            resultado=timedelta())

    def test_con_un_horario_anterior_a_la_entrada_deberia_ser_el_rango_del_dia(self):
        """
            Entre un horario anterior a la entrada y un horario laboral, no se cuenta
            el tiempo entre la hora de entrada y el horario anterior a la entrada
        """
        hora_no_laboral = time(self.horario_laboral._entrada_dia_de_semana.hour - 1, 0, 0, 0)
        hora_laboral = una_hora_laboral()

        lunes_una_hora_antes_de_la_entrada = make_aware(datetime.combine(self.lunes, hora_no_laboral))
        lunes_a_la_salida = make_aware(datetime.combine(self.lunes, hora_laboral))

        self.assertEqualRangoLaboral(
            fecha_inicial=lunes_una_hora_antes_de_la_entrada,
            fecha_final=lunes_a_la_salida,
            resultado=lunes_a_la_salida - lunes_una_hora_antes_de_la_entrada - timedelta(hours=1))

    def test_con_un_horario_posterior_a_la_entrada_deberia_ser_el_rango_del_dia(self):
        """
            Entre una hora laboral y otra hora pasada de ese horario no se cuentan las horas
            una vez pasada la ultima hora laboral
        """
        horas_fuera_de_horario_laboral = 1
        hora_no_laboral = time(
            self.horario_laboral._salida_dia_de_semana.hour + horas_fuera_de_horario_laboral, 0, 0, 0)
        hora_laboral = una_hora_laboral()

        lunes_en_horario_laboral = make_aware(datetime.combine(self.lunes, hora_laboral))
        lunes_una_hora_despues_de_la_salida = make_aware(datetime.combine(self.lunes, hora_no_laboral))

        self.assertEqualRangoLaboral(
            fecha_inicial=lunes_en_horario_laboral,
            fecha_final=lunes_una_hora_despues_de_la_salida,
            resultado=lunes_una_hora_despues_de_la_salida - lunes_en_horario_laboral - timedelta(
                hours=horas_fuera_de_horario_laboral))

    def test_rango_laboral_entre_un_dia_laboral_en_horario_laboral_y_el_dia_siguiente_en_horario_laboral_no_tiene_en_cuenta_el_horario_no_laboral_en_el_medio(self):
        """
            Cuando pasa un dia entre la primer fecha y la segunda no se cuentan las horas no laborales que hay
            entre esos dias
        """
        primer_hora_laboral = self.horario_laboral._entrada_dia_de_semana
        otra_hora_laboral = una_hora_laboral()

        lunes_a_primera_hora_laboral = make_aware(datetime.combine(self.lunes, primer_hora_laboral))
        martes_en_horario_laboral = make_aware(datetime.combine(self.martes, otra_hora_laboral))

        self.assertEqualRangoLaboral(
            fecha_inicial=lunes_a_primera_hora_laboral,
            fecha_final=martes_en_horario_laboral,
            resultado=martes_en_horario_laboral - lunes_a_primera_hora_laboral -
                self.horas_entre_dos_dias_laborales_consecutivos(horario_laboral=self.horario_laboral))

    def test_rango_laboral_entre_un_dia_laboral_antes_de_horario_laboral_y_el_dia_siguiente_en_horario_laboral_no_tiene_en_cuenta_el_horario_no_laboral_en_el_medio(self):
        """
            Cuando pasa un dia entre la primer fecha y la segunda y ademas la primera es anterior al horario de entrada
            de ese dia, no se cuenta el tiempo no laboral entre esos dos dias y tampoco el tiempo entre la hora de la
            primer fecha y el horario de entrada de esa fecha
        """
        horas_fuera_de_horario_laboral = 1
        antes_de_primer_hora_laboral = time(
            self.horario_laboral._entrada_dia_de_semana.hour - horas_fuera_de_horario_laboral, 0, 0, 0)
        otra_hora_laboral = una_hora_laboral()

        lunes_una_hora_antes_de_la_primer_hora_laboral = make_aware(datetime.combine(self.lunes,
                                                                                     antes_de_primer_hora_laboral))
        martes_en_horario_laboral = make_aware(datetime.combine(self.martes, otra_hora_laboral))

        resultado = martes_en_horario_laboral - lunes_una_hora_antes_de_la_primer_hora_laboral - \
            self.horas_entre_dos_dias_laborales_consecutivos(horario_laboral=self.horario_laboral) - timedelta(
                hours=horas_fuera_de_horario_laboral)

        self.assertEqualRangoLaboral(fecha_inicial=lunes_una_hora_antes_de_la_primer_hora_laboral,
                                     fecha_final=martes_en_horario_laboral,
                                     resultado=resultado)

    def test_rango_laboral_entre_un_dia_laboral_en_horario_laboral_y_el_dia_siguiente_despues_de_horario_laboral_no_tiene_en_cuenta_el_horario_no_laboral_en_el_medio(self):
        """
            Cuando pasa un dia entre la primer fecha y la segunda y ademas la segunda fecha es posterior al horario de
            salida de ese dia, no se cuenta el tiempo no laboral entre esos dos dias y tampoco el tiempo entre la hora
            de la segunda fecha y el horario de salida de esa fecha
        """
        primer_hora_laboral = self.horario_laboral._entrada_dia_de_semana
        horas_fuera_de_horario_laboral = 1
        despues_de_ultima_hora_laboral = time(
            self.horario_laboral._salida_dia_de_semana.hour + horas_fuera_de_horario_laboral, 0, 0, 0)

        lunes_a_primera_hora_laboral = make_aware(datetime.combine(self.lunes, primer_hora_laboral))
        martes_pasada_la_hora_laboral = make_aware(datetime.combine(self.martes, despues_de_ultima_hora_laboral))

        resultado = martes_pasada_la_hora_laboral - lunes_a_primera_hora_laboral - \
            self.horas_entre_dos_dias_laborales_consecutivos(horario_laboral=self.horario_laboral) - timedelta(
                hours=horas_fuera_de_horario_laboral)

        self.assertEqualRangoLaboral(fecha_inicial=lunes_a_primera_hora_laboral,
                                     fecha_final=martes_pasada_la_hora_laboral,
                                     resultado=resultado)

    def test_rango_laboral_entre_dos_dias_laborales_con_otro_dia_laboral_en_el_medio(self):
        """Al haber dos dias de distancia entre las dos fechas no se cuenta el tiempo en el medio entre el primer dia y
        el segundo ni el tiempo entre el segundo y el tercero"""
        hora_laboral = self.horario_laboral._entrada_dia_de_semana

        lunes_en_horario_laboral = make_aware(datetime.combine(self.lunes, hora_laboral))
        miercoles_en_horario_laboral = make_aware(datetime.combine(self.miercoles, hora_laboral))

        resultado = (self.horario_laboral.duracion_de_jornada_laboral_en_dia_de_semana())*2

        self.assertEqualRangoLaboral(fecha_inicial=lunes_en_horario_laboral,
                                     fecha_final=miercoles_en_horario_laboral,
                                     resultado=resultado)

    def test_el_domingo_no_suma_horas_al_rango_laboral(self):
        """
            Cuando uno de los dias en el intervalo es un domingo no se cuentan las horas de ese dia
        """
        hora_laboral = self.horario_laboral._entrada_dia_de_semana

        sabado_en_horario_laboral = make_aware(datetime.combine(self.sabado, hora_laboral))
        lunes_en_horario_laboral = make_aware(datetime.combine(self.otro_lunes, hora_laboral))

        resultado = self.horario_laboral.duracion_de_jornada_laboral_en_dia_sabado()

        self.assertEqualRangoLaboral(fecha_inicial=sabado_en_horario_laboral,
                                     fecha_final=lunes_en_horario_laboral,
                                     resultado=resultado)

    def test_rango_laboral_entre_dos_dias_laborales_con_feriado_en_el_medio(self):
        """
        Pruebo el rango para dos horarios, uno el 24/5 y otro el 26/5 (25/5 es feriado)
        """
        hora_laboral = self.horario_laboral._entrada_dia_de_semana

        veinticuatro_de_mayo = datetime(year=2016, month=5, day=24)
        veintiseis_de_mayo = datetime(year=2016, month=5, day=26)

        veinticuatro_de_mayo_en_horario_laboral = make_aware(datetime.combine(veinticuatro_de_mayo, hora_laboral))
        veintiseis_de_mayo_en_horario_laboral = make_aware(datetime.combine(veintiseis_de_mayo, hora_laboral))

        resultado = self.horario_laboral.duracion_de_jornada_laboral_en_dia_de_semana()

        self.assertEqualRangoLaboral(fecha_inicial=veinticuatro_de_mayo_en_horario_laboral,
                                     fecha_final=veintiseis_de_mayo_en_horario_laboral,
                                     resultado=resultado)

    def test_rango_laboral_con_una_fecha_final_anterior(self):
        """
            Cuando la fecha y hora inicial de un rango laboral es mayor a la fecha incial se levanta una excepcion
        """
        primer_hora_laboral = self.horario_laboral._entrada_dia_de_semana
        otra_hora_laboral = una_hora_laboral()

        lunes_en_el_horario_de_entrada = make_aware(datetime.combine(self.lunes, primer_hora_laboral))
        lunes_en_otra_hora_laboral_posterior = make_aware(datetime.combine(self.lunes, otra_hora_laboral))

        self.assertRaisesMessage(ValueError,
                                 CalendarioLaboral.mensaje_error_fechas_invalidas(),
                                 self.horario_laboral.horas_laborales_entre,
                                 fecha_y_hora_final=lunes_en_el_horario_de_entrada,
                                 fecha_y_hora_inicial=lunes_en_otra_hora_laboral_posterior)

    def horas_entre_dos_dias_laborales_consecutivos(self, horario_laboral):
        hoy_a_la_salida = datetime.combine(date.today(), horario_laboral._salida_dia_de_semana)
        maniana_a_la_entrada = datetime.combine(date.today() + timedelta(days=1), horario_laboral._entrada_dia_de_semana)
        return maniana_a_la_entrada - hoy_a_la_salida

    def assertEqualRangoLaboral(self, fecha_inicial, fecha_final, resultado):
        self.assertEqual(self.horario_laboral.horas_laborales_entre(fecha_inicial, fecha_final), resultado)
