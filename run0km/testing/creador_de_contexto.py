# coding=utf-8
import random
import string

from django.utils import timezone
from freezegun import freeze_time
from rest_framework.authtoken.models import Token

from campanias.models import CategoriaDeCampania, Campania
from conversaciones.models import Conversacion, MensajesWhatsapp
from crms.models import OpcionClienteCRM, ConfiguracionDePilot, ConfiguracionDeTecnom, ConfiguracionDeSirena, \
    ConfiguracionDeMaipu
from layers.application.commands.eavisos import RecibirMensajeDePublicacionEAvisosComando
from occ.models.anura import LlamadaRealizadaDeAnura, IntentoDeLlamado
from occ.models.eavisos import PublicacionEAvisos
from propuestas.models import Propuesta
from prospectos.models import PedidoDeProspecto, ConfiguracionDeNotificacionDePedido, \
    FiltroDePedido, Proveedor, Alias, Marca, Modelo, MarcaDeTarjetaDeCredito, CampoExtra
from prospectos.models.entrega_de_datos.opciones import MetodosDeAsignacionChoices
from prospectos.models.gestor.constructor_de_prospectos import ConstructorDeProspectos
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.tests.distribucion.ingresos.control_del_tiempo import ControladorDelTiempo
from testing.factories import VendedoresFactory, ProspectosFactory, UsersFactory, PedidosDeProspectoFactory
from users.models import User
from vendedores.gestion_de_ventas import GestorDeVenta
from vendedores.models import Vendedor, LogActividad


class CreadorDeContexto(object):
    """
        Tenemos algunos problemas con las nuevas restricciones de repetidos y datos sin contacto.
        Por ahora meti un parche para generar un prospecto con telefono random.

    """

    def __init__(self, fixture, supervisor=None):
        super(CreadorDeContexto, self).__init__()
        self.fixture = fixture
        self.supervisor = supervisor
        self.hoy = timezone.now().date()
        self.repartidor = RepartidorDeProspectos.nuevo()

    def crear_marcas_de_tarjetas_de_credito(self):
        nombres_de_marcas_a_crear = [opcion[0] for opcion in MarcaDeTarjetaDeCredito.OPCIONES]
        for nombre in nombres_de_marcas_a_crear:
            try:
                MarcaDeTarjetaDeCredito.objects.con_nombre(nombre=nombre)
            except MarcaDeTarjetaDeCredito.DoesNotExist:
                MarcaDeTarjetaDeCredito.nueva(nombre=nombre, logo=None)

    def crear_usuario(self, username='username', first_name='first_name', last_name='last_name', password='password',
                      email='<EMAIL>'):
        user = User.nuevo(username=username, first_name=first_name, last_name=last_name, password=password, email=email)
        return user

    def set_supervisor_default(self, supervisor):
        self.supervisor = supervisor

    def agregar_token_para(self, usuario):
        token, was_created = Token.objects.get_or_create(user=usuario)
        return token

    def crear_campania_externa_para(self, vendedor):
        concesionaria = vendedor.obtener_concesionaria()
        origen = self.fixture['tipo_s']
        try:
            categoria = CategoriaDeCampania.objects.externas().get(tipo_de_origen=origen)
        except CategoriaDeCampania.DoesNotExist:
            categoria = CategoriaDeCampania.nueva_externa('externa', origen)
        campania = Campania.objects.create(concesionaria=concesionaria, nombre='mi-campania', categoria=categoria)
        return campania

    def crear_campania(self, nombre, categoria=None):
        categoria = categoria or self.fixture['cat_s']
        campania = Campania.objects.create(nombre=nombre, categoria=categoria)
        return campania

    def crear_filtro_de_exclusion_para(self, pedido, campo, selector, valor):
        filtro = FiltroDePedido.nuevo_para_exclusion(pedido=pedido, campo=campo, selector=selector, valor=valor)
        return filtro

    def crear_filtro_de_inclusion_para(self, pedido, campo, selector, valor):
        filtro = FiltroDePedido.nuevo_para_inclusion(pedido=pedido, campo=campo, selector=selector, valor=valor)
        return filtro

    def crear_filtro_para_marca(self, pedido, nombre_de_marca):
        self.crear_filtro_de_inclusion_para(
            pedido, campo='marca', selector=FiltroDePedido.CONTIENE, valor=nombre_de_marca)

    def crear_nueva_conversacion_de_whatsapp_para(self, prospecto, mensaje='', telefono=''):
        MensajesWhatsapp.nuevo_mensaje(prospecto, mensaje, telefono)
        return Conversacion.objects.conversacion_whatsapp_de_prospecto(prospecto)

    def crear_prospectos_nuevos(self, cantidad, fecha=None, telefono='', es_telefono_movil=None,
                                nombre='prospecto_test', campania=None, provincia='', localidad='',
                                proveedor=None, email='', marca=''):
        prospectos = []
        campania = campania or self.fixture['camp_1']
        fecha = fecha or timezone.now()
        for indice in range(0, cantidad):
            p = ProspectosFactory(campania=campania,
                                  vendedor=None,
                                  responsable=None,
                                  fecha=fecha,
                                  telefono=telefono,
                                  es_telefono_movil=es_telefono_movil,
                                  nombre=nombre,
                                  proveedor=proveedor or Proveedor.vacio(),
                                  provincia=provincia,
                                  localidad=localidad,
                                  email=email,
                                  marca=marca)
            prospectos.append(p)

        return prospectos

    def crear_prospecto_nuevo(self, fecha=None, telefono='', es_telefono_movil=None,
                                nombre='prospecto_test', campania=None, provincia='', localidad='',
                                proveedor=None, email='', marca=''):
        return self.crear_prospectos_nuevos(cantidad=1, fecha=fecha, telefono=telefono, es_telefono_movil=es_telefono_movil,
                                            nombre=nombre, campania=campania, provincia=provincia, localidad=localidad,
                                            proveedor=proveedor,email=email, marca=marca)[0]

    def asignar_prospecto_nuevo_a(self, vendedor, supervisor=None, tiempo_de_respuesta=60, fecha=None,
                                  fecha_de_asignacion_a_vendedor=None, telefono='', es_telefono_movil=None,
                                  nombre='prospecto_test', campania=None, provincia='', localidad='',
                                  email='', nombre_de_marca='', proveedor='', prefijo='', mensaje=''):
        if not telefono and not email:
            telefono = self._random_telefono()
        if vendedor:
            supervisor = vendedor.responsable()
        return self.asignar_prospectos_nuevos_para(vendedores=[vendedor], cantidad=1, supervisor=supervisor,
                                                   tiempo_de_respuesta=tiempo_de_respuesta, fecha=fecha,
                                                   fecha_de_asignacion_a_vendedor=fecha_de_asignacion_a_vendedor,
                                                   telefono=telefono, es_telefono_movil=es_telefono_movil,
                                                   nombre=nombre, campania=campania,
                                                   provincia=provincia, localidad=localidad, email=email,
                                                   nombre_de_marca=nombre_de_marca, proveedor=proveedor,
                                                   prefijo=prefijo, mensaje=mensaje)[0]

    def crear_proveedor(self, nombre_de_proveedor):
        if nombre_de_proveedor is None:
            return Proveedor.vacio()
        proveedor = Proveedor.obtener_o_crear(nombre=nombre_de_proveedor, source_id=nombre_de_proveedor)
        return proveedor

    def poner_a_cargo_prospectos_nuevos(self, supervisor, cantidad, campania=None, marca='', fecha=None,
                                        telefono=None, email='',
                                        proveedor=None, fecha_de_asignacion_a_supervisor=None,
                                        nombre='prospecto_test'):
        prospectos = []
        fecha_de_asignacion_a_supervisor = fecha_de_asignacion_a_supervisor or timezone.now()
        proveedor = self.crear_proveedor(proveedor)
        datos = {'campania': campania or self.fixture['camp_1'], 'responsable': supervisor or self.supervisor,
                 'fecha': fecha or timezone.now(), 'nombre': nombre, 'ip': None, 'proveedor': proveedor,
                 'email': email, 'marca': marca}

        with freeze_time(fecha_de_asignacion_a_supervisor or timezone.now()):
            for indice in range(0, cantidad):
                if telefono is None:
                    datos['telefono'] = self._random_telefono()
                else:
                    datos['telefono'] = telefono
                resultado = self.repartidor.crear_nuevo_prospecto_desde(datos)
                prospectos.append(resultado.prospecto())

        return prospectos

    def _random_telefono(self):
        return ''.join([random.choice(string.digits) for _ in range(15)])

    def asignar_prospectos_nuevos_para(self, cantidad, vendedores=None, supervisor=None, tiempo_de_respuesta=60,
                                       fecha=None,
                                       fecha_de_asignacion_a_vendedor=None, telefono='', es_telefono_movil=None,
                                       nombre='prospecto_test', campania=None, provincia='', localidad='',
                                       nombre_de_marca='ford', email='', proveedor='', prefijo='', mensaje=''):
        """
                Asigna la cantidad "cantidad" de Prospectos a cada vendedor de "vendedores". Todos los prospectos
                creados son iguales.
                En caso de que no haya vendedores, se crea la cantidad "cantidad" de Prospectos sin asignar.

                TODO: no deberia tomar telefono y email ya que genera datos de contactos repetidos
        """
        proveedor = self.crear_proveedor(proveedor)
        supervisor = self._obtener_supervisor(supervisor, vendedores)

        datos = {'campania': campania or self.fixture['camp_1'], 'responsable': supervisor or self.supervisor,
                 'fecha': fecha or timezone.now(),
                 '_tiempo_de_respuesta': tiempo_de_respuesta, 'telefono': telefono, 'nombre': nombre,
                 'es_telefono_movil': es_telefono_movil, 'provincia': provincia, 'localidad': localidad,
                 'marca': nombre_de_marca, 'ip': None, 'email': email, 'proveedor': proveedor,
                 'prefijo': prefijo, 'mensaje': mensaje}

        with freeze_time(fecha_de_asignacion_a_vendedor or timezone.now()):
            if not vendedores:
                prospectos = self._crear_prospectos(cantidad, datos, vendedor=None)
            else:
                prospectos = []
                for vendedor in vendedores:
                    prospectos_de_vendedor = self._crear_prospectos(cantidad, datos, vendedor)
                    prospectos.extend(prospectos_de_vendedor)
            return prospectos

    def _obtener_supervisor(self, supervisor, vendedores):
        if supervisor is None:
            if vendedores is not None and vendedores[0] is not None:
                supervisor = vendedores[0].responsable()
            else:
                supervisor = self.supervisor
        return supervisor

    def crear_repetido_asignado_a(self, prospecto_original, asignado_a, tiempo_frizado=None):
        """
        :param prospecto_original: El Prospecto a enlazar con el Repetido como su Original.
        :param asignado_a: El Vendedor o Supervisor al que se le va a asignar el Repetido.
        :param tiempo_frizado: una instancia de freeze_time para controlar el paso del tiempo. Si no es None avanza
        en el tiempo la fecha fuera del gap
        :return: El Prospecto Repetido
        """
        fecha_fuera_del_gap = self._fecha_fuera_del_gap_de_mergeo(prospecto_original)
        self._avanzar_en_el_tiempo_si_la_fecha_es_futura(fecha_fuera_del_gap, tiempo_frizado)

        with freeze_time(fecha_fuera_del_gap):
            prospecto_repetido = self.asignar_prospecto_nuevo_a(
                vendedor=None, email=prospecto_original.email, telefono=prospecto_original.telefono,
                nombre_de_marca=prospecto_original.obtener_marca().nombre()
            )
        ConstructorDeProspectos.nuevo().agregar_prospecto_repetido_a(
            prospecto_original=prospecto_original, prospecto_repetido=prospecto_repetido)
        repartidor = RepartidorDeProspectos.nuevo()
        repartidor.asignar_prospecto_a(vendedor=asignado_a, prospecto=prospecto_repetido)
        return prospecto_repetido

    def _avanzar_en_el_tiempo_si_la_fecha_es_futura(self, fecha, tiempo_frizado):
        gap = fecha - timezone.now()
        if tiempo_frizado is not None and gap > timezone.timedelta():
            tiempo_frizado.tick(gap)

    def _fecha_fuera_del_gap_de_mergeo(self, prospecto_original):
        grupo_de_repetidos = prospecto_original.grupo_de_repetidos()
        fecha = None
        if grupo_de_repetidos:
            repetidos = grupo_de_repetidos.prospectos().all()
            repetido = repetidos.ordenar_por_fecha_de_creacion(ascendente=False).last()

            if repetido:
                fecha = repetido.obtener_fecha_de_creacion()
        fecha = fecha or timezone.now()
        if fecha < prospecto_original.fecha_creacion:
            fecha = prospecto_original.fecha_creacion

        fecha_fuera_del_gap = fecha + ControladorDelTiempo().timedelta_del_doble_del_gap()
        return fecha_fuera_del_gap

    def asignar_venta_a(self, vendedor, fecha=None, prospecto=None):
        if not prospecto:
            prospectos = vendedor.prospectos.filter(estado='N')
            assert prospectos.count() > 0
            prospecto = prospectos.first()
        if not fecha:
            fecha = timezone.now()
        gestor = GestorDeVenta()
        venta = gestor.nueva_venta(prospecto=prospecto, vendedor=vendedor,
                                   fecha_de_realizacion=fecha, precio=100000, numero_de_contrato=1, marca='ford',
                                   modelo='falcon')
        return venta

    def asignar_a(self, vendedor, prospectos_nuevos, tiempo_de_respuesta, ventas=0, fecha=None,
                  nombre='prospecto_test', nombre_de_marca=''):
        cantidad = prospectos_nuevos + ventas
        assert cantidad > 0
        for indice in range(0, cantidad):
            prospecto = ProspectosFactory(vendedor=vendedor, _tiempo_de_respuesta=tiempo_de_respuesta,
                                          fecha_de_asignacion_a_vendedor=fecha, nombre=nombre, marca=nombre_de_marca,
                                          campania=self.fixture['camp_1'])
        # self.asignar_prospectos_nuevos_para(vendedores=[vendedor], cantidad=cantidad,
        #                                     tiempo_de_respuesta=tiempo_de_respuesta,
        #                                     fecha_de_asignacion_a_vendedor=now(), nombre=nombre,
        #                                     nombre_de_marca=nombre_de_marca, )
        for indice in range(0, ventas):
            venta = self.asignar_venta_a(vendedor, fecha)
            prospecto = venta.prospecto
            prospecto._tiempo_de_respuesta = tiempo_de_respuesta
            prospecto.save()

    def asignar_ventas_y_tiempos_de_respuesta(self, vendedor, nuevos, ventas_semanales, ventas_hace_tres_semanas,
                                              tiempo_de_respuesta_semanal, tiempo_de_respuesta_mensual):

        assert (ventas_hace_tres_semanas > 0) & (tiempo_de_respuesta_semanal > 0) & (nuevos > 0) & (
                ventas_semanales > 0)
        promedio = tiempo_de_respuesta_mensual - (
                nuevos + ventas_semanales) * tiempo_de_respuesta_semanal / ventas_hace_tres_semanas
        tres_semanas_atras = timezone.now() - timezone.timedelta(days=21)
        self.asignar_a(vendedor, prospectos_nuevos=0, tiempo_de_respuesta=promedio,
                       ventas=ventas_hace_tres_semanas, fecha=tres_semanas_atras)
        ayer = timezone.now() - timezone.timedelta(days=1)
        self.asignar_a(vendedor, prospectos_nuevos=nuevos, tiempo_de_respuesta=tiempo_de_respuesta_semanal,
                       ventas=ventas_semanales, fecha=ayer)

    def agregar_vendedor_a_supervisor(self, supervisor=None, limite_de_datos_diarios_en_pedidos=None,
                                      limite_de_datos_nuevos_en_pedidos=None,
                                      factor_de_asignacion=10, ultima_actividad=None, first_name=None,
                                      last_name=None):
        if not supervisor:
            supervisor = self.supervisor
        cantidad = supervisor.vendedores.count() + 1
        nombre = 'sup%s-vend%d' % (supervisor.user.last_name, cantidad)
        if last_name is None:
            last_name = nombre
        if first_name is None:
            first_name = nombre
        user = UsersFactory(username=nombre, first_name=first_name, last_name=last_name, password='pw',
                            email='%<EMAIL>' % nombre)
        vendedor = VendedoresFactory(user=user, supervisor=supervisor,
                                     concesionaria=supervisor.concesionaria,
                                     limite_de_datos_diarios_en_pedidos=limite_de_datos_diarios_en_pedidos,
                                     limite_de_datos_nuevos_en_pedidos=limite_de_datos_nuevos_en_pedidos,
                                     factor_de_asignacion=factor_de_asignacion, _ultima_actividad=ultima_actividad)
        return Vendedor.objects.get(pk=vendedor.pk)

    def nuevo_supervisor(self, concesionaria):
        cantidad = concesionaria.supervisores().count() + 1
        nombre = 'conc%s-sup%d' % (concesionaria.nombre, cantidad)
        user = UsersFactory(username=nombre, first_name=nombre, last_name=nombre, password='pw',
                            email='%<EMAIL>' % nombre)
        vendedor = VendedoresFactory(user=user, concesionaria=concesionaria, cargo='Supervisor')
        return Vendedor.objects.get(pk=vendedor.pk)

    def crear_pedido(self, supervisor=None, nombre='', fecha=None, asignar_a='T', vendedor=None,
                     metodo_de_asignacion=None, metodo_por_productividad=False, equipo=None,
                     credito=10, yapa=5, consumido=2, es_renovable=False, campania=None,
                     restringir_por_datos_nuevos=False,
                     restringir_por_datos_diarios=False, restringir_por_acceso=False,
                     forma_de_entrega=PedidoDeProspecto.NUEVA_FORMA_DE_ENTREGA, excluye_campanias=False, calidades=None,
                     factor_de_distribucion=10, finalizado=False,
                     actualizacion_limite_diario_de_supervisor_habilitada=True):

        """
            Idea de refactor. Comentario de Serge:

                aca podemos hacer que el metodo en vez de aceptar bocha de parametros acepte un diccionario y que
                ese diccionario lo cree un metodo. Y que el factory tambien acepte un diccionario.

                Despues podemos tener un factory de "datos para ..." que nombra las distintas entidades, y con eso
                tenes un repositorio de datos para crear diferentes objetos de tu dominio/aplicacion

        """
        if not supervisor:
            supervisor = self.supervisor
        if not metodo_de_asignacion:
            metodo_de_asignacion = MetodosDeAsignacionChoices.UNIFORME
        if not fecha:
            fecha = self.hoy
        pedido = PedidosDeProspectoFactory(
            nombre=nombre, supervisor=supervisor, credito=credito, yapa=yapa, consumido=consumido,
            asignar_a=asignar_a, vendedor=vendedor, fecha=fecha, equipo=equipo,
            es_renovable=es_renovable, metodo_de_asignacion=metodo_de_asignacion,
            metodo_por_productividad=metodo_por_productividad, restringir_por_datos_nuevos=restringir_por_datos_nuevos,
            restringir_por_datos_diarios=restringir_por_datos_diarios, restringir_por_acceso=restringir_por_acceso,
            forma_de_entrega=forma_de_entrega, factor_de_distribucion=factor_de_distribucion, finalizado=finalizado,
            _actualizacion_limite_diario_de_supervisor_habilitada=actualizacion_limite_diario_de_supervisor_habilitada)

        if not campania:
            campania = self.fixture['camp_1']
        categoria = campania.categoria
        pedido.categorias.add(categoria)
        pedido.campanias.add(campania)
        if calidades:
            pedido.cambiar_calidades_por(calidades=calidades)
        else:
            calidad = categoria.tipo_de_origen
            pedido.cambiar_calidades_por(calidades=[calidad])
        if not excluye_campanias:
            pedido.cambiar_a_inclusion_de_campanias()

        pedido.save()
        return pedido

    def agregar_pedido_asignado_a_vendedor(self, vendedor, supervisor=None, fecha=None, credito=10, yapa=5, consumido=2,
                                           campania=None, restringir_por_datos_nuevos=False,
                                           restringir_por_datos_diarios=False, restringir_por_acceso=False,
                                           forma_de_entrega=PedidoDeProspecto.NUEVA_FORMA_DE_ENTREGA,
                                           excluye_campanias=False, calidades=None, factor_de_distribucion=10,
                                           finalizado=False):
        pedido = self.crear_pedido(supervisor=supervisor or vendedor.responsable(), fecha=fecha, asignar_a='V', vendedor=vendedor,
                                   restringir_por_datos_nuevos=restringir_por_datos_nuevos,
                                   restringir_por_datos_diarios=restringir_por_datos_diarios,
                                   restringir_por_acceso=restringir_por_acceso,
                                   credito=credito, yapa=yapa, consumido=consumido, campania=campania,
                                   forma_de_entrega=forma_de_entrega, excluye_campanias=excluye_campanias,
                                   calidades=calidades, factor_de_distribucion=factor_de_distribucion,
                                   finalizado=finalizado)
        return pedido

    def agregar_pedido_para_todos_los_vendedores(self, supervisor=None, metodo_de_asignacion=None,
                                                 metodo_por_productividad=False, fecha=None,
                                                 credito=10, yapa=5, consumido=2,
                                                 campania=None, restringir_por_datos_nuevos=False,
                                                 restringir_por_datos_diarios=False, restringir_por_acceso=False,
                                                 forma_de_entrega=PedidoDeProspecto.NUEVA_FORMA_DE_ENTREGA,
                                                 es_renovable=False, excluye_campanias=False, calidades=None,
                                                 factor_de_distribucion=10, finalizado=False):
        pedido = self.crear_pedido(supervisor=supervisor, fecha=fecha, asignar_a='T',
                                   metodo_de_asignacion=metodo_de_asignacion,
                                   metodo_por_productividad=metodo_por_productividad,
                                   credito=credito, yapa=yapa, consumido=consumido, campania=campania,
                                   restringir_por_datos_nuevos=restringir_por_datos_nuevos,
                                   restringir_por_datos_diarios=restringir_por_datos_diarios,
                                   restringir_por_acceso=restringir_por_acceso,
                                   forma_de_entrega=forma_de_entrega,
                                   es_renovable=es_renovable,
                                   excluye_campanias=excluye_campanias, calidades=calidades,
                                   factor_de_distribucion=factor_de_distribucion, finalizado=finalizado)
        return pedido

    def agregar_pedido_para_poner_a_cargo(
            self, nombre='', fecha=None, forma_de_entrega=PedidoDeProspecto.NUEVA_FORMA_DE_ENTREGA,
            campania=None, consumido=2, supervisor=None, restringir_por_datos_diarios=False,
            restringir_por_acceso=False, excluye_campanias=False, calidades=None,
            factor_de_distribucion=10, credito=10, yapa=5, es_renovable=False, finalizado=False,
            actualizacion_limite_diario_de_supervisor_habilitada=True
    ):
        pedido = self.crear_pedido(
            nombre=nombre, supervisor=supervisor, fecha=fecha, asignar_a='', forma_de_entrega=forma_de_entrega,
            campania=campania, consumido=consumido, yapa=yapa,
            restringir_por_acceso=restringir_por_acceso,
            restringir_por_datos_diarios=restringir_por_datos_diarios,
            excluye_campanias=excluye_campanias, calidades=calidades, credito=credito,
            factor_de_distribucion=factor_de_distribucion, es_renovable=es_renovable, finalizado=finalizado,
            actualizacion_limite_diario_de_supervisor_habilitada=actualizacion_limite_diario_de_supervisor_habilitada)
        return pedido

    def agregar_pedido_asignado_a_equipo(self, equipo, fecha=None, credito=10, yapa=5, consumido=2,
                                         campania=None, forma_de_entrega=PedidoDeProspecto.NUEVA_FORMA_DE_ENTREGA,
                                         excluye_campanias=False, calidades=None, factor_de_distribucion=10,
                                         finalizado=False):
        pedido = self.crear_pedido(supervisor=self.supervisor, fecha=fecha, asignar_a='E', equipo=equipo,
                                   credito=credito, yapa=yapa, consumido=consumido, campania=campania,
                                   forma_de_entrega=forma_de_entrega, excluye_campanias=excluye_campanias,
                                   calidades=calidades, factor_de_distribucion=factor_de_distribucion,
                                   finalizado=finalizado)
        return pedido

    def agregar_configuracion_de_notificacion_a_pedido(self, pedido, por_mail=False, por_sms=False, por_whatsapp=False):
        if hasattr(pedido, 'configuracion_de_notificacion'):
            configuracion = pedido.configuracion_de_notificacion
            configuracion.por_mail = por_mail or configuracion.por_mail
            configuracion.por_sms = por_sms or configuracion.por_sms
            configuracion.por_whatsapp = por_whatsapp or configuracion.por_whatsapp
            configuracion.save()
        else:
            ConfiguracionDeNotificacionDePedido.objects.create(pedido=pedido, por_mail=por_mail,
                                                               por_sms=por_sms, por_whatsapp=por_whatsapp)

    def agregar_configuracion_pilot_para(self, pedido, descripcion, appkey, suborigin_id, business_type):
        configuracion = ConfiguracionDePilot.nuevo(descripcion, appkey, suborigin_id, business_type)
        self.crear_opcion_crm_para_configuracion(pedido, configuracion)

    def agregar_configuracion_sirena_para(self, pedido, descripcion):
        configuracion = ConfiguracionDeSirena.nuevo(descripcion)
        self.crear_opcion_crm_para_configuracion(pedido, configuracion)

    def agregar_configuracion_maipu_para(self, pedido, descripcion):
        configuracion = ConfiguracionDeMaipu.nuevo(descripcion)
        self.crear_opcion_crm_para_configuracion(pedido, configuracion)

    def agregar_configuracion_tecnom_para(self, pedido, descripcion, usuario, contrasenia, vendor_name, proveedor,
                                          subdominio, vendor_email):
        configuracion = ConfiguracionDeTecnom.nuevo(
            descripcion=descripcion, usuario=usuario, contrasenia=contrasenia, vendor_name=vendor_name,
            nombre_de_proveedor=proveedor, sub_dominio=subdominio, vendor_email=vendor_email)
        self.crear_opcion_crm_para_configuracion(pedido, configuracion)

    def crear_opcion_crm_para_configuracion(self, pedido, configuracion):
        opcion_crm = OpcionClienteCRM.nuevo(configuracion=configuracion, pedido=pedido)
        return opcion_crm

    def asignar_conversaciones_nuevas_para(self, vendedores, cantidad):
        conversaciones = []
        for vendedor in vendedores:
            for _ in range(0, cantidad):
                prospecto = self.asignar_prospecto_nuevo_a(vendedor=vendedor)
                conversacion = self.crear_nueva_conversacion_de_whatsapp_para(prospecto=prospecto)
                conversaciones.append(conversacion)

        return conversaciones

    def crear_marca(self, nombre):
        if not nombre:
            raise NameError('Debe definir un nombre para la marca')
        nombre = nombre.lower()
        alias = Alias.objects.con_nombre(nombre)
        if alias:
            return alias.marca()
        return Marca.nueva_normalizada(nombre)

    def crear_modelo(self, nombre, marca):
        modelo = Modelo.objects.de_marca(marca).con_nombre(nombre)
        if modelo is None:
            modelo = Modelo.nuevo(nombre=nombre, codigo=nombre.lower(), marca=marca)
        return modelo

    def crear_log_de_actividad_para_vendedor_con_fecha(self, vendedor, fecha=None):
        if fecha is None:
            fecha = timezone.now()
        log = LogActividad(vendedor=vendedor, anio=fecha.year, mes=fecha.month, ultima=fecha, cantidad=1)
        log.save()

    def crear_propuesta(self, vendedor, identificador=1, titulo="propuesta one"):
        propuesta = Propuesta.nueva(identificador=identificador, user=vendedor.usuario(), titulo=titulo)
        return propuesta

    def crear_conversacion_de_e_avisos_mercado_libre(
            self, vendedor, identificador_propuesta=1, id_mensaje_externo=591,
            contenido='CONVERSACION NUEVA CON MENSAJE NUEVO!', whatsapp_del_interesado='1178675364',
            nombre_del_interesado='Norberto', telefono_del_interesado='11223344', email_del_interesado='<EMAIL>',
            cantidad_de_respuestas_permitidas=2):
        comando = RecibirMensajeDePublicacionEAvisosComando()
        propuesta = self.crear_propuesta(vendedor=vendedor, identificador=identificador_propuesta)
        comando.set_arguments({
            'lugar': PublicacionEAvisos.MERCADO_LIBRE,
            'id_del_aviso_en_el_lugar_de_publicacion': 'MLA757499971',
            'id_de_mensaje_en_el_lugar_de_publicacion': '1234',
            'id_externo_de_propuesta': propuesta.identificador_externo(),
            'id_de_mensaje_externo': id_mensaje_externo,
            'contenido': contenido,
            'whatsapp_del_interesado': whatsapp_del_interesado,
            'username': vendedor.username(),
            'nombre_del_interesado': nombre_del_interesado,
            'telefono_del_interesado': telefono_del_interesado,
            'email_del_interesado': email_del_interesado,
            'cantidad_de_respuestas_permitidas': cantidad_de_respuestas_permitidas
        })
        result = comando.execute()
        return result

    def agregar_mensaje_a_conversacion_e_avisos(
            self, conversacion, vendedor, id_mensaje_externo=691, contenido='MENSAJE NUEVO!',
            whatsapp_del_interesado='1178675364', identificador_propuesta=1,
            nombre_del_interesado='Norberto', telefono_del_interesado='11223344', email_del_interesado='<EMAIL>',
            cantidad_de_respuestas_permitidas=2):
        propuesta = self.crear_propuesta(vendedor=vendedor, identificador=identificador_propuesta)
        comando = RecibirMensajeDePublicacionEAvisosComando()
        comando.set_arguments({
            'lugar': PublicacionEAvisos.MERCADO_LIBRE,
            'id_del_aviso_en_el_lugar_de_publicacion': 'MLA757499971',
            'id_de_mensaje_en_el_lugar_de_publicacion': '1234',
            'id_externo_de_propuesta': propuesta.identificador_externo(),
            'id_de_mensaje_externo': id_mensaje_externo,
            'contenido': contenido,
            'username': vendedor.username(),
            'whatsapp_del_interesado': whatsapp_del_interesado,
            'nombre_del_interesado': nombre_del_interesado,
            'telefono_del_interesado': telefono_del_interesado,
            'email_del_interesado': email_del_interesado,
            'cantidad_de_respuestas_permitidas': cantidad_de_respuestas_permitidas
        })
        result = comando.execute()
        return result

    def _crear_prospectos(self, cantidad, datos, vendedor):
        # Copias los datos para no modificar los originales
        copia_de_datos = dict(datos)
        prospectos = []
        telefono = copia_de_datos['telefono']
        email = copia_de_datos['email']
        copia_de_datos['vendedor'] = vendedor
        for indice in range(0, cantidad):
            if not telefono and not email:
                copia_de_datos['telefono'] = self._random_telefono()
            resultado = self.repartidor.crear_nuevo_prospecto_desde(copia_de_datos)
            prospectos.append(resultado.prospecto())
        return prospectos

    def crear_llamadas_anura(self, vendedor, fecha_comienzo, duracion, prospecto):
        intento_de_llamada = IntentoDeLlamado.nuevo(vendedor=vendedor, prospecto=prospecto,
                                                    telefono=prospecto.obtener_telefono())
        llamada = LlamadaRealizadaDeAnura.nueva(intento_de_llamado=intento_de_llamada, fecha_comienzo=fecha_comienzo,
                                                id_externo=intento_de_llamada.id,
                                                duracion=duracion, audio_url='')
        return llamada


    def agregar_campo_extra_a(self, prospecto, nombre, valor):
        return CampoExtra.nuevo(prospecto=prospecto, nombre=nombre, valor=valor)



class ModificadorDeContextoParaTesting(object):
    @classmethod
    def nuevo(cls):
        modificador = cls()
        return modificador

    def modificar_fecha_de_creacion_de_prospecto(self, prospecto, nueva_fecha_de_creacion):
        prospecto.fecha_creacion = nueva_fecha_de_creacion
        prospecto.save()
        return prospecto

    def modificar_proveedor_de_prospecto(self, prospecto, proveedor):
        prospecto.proveedor = proveedor
        prospecto.save()
        return prospecto

    def modificar_marca_de_prospecto(self, prospecto, marca):
        prospecto._marca = marca
        prospecto.save()
        return prospecto

    def modificar_modelo_de_prospecto(self, prospecto, modelos):
        prospecto._modelos = modelos
        prospecto.save()
        return prospecto

    def modificar_usuario_a_inactivo(self, usuario):
        usuario.is_active = False
        usuario.save()
        return usuario

    def modificar_usuario_a_activo(self, usuario):
        usuario.is_active = True
        usuario.save()
        return usuario
