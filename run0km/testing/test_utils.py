from django.core.files.uploadedfile import SimpleUploadedFile


def reload_model(obj):
    return obj.__class__.objects.get(pk=obj.pk)


class PedidoDeCreacionDeProspecto(object):
    def desde_api_con_formato_simple(self):
        raise NotImplementedError("Sublass Responsiblity")

    def desde_chat_con_formato_simple(self):
        raise NotImplementedError("Sublass Responsiblity")

    def desde_chat_con_formato_editable(self):
        raise NotImplementedError("Subclass Responsibility")


class PedidoDeCreacionXML(PedidoDeCreacionDeProspecto):
    # TODO: Usar el mismo XML_DATA de base y overridear en vez de copy-paste

    def base_con(self, prospectos, origen=None, campania=None, source=None):
        origen = origen or 'S'
        campania = campania or 'SMS'
        source = source or 'IDProv1234'

        data = self.base_template() % {
            'origen': origen, 'campania': campania, 'source': source, 'prospectos': prospectos}
        return data

    def prospecto_con(self, responsable, vendedor, telefono='', email='', nombre='', marca='', ip='',
                      campos_extras=None, programar_reunion=None, fecha_reunion=None, **kwargs):
        campos_extras = campos_extras or {}
        prospecto_xml = self.prospecto_tempplate() % {
            'email': email, 'telefono': telefono, 'responsable': responsable, 'vendedor': vendedor,
            'nombre': nombre, 'marca': marca, 'programar_reunion': programar_reunion or '',
            'fecha_reunion': fecha_reunion or ''}

        campos = dict(list({'ip': ip}.items()) + list(campos_extras.items()))
        prospecto_xml = self._agregar_campos_extras(campos, prospecto_xml)
        prospecto_xml = self._agregar_campos_extras_fuera_del_tag_extra(prospecto_xml=prospecto_xml, **kwargs)
        prospecto_xml = self._remover_campos_vacios(prospecto_xml)
        return prospecto_xml

    def prospecto_tempplate(self):
        return '<list-item>' \
               '<responsable>%(responsable)s</responsable>' \
               '<vendedor>%(vendedor)s</vendedor>' \
               '<nombre>%(nombre)s</nombre>' \
               '<telefono>%(telefono)s</telefono>' \
               '<email>%(email)s</email>' \
               '<marca>%(marca)s</marca>' \
               '<programar_reunion>%(programar_reunion)s</programar_reunion>' \
               '<fecha_reunion>%(fecha_reunion)s</fecha_reunion>' \
               '<extra></extra>' \
               '<extras-inline></extras-inline>' \
               '</list-item>'

    def base_template(self):
        return '''<?xml version="1.0" encoding="utf-8" ?>
            <root>
              <origen>%(origen)s</origen>
              <campania>%(campania)s</campania>
              <source>%(source)s</source>
              <prospectos>%(prospectos)s</prospectos>
            </root>
            '''

    def desde_api_con_formato_simple(self):
        data = '''<?xml version="1.0" encoding="utf-8" ?>
        <root>
          <origen>S</origen>
          <campania>SMS</campania>
          <source>IDProv1234</source>
          <prospectos>
            <list-item>
              <responsable>sup1</responsable>
              <vendedor>vend1</vendedor>
              <nombre>Nombre Prospecto 1</nombre>
              <telefono>444000</telefono>
              <email><EMAIL></email>
              <direccion>Calle 4000</direccion>
              <fecha>10/05/2013 00:00</fecha>
              <source>IDProvXXX</source>
              <extra>
                <list-item>
                  <nombre>Nombre Campo Extra 0</nombre>
                  <valor>Valor Extra 0</valor>
                </list-item>
                <list-item>
                  <nombre>IP</nombre>
                  <valor></valor>
                </list-item>
                <list-item>
                  <nombre>Nombre Campo Extra 1</nombre>
                  <valor>Valor Extra 1</valor>
                </list-item>
              </extra>
            </list-item>
            <list-item>
              <nombre>Nombre prospecto 2</nombre>
              <telefono>444001</telefono>
              <email><EMAIL></email>
              <fecha>2010-05-13 00:00</fecha>
            </list-item>
          </prospectos>
        </root>
        '''
        return data

    def desde_chat_con_formato_simple(self):
        data = '''<?xml version="1.0" encoding="utf-8" ?>
        <root>
          <origen>S</origen>
          <campania>SMS</campania>
          <source>IDProv1234</source>
          <chat-operador></chat-operador>
          <prospectos>
            <list-item>
              <responsable>sup1</responsable>
              <vendedor>vend1</vendedor>
              <nombre>Nombre Prospecto 1</nombre>
              <telefono>444000</telefono>
              <email><EMAIL></email>
              <marca></marca>
              <direccion>Calle 4000</direccion>
              <fecha>10/05/2013 00:00</fecha>
              <source>IDProvXXX</source>
              <extra>
                <list-item>
                  <nombre>Nombre Campo Extra 0</nombre>
                  <valor>Valor Extra 0</valor>
                </list-item>
                <list-item>
                  <nombre>Nombre Campo Extra 1</nombre>
                  <valor>Valor Extra 1</valor>
                </list-item>
`                <list-item>
                  <nombre>Operator.Token</nombre>
                  <valor>Operator.Token-value</valor>
                </list-item>
              </extra>
            </list-item>
          </prospectos>
        </root>
        '''
        return data

    def desde_chat_con_formato_editable(self, origen='S', campania='SMS', source='IDProv1234', operador_de_chat='',
                                        supervisor='sup1', vendedor='vend1', telefono='444000', direccion='Calle 4000',
                                        nombre='Nombre Prospecto 1', email='<EMAIL>'):
        # Ejemplo de operador de chat = vendedor.username()

        data = '''<?xml version="1.0" encoding="utf-8" ?>
        <root>
          <origen>{0}</origen> '''.format(origen) + '''
          <campania>{0}</campania>'''.format(campania) + '''
          <source>{0}</source>'''.format(source) + '''
          <chat-operador></chat-operador>'''.format(operador_de_chat) + '''
          <prospectos>
            <list-item>
              <responsable>sup1</responsable>'''.format(supervisor) + '''
              <vendedor>vend1</vendedor>'''.format(vendedor) + '''
              <nombre>Nombre Prospecto 1</nombre>'''.format(nombre) + '''
              <telefono>444000</telefono>'''.format(telefono) + '''
              <email><EMAIL></email>'''.format(email) + '''
              <direccion>Calle 4000</direccion>'''.format(direccion) + '''
              <fecha>10/05/2013 00:00</fecha>
              <source>IDProvXXX</source>
              <extra>
                <list-item>
                  <nombre>Nombre Campo Extra 0</nombre>
                  <valor>Valor Extra 0</valor>
                </list-item>
                <list-item>
                  <nombre>Nombre Campo Extra 1</nombre>
                  <valor>Valor Extra 1</valor>
                </list-item>
              </extra>
            </list-item>
          </prospectos>
        </root>
        '''
        return data

    def _agregar_campos_extras(self, campos_extra, prospecto_xml):
        campos = ''
        for nombre, valor in list(campos_extra.items()):
            if valor:
                campos += self._campos_extras_con(nombre, valor)
        extras = '<extra>%s</extra>' % campos
        prospecto_xml = prospecto_xml.replace('<extra></extra>', extras)
        return prospecto_xml

    def _agregar_campos_extras_fuera_del_tag_extra(self, prospecto_xml, **kwargs):
        campos = ''
        for nombre, valor in list(kwargs.items()):
            if valor:
                campos += '<%(nombre)s>%(valor)s</%(nombre)s>' % {'nombre': nombre, 'valor': valor}
        prospecto_xml = prospecto_xml.replace('<extras-inline></extras-inline>' , campos)
        return prospecto_xml

    def _campos_extras_con(self, nombre, valor):
        template = '<list-item><nombre>%s</nombre><valor>%s</valor></list-item>'
        return template % (nombre, valor)

    def _remover_campos_vacios(self, prospecto_xml):
        prospecto_xml = prospecto_xml.replace('<responsable></responsable>', '')
        prospecto_xml = prospecto_xml.replace('<vendedor></vendedor>', '')
        prospecto_xml = prospecto_xml.replace('<marca></marca>', '')
        prospecto_xml = prospecto_xml.replace('<extra></extra>', '')
        return prospecto_xml


class IngresoDeProspectosViaCSVHelper(object):
    def json_con_archivo_con_template_para(self, indice):
        return self.json_con_archivo_con(
            '\nresponsable,vendedor,nombre,telefono,otro,source\n'
            'sup1,,Josefo,4342-234%d,,567\n'
            ',vend2,Pepe,4342-234%d,extra,\n' % (indice, indice + 1))

    def json_con_archivo_vacio(self):
        return self.json_con_archivo_con(contenido='')

    def json_con_archivo_con(self, contenido):
        archivo = self.archivo_con(contenido=contenido)
        return self.json_para_archivo(archivo)

    def archivo_vacio(self):
        return self.archivo_con(contenido='')

    def json_para_archivo(self, archivo):
        return {'file_upload': archivo}

    def archivo_con(self, contenido):
        return SimpleUploadedFile('file.csv', contenido.encode('utf-8'))

    def archivo_con_tabla(self, header, filas):
        contenido = header + '\n'
        contenido += '\n'.join(filas)
        return self.archivo_con(contenido)

    def archivo_con_tabla_fila(self, header, **kwargs):
        keys = header.split(', ')
        assert set(keys) == set(kwargs.keys())
        valores = []
        for key in keys:
            valores.append(kwargs.get(key))
        fila = ', '.join(valores)
        return self.archivo_con_tabla(header, [fila])

    def json_con_archivo_con_fila(self, responsable, vendedor, nombre, telefono, otro, source):
        archivo = self.archivo_con_fila(responsable, vendedor, nombre, telefono, otro, source)
        return self.json_para_archivo(archivo)

    def archivo_con_fila(self, responsable, vendedor, nombre, telefono, otro='', source=''):
        header = """responsable,vendedor,nombre,telefono,otro,source"""
        fila = ','.join([responsable, vendedor, nombre, telefono, otro, source])
        return self.archivo_con_tabla(header, filas=[fila])


