# -*- coding: utf-8 -*-
from datetime import timedelta

from django.db.models import Q

from core.models import Sistema
from occ.models import CampaniaDeComunicacion
from occ.tests.factories import MedioFactory
from testing.factories import *


def basic_fixture():

    # Aux
    hace_dias = now() + timedelta(days=-10)
    HACE_anios = now() + timedelta(days =-1000)
    HACE_anio = now() + timedelta(days=-365)
    pw = "pbkdf2_sha256$10000$EHBs52ZGwBa2$nUonl+KPXuXo3pFeESkQmPBOpB3bjMh8r+VovoS5UzE="
    f = {}
    # Admin:
    f['usr_admin'] = UsersFactory(username='admin', is_superuser=True, is_staff=True, password=pw, email="<EMAIL>")

    #####
    crear_objetos_pilares(f, pw)

    # Concesionaria
    f['conce_1'] = ConcesionariasFactory(nombre='conce_1', dia_inicio_periodos=28, dia_fin_periodos=20, sitio=None)
    f['conce_2'] = ConcesionariasFactory(nombre='conce_2', dia_inicio_periodos=28, dia_fin_periodos=20, sitio=None)

    # Gerentes
    f['usr_ger_1'] = UsersFactory(username='ger1', first_name='ger1', last_name='ger1', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['ger_1'] = GerentesFactory(user=f['usr_ger_1'], concesionaria=f['conce_1'])

    f['usr_ger_2'] = UsersFactory(username='ger2', first_name='ger2', last_name='ger2', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['ger_2'] = GerentesFactory(user=f['usr_ger_2'], concesionaria=f['conce_2'])

    # Supervisores
    f['usr_1'] = UsersFactory(username='sup1', first_name='sup1', last_name='sup1', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['sup_1'] = VendedoresFactory(user=f['usr_1'], cargo='Supervisor', alerta_diaria=False, alerta_a_supervisor=False, concesionaria=f['conce_1'])

    f['usr_2'] = UsersFactory(username='sup2', first_name='sup2', last_name='sup2', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['sup_2'] = VendedoresFactory(user=f['usr_2'], cargo='Supervisor', alerta_diaria=False, alerta_a_supervisor=False, concesionaria=f['conce_1'])

    f['usr_9'] = UsersFactory(username='sup3', first_name='sup3', last_name='sup3', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['sup_3'] = VendedoresFactory(user=f['usr_9'], cargo='Supervisor', alerta_diaria=False, alerta_a_supervisor=False, concesionaria=f['conce_1'])
    # Vendedores
    f['usr_3'] = UsersFactory(username='vend1', first_name='vend1', last_name='vend1', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['vend_1'] = VendedoresFactory(user=f['usr_3'], supervisor_id=f['sup_1'].id, concesionaria=f['conce_1'])
    f['usr_4'] = UsersFactory(username='vend2', first_name='vend2', last_name='vend2', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['vend_2'] = VendedoresFactory(user=f['usr_4'], supervisor_id=f['sup_1'].id, concesionaria=f['conce_1'])
    f['usr_5'] = UsersFactory(username='vend3', first_name='vend3', last_name='vend3', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['vend_3'] = VendedoresFactory(user=f['usr_5'], supervisor_id=f['sup_2'].id, concesionaria=f['conce_1'])
    f['usr_6'] = UsersFactory(username='vend4', first_name='vend4', last_name='vend4', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['vend_4'] = VendedoresFactory(user=f['usr_6'], supervisor_id=f['sup_2'].id, concesionaria=f['conce_1'])
    f['usr_7'] = UsersFactory(username='vend5', first_name='vend5', last_name='vend5', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['vend_5'] = VendedoresFactory(user=f['usr_7'], supervisor_id=f['sup_1'].id)
    f['usr_8'] = UsersFactory(username='vend6', first_name='vend6', last_name='vend6', last_login=hace_dias, password=pw, email='<EMAIL>')
    f['vend_6'] = VendedoresFactory(user=f['usr_8'], supervisor_id=f['sup_3'].id)
    #Creditos
    f['crd_1'] = CreditoFactory(vendedor=f['vend_1'], credito_base=1000000000)
    f['crd_2'] = CreditoFactory(vendedor=f['sup_1'], credito_base=1000000000)

    #Campanias
    f['tipo_s'] = TipoDeOrigen.objects.get(codigo='S')
    f['tipo_w'] = TipoDeOrigen.objects.get(codigo='W')
    f['tipo_m'] = TipoDeOrigen.objects.get(codigo='M')
    f['cat_s'] = cat_s = CategoriaDeCampania.objects.get(
        nombre='Genérica SMS', tipo_de_origen__codigo='S', es_externa=False)
    f['cat_m'] = cat_m = CategoriaDeCampania.objects.get(
        nombre='Genérica Mailing', tipo_de_origen__codigo='M', es_externa=False)
    f['cat_w'] = cat_w = CategoriaDeCampania.objects.get(
        nombre='Genérica Web', tipo_de_origen__codigo='W', es_externa=False)
    cs = f['camp_1'] = CampaniasFactory(nombre='SMS', categoria=cat_s)
    cm = f['camp_2'] = CampaniasFactory(nombre='Mail', categoria=cat_m)
    cw = f['camp_3'] = CampaniasFactory(nombre='Web', categoria=cat_w)

    # Prospectos Vendedor 1
    f['p_1'] = ProspectosFactory(campania=cs, vendedor=f['vend_1'], fecha=HACE_anio,
                                 fecha_de_asignacion_a_vendedor=hace_dias)
    f['p_2'] = ProspectosFactory(campania=cs, vendedor=f['vend_1'], fecha=HACE_anios,
                                 fecha_de_asignacion_a_vendedor=hace_dias, estado='P')
    f['p_3'] = ProspectosFactory(campania=cs, vendedor=f['vend_1'], fecha=hace_dias,
                                 fecha_de_asignacion_a_vendedor=hace_dias)
    # Sin Asignar
    f['p_4'] = ProspectosFactory(campania=cs, fecha= HACE_anios)
    f['p_5'] = ProspectosFactory(campania=cs, fecha= HACE_anios)
    # Vendedor 2
    f['p_6'] = ProspectosFactory(campania=cs, vendedor=f['vend_2'], fecha=hace_dias,
                                 fecha_de_asignacion_a_vendedor=hace_dias)
    f['p_7'] = ProspectosFactory(campania=cs, vendedor=f['vend_2'], fecha=hace_dias,
                                 fecha_de_asignacion_a_vendedor=hace_dias)
    f['p_8'] = ProspectosFactory(campania=cm, vendedor=f['vend_2'],
                                 fecha_de_asignacion_a_vendedor=hace_dias)

    # Vendedor 3
    f['p_9'] = ProspectosFactory(campania=cw, vendedor=f['vend_3'], fecha=hace_dias,
                                 fecha_de_asignacion_a_vendedor=hace_dias)
    f['p_10'] = ProspectosFactory(campania=cw, vendedor=f['vend_3'], fecha=hace_dias,
                                  fecha_de_asignacion_a_vendedor=hace_dias)
    f['p_11'] = ProspectosFactory(campania=cw, vendedor=f['vend_3'],
                                  fecha_de_asignacion_a_vendedor=hace_dias)

    # Vendedor 4
    f['p_12'] = ProspectosFactory(campania=cw, vendedor=f['vend_4'], fecha=hace_dias,
                                  fecha_de_asignacion_a_vendedor=hace_dias)
    f['p_13'] = ProspectosFactory(campania=cw, vendedor=f['vend_4'], fecha=hace_dias,
                                  fecha_de_asignacion_a_vendedor=hace_dias)

    # Vendedor 5
    f['p_14'] = ProspectosFactory(campania=cw, vendedor=f['vend_5'], fecha=hace_dias,
                                  fecha_de_asignacion_a_vendedor=hace_dias)
    f['t_1'] = TelefonosExtraFactory(prospecto=f['p_14'], vendedor=f['vend_5'])

    # Vendedor 6
    f['p_15'] = ProspectosFactory(campania=cs, vendedor=f['vend_6'], responsable=f['sup_3'], fecha=HACE_anio,
                                  fecha_de_asignacion_a_vendedor=hace_dias)

    # Supervisor 3
    f['p_16'] = ProspectosFactory(campania=cs, vendedor=f['sup_3'], responsable=f['sup_3'], fecha=HACE_anio,
                                  fecha_de_asignacion_a_vendedor=hace_dias)

    # Comentarios
    f['comentario_1'] = ComentariosFactory(vendedor=f['vend_1'], comentario='Llamado planificado...', prospecto=f['p_2'], datetime=hace_dias)

    # Llamados
    f['llamado_1'] = LlamadosFactory(vendedor=f['vend_1'], fecha=hace_dias, prospecto=f['p_2'], datetime=hace_dias)

    # Motivo de Finalizacion
    f['motivo_1'] = MotivoDeFinalizacionFactory(descripcion="Usuario Pierde Interes")
    f['motivo_2'] = MotivoDeFinalizacionFactory(descripcion="Usuario no responde")

    #Equipos
    f['equipo_1'] = EquiposFactory(supervisor=f['sup_1'], nombre='Equipo 1')
    f['equipo_2'] = EquiposFactory(supervisor=f['sup_2'], nombre='Equipo 2')

    #Campaña de venta
    f['medio_1'] = MedioFactory()
    f['cv_1'] = CampaniaDeComunicacion.nueva_campania_con(nombre='nombre', mensaje='mensaje', medio=f['medio_1'],
                                                          vendedor=f['sup_1'],
                                                          prospectos=Prospecto.objects.filter(vendedor=f['vend_1']))
    f['cv_2'] = CampaniaDeComunicacion.nueva_campania_con(nombre='nombre', mensaje='mensaje', medio=f['medio_1'],
                                                          vendedor=f['sup_1'],
                                                          prospectos=Prospecto.objects.filter(vendedor=f['vend_4']))
    f['cv_3'] = CampaniaDeComunicacion.nueva_campania_con(nombre='nombre', mensaje='mensaje', medio=f['medio_1'],
                                                          vendedor=f['sup_1'],
                                                          prospectos=Prospecto.objects.filter(vendedor=f['vend_5']))
    f['cv_4'] = CampaniaDeComunicacion.nueva_campania_con(nombre='nombre2', mensaje='mensaje', medio=f['medio_1'],
                                                          vendedor=f['sup_1'],
                                                          prospectos=Prospecto.objects.filter(Q(vendedor=f['vend_4']) |
                                                                                              Q(vendedor=f['vend_1'])))
    f['cv_5'] = CampaniaDeComunicacion.nueva_campania_con(nombre='nombre', mensaje='mensaje', medio=f['medio_1'],
                                                          vendedor=f['sup_3'],
                                                          prospectos=Prospecto.objects.filter(vendedor=f['sup_3']))

    return f


def crear_objetos_pilares(f, pw):
    tipo_s = TipoDeOrigenFactory(nombre='SMS', codigo='S')
    tipo_m = TipoDeOrigenFactory(nombre='Mailing', codigo='M')
    tipo_w = TipoDeOrigenFactory(nombre='Web', codigo='W')

    cat_s = CategoriasDeCampaniaFactory(nombre='Genérica SMS', valor=1, tipo_de_origen=tipo_s)
    cat_m = CategoriasDeCampaniaFactory(nombre='Genérica Mailing', valor=1, tipo_de_origen=tipo_m)
    cat_w = CategoriasDeCampaniaFactory(nombre='Genérica Web', valor=1, tipo_de_origen=tipo_w)

    generica_sms = CampaniasFactory(nombre='Genérica SMS', categoria=cat_s)
    generica_mailing = CampaniasFactory(nombre='Genérica Mailing', categoria=cat_m)
    generica_web = CampaniasFactory(nombre='Genérica Web', categoria=cat_w)

    sistema = Sistema.instance()
    sistema.configurar_ids_de_campanias_genericas([generica_mailing.id, generica_sms.id, generica_web.id])

    TipoDeReporteFactory(nombre='Entrega')
    TipoDeReporteFactory(nombre='Ventas')
    TipoDeReporteFactory(nombre='Uso')

    f['usr_migracion'] = UsersFactory(username="distribucion", first_name="Administrador", last_name="Distribucion",
                                      is_staff=True, password=pw, email="<EMAIL>")

