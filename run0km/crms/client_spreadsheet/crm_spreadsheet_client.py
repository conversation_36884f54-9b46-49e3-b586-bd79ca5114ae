# coding=utf-8
import os

import gspread
from django.conf import settings
from oauth2client.service_account import ServiceAccountCredentials

from core.locker.errors import ResourceLockedError
from core.locker.mem_locker import Locker
from gsuite.models.spreadsheet import GSuiteDriveSpreadsheetForDeliveryRunUser
from prospectos.utils.exportacion_a_csv import GeneradorDeDatosDeExportacionDeProspectos


class CRMSpreadsheetClient(object):
    """
        Mis instancias saben incluir un prospecto a la spreadsheet del supervisor. Para eso sabe responder #call.

        Nota: quedó en un spanglish porque es necesario que sea polimorfico a los ApiClient y sus subclases.

        TODO future refactor: usar la app gsuite para que se ocupe de elegir entre gmail esto y otras gApps + auth
    """

    def __init__(self, supervisor):
        super().__init__()
        self._supervisor = supervisor
        self.gspread_client = None
        self._locker = Locker.new_for_group(group_name='ingresos-de-datos-google-sheets')

    @classmethod
    def nuevo_para(cls, supervisor):
        return cls(supervisor)

    def name(self):
        return 'CRMSpreadsheetClient'

    def call(self, a_spreadsheet_lead):
        """
        Add request to the Spreadsheet.
        :param a_spreadsheet_lead: SpreadsheetLead
        """
        planilla = GSuiteDriveSpreadsheetForDeliveryRunUser.objects.para_supervisor(self._supervisor)
        resultado = self._evaluar_lockeando(planilla=planilla, funcion=self._authenticate_and_write,
                                            argumentos=[a_spreadsheet_lead])
        return resultado

    def _evaluar_lockeando(self, planilla, funcion, argumentos):
        """
        Para deshabilitar el locker, descomentar la linea de abajo y comentar el resto de la funcion.
        """

        # return funcion(*argumentos)
        if planilla is not None:
            spreadsheet_lead_resource_name = self._resource_name_for(planilla)
            resultado = self._locker.do_locking_each([spreadsheet_lead_resource_name], funcion, argumentos)
            return resultado
        else:
            return funcion(*argumentos)

    def _resource_name_for(self, planilla):
        return planilla.spreadsheet_key()

    def _authenticate_and_write(self, a_spreadsheet_lead):
        # Este método puede raisear APIError de GSpread. Por ahora dejamos que explote

        credentials = self._google_drive_credentials()
        self._authorize(credentials)
        self._write_lead_for(a_spreadsheet_lead)

    def _es_resource_locked_error(self, exception):
        return isinstance(exception, ResourceLockedError)

    def _google_drive_credentials(self):
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        oauth2_json_path = os.path.join(settings.PROJECT_PATH, 'gsuite-oauth2-credentials.json')
        credentials = ServiceAccountCredentials.from_json_keyfile_name(oauth2_json_path, scope)
        return credentials

    def _write_lead_for(self, a_spreadsheet_lead):
        email = self._supervisor.email()
        if email:
            spreadsheet = self._get_spreadsheet_for(self._supervisor)
            worksheet = spreadsheet.sheet1
            self._add_prospect_information(a_spreadsheet_lead, worksheet)

    def _authorize(self, credentials):
        self.gspread_client = gspread.authorize(credentials)

    def _create_sheet(self, sheet_name):
        sheet = self.gspread_client.create(sheet_name)
        return sheet

    def _open_sheet(self, key):
        sheet = self.gspread_client.open_by_key(key)
        return sheet

    def _get_spreadsheet_for(self, supervisor):
        planilla = GSuiteDriveSpreadsheetForDeliveryRunUser.objects.para_supervisor(supervisor)
        if planilla is not None:
            spreadsheet = self._open_sheet(planilla.spreadsheet_key())
            return spreadsheet
        else:
            spreadsheet = self._prepare_new_spreadsheet_for(supervisor)
            return spreadsheet

    def _prepare_new_spreadsheet_for(self, responsable):
        spreadsheet_name = self._spreadsheet_name_for(responsable)
        spreadsheet = self._create_sheet(spreadsheet_name)
        worksheet = spreadsheet.sheet1
        self._add_headers(worksheet, self._default_headers())
        self._save_spreadsheet_for(spreadsheet, responsable)
        self._share_spreadsheet_with(spreadsheet, responsable)
        self._share_spreadsheet_with_gsuite_owner(spreadsheet)
        return spreadsheet

    def _spreadsheet_name_for(self, supervisor):
        return "Prospectos asignados a %s" % supervisor.full_name()

    def _save_spreadsheet_for(self, spreadsheet, supervisor):
        GSuiteDriveSpreadsheetForDeliveryRunUser.new_for(supervisor, spreadsheet.id)

    def _share_spreadsheet_with(self, spreadsheet, supervisor):
        spreadsheet.share(supervisor.email(), perm_type='user', role='writer')

    def _share_spreadsheet_with_gsuite_owner(self, spreadsheet):
        spreadsheet.share(settings.GSUITE_OWNER_EMAIL, perm_type='user', role='writer')

    def _add_prospect_information(self, a_spreadsheet_lead, worksheet):
        self._add_values_in_row(worksheet, a_spreadsheet_lead.as_dict())

    def _add_headers(self, worksheet, headers):
        row_to_fill = 1
        for col_0_indexed, value in enumerate(headers):
            col = col_0_indexed + 1
            worksheet.update_cell(row_to_fill, col, value)

    def _add_values_in_row(self, worksheet, row):
        values = self._spreadsheet_row_values_for(row)
        result = worksheet.append_row(values)
        print(result)

    def _spreadsheet_row_values_for(self, row):
        headers = self._default_headers()
        values = []
        for each_header in headers:
            value = row[each_header]
            values.append(value)
        return values

    def _default_headers(self):
        return GeneradorDeDatosDeExportacionDeProspectos.campos_por_defecto()
