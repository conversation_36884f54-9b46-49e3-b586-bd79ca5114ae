# coding=utf-8
from crms.integraciones.google_spreadsheet import IntegradorConGoogleSpreadsheet
from crms.tests.integraciones.test_integracion_de_pedidos_con_crm import IntegracionDePedidosConCRMTest


class IntegradorConGoogleSpreadsheetTest(IntegracionDePedidosConCRMTest):

    def test_cliente_es_configurado_desde_los_parametros_de_la_configuracion(self):
        # Dado
        configuracion = self._crear_configuracion()
        integrador = IntegradorConGoogleSpreadsheet.nuevo_para(configuracion=configuracion)
        prospecto = self._crear_prospecto()

        # Cuando
        cliente = integrador.cliente_para(prospecto)

        # Entonces
        self.assertEqual(cliente.name(), 'CRMSpreadsheetClient')

    def test_la_generacion_de_request_desde_prospecto_es_exitosa(self):
        # Dado
        prospecto = self._crear_prospecto()
        configuracion = self._crear_configuracion()
        integrador = IntegradorConGoogleSpreadsheet.nuevo_para(configuracion=configuracion)

        # Cuando
        request = integrador.generar_lead_para(prospecto)
        datos = request.as_dict()

        # Entonces
        self._validar_generacion_de_datos_desde_prospecto(datos, prospecto, configuracion)

    def _crear_configuracion(self):
        return self._crear_configuracion_maipu()

    def _validar_generacion_de_datos_desde_prospecto(self, datos, prospecto, configuracion):
        """El formato exacto es testeado en los tests de MaipuRequest"""

        datos_esperados = self._lead_para(prospecto, configuracion)
        self.assertEqual(datos, datos_esperados)

    def _lead_para(self, prospecto, configuracion):
        fecha_como_string = prospecto.obtener_fecha().strftime('%d/%m/%Y %H:%M')
        lead = {
            'modelos': 'Modelo_1',
            'provincia': '',
            'estado': 'Nuevo',
            'fecha': fecha_como_string,
            'nombre_alternativo': '',
            'mensaje': 'hola que tal',
            'marca': 'Marqua_1',
            'campa\xf1a': 'SMS',
            'origen': 'S',
            'vendedor': 'suppedidos-user14-sup1-vend1',
            'localidad': '',
            'nombre': 'nombre_1',
            'prefijo': '',
            'telefono': '11778899',
            'email': '<EMAIL>'
        }
        return lead
