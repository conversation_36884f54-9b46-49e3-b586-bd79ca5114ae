# coding=utf-8
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.test import override_settings

from lib.client_pilot import PilotLead
from crms.integraciones import IntegradorConPilot
from crms.tests.integraciones.test_integracion_de_pedidos_con_crm import IntegracionDePedidosConCRMTest
from prospectos.models import CampoExtra


@override_settings(PILOT_DEBUG=True)
class IntegradorConPilotTest(IntegracionDePedidosConCRMTest):
    TEST_URL = 'https://sandbox.pilot.com/api/'

    @override_settings(PILOT_URL=TEST_URL)
    def test_cliente_es_configurado_desde_los_parametros_de_la_configuracion(self):
        # Dado
        configuracion = self._crear_configuracion()
        integrador = IntegradorConPilot.nuevo_para(configuracion=configuracion)

        # Cuando
        cliente = integrador.cliente_para(prospecto=None)

        # Entonces
        self.assertEqual(cliente.name(), 'Pilot')
        self.assertEqual(cliente.url, self.TEST_URL)

    def test_la_generacion_de_request_desde_prospecto_se_realiza_correctamente(self):
        # Dado
        prospecto = self._crear_prospecto()
        configuracion = self._crear_configuracion()
        integrador = IntegradorConPilot.nuevo_para(configuracion=configuracion)

        # Cuando
        request = integrador.generar_lead_para(prospecto)
        datos = request.as_dict()

        # Entonces
        self._validar_generacion_de_datos_desde_prospecto(datos, prospecto, configuracion)

    def _crear_configuracion(self):
        return self._crear_configuracion_pilot()

    def _validar_generacion_de_datos_desde_prospecto(self, datos, prospecto, configuracion):
        """El formato exacto es testeado en los tests de PilotRequest"""
        request = self._lead_pilot_para(prospecto, configuracion)
        self.assertEqual(datos, request.as_dict())

    def _lead_pilot_para(self, prospecto, configuracion):
        request = PilotLead.new_creation_action(
            phone=prospecto.telefono,
            business_type_id=configuracion.business_type(),
            contact_type_id=settings.PILOT_CONTACT_TYPE,
            suborigin_id=configuracion.suborigin_id(),
            firstname=prospecto.nombre,
            appkey=configuracion.appkey(),
        )
        request.debug = settings.PILOT_DEBUG
        request.notification_email = settings.PILOT_NOTIFICATION_EMAIL
        request.cellphone = prospecto.celular()
        try :
            request.notes = CampoExtra.objects.obtener_valor('transcriptUrl', prospecto)
        except ObjectDoesNotExist:
            request.notes = prospecto.mensaje
        request.email = prospecto.obtener_email_activo()
        request.car_brand = prospecto.obtener_marca().codigo()
        request.car_modelo = prospecto.modelos_como_string()
        request.city = prospecto.obtener_localidad()
        request.province = prospecto.obtener_provincia()
        request.provider_service = configuracion.proveedor()
        request.assigned_user = prospecto.obtener_vendedor().email()
        return request
