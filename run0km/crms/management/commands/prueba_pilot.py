# coding=utf-8
import mock
from django.conf import settings
from django.core.management.base import BaseCommand, CommandError
from django.test import override_settings

from crms.integraciones import IntegradorConPilot
from crms.models import ConfiguracionDePilot
from crms.tests.integraciones.tests_integracion_crm_real import MockRequests
from prospectos.models import Prospecto


class Command(BaseCommand):
    help = 'Pruebas de Pilot'

    def add_arguments(self, parser):
        # Positional arguments
        parser.add_argument('--prospecto', dest='prospecto_id')

    def handle(self, *args, **options):
        prospecto_id = options.get('prospecto_id', None)
        try:
            prospecto = Prospecto.objects.get(pk=prospecto_id)
        except Prospecto.DoesNotExist:
            raise CommandError('Prospecto "%s" does not exist' % prospecto_id)

        self.enviar_prospecto(prospecto)

    @override_settings(PILOT_DEBUG=True, PILOT_NOTIFICATION_EMAIL='<EMAIL>')
    def enviar_prospecto(self, prospecto):
        mock_requests = MockRequests()

        with mock.patch("requests.post", side_effect=mock_requests.post) as mock_post:
            configuracion = ConfiguracionDePilot.nuevo(descripcion='Por Defecto',
                                                       appkey=settings.PILOT_DEFAULT_API_KEY,
                                                       suborigin_id=settings.PILOT_DEFAULT_CODIGO_DE_ORIGEN,
                                                       business_type=ConfiguracionDePilot.PLAN_DE_AHORRO)
            integrador = IntegradorConPilot.nuevo_para(configuracion)
            integrador.evaluar_con(prospecto, configuracion)
            print(mock_requests.last_response().content)
