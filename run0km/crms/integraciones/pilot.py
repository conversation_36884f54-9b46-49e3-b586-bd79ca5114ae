# coding=utf-8
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist

from lib.client_pilot import PilotLead, PilotSender
from crms.integraciones.base import IntegradorDeProspectoConCRM
from prospectos.models import CampoExtra


class IntegradorConPilot(IntegradorDeProspectoConCRM):
    def cliente_para(self, prospecto):
        return PilotSender.new_for(settings.PILOT_URL)

    def generar_lead_para(self, prospecto):
        request = PilotLead.new_creation_action(
            phone=prospecto.telefono,
            business_type_id=self._configuracion.business_type(),
            contact_type_id=settings.PILOT_CONTACT_TYPE,
            suborigin_id=self._configuracion.suborigin_id(),
            firstname=prospecto.obtener_nombre(),
            appkey=self._configuracion.appkey(),
        )
        request.debug = settings.PILOT_DEBUG
        request.notification_email = settings.PILOT_NOTIFICATION_EMAIL
        request.cellphone = prospecto.celular()
        try:
            request.notes = CampoExtra.objects.obtener_valor('transcriptUrl', prospecto)
        except ObjectDoesNotExist:
            request.notes = prospecto.mensaje
        request.email = prospecto.obtener_email_activo()
        request.car_brand = prospecto.obtener_marca().codigo()
        request.car_modelo = prospecto.modelos_como_string()
        request.city = prospecto.obtener_localidad()
        request.province = prospecto.obtener_provincia()
        request.provider_service = self._configuracion.proveedor()
        if prospecto.tiene_vendedor():
            request.assigned_user = prospecto.obtener_vendedor().email()

        return request


