# coding=utf-8
from abc import ABCMeta, abstractmethod

from lib.api_client.errors import ClientValidationError, ClientComunicationError
from crms.utils.loggers import LoggerDeCRMs


class IntegradorDeProspectoConCRM(object, metaclass=ABCMeta):

    """
        La responsabilidad de estos objetos es enviar prospectos a un CRM, para ello define el cliente y como
        se genera el lead para dicho cliente a partir de un prospecto.
    """

    def __init__(self, configuracion):
        self._configuracion = configuracion
        self._logger = LoggerDeCRMs().para_configuracion(self._configuracion)

    # Metodo de conveniencia para debug
    def print_resultado_de_evaluar_con(self, prospecto):
        try:
            return self.evaluar_con(prospecto)
        except ClientComunicationError as error:
            print(error.response)
            return error

    def evaluar_con(self, prospecto):
        try:
            lead = self.generar_lead_para(prospecto)
        except ClientValidationError as exc:
            self._logger.error('Key: %s ; Message: %s |||', exc.key, str(exc))
            raise exc
        response = self._enviar_lead(prospecto, lead)
        return response

    def configuracion(self):
        return self._configuracion

    def _enviar_lead(self, prospecto, lead):
        cliente = self.cliente_para(prospecto)
        response = cliente.call(lead)
        self._logger.info(f'{cliente.name()} - Prospecto Id: {prospecto.pk}\n Response: {response} - Request: {lead.as_dict()}')
        return response

    @abstractmethod
    def cliente_para(self, prospecto):
        raise NotImplementedError('subclass responsibility')

    @abstractmethod
    def generar_lead_para(self, prospecto):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def nuevo_para(cls, configuracion):
        return cls(configuracion)
