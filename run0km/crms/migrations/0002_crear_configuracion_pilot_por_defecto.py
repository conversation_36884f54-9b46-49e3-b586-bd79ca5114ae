# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-09-13 00:01


from django.db import migrations


def crear_configuracion_por_defecto(apps, schema_editor):
    configuracion_de_pilot_class = apps.get_model("crms", "ConfiguracionDePilot")
    configuracion_de_pilot_class.objects.create(descripcion='Por Defecto',
                                                appkey='F2AB8C31-661F-4721-9B14-E681A660F515',
                                                suborigin_id='90844C2E')


def borrar_configuracion_por_defecto(apps, schema_editor):
    configuracion_de_pilot_class = apps.get_model("crms", "ConfiguracionDePilot")
    configuracion_de_pilot_class.objects.filter(descripcion='Por Defecto',
                                                appkey='F2AB8C31-661F-4721-9B14-E681A660F515',
                                                suborigin_id='90844C2E').all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('crms', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(crear_configuracion_por_defecto, borrar_configuracion_por_defecto),
    ]
