# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-10-21 20:05


from django.conf import settings
from django.db import migrations


def establecer_configuraciones_genericas(apps, schema_editor):
    if settings.ES_AMBIENTE_DE_TESTING:
        return

    opcion_cliente_crm_class = apps.get_model("crms", "OpcionClienteCRM")
    content_type_class = apps.get_model('contenttypes', 'ContentType')
    pilot_content_type = content_type_class.objects.get(app_label='crms', model='ConfiguracionDePilot')
    tecnom_content_type = content_type_class.objects.get(app_label='crms', model='ConfiguracionDeTecnom')

    for opcion in opcion_cliente_crm_class.objects.all():
        configuracion = opcion.configuracion_pilot
        if configuracion is not None:
            opcion._configuracion_content_type = pilot_content_type
        else:
            configuracion = opcion.configuracion_tecnom
            assert configuracion is not None
            opcion._configuracion_content_type = tecnom_content_type

        opcion._configuracion_id = configuracion.id
        opcion.save()


def undo_establecer_configuraciones_genericas(apps, schema_editor):
    pass


class Migration(migrations.Migration):
    dependencies = [
        ('crms', '0013_auto_20181021_1704'),
    ]

    operations = [
        migrations.RunPython(establecer_configuraciones_genericas, undo_establecer_configuraciones_genericas),
    ]
