# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2018-10-22 00:31


from django.db import migrations


def crear_configuracion_por_defecto(apps, schema_editor):
    configuracion_de_maipu_class = apps.get_model("crms", "ConfiguracionDeMaipu")
    configuracion_de_maipu_class.objects.create(_descripcion='Por Defecto')


def borrar_configuracion_por_defecto(apps, schema_editor):
    configuracion_de_maipu_class = apps.get_model("crms", "ConfiguracionDeMaipu")
    configuracion_de_maipu_class.objects.filter(_descripcion='Por Defecto').all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('crms', '0016_configuraciondemaipu'),
    ]

    operations = [
        migrations.RunPython(crear_configuracion_por_defecto, borrar_configuracion_por_defecto),
    ]
