import os

from langchain_google_genai import ChatGoogleGenerativeAI

from src.rag.ia import IA
from src.rag.vector_database import VectorDatabase


class EryxRAG:
    def __init__(self, chroma_path: str):
        self._db = VectorDatabase(chroma_path)
        self._ia = IA(llm=ChatGoogleGenerativeAI(model="gemini-1.5-pro", google_api_key=os.environ["GOOGLE_API_KEY"]))

    def ask(self, question: str):
        docs = self._db.search_for(question, max_results=5)
        context = "\n\n---\n\n".join(
            [f"Contenido:{doc.page_content}\n\nRecurso:{doc.metadata['source']}" for doc in docs])
        return self._ia.ask(base_prompt=self._base_prompt(), question=question, context=context)

    def _base_prompt(self) -> str:
        prompt_template = """
            Sos un asistente para la wiki de la empresa de tecnología Eryx en donde trabajan 36 socios.
            Intenta responder siguiente consulta: {question} 
            Teniendo en cuenta el siguiente contexto sobre la empresa: {context}.
            Si no podés encontrar una respuesta satisfactoria en el contexto dado responde "anda a agarrar la pala". No inventes información.
        """
        return prompt_template