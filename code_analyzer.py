#!/usr/bin/env python3
"""
Analizador de código Python para obtener estadísticas.
Contiene la lógica principal para analizar archivos y directorios Python.
"""

import ast
import os


class PythonCodeAnalyzer:
    """Analizador de código Python para obtener estadísticas."""
    
    def count_classes_in_file(self, file_path):
        """
        Cuenta las clases definidas en un archivo Python.
        
        Args:
            file_path (str): Ruta al archivo Python a analizar
            
        Returns:
            int: Número de clases encontradas
        """
        class_names = self.get_class_names_in_file(file_path)
        return len(class_names)
    
    def get_class_names_in_file(self, file_path):
        """
        Obtiene los nombres de las clases definidas en un archivo Python usando AST.
        
        Args:
            file_path (str): Ruta al archivo Python a analizar
            
        Returns:
            list: Lista de nombres de clases encontradas
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parsear el código usando AST (Abstract Syntax Tree)
            tree = ast.parse(content)
            
            # Buscar nodos de tipo ClassDef
            class_names = []
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_names.append(node.name)
            
            return class_names
            
        except Exception as e:
            # En caso de error (archivo con sintaxis inválida, etc.), retornar lista vacía
            return []
    
    def scan_directory(self, directory_path):
        """
        Escanea un directorio recursivamente buscando archivos Python y cuenta todas las clases.
        
        Args:
            directory_path (str): Ruta al directorio a escanear
            
        Returns:
            int: Número total de clases encontradas en todos los archivos Python
        """
        class_info = self.get_directory_class_info(directory_path)
        return sum(len(classes) for classes in class_info.values())
    
    def get_directory_class_info(self, directory_path):
        """
        Escanea un directorio recursivamente y obtiene información de clases por archivo.
        
        Args:
            directory_path (str): Ruta al directorio a escanear
            
        Returns:
            dict: Diccionario con archivo como clave y lista de nombres de clases como valor
        """
        class_info = {}
        
        try:
            # Recorrer el directorio recursivamente
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    # Solo procesar archivos Python
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        class_names = self.get_class_names_in_file(file_path)
                        if class_names:  # Solo agregar si tiene clases
                            class_info[file_path] = class_names
            
            return class_info
            
        except Exception as e:
            # En caso de error, retornar diccionario vacío
            return {}
