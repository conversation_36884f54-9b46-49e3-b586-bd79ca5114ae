#!/usr/bin/env python3

import os
import tempfile
import unittest
import shutil

from count_classes import ClassCounter, count_classes_in_file

class TestStringClassDetection(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename, content):
        """Helper method to create test files with specific content"""
        file_path = os.path.join(self.test_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_class_in_single_quoted_string(self):
        """Test that classes in single-quoted strings are not counted"""
        content = """
# Real class
class RealClass:
    pass

# String with 'class X' in it
message = 'This is a class X example that should not be counted'
"""
        file_path = self.create_test_file('single_quoted.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_double_quoted_string(self):
        """Test that classes in double-quoted strings are not counted"""
        content = """
# Real class
class RealClass:
    pass

# String with "class X" in it
message = "This is a class X example that should not be counted"
"""
        file_path = self.create_test_file('double_quoted.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_triple_single_quoted_string(self):
        """Test that classes in triple single-quoted strings are not counted"""
        content = """
# Real class
class RealClass:
    pass

# Triple single-quoted string with class definition
message = '''
class FakeClass:
    def fake_method(self):
        pass
'''
"""
        file_path = self.create_test_file('triple_single.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_triple_double_quoted_string(self):
        """Test that classes in triple double-quoted strings are not counted"""
        content = """
# Real class
class RealClass:
    pass

# Triple double-quoted string with class definition
message = \"\"\"
class FakeClass:
    def fake_method(self):
        pass
\"\"\"
"""
        file_path = self.create_test_file('triple_double.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_multiline_string(self):
        """Test that classes in multiline strings are not counted"""
        content = """
# Real class
class RealClass:
    pass

# Multiline string with class definition
message = (
    "This is a multiline string "
    "that contains class X "
    "and should not be counted"
)
"""
        file_path = self.create_test_file('multiline.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_f_string(self):
        """Test that classes in f-strings are not counted"""
        content = """
# Real class
class RealClass:
    pass

# f-string with class reference
name = "X"
message = f"This is a class {name} example that should not be counted"
"""
        file_path = self.create_test_file('f_string.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_raw_string(self):
        """Test that classes in raw strings are not counted"""
        content = """
# Real class
class RealClass:
    pass

# Raw string with class reference
message = r"This is a class X example with raw string that should not be counted"
"""
        file_path = self.create_test_file('raw_string.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_escaped_quotes(self):
        """Test handling of escaped quotes in strings"""
        content = """
# Real class
class RealClass:
    pass

# String with escaped quotes
message = "This is a \\"class X\\" example with escaped quotes"
"""
        file_path = self.create_test_file('escaped_quotes.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_docstring(self):
        """Test that classes in docstrings are not counted"""
        content = """
# Real class
class RealClass:
    \"\"\"
    This is a docstring that contains a class definition:
    
    class FakeClass:
        pass
    \"\"\"
    pass
"""
        file_path = self.create_test_file('docstring.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_class_in_string_with_line_continuation(self):
        """Test that classes in strings with line continuation are not counted"""
        content = """
# Real class
class RealClass:
    pass

# String with line continuation
message = "This is a string with \
class X that uses line continuation"
"""
        file_path = self.create_test_file('line_continuation.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)
    
    def test_mixed_real_and_string_classes(self):
        """Test a file with both real classes and classes in strings"""
        content = """
# Real classes
class RealClass1:
    pass

message1 = "This is a class X example"

class RealClass2:
    pass

message2 = '''
class FakeClass:
    pass
'''

class RealClass3:
    \"\"\"This docstring has a class X reference\"\"\"
    pass
"""
        file_path = self.create_test_file('mixed.py', content)
        self.assertEqual(count_classes_in_file(file_path), 3)
    
    def test_string_with_class_at_beginning_of_line(self):
        """Test strings with 'class X' at the beginning of a line"""
        content = """
# Real class
class RealClass:
    pass

message = \"\"\"
This is a multiline string.
class FakeClass:
    pass
This should not be counted.
\"\"\"
"""
        file_path = self.create_test_file('string_with_class_at_beginning.py', content)
        self.assertEqual(count_classes_in_file(file_path), 1)

if __name__ == '__main__':
    unittest.main()
