!classDefinition: #CartTest category: 'TusLibros-Model'!
TestCase subclass: #CartTest
	instanceVariableNames: 'objectsFactory'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!CartTest methodsFor: 'tests' stamp: 'LL 2/16/2021 19:50:24'!
setUp

	objectsFactory := PublisherTestObjectsFactory new! !

!CartTest methodsFor: 'tests' stamp: 'LL 1/21/2021 20:09:58'!
test01NewCartIsEmpty

	| cart |
	
	cart := self createCart.
	
	self assert: cart isEmpty.! !

!CartTest methodsFor: 'tests' stamp: 'LL 2/16/2021 19:50:24'!
test02CanAddABookToACart

	| cart |
	cart := self createCart.
	
	cart add: objectsFactory bookFromTheEditorial.
	
	self deny: cart isEmpty.! !

!CartTest methodsFor: 'tests' stamp: 'LL 12/1/2023 14:00:27'!
test03CannotAddABookNotInCatalog

	| cart bookNotInCatalog |
	cart := self createCart.
	bookNotInCatalog := 'DEF456'.

	self assert: cart isEmptyAfter: [ cart add: bookNotInCatalog ] 
		raisesErrorWithMessage: Cart bookNotInCatalogErrorMessage.
	! !

!CartTest methodsFor: 'tests' stamp: 'LL 9/25/2023 01:07:50'!
test04CartRemembersAddedBooks

	| cart |
	cart := self createCart.
	
	cart add: objectsFactory bookFromTheEditorial.
	
	self assert: (cart includes: objectsFactory bookFromTheEditorial).! !

!CartTest methodsFor: 'tests' stamp: 'LL 10/20/2023 17:57:42'!
test05CanAddMultipleCopiesOfABook

	| cart |
	cart := self createCart.
	
	cart add: objectsFactory bookFromTheEditorial quantity: 2.
	
	self assert: (cart quantityOf: objectsFactory bookFromTheEditorial) equals: 2.! !

!CartTest methodsFor: 'tests' stamp: 'LL 12/1/2023 14:00:15'!
test06CannotAddANonPositiveNumberOfCopiesOfABook

	| cart |
	cart := self createCart.
	
	self assert: cart isEmptyAfter: [ cart add: objectsFactory bookFromTheEditorial quantity: 0 ] 
		raisesErrorWithMessage: Cart invalidNumberOfCopiesErrorMessage.! !


!CartTest methodsFor: 'private' stamp: 'LL 1/21/2021 21:24:13'!
assert: cart isEmptyAfter: blockExpectedToFail raisesErrorWithMessage: errorMessage
	
	self should: blockExpectedToFail raise: Error withExceptionDo: [ :anException |
		self assert: anException messageText equals: errorMessage.
		self assert: cart isEmpty.
	]! !

!CartTest methodsFor: 'private' stamp: 'LL 2/16/2021 19:50:24'!
createCart

	^ objectsFactory anEmptyCart! !


!classDefinition: #CashierTest category: 'TusLibros-Model'!
TestCase subclass: #CashierTest
	instanceVariableNames: 'objectsFactory merchantProcessor cashier salesBook'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 17:52:47'!
setUp

	objectsFactory := PublisherTestObjectsFactory new.
	merchantProcessor := MerchantProcessorStub new.
	salesBook := objectsFactory anEmptySalesBook.
	cashier := Cashier registeringSalesOn: salesBook.! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 17:53:27'!
test01CannotCheckoutAnEmptyCart

	| cart |
	cart := objectsFactory anEmptyCart.
	
	self should: [ self checkout: cart ]
		raise: Error - MessageNotUnderstood 
		withExceptionDo: [ :anException |
			self assert: anException messageText equals: Cashier cannotCheckoutAnEmptyCart.
			self assert: salesBook isEmpty
		]! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 17:54:34'!
test02TotalIsCalculatedCorrectlyAfterCheckout

	| ticket |
	ticket := self purchaseABook.
	
	self assert: objectsFactory bookFromTheEditorialPrice equals: ticket value.! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 17:56:47'!
test03CannotCheckoutIfCardIsExpired

	| merchantProcessorWasContacted |
	merchantProcessorWasContacted := false.
	merchantProcessor changeBehaviour: [ :amountToDebit :aCreditCard |  merchantProcessorWasContacted := true ].
	
	self should: [ self purchaseABookUsing: objectsFactory anExpiredCreditCard ]
		raise: Error - MessageNotUnderstood 
		withExceptionDo: [ :anException |
			self assert: anException messageText equals: Cashier cannotCheckoutUsingAnExpiredCard.
			self deny: merchantProcessorWasContacted.
			self assert: salesBook isEmpty.
		]! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 18:00:41'!
test04CheckoutDebitsCreditCardUsingMerchantProcessor

	| ticket debitedAmountFromCreditCard debitedCreditCard creditCard |		
	merchantProcessor changeBehaviour: [ :anAmountToDebit :aCreditCard | 
		debitedAmountFromCreditCard := anAmountToDebit.
		debitedCreditCard := aCreditCard.
	].
	creditCard := objectsFactory aValidCreditCard.
	
	ticket := self purchaseABookUsing: creditCard.
	
	self assert: debitedAmountFromCreditCard equals: ticket value.
	self assert: debitedCreditCard equals: creditCard.! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 17:55:19'!
test05CheckoutFailsIfMerchantProcessorCantProcessPayment
	
	merchantProcessor changeBehaviour: [ :anAmountToDebit :aCreditCard | Error new signal ].
	
	self should: [ self purchaseABook ]
		raise: Error - MessageNotUnderstood 
		withExceptionDo: [ :exceptionRaised | 
			self assert: exceptionRaised messageText equals: Cashier couldNotProcessPaymentErrorMessage.
			self assert: salesBook isEmpty.
		]! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 17:55:35'!
test06CheckingOutACartRegistersASale

	| ticket |
	ticket := self purchaseABook.
	
	self deny: salesBook isEmpty.
	self assert: (salesBook totalSpentBy: objectsFactory aClient) equals: ticket.! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 19:02:17'!
test07SalesBookRegistersTotalSpentByClient

	| ticket anotherTicket |
	ticket := self purchaseABook.
	anotherTicket := self purchaseABook.
	
	self assert: (salesBook totalSpentBy: objectsFactory aClient) equals: ticket + anotherTicket.! !

!CashierTest methodsFor: 'tests' stamp: 'LL 11/26/2023 19:12:10'!
test08SalesBookRegistersTotalQuantityOfItemsPurchasedByClient

	self purchaseABook.
	self purchaseABook.
	
	self assert: (salesBook soldItemsTo: objectsFactory aClient) equals: 
		(Bag new add: objectsFactory bookFromTheEditorial withOccurrences: 2; yourself).! !


!CashierTest methodsFor: 'private' stamp: 'LL 11/26/2023 17:53:37'!
checkout: cart

	^self checkout: cart debitingOn: objectsFactory aValidCreditCard! !

!CashierTest methodsFor: 'private' stamp: 'LL 11/26/2023 17:53:37'!
checkout: cart debitingOn: aCreditCard

	^cashier checkout: cart ownedBy: objectsFactory aClient payingWith: aCreditCard through: merchantProcessor on: ManualClock new now.! !

!CashierTest methodsFor: 'private' stamp: 'LL 11/26/2023 17:54:15'!
purchaseABook

	| cart |
	cart := objectsFactory cartWithABook.	
	^self checkout: cart.! !

!CashierTest methodsFor: 'private' stamp: 'LL 11/26/2023 17:56:14'!
purchaseABookUsing: aCreditCard

	| cart |
	cart := objectsFactory cartWithABook.	
	^self checkout: cart debitingOn: aCreditCard.! !


!classDefinition: #CreditCardTest category: 'TusLibros-Model'!
TestCase subclass: #CreditCardTest
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!CreditCardTest methodsFor: 'tests' stamp: 'LL 1/24/2021 18:15:23'!
test01CreditCardOwnerNameCannotBeBlank

	^self should: [ CreditCard of: '' number: 11111111 expiringOn: (FixedGregorianDate today) monthOfYear ] 
		raise: Error - MessageNotUnderstood 
		withExceptionDo: [ :exceptionRaised | 
			self assert: exceptionRaised messageText equals: CreditCard nameCannotBeBlankErrorMessage.
		]! !

!CreditCardTest methodsFor: 'tests' stamp: 'LL 2/21/2021 21:25:25'!
test02CanGetCreditCardDataFromPlastic

	| creditCard owner number expirationDate |
	owner := 'Juan Perez'.
	number := '11111111'.
	expirationDate := (FixedGregorianDate today) monthOfYear.
	creditCard := CreditCard of: owner number: number expiringOn: expirationDate.
	
	self assert: creditCard owner equals: owner.
	self assert: creditCard number equals: number.
	self assert: creditCard expirationDate equals: expirationDate.
	
	
	! !


!classDefinition: #Cart category: 'TusLibros-Model'!
Object subclass: #Cart
	instanceVariableNames: 'books bookCatalog'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!Cart methodsFor: 'initialization' stamp: 'LL 10/20/2023 17:58:45'!
initializeWithCatalog: aBookCatalog
	
	bookCatalog := aBookCatalog.
	books := Bag new.! !


!Cart methodsFor: 'adding' stamp: 'LL 10/20/2023 18:06:10'!
add: aBook
	
	self add: aBook quantity: 1.! !

!Cart methodsFor: 'adding' stamp: 'LL 10/20/2023 18:14:29'!
add: aBook quantity: aQuantity

	self assertCanAdd: aBook quantity: aQuantity.
	books add: aBook withOccurrences: aQuantity.! !


!Cart methodsFor: 'accessing' stamp: 'LL 2/16/2021 20:17:50'!
contents

	^books copy! !

!Cart methodsFor: 'accessing' stamp: 'LL 1/24/2021 17:42:15'!
total
	
	^books sum: [ :aBook | bookCatalog at: aBook ].! !


!Cart methodsFor: 'testing' stamp: 'LL 1/21/2021 21:39:26'!
includes: aBook

	^ books includes: aBook! !

!Cart methodsFor: 'testing' stamp: 'LL 1/20/2021 21:33:04'!
isEmpty

	^books isEmpty! !


!Cart methodsFor: 'private - assertions' stamp: 'LL 10/20/2023 18:14:35'!
assertCanAdd: aBook quantity: aQuantity  

	self assertIsInCatalog: aBook.
	self assertValidQuantity: aQuantity.
! !

!Cart methodsFor: 'private - assertions' stamp: 'LL 1/24/2021 15:49:03'!
assertIsInCatalog: aBook

	^ (bookCatalog includesKey: aBook) ifFalse: [ self error: self class bookNotInCatalogErrorMessage ]! !

!Cart methodsFor: 'private - assertions' stamp: 'LL 10/20/2023 18:08:18'!
assertValidQuantity: aQuantity

	^ (aQuantity > 0) ifFalse: [ self error: self class invalidNumberOfCopiesErrorMessage ]! !


!Cart methodsFor: 'as yet unclassified' stamp: 'LL 10/20/2023 17:59:59'!
quantityOf: aBook 
	^books occurrencesOf: aBook! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'Cart class' category: 'TusLibros-Model'!
Cart class
	instanceVariableNames: ''!

!Cart class methodsFor: 'instance creation' stamp: 'LL 1/25/2021 18:29:34'!
acceptingItemsOf: aBookCatalog
 
	^self new initializeWithCatalog: aBookCatalog ! !


!Cart class methodsFor: 'error messages' stamp: 'LL 2/21/2021 21:11:37'!
bookNotInCatalogErrorMessage

	^'Cannot add a book that is not from the publisher'! !


!Cart class methodsFor: 'as yet unclassified' stamp: 'LL 10/20/2023 18:07:15'!
invalidNumberOfCopiesErrorMessage
	
	^'Cannot add less than one book'! !


!classDefinition: #Cashier category: 'TusLibros-Model'!
Object subclass: #Cashier
	instanceVariableNames: 'salesBook'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!Cashier methodsFor: 'initialization' stamp: 'LL 1/25/2021 18:49:19'!
initializeWithSalesBook: aSalesBook

	salesBook := aSalesBook! !


!Cashier methodsFor: 'checkout' stamp: 'LL 12/1/2023 12:57:58'!
checkout: aCart ownedBy: aCustomer payingWith: aCreditCard through: aMerchantProcessor on: aCheckoutDateTime  

	| ticket |
	self assertCanCheckout: aCart using: aCreditCard on: aCheckoutDateTime.
	
	ticket := self total: aCart.
	
	self debit: ticket value from: aCreditCard using: aMerchantProcessor.
	
	self registerSaleOf: aCustomer ownedBy: aCart.
	
	^ticket! !


!Cashier methodsFor: 'assertions' stamp: 'LL 1/25/2021 23:10:22'!
assertCanCheckout: aCart using: aCreditCard on: aDateTime 

	self assertContainsBooks: aCart.
	self assertIsNotExpired: aCreditCard on: aDateTime! !

!Cashier methodsFor: 'assertions' stamp: 'LL 1/24/2021 17:45:42'!
assertContainsBooks: aCart

	^ aCart isEmpty ifTrue: [	 self error: Cashier cannotCheckoutAnEmptyCart ]! !

!Cashier methodsFor: 'assertions' stamp: 'LL 1/25/2021 23:11:12'!
assertIsNotExpired: aCreditCard on: aDateTime 

	^ (aCreditCard isExpiredOn: aDateTime) ifTrue: [ self error: Cashier cannotCheckoutUsingAnExpiredCard ]! !


!Cashier methodsFor: 'private' stamp: 'LL 1/24/2021 18:23:28'!
debit: anAmount from: aCreditCard using: aMerchantProcessor

	^ [ aMerchantProcessor debit: anAmount from: aCreditCard ]
		on: Error - MessageNotUnderstood 
		do: [ :exceptionRaised |
			self error: self class couldNotProcessPaymentErrorMessage.
		]! !

!Cashier methodsFor: 'private' stamp: 'LL 12/1/2023 12:57:58'!
registerSaleOf: aCustomer ownedBy: aCart

	^ salesBook registerSaleOf: aCustomer whoBought: aCart contents spending: (self total: aCart)! !

!Cashier methodsFor: 'private' stamp: 'LL 12/1/2023 12:57:58'!
total: aCart

	^ aCart total! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'Cashier class' category: 'TusLibros-Model'!
Cashier class
	instanceVariableNames: ''!

!Cashier class methodsFor: 'as yet unclassified' stamp: 'LL 1/21/2021 21:50:07'!
cannotCheckoutAnEmptyCart
	^'Cannot checkout an empty cart'! !

!Cashier class methodsFor: 'as yet unclassified' stamp: 'LL 1/24/2021 16:44:06'!
cannotCheckoutUsingAnExpiredCard

	^'Cannot checkout using an expired card'! !

!Cashier class methodsFor: 'as yet unclassified' stamp: 'LL 2/21/2021 21:12:09'!
couldNotProcessPaymentErrorMessage

	^'Could not process payment'! !

!Cashier class methodsFor: 'as yet unclassified' stamp: 'LL 1/25/2021 18:48:59'!
registeringSalesOn: aSalesBook

	^ self new initializeWithSalesBook: aSalesBook.! !


!classDefinition: #CreditCard category: 'TusLibros-Model'!
Object subclass: #CreditCard
	instanceVariableNames: 'creditCardNumber owner expirationDate'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!CreditCard methodsFor: 'accessing' stamp: 'LL 2/21/2021 21:26:03'!
expirationDate
	
	^ expirationDate! !

!CreditCard methodsFor: 'accessing' stamp: 'LL 2/21/2021 21:25:50'!
number

	^ creditCardNumber! !

!CreditCard methodsFor: 'accessing' stamp: 'LL 2/21/2021 21:25:37'!
owner
	^owner! !


!CreditCard methodsFor: 'testing' stamp: 'LL 2/16/2021 18:05:42'!
isExpiredOn: aDateTime

	^ aDateTime > (expirationDate lastDate)! !


!CreditCard methodsFor: 'initialization' stamp: 'LL 2/16/2021 18:05:42'!
initializeOf: aPerson number: aCreditCardNumber expiringOn: aMonthOfYear 

	owner := aPerson.
	creditCardNumber := aCreditCardNumber.
	expirationDate := aMonthOfYear.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'CreditCard class' category: 'TusLibros-Model'!
CreditCard class
	instanceVariableNames: ''!

!CreditCard class methodsFor: 'as yet unclassified' stamp: 'LL 1/24/2021 18:01:14'!
nameCannotBeBlankErrorMessage

	^'Name cannot be blank'! !


!CreditCard class methodsFor: 'instance creation' stamp: 'LL 1/24/2021 18:15:40'!
of: nameOfTheOwner number: aCreditCardNumber expiringOn: aMonthOfYear

	nameOfTheOwner isEmpty ifTrue: [ self error: self nameCannotBeBlankErrorMessage ].
	
	^self new initializeOf: nameOfTheOwner number: aCreditCardNumber expiringOn: aMonthOfYear ! !


!classDefinition: #ManualClock category: 'TusLibros-Model'!
Object subclass: #ManualClock
	instanceVariableNames: 'now'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!ManualClock methodsFor: 'public' stamp: 'LL 2/19/2021 23:39:23'!
advanceTime: aTimeMeasure

	now := now next: aTimeMeasure! !

!ManualClock methodsFor: 'public' stamp: 'LL 2/19/2021 23:37:05'!
now

	^ now! !

!ManualClock methodsFor: 'public' stamp: 'LL 2/24/2021 15:30:58'!
revertTime: aTimeMeasure

	now := now previous: aTimeMeasure! !


!ManualClock methodsFor: 'initialization' stamp: 'LL 2/19/2021 23:37:27'!
initialize

	now := GregorianDateTime now.! !


!classDefinition: #MerchantProcessor category: 'TusLibros-Model'!
Object subclass: #MerchantProcessor
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!MerchantProcessor methodsFor: 'public' stamp: 'LL 2/19/2021 23:33:44'!
debit: anAmountToDebit from: aCreditCard

	self subclassResponsibility! !


!classDefinition: #MerchantProcessorStub category: 'TusLibros-Model'!
MerchantProcessor subclass: #MerchantProcessorStub
	instanceVariableNames: 'simulatedBehaviour'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!MerchantProcessorStub methodsFor: 'public' stamp: 'LL 2/19/2021 23:29:45'!
changeBehaviour: newBehaviour

	simulatedBehaviour := newBehaviour! !

!MerchantProcessorStub methodsFor: 'public' stamp: 'LL 2/19/2021 23:27:19'!
debit: anAmountToDebit from: aCreditCard

	^simulatedBehaviour value: anAmountToDebit value: aCreditCard! !


!MerchantProcessorStub methodsFor: 'initialization' stamp: 'LL 2/19/2021 23:28:06'!
initialize

	simulatedBehaviour := [ :anAmountToDebit :aCreditCard |  ]! !


!classDefinition: #PublisherTestObjectsFactory category: 'TusLibros-Model'!
Object subclass: #PublisherTestObjectsFactory
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 20:18:41'!
aBagWithABookFromTheEditorial

	^ Bag new add: self bookFromTheEditorial withOccurrences: 1; yourself! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 20:56:10'!
aCashier

	^ Cashier registeringSalesOn: self anEmptySalesBook.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 20:16:49'!
aCatalog

	^Dictionary newFromPairs: {self bookFromTheEditorial. self bookFromTheEditorialPrice}.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 11/16/2023 01:16:25'!
aClient

	^'Juan Perez'! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 19:45:08'!
aValidCreditCard

	| nextMonth |
	nextMonth := (FixedGregorianDate today next: GregorianMonth oneMonth) monthOfYear.
	^CreditCard of: 'Juan Perez' number: 11111111 expiringOn: nextMonth.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 20:17:11'!
anEmptyCart

	^Cart acceptingItemsOf: self aCatalog.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 20:54:25'!
anEmptySalesBook

	^SalesBook new.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 19:43:38'!
anExpiredCreditCard
	
	| lastMonth |
	lastMonth := (FixedGregorianDate today previous: GregorianMonth oneMonth) monthOfYear.
	^CreditCard of: 'Juan Perez' number: 11111111 expiringOn: lastMonth.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 1/24/2021 15:59:26'!
bookFromTheEditorial

	^ 'ABC123'! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 1/24/2021 17:11:28'!
bookFromTheEditorialPrice

	^ 1000 * peso! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 19:46:27'!
cartWithABook

	| cart |
	cart := self anEmptyCart.
	cart add: self bookFromTheEditorial.
	^cart! !


!classDefinition: #SalesBook category: 'TusLibros-Model'!
Object subclass: #SalesBook
	instanceVariableNames: 'booksSoldByCustomer spentByCustomer'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!SalesBook methodsFor: 'accessing' stamp: 'LL 2/16/2021 21:09:32'!
soldItemsTo: aCustomer

	^ booksSoldByCustomer at: aCustomer ifAbsent: [ Bag new ].! !

!SalesBook methodsFor: 'accessing' stamp: 'LL 2/16/2021 21:09:52'!
totalSpentBy: aCustomer

	^ spentByCustomer at: aCustomer ifAbsent: [ 0 ].! !


!SalesBook methodsFor: 'initialization' stamp: 'LL 2/16/2021 20:50:25'!
initialize

	booksSoldByCustomer := Dictionary new.
	spentByCustomer := Dictionary new.! !


!SalesBook methodsFor: 'testing' stamp: 'LL 2/16/2021 20:55:05'!
isEmpty

	^booksSoldByCustomer isEmpty! !


!SalesBook methodsFor: 'adding' stamp: 'LL 11/26/2023 19:15:15'!
registerSaleOf: aClient whoBought: aBagOfItems spending: aTotal

	| previousAmount previousItems |
	previousItems := self soldItemsTo: aClient.
	booksSoldByCustomer at: aClient put: (previousItems addAll: aBagOfItems; yourself).
	
	previousAmount := self totalSpentBy: aClient.
	spentByCustomer at: aClient put: aTotal + previousAmount.! !
!classDefinition: #TusLibrosWebServer category: 'TusLibros-ProductionServer'!
Object subclass: #TusLibrosWebServer
	instanceVariableNames: 'server restInterface'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-ProductionServer'!

!TusLibrosWebServer methodsFor: 'initialization' stamp: 'LL 11/5/2023 20:53:24'!
initializeListeningOn: aPort

	restInterface := TusLibrosRestApi for: self configureSystem.

	self startServerOn: aPort.
	
	self registerServices.! !


!TusLibrosWebServer methodsFor: 'stopping' stamp: 'LL 2/20/2021 02:00:46'!
stop

	^ server destroy.! !


!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 11/21/2023 20:11:54'!
configureSystem

	| latestCatalog authSystem |
	latestCatalog := Dictionary newFromPairs: {
		'9780137314942'. 31505*peso. 
		'9780321278654'. 45305*peso.         
		'9780201710915'. 45180*peso.         
		'9780321125217'. 41000*peso.     
		'9780735619654'. 34900*peso. 
		'9780321146533'. 29100*peso.
	}.
	authSystem := RealAuthenticationSystem new.
	
	^ TusLibrosSystem acceptingItemsOf: latestCatalog authenticatingWith: authSystem debitingWith: MerchantProcessorStub new measuringTimeWith: GregorianDateTime.
! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 11/26/2023 19:50:26'!
registerServices

	server addService: '/createCart' action: [ :request | self send: #createCart: toInterfaceAdapting: request ].
	server addService: '/addToCart' action: [ :request | self send: #addToCart: toInterfaceAdapting: request ].
	server addService: '/listCart' action: [ :request | self send: #listCart: toInterfaceAdapting: request ].
	server addService: '/checkoutCart' action: [ :request | self send: #checkoutCart: toInterfaceAdapting: request ].
	server addService: '/listPurchases' action: [ :request | self send: #listPurchases: toInterfaceAdapting: request ].! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 2/21/2021 20:47:12'!
send: anTusLibrosRestInterfaceMessage toInterfaceAdapting: aWebRequest

	| response |
	response := restInterface perform: anTusLibrosRestInterfaceMessage with: (HttpRequest withFields: aWebRequest fields).
	
	aWebRequest sendResponseCode: response status content: response body type: 'text/plain; charset=utf-8' do: [ :aResponse | ].! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 2/20/2021 01:36:51'!
startServerOn: aPort

	server := WebServer reset default.
	server listenOn: aPort.
	server useDebugErrorHandler! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosWebServer class' category: 'TusLibros-ProductionServer'!
TusLibrosWebServer class
	instanceVariableNames: ''!

!TusLibrosWebServer class methodsFor: 'as yet unclassified' stamp: 'LL 2/6/2021 18:29:32'!
listeningOn: aPort

	^self new initializeListeningOn: aPort! !
!classDefinition: #TusLibrosRestApiTest category: 'TusLibros-RestApi'!
TestCase subclass: #TusLibrosRestApiTest
	instanceVariableNames: 'restApi objectsFactory validClientIdField'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!TusLibrosRestApiTest methodsFor: 'assertions' stamp: 'LL 2/21/2021 17:45:05'!
assert: response isOkAndBodyBeginsWith: aString

	self assert: response status equals: 200.
	self assert: (response body beginsWith: aString)! !

!TusLibrosRestApiTest methodsFor: 'assertions' stamp: 'LL 2/20/2021 02:17:34'!
assert: response isOkAndBodyEquals: expectedBody

	self assert: response status equals: 200.
	self assert: response body equals: expectedBody! !

!TusLibrosRestApiTest methodsFor: 'assertions' stamp: 'LL 11/6/2023 19:43:18'!
assertCannotParseUUIDFrom: response

	self assert: response isOkAndBodyEquals: '1|', restApi cannotParseUUIDErrorMessage.
	! !

!TusLibrosRestApiTest methodsFor: 'assertions' stamp: 'LL 11/17/2023 20:01:25'!
assertOkResponseWithId: response

	self assert: response isOkAndBodyBeginsWith: '0|'.
	self assert: response body size >= 3! !


!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/6/2023 19:46:07'!
setUp

	objectsFactory := PublisherTestObjectsFactory new.
	
	self restApiWithAuthSystem: AuthenticationSystemStub bypassingAllUsers.
! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/17/2023 20:01:21'!
test01CreateCartReturnsCartIdOnSuccess

	| response |

	response := self createCart.

	self assertOkResponseWithId: response.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:32'!
test02AddToCartReturnsOKOnSuccess

	| response cartId |
	
	cartId := self createCartAndReturnId.
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: cartId.

	self assert: response isOkAndBodyEquals: '0|OK'.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:39'!
test03AddToCartReturnsErrorOnASemanticError

	| response cartId |
	
	cartId := self createCartAndReturnId.
	
	response := self addBook: 'book ISBN not in catalog' toCart: cartId.

	self assert: response isOkAndBodyBeginsWith: '1|'.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:44'!
test04ListCartReturnsCartContentsOnSuccess

	| response cartId |
	
	cartId := self createCartAndReturnId.
	
	self addBook: objectsFactory bookFromTheEditorial toCart: cartId.
	
	response := self listCart: cartId.

	self assert: response isOkAndBodyEquals: '0|', objectsFactory bookFromTheEditorial, '|1'.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 17:47:51'!
test05ListCartReturnsErrorOnASemanticError

	| response |
	
	response := self listNonExistantCart.

	self assert: response isOkAndBodyBeginsWith: '1|'
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 18:39:53'!
test06AddToCartReturnErrorWhenCannotParseCartId

	| response |
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: Object new asString.

	self assertCannotParseUUIDFrom: response.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 18:40:02'!
test07ListCartReturnErrorWhenCannotParseCartId

	| response |
	
	response := self listCart: Object new asString.

	self assertCannotParseUUIDFrom: response.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:58'!
test08AddToCartParsesQuantityParameterCorrectly
	
	| response cartId |

	cartId := self createCartAndReturnId.
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: cartId withQuantity: '2'.
	
	response := self listCart: cartId.
	self assert: (self quantityFrom: response) equals: 2.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/6/2023 19:43:18'!
test09AddToCartReturnsErrorOnUnparsableQuantity
	
	| response cartId |

	cartId := self createCartAndReturnId.
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: cartId withQuantity: 'x'.
	
	self assert: response isOkAndBodyEquals: '1|', restApi cannotParseBookQuantityErrorMessage.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/17/2023 20:01:21'!
test10CreateCartParsesAuthParametersCorrectly
	
	| response parsedClientId parsedPassword aClientId aPassword |
	self restApiWithAuthBehavingLike: [ :clientId :password |
		parsedClientId := clientId.
		parsedPassword := password.
	].
	aClientId := 'a valid client id'.
	aPassword := 'a valid password'.
	
	response := self createCartFor: aClientId withPassword: aPassword.
	
	self assertOkResponseWithId: response.	
	self assert: parsedClientId equals: aClientId.
	self assert: parsedPassword equals: aPassword.

	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/6/2023 19:43:05'!
test11CreateCartReturnsErrorOnASemanticError
	
	| response errorMessage |
	errorMessage := 'Invalid credentials'.
	self restApiWithAuthBehavingLike: [ :clientId :password | self error: errorMessage ].
	
	response := self createCart.

	self assert: response isOkAndBodyEquals: '1|', errorMessage
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/17/2023 20:01:37'!
test12CheckoutReturnTransactionIdOnSuccess

	| response |
	
	response := self addToCartAndCheckout.

	self assertOkResponseWithId: response.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/17/2023 20:13:12'!
test13CheckoutReturnsErrorOnASemanticError

	| response |
	
	response := self checkoutCart: UUID new asString.

	self assert: response isOkAndBodyBeginsWith: '1|'.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/21/2023 20:43:45'!
test14CheckoutReturnsErrorOnUnparsableCreditCardExpirationDate

	| response aValidCartId |
	
	aValidCartId := self createCartAndReturnId.
	self addBook: objectsFactory bookFromTheEditorial toCart: aValidCartId asString.
	response := self checkoutCart: aValidCartId withCreditCartExpirationDateField: 'invalidMonthOfYear'.

	self assert: response isOkAndBodyEquals: '1|', restApi cannotParseCreditCardExpirationDate.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/26/2023 19:45:41'!
test15ListPurchasesReturnOKOnNoPurchases

	| response |
	
	response := self listPurchasesOf: self aValidClientIdField withPassword: 'password'.

	self assert: response isOkAndBodyEquals: '0||0'.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/26/2023 19:46:23'!
test16ListPurchasesReturnsSoldItemsAndTotalSpentAsString

	| response |
	
	self addToCartAndCheckout.
	
	response := self listPurchasesOf: self aValidClientIdField withPassword: 'password'.

	self assert: response isOkAndBodyEquals: '0|', objectsFactory bookFromTheEditorial, '|', 1 asString, '|', objectsFactory bookFromTheEditorialPrice amount asString.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/26/2023 19:48:42'!
test17ListPurchasesReturnsErrorOnASemanticError

	| response errorMessage |

	errorMessage := 'Invalid credentials'.
	self restApiWithAuthBehavingLike: [ :clientId :password | self error: errorMessage ].

	response := self listPurchasesOf: 'invalid client id' withPassword: 'password'.

	self assert: response isOkAndBodyBeginsWith: '1|', errorMessage! !


!TusLibrosRestApiTest methodsFor: 'test objects' stamp: 'LL 11/26/2023 19:31:35'!
aValidClientIdField
	
	^'1'! !

!TusLibrosRestApiTest methodsFor: 'test objects' stamp: 'LL 11/17/2023 20:08:07'!
aValidCreditCardNumberField
	^'1111 1111 1111 1111'! !

!TusLibrosRestApiTest methodsFor: 'test objects' stamp: 'LL 11/17/2023 20:08:03'!
aValidCreditCardOwnerField

	^'Juan Perez'! !

!TusLibrosRestApiTest methodsFor: 'test objects' stamp: 'LL 11/17/2023 20:07:58'!
aValidExpirationDateField
	
	^'032099'! !


!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/22/2023 18:57:03'!
addBook: aBookISBN toCart: cartIdAsString
	
	^self addBook: aBookISBN toCart: cartIdAsString withQuantity: '1'. ! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/6/2023 19:43:18'!
addBook: aBookISBN toCart: cartIdAsString withQuantity: aQuantityAsString 
	| aRequest |
	
	aRequest := HttpRequest withFields: (Dictionary newFromPairs: {'cartId'. cartIdAsString. 'bookIsbn'. aBookISBN. 'bookQuantity'. aQuantityAsString}).
	
	^restApi addToCart: aRequest.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/17/2023 20:06:04'!
addToCartAndCheckout

	| aValidCartId |
	
	aValidCartId := self createCartAndReturnId.
	
	self addBook: objectsFactory bookFromTheEditorial toCart: aValidCartId asString.
	
	^self checkoutCart: aValidCartId! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/21/2023 20:35:26'!
checkoutCart: aValidCartId
	
	^self checkoutCart: aValidCartId withCreditCartExpirationDateField: self aValidExpirationDateField.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/21/2023 20:35:26'!
checkoutCart: aValidCartId withCreditCartExpirationDateField: aMonthOfYearAsString
	
	| aRequest |
	aRequest := HttpRequest withFields: (Dictionary newFromPairs: {'cartId'. aValidCartId. 
													            'ccn'. self aValidCreditCardNumberField.
													            'cced'. aMonthOfYearAsString.
													            'cco'. self aValidCreditCardOwnerField.}).	
	
	^ restApi checkoutCart: aRequest.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/26/2023 19:26:11'!
createCart

	^self createCartFor: self aValidClientIdField withPassword: 'a valid password'! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/22/2023 19:08:02'!
createCartAndReturnId

	| response |

	response := self createCart.
	
	^self cartIdFrom: response.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/6/2023 19:48:19'!
createCartFor: aClientId withPassword: aPassword

	| aRequest |

	aRequest := HttpRequest withFields: (Dictionary newFromPairs: {'clientId'. aClientId. 'password'. aPassword}).
	
	^restApi createCart: aRequest.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/6/2023 19:43:18'!
listCart: cartIdAsString

	| aRequest |
	
	aRequest := HttpRequest withFields: (Dictionary newFromPairs: {'cartId'. cartIdAsString}).
	
	^restApi listCart: aRequest.
	! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/22/2023 18:11:26'!
listNonExistantCart

	^self listCart: UUID new asString.
	! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 11/26/2023 18:35:38'!
listPurchasesOf: clientIdAsString withPassword: aPassword
	
	| aRequest |
	
	aRequest := HttpRequest withFields: (Dictionary newFromPairs: {'clientId'. clientIdAsString. 'password'. aPassword}).
	
	^restApi listPurchases: aRequest.
	! !


!TusLibrosRestApiTest methodsFor: 'private' stamp: 'LL 10/20/2023 16:18:19'!
cartIdFrom: response

	^ response body allButFirst: 2! !

!TusLibrosRestApiTest methodsFor: 'private' stamp: 'LL 10/22/2023 18:53:48'!
quantityFrom: response 
	^response body last asString asNumber.! !

!TusLibrosRestApiTest methodsFor: 'private' stamp: 'LL 11/6/2023 19:46:18'!
restApiWithAuthBehavingLike: expectedBehaviour

	self restApiWithAuthSystem: (AuthenticationSystemStub behavingLike: expectedBehaviour)	! !

!TusLibrosRestApiTest methodsFor: 'private' stamp: 'LL 11/16/2023 11:34:08'!
restApiWithAuthSystem: authSystem

	| anInterface |
	anInterface := TusLibrosSystem acceptingItemsOf: objectsFactory aCatalog authenticatingWith: authSystem debitingWith: MerchantProcessorStub new measuringTimeWith: ManualClock new.
	restApi := TusLibrosRestApi for: anInterface.

	! !


!classDefinition: #HttpRequest category: 'TusLibros-RestApi'!
Object subclass: #HttpRequest
	instanceVariableNames: 'fields'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!HttpRequest methodsFor: 'initialization' stamp: 'LL 2/6/2021 17:48:39'!
initializeWithFields: requestFields 
	fields := requestFields.! !


!HttpRequest methodsFor: 'accessing' stamp: 'LL 12/1/2023 19:29:10'!
fieldAt: aFieldName

	^fields at: aFieldName! !

!HttpRequest methodsFor: 'accessing' stamp: 'LL 2/6/2021 17:48:52'!
fields

	^fields! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'HttpRequest class' category: 'TusLibros-RestApi'!
HttpRequest class
	instanceVariableNames: ''!

!HttpRequest class methodsFor: 'instance creation' stamp: 'LL 2/6/2021 17:48:17'!
withFields: requestFields
	^self new initializeWithFields: requestFields ! !


!classDefinition: #HttpResponse category: 'TusLibros-RestApi'!
Object subclass: #HttpResponse
	instanceVariableNames: 'responseBody status'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!HttpResponse methodsFor: 'initialization' stamp: 'LL 2/6/2021 17:52:59'!
initializeOkResponseWith: aResponseBody 
	
	status := 200.
	responseBody := aResponseBody.! !


!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 2/6/2021 17:53:48'!
body

	^responseBody! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 2/6/2021 17:53:36'!
status
	
	^status! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'HttpResponse class' category: 'TusLibros-RestApi'!
HttpResponse class
	instanceVariableNames: ''!

!HttpResponse class methodsFor: 'instance creation' stamp: 'LL 2/6/2021 17:52:10'!
okResponseWith: aResponseBody

	^self new initializeOkResponseWith: aResponseBody! !


!classDefinition: #TusLibrosRestApi category: 'TusLibros-RestApi'!
Object subclass: #TusLibrosRestApi
	instanceVariableNames: 'interface'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 12/1/2023 19:44:32'!
addToCart: aHttpRequest 
	
	| bookIsbn cartId quantity |
	bookIsbn := self parseBookISBNFrom: aHttpRequest.
	
	^ self answer: [
		cartId := self parseCartIdFrom: aHttpRequest.
		quantity := self parseBookQuantityFrom: aHttpRequest.
		interface addToCart: cartId book: bookIsbn quantity: quantity.
		'OK'
	]! !

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 12/1/2023 19:46:43'!
checkoutCart: aHttpRequest 
	
	| ccn cco |
	ccn := self parseCreditCardNumberFrom: aHttpRequest.
	cco := self parseCreditCardOwnerFrom: aHttpRequest.
	
	^ self answer: [ 
		| cartId expirationDate transactionId | 
		cartId := self parseCartIdFrom: aHttpRequest.
		expirationDate := self parseCreditCardExpirationDateFrom: aHttpRequest.
		transactionId := interface checkoutCart: cartId withCreditCardNumber: ccn expiringOn: expirationDate ownedBy: cco.
		self idAsString: transactionId.
	]! !

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 12/1/2023 19:46:31'!
createCart: aHttpRequest
	
	| cartId clientId password |
	
	clientId := self parseClientIdFrom: aHttpRequest.
	password := self parsePasswordFrom: aHttpRequest.
	
	^self answer: [
		cartId := interface createCartFor: clientId with: password.
		self idAsString: cartId.
	]! !

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 12/1/2023 19:35:20'!
listCart: aHttpRequest 
	
	| cartContents cartId |
	
	^ self answer: [ 
		cartId := self parseCartIdFrom: aHttpRequest.
		cartContents := interface listCart: cartId.
		self bagAsString: cartContents.
	]! !

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 12/1/2023 19:45:08'!
listPurchases: aHttpRequest 
	| clientId password |
	clientId := self parseClientIdFrom: aHttpRequest.
	password := self parsePasswordFrom: aHttpRequest.

	^self answer: [ |  purchasesInfo |
		purchasesInfo := interface listPurchases: clientId with: password.
		self purchaseInfoAsString: purchasesInfo.
	]! !


!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 12/1/2023 19:44:32'!
parseBookISBNFrom: aHttpRequest

	^ aHttpRequest fieldAt: 'bookIsbn'! !

!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 10/22/2023 19:05:07'!
parseBookQuantityFrom: aHttpRequest
	
	| bookQuantityAsString |
	
	bookQuantityAsString := aHttpRequest fields at: 'bookQuantity'.
	
	[ ^bookQuantityAsString asNumber ] on: Error do: [ :anException | 
		self error: self cannotParseBookQuantityErrorMessage.
	].! !

!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 10/22/2023 19:02:34'!
parseCartIdFrom: aHttpRequest
	
	| cartIdAsString |
	
	cartIdAsString := aHttpRequest fields at: 'cartId'.
	
	[ ^UUID fromString: cartIdAsString ] on: Error do: [ :anException | 
		self error: self cannotParseUUIDErrorMessage
	].! !

!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 12/1/2023 19:44:42'!
parseClientIdFrom: aRequest

	^ aRequest fieldAt: 'clientId'! !

!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 12/1/2023 19:44:50'!
parseCreditCardExpirationDateFrom: aHttpRequest

	| cced |
	cced := aHttpRequest fieldAt: 'cced'.	

	[ ^GregorianMonthOfYear yearNumber: (cced last: 4) asNumber monthNumber: (cced first: 2) asNumber ] 
		on: Error - MessageNotUnderstood 
		do: [:anException | self error: self cannotParseCreditCardExpirationDate ]! !

!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 12/1/2023 19:44:56'!
parseCreditCardNumberFrom: aHttpRequest

	^ aHttpRequest fieldAt: 'ccn'! !

!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 12/1/2023 19:45:02'!
parseCreditCardOwnerFrom: aHttpRequest

	^ aHttpRequest fieldAt: 'cco'! !

!TusLibrosRestApi methodsFor: 'parse parameters' stamp: 'LL 12/1/2023 19:45:08'!
parsePasswordFrom: aRequest

	^ aRequest fieldAt: 'password'! !


!TusLibrosRestApi methodsFor: 'serialize responses' stamp: 'LL 11/26/2023 19:41:46'!
bagAsString: aBag
	
	^ String streamContents: [:stream |
		aBag contents associations
			do: [ :itemAndQuantity | stream nextPutAll: itemAndQuantity key; nextPut: $|; nextPutAll: itemAndQuantity value asString ]
			separatedBy: [ stream nextPut: $| ]
	]! !

!TusLibrosRestApi methodsFor: 'serialize responses' stamp: 'LL 12/1/2023 19:47:01'!
idAsString: anId

	^ anId asString! !

!TusLibrosRestApi methodsFor: 'serialize responses' stamp: 'LL 11/26/2023 19:43:31'!
purchaseInfoAsString: aPurchasesInfo

	| soldItems total |
	soldItems := aPurchasesInfo first.
	total := aPurchasesInfo second.
	
	^ (self bagAsString: soldItems), '|', total amount asString! !


!TusLibrosRestApi methodsFor: 'private' stamp: 'LL 12/1/2023 19:41:07'!
answer: aBlock
	
	| responseContent |
	
	[ responseContent := aBlock value ] on: Error - MessageNotUnderstood do: [ :anException | 
		^HttpResponse okResponseWith: (self errorResponseOn: anException)
	].
	
	^HttpResponse okResponseWith: (self sucessfulResponseWith: responseContent)! !

!TusLibrosRestApi methodsFor: 'private' stamp: 'LL 12/1/2023 19:41:07'!
errorResponseOn: anException

	^ '1|', anException messageText! !

!TusLibrosRestApi methodsFor: 'private' stamp: 'LL 12/1/2023 19:34:51'!
sucessfulResponseWith: aContent

	^ '0|', aContent! !


!TusLibrosRestApi methodsFor: 'initialization' stamp: 'LL 11/5/2023 20:50:49'!
initializeFor: aTusLibrosSystem 

	interface := aTusLibrosSystem.! !


!TusLibrosRestApi methodsFor: 'error handling' stamp: 'LL 10/22/2023 19:05:07'!
cannotParseBookQuantityErrorMessage

	^ 'Cannot parse book quantity'! !

!TusLibrosRestApi methodsFor: 'error handling' stamp: 'LL 11/21/2023 20:43:59'!
cannotParseCreditCardExpirationDate

	^ 'Invalid expiration date'! !

!TusLibrosRestApi methodsFor: 'error handling' stamp: 'LL 10/22/2023 18:38:49'!
cannotParseUUIDErrorMessage

	^ 'Cannot parse UUID'! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosRestApi class' category: 'TusLibros-RestApi'!
TusLibrosRestApi class
	instanceVariableNames: ''!

!TusLibrosRestApi class methodsFor: 'instance creation' stamp: 'LL 2/6/2021 18:26:34'!
for: aTusLibrosSystem 

	^self new initializeFor: aTusLibrosSystem ! !
!classDefinition: #TusLibrosSystemTest category: 'TusLibros-System'!
TestCase subclass: #TusLibrosSystemTest
	instanceVariableNames: 'objectsFactory interface authBehaviour authSystem validClientId clock'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-System'!

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/19/2023 20:03:02'!
setUp

	| merchantProcessor |
	objectsFactory := PublisherTestObjectsFactory new.
	authSystem := AuthenticationSystemStub bypassingAllUsers.
	merchantProcessor := MerchantProcessorStub new.
	clock := ManualClock new.
	interface := TusLibrosSystem 	acceptingItemsOf: objectsFactory aCatalog authenticatingWith: authSystem debitingWith: merchantProcessor measuringTimeWith: clock.! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:00:19'!
test01CanListAnEmptyCart

	| cartId |
	cartId := self createCart.
	
	self assert: (self listCart: cartId) isEmpty.! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:01:00'!
test02CanAddBooksToCartAndListItsContent

	| cartId |
	cartId := self createCart.
	
	self addBookToCart: cartId.
	
	self assert: (self listCart: cartId) equals: objectsFactory aBagWithABookFromTheEditorial.! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 10/19/2023 16:49:20'!
test03CannotAddToCartNotCreatedBefore

	self shouldRaiseCartNotFoundWhen: [ self addBookToCart: Object new ]
	
	! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:01:12'!
test04CannotListCartNotCreatedBefore

	self shouldRaiseCartNotFoundWhen: [ self listCart: Object new ]
	
	! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:02:38'!
test05CanCreateDifferentCarts

	| aCartId anotherCartId |
	aCartId := self createCart.
	
	anotherCartId := self createCart.
	
	self addBookToCart: aCartId.
	
	self assert: (self listCart: anotherCartId) isEmpty.
	! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 10/20/2023 18:28:25'!
test06CanAddMultipleCopiesOfABook

	| cartId |
	cartId := self createCart.
	
	self addBookToCart: cartId withCopies: 2.
	
	self assert: (self listCart: cartId) equals: (Bag new add: objectsFactory bookFromTheEditorial withOccurrences: 2; yourself).! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/26/2023 18:58:03'!
test07CannotCreateCartWithInvalidCredentials
		
	self shouldRaiseInvalidCredentialsWhen: [ interface createCartFor: 'client' with: 'invalid password' ]! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/16/2023 00:16:28'!
test08CannotCheckoutCartNotCreatedBefore

	self shouldRaiseCartNotFoundWhen: [ self checkoutCart: Object new ]
	
	! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/16/2023 00:59:37'!
test09CanListPurchaseAfterASuccessfulCheckout

	| cartId expectedBooks expectedAmountSpent |
	cartId := self createCart.
	self addBookToCart: cartId.
	self addBookToCart: cartId.
	self checkoutCart: cartId.
	
	expectedBooks := Bag new add: objectsFactory bookFromTheEditorial withOccurrences: 2; yourself.
	expectedAmountSpent := objectsFactory bookFromTheEditorialPrice * 2.
	self assertPurchasesOfClientWere: expectedBooks havingSpent: expectedAmountSpent.
	
	! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/23/2023 23:16:55'!
test10CannotAddBooksToCartAfterSessionIsExpired

	| cartId |
	cartId := self createCart.
	
	self shouldRaiseSessionExpiredWhen: [ self addBookToCart: cartId ] asserting: [
		self assert: (interface listCart: cartId) isEmpty.
	].! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/23/2023 23:16:27'!
test11CannotListCartAfterSessionIsExpired

	| cartId |
	cartId := self createCart.

	self shouldRaiseSessionExpiredWhen: [ interface listCart: cartId ] .! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/23/2023 23:16:55'!
test12CannotCheckoutCartAfterSessionIsExpired

	| cartId |
	cartId := self createCart.

	self shouldRaiseSessionExpiredWhen: [ self checkoutCart: cartId ] asserting: [
		self assertClientMadeNoPurchases.
	].! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/19/2023 20:03:02'!
test13PerformingAnActionWithTheCartResetsExpirationTime

	| cartId |
	cartId := self createCart.
	clock advanceTime: interface sessionDuration - (1 * second).
	
	self addBookToCart: cartId.
	
	clock advanceTime: 2 * second.
	
	self checkoutCart: cartId.
	
	self assertClientMadeOnePurchase.! !

!TusLibrosSystemTest methodsFor: 'tests' stamp: 'LL 11/26/2023 18:57:34'!
test14CannotListPurchaseWithInvalidCredentials

	self shouldRaiseInvalidCredentialsWhen: [ interface listPurchases: 'client' with: 'invalid password' ]
	! !


!TusLibrosSystemTest methodsFor: 'assertions' stamp: 'LL 2/24/2021 18:17:38'!
assertClientMadeNoPurchases

	^ self assert: (self listPurchasesOfClient) first isEmpty.! !

!TusLibrosSystemTest methodsFor: 'assertions' stamp: 'LL 2/24/2021 18:17:23'!
assertClientMadeOnePurchase

	^ self assert: (self listPurchasesOfClient) first size equals: 1.! !

!TusLibrosSystemTest methodsFor: 'assertions' stamp: 'LL 2/24/2021 18:17:03'!
assertPurchasesOfClientWere: bagOfBooks havingSpent: amountOfMoney 
		
	self assert: (self listPurchasesOfClient) first equals: bagOfBooks.
	self assert: (self listPurchasesOfClient) second equals: amountOfMoney.! !

!TusLibrosSystemTest methodsFor: 'assertions' stamp: 'LL 11/16/2023 00:13:35'!
shouldRaiseCartNotFoundWhen: aBlock
	
	self should: aBlock
		raise: Error - MessageNotUnderstood 
		withExceptionDo: [ :exceptionRaised | 
			self assert: exceptionRaised messageText equals: TusLibrosSystem cartNotFoundErrorMessage.
		]
	
	! !

!TusLibrosSystemTest methodsFor: 'assertions' stamp: 'LL 11/26/2023 18:58:34'!
shouldRaiseInvalidCredentialsWhen: anInterfaceOperation
	
	authSystem behaveLike: [ :clientId :password | self error: 'Invalid credentials' ].
	
	self should: anInterfaceOperation
		raise: Error - MessageNotUnderstood 
		withExceptionDo: [ :exceptionRaised | 
			self assert: exceptionRaised messageText equals: 'Invalid credentials'.
		]! !

!TusLibrosSystemTest methodsFor: 'assertions' stamp: 'LL 11/23/2023 23:16:55'!
shouldRaiseSessionExpiredWhen: usingCartAgain 

	self shouldRaiseSessionExpiredWhen: usingCartAgain asserting: [].! !

!TusLibrosSystemTest methodsFor: 'assertions' stamp: 'LL 11/23/2023 23:16:55'!
shouldRaiseSessionExpiredWhen: usingCartAgain asserting: aBlock

	| elapsedTime |
	elapsedTime := interface sessionDuration + (1 * second).

	clock advanceTime: elapsedTime.
	
	self should: usingCartAgain raise: Error - MessageNotUnderstood withExceptionDo: [ :raisedException |
		self assert: raisedException messageText equals: CartSession sessionExpiredErrorMessage.
		clock revertTime: elapsedTime.	
		aBlock value.
	].! !


!TusLibrosSystemTest methodsFor: 'private' stamp: 'LL 10/20/2023 18:29:39'!
addBookToCart: cartId 
	
	self addBookToCart: cartId withCopies: 1.
	
	! !

!TusLibrosSystemTest methodsFor: 'private' stamp: 'LL 11/5/2023 20:50:26'!
addBookToCart: cartId withCopies: aNumberOfCopies
	
	interface addToCart: cartId book: objectsFactory bookFromTheEditorial quantity: aNumberOfCopies.
	
	! !

!TusLibrosSystemTest methodsFor: 'private' stamp: 'LL 11/16/2023 00:15:44'!
checkoutCart: cartId 

	^ interface checkoutCart: cartId withCreditCardNumber: 11111111 expiringOn: GregorianMonthOfYear current ownedBy: 'Juan Perez'
	
	! !

!TusLibrosSystemTest methodsFor: 'private' stamp: 'LL 11/23/2023 22:54:28'!
createCart

	^interface createCartFor: self aValidClientId with: Object new.! !

!TusLibrosSystemTest methodsFor: 'private' stamp: 'LL 11/5/2023 20:50:26'!
listCart: cartId 
	
	^interface listCart: cartId.
	
	! !

!TusLibrosSystemTest methodsFor: 'private' stamp: 'LL 11/16/2023 00:29:48'!
listPurchasesOfClient

	^interface listPurchases: self aValidClientId with: Object new.
	
	! !


!TusLibrosSystemTest methodsFor: 'test objects' stamp: 'LL 11/16/2023 11:46:31'!
aValidClientId

	^ validClientId ifNil: [ validClientId := UUID new ]! !


!classDefinition: #AuthenticationSystem category: 'TusLibros-System'!
Object subclass: #AuthenticationSystem
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-System'!

!AuthenticationSystem methodsFor: 'public' stamp: 'LL 11/6/2023 20:14:54'!
authenticate: aClientId withPassword: aPassword

	self subclassResponsibility! !


!classDefinition: #AuthenticationSystemStub category: 'TusLibros-System'!
AuthenticationSystem subclass: #AuthenticationSystemStub
	instanceVariableNames: 'authBehaviour'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-System'!

!AuthenticationSystemStub methodsFor: 'public' stamp: 'LL 11/6/2023 19:22:31'!
authenticate: aClientId withPassword: aPassword

	authBehaviour value: aClientId value: aPassword.! !


!AuthenticationSystemStub methodsFor: 'change behaviour' stamp: 'LL 11/6/2023 19:39:06'!
behaveLike: anAuthBehaviourBlock

	authBehaviour := anAuthBehaviourBlock.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'AuthenticationSystemStub class' category: 'TusLibros-System'!
AuthenticationSystemStub class
	instanceVariableNames: ''!

!AuthenticationSystemStub class methodsFor: 'instance creation' stamp: 'LL 11/6/2023 19:39:06'!
behavingLike: aBehaviour

	^self new behaveLike: aBehaviour! !

!AuthenticationSystemStub class methodsFor: 'instance creation' stamp: 'LL 11/6/2023 19:37:20'!
bypassingAllUsers

	^self behavingLike: [ :clientId :password | ]! !


!classDefinition: #RealAuthenticationSystem category: 'TusLibros-System'!
AuthenticationSystem subclass: #RealAuthenticationSystem
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-System'!

!RealAuthenticationSystem methodsFor: 'public' stamp: 'LL 11/6/2023 20:21:49'!
authenticate: aClientId withPassword: aPassword

	(aClientId = 'libreria' and: [ aPassword = '1234' ]) ifFalse: [ self error: 'Invalid credentials' ]! !


!classDefinition: #CartSession category: 'TusLibros-System'!
Object subclass: #CartSession
	instanceVariableNames: 'client cart lastAccess system'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-System'!

!CartSession methodsFor: 'public' stamp: 'LL 2/20/2021 00:31:50'!
addToCart: aBook quantity: aNumberOfCopies
	
	self whenNotExpiredDo: [ cart add: aBook quantity: aNumberOfCopies ]! !

!CartSession methodsFor: 'public' stamp: 'LL 11/24/2023 00:11:17'!
checkoutCartPayingWith: aCreditCard 

	self whenNotExpiredDo: [ system checkout: cart ownedBy: client payingWith: aCreditCard ]

	! !

!CartSession methodsFor: 'public' stamp: 'LL 11/23/2023 23:20:12'!
listCart

	self whenNotExpiredDo: [ ^cart contents ]! !


!CartSession methodsFor: 'assertions' stamp: 'LL 2/20/2021 00:30:11'!
assertSessionIsNotExpired

	^ self isExpired ifTrue: [ self error: self class sessionExpiredErrorMessage ]! !


!CartSession methodsFor: 'private' stamp: 'LL 2/24/2021 19:05:43'!
isExpired
	
	^(lastAccess distanceTo: system now) > system sessionDuration! !

!CartSession methodsFor: 'private' stamp: 'LL 2/20/2021 00:31:50'!
whenNotExpiredDo: aBlock

	self assertSessionIsNotExpired.
	
	^ aBlock ensure: [ lastAccess := system now ].

	! !


!CartSession methodsFor: 'initialization' stamp: 'LL 11/24/2023 00:11:24'!
initializeWith: aCart client: aClient system: aSystem
	
	client := aClient.
	system := aSystem.
	cart := aCart.
	lastAccess := system now.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'CartSession class' category: 'TusLibros-System'!
CartSession class
	instanceVariableNames: ''!

!CartSession class methodsFor: 'as yet unclassified' stamp: 'LL 11/24/2023 00:11:04'!
newFor: aCart ownedBy: aClient on: aSystem

	^self new initializeWith: aCart client: aClient system: aSystem.! !

!CartSession class methodsFor: 'as yet unclassified' stamp: 'LL 2/1/2021 20:58:25'!
sessionExpiredErrorMessage

	^'Cart session has expired'! !


!classDefinition: #TusLibrosSystem category: 'TusLibros-System'!
Object subclass: #TusLibrosSystem
	instanceVariableNames: 'catalog authenticationSystem salesBook merchantProcessor clock cartSessions'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-System'!

!TusLibrosSystem methodsFor: 'initialization' stamp: 'LL 11/23/2023 23:30:45'!
initializeWithCatalog: aCatalog authenticationSystem: anAuthSystem merchantProcessor: aMerchantProcessor clock: aClock  

	catalog := aCatalog.
	cartSessions := Dictionary new.
	authenticationSystem := anAuthSystem.
	salesBook := SalesBook new.
	merchantProcessor := aMerchantProcessor.
	clock := aClock.
! !


!TusLibrosSystem methodsFor: 'public' stamp: 'LL 11/23/2023 23:19:32'!
addToCart: aCartId book: aBook quantity: aQuantity  

	self withCartSessionOf: aCartId do: [ :cartSession | cartSession addToCart: aBook quantity: aQuantity ]

	
	! !

!TusLibrosSystem methodsFor: 'public' stamp: 'LL 11/23/2023 23:26:57'!
checkoutCart: aCartId withCreditCardNumber: aCreditCardNumber expiringOn: anExpirationMonthOfYear ownedBy: creditCardOwner    
	
	self withCartSessionOf: aCartId do: [ :cartSession | 
		| creditCard |
		creditCard := CreditCard of: creditCardOwner number: aCreditCardNumber expiringOn: anExpirationMonthOfYear.		
		cartSession checkoutCartPayingWith: creditCard.
		^self newId.
	]. 
	
	! !

!TusLibrosSystem methodsFor: 'public' stamp: 'LL 11/24/2023 00:18:14'!
createCartFor: aClientId with: aPassword
	
	| cartId |
	self authenticate: aClientId with: aPassword.
	
	cartId := self newId.
	self registerCartSessionFor: self newCart with: cartId ownedBy: aClientId.
	^cartId
	
	! !

!TusLibrosSystem methodsFor: 'public' stamp: 'LL 11/23/2023 23:20:47'!
listCart: aCartId

	^self withCartSessionOf: aCartId do: [ :cartSession | cartSession listCart ]! !

!TusLibrosSystem methodsFor: 'public' stamp: 'LL 11/26/2023 18:55:48'!
listPurchases: aClientId with: aPassword
	
	| bagOfBooks totalPurchased |

	self authenticate: aClientId with: aPassword. 

	bagOfBooks := salesBook soldItemsTo: aClientId.
	totalPurchased := salesBook totalSpentBy: aClientId.
	
	^Array with: bagOfBooks with: totalPurchased.
	
	! !


!TusLibrosSystem methodsFor: 'private' stamp: 'LL 11/24/2023 00:02:31'!
authenticate: aClientId with: aPassword

	^ authenticationSystem authenticate: aClientId withPassword: aPassword! !

!TusLibrosSystem methodsFor: 'private' stamp: 'LL 11/24/2023 00:18:04'!
newCart

	^ Cart acceptingItemsOf: catalog! !

!TusLibrosSystem methodsFor: 'private' stamp: 'LL 11/23/2023 23:26:57'!
newId

	^ UUID new! !


!TusLibrosSystem methodsFor: 'for cart session' stamp: 'LL 11/23/2023 22:52:19'!
cartSessionWith: aCartId.
	
	^ cartSessions at: aCartId ifAbsent: [ self error: self class cartNotFoundErrorMessage ]! !

!TusLibrosSystem methodsFor: 'for cart session' stamp: 'LL 11/23/2023 23:29:46'!
checkout: aCart ownedBy: aClient payingWith: creditCard
	
	| cashier |
	cashier := Cashier registeringSalesOn: salesBook.		
	^cashier checkout: aCart ownedBy: aClient payingWith: creditCard through: merchantProcessor on: clock now.! !

!TusLibrosSystem methodsFor: 'for cart session' stamp: 'LL 2/20/2021 00:27:41'!
now

	^clock now! !

!TusLibrosSystem methodsFor: 'for cart session' stamp: 'LL 11/24/2023 00:15:52'!
registerCartSessionFor: aCart with: aCartId ownedBy: aClient
	| newSession |

	newSession := CartSession newFor: aCart ownedBy: aClient on: self.
	cartSessions at: aCartId put: newSession.
	
! !

!TusLibrosSystem methodsFor: 'for cart session' stamp: 'LL 11/26/2023 20:11:55'!
sessionDuration

	^30 * minute.! !

!TusLibrosSystem methodsFor: 'for cart session' stamp: 'LL 2/1/2021 21:55:32'!
withCartSessionOf: aCartId do: aBlock

	| cartSession |
	cartSession := self cartSessionWith: aCartId.
	
	^aBlock value: cartSession.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosSystem class' category: 'TusLibros-System'!
TusLibrosSystem class
	instanceVariableNames: ''!

!TusLibrosSystem class methodsFor: 'as yet unclassified' stamp: 'LL 1/26/2021 22:00:15'!
cartNotFoundErrorMessage

	^'Cart not found'! !


!TusLibrosSystem class methodsFor: 'instance creation' stamp: 'LL 11/16/2023 11:40:18'!
acceptingItemsOf: aCatalog authenticatingWith: anAuthSystem debitingWith: aMerchantProcessor measuringTimeWith: aClock  

	^ self new initializeWithCatalog: aCatalog authenticationSystem: anAuthSystem merchantProcessor: aMerchantProcessor clock: aClock.! !
