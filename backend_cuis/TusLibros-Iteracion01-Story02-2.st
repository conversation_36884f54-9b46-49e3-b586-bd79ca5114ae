!classDefinition: #CartTest category: #TusLibros!
TestCase subclass: #CartTest
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!CartTest methodsFor: 'tests' stamp: 'LL 1/21/2021 20:09:58'!
test01NewCartIsEmpty

	| cart |
	
	cart := self createCart.
	
	self assert: cart isEmpty.! !

!CartTest methodsFor: 'tests' stamp: 'LL 10/22/2024 20:43:50'!
test02CanAddABookToACart

	| cart |
	cart := self createCart.
	
	cart add: self bookFromTheEditorial.
	
	self deny: cart isEmpty.
	self assert: (cart includes: self bookFromTheEditorial)! !

!CartTest methodsFor: 'tests' stamp: 'LL 1/21/2021 21:25:18'!
test03CannotAddABookNotInCatalog

	| cart bookNotInCatalog |
	cart := self createCart.
	bookNotInCatalog := 'DEF456'.

	self assert: cart isEmptyAfter: [ cart add: bookNotInCatalog ] raisesErrorWithMessage: Cart bookNotInCatalogErrorMessage.
	! !


!CartTest methodsFor: 'private' stamp: 'LL 1/21/2021 21:24:13'!
assert: cart isEmptyAfter: blockExpectedToFail raisesErrorWithMessage: errorMessage
	
	self should: blockExpectedToFail raise: Error withExceptionDo: [ :anException |
		self assert: anException messageText equals: errorMessage.
		self assert: cart isEmpty.
	]! !

!CartTest methodsFor: 'private' stamp: 'LL 1/21/2021 20:08:01'!
bookFromTheEditorial

	^ 'ABC123'! !

!CartTest methodsFor: 'private' stamp: 'LL 1/21/2021 20:09:10'!
createCart

	| aCatalog |
	aCatalog := Set with: self bookFromTheEditorial.
	^Cart withCatalog: aCatalog.! !


!classDefinition: #RestInterfaceTest category: #TusLibros!
TestCase subclass: #RestInterfaceTest
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/29/2024 21:14:28'!
aBookFromThePublisher

	^ 'ABC123'! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 21:09:12'!
add: book toCartOfSampleUserUsing: interface

	^ interface addToCart: (Dictionary new add: 'userId'->'aUser'; add: 'bookIsbn'->book; add: 'quantity'->'1'; yourself)! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/29/2024 21:17:16'!
anotherBookFromThePublisher

	^ 'DEF456'! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/29/2024 21:20:11'!
assert: response is422WithError: expectedError

	self assert: response is422.
	self assert: (response bodyEquals: '1|', expectedError)! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/29/2024 21:13:37'!
createCartForSampleUserOn: interface

	^ interface createCart: (Dictionary new add: 'userId'->'aUser'; add: 'password'->'a password'; yourself)! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/29/2024 21:19:23'!
listCartOfSampleUserUsing: interface

	^ interface listCart: (Dictionary new add: 'userId'->'aUser'; yourself)! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
test01

	| interface response |
	interface := RestInterface using: (TusLibrosSystem acceptingItemsOf: (Set with: self aBookFromThePublisher)).
	
	response := self createCartForSampleUserOn: interface.
	
	self assert: response is201.
	self assert: (response bodyEquals: '0|OK')! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
test02

	| interface response book |
	book := self aBookFromThePublisher.
	interface := RestInterface using: (TusLibrosSystem acceptingItemsOf: (Set with: book)).
	self createCartForSampleUserOn: interface.
		
	response := self add: book toCartOfSampleUserUsing: interface.
	
	self assert: response is200.
	self assert: (response bodyEquals: '0|OK')! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
test03

	| interface response bookNotInCatalog |
	interface := RestInterface using: (TusLibrosSystem acceptingItemsOf: (Set new)).
	self createCartForSampleUserOn: interface.
	bookNotInCatalog := self anotherBookFromThePublisher.
	
	response := self add: bookNotInCatalog toCartOfSampleUserUsing: interface.
	
	self assert: response is422WithError: Cart bookNotInCatalogErrorMessage! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
test04

	| interface response |
	interface := RestInterface using: (TusLibrosSystem acceptingItemsOf: (Set with: self aBookFromThePublisher with: self anotherBookFromThePublisher)).
	self createCartForSampleUserOn: interface.	
	self add: self aBookFromThePublisher toCartOfSampleUserUsing: interface.
	self add: self anotherBookFromThePublisher toCartOfSampleUserUsing: interface.
	
	response := self listCartOfSampleUserUsing: interface.
	
	self assert: response is200.
	self assert: (response bodyEquals: '0|DEF456|1|ABC123|1')! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
test05

	| interface response |
	interface := RestInterface using: (TusLibrosSystem acceptingItemsOf: (Set with: self aBookFromThePublisher with: self anotherBookFromThePublisher)).
	
	response := self listCartOfSampleUserUsing: interface.
	
	self assert: response is422WithError: TusLibrosSystem cartNotFoundErrorMessage! !

!RestInterfaceTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
test06

	| interface response |
	interface := RestInterface using: (TusLibrosSystem acceptingItemsOf: Set new).
	
	response :=  interface createCart: (Dictionary new).
	
	self assert: response is400.
	self assert: (response bodyEquals: '1|', RestInterface missingParameterErrorMessage)! !


!classDefinition: #TusLibrosSystemTest category: #TusLibros!
TestCase subclass: #TusLibrosSystemTest
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!TusLibrosSystemTest methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
createSystem

	^ TusLibrosSystem acceptingItemsOf: (Set with: 'ABC123')! !

!TusLibrosSystemTest methodsFor: 'as yet unclassified' stamp: 'LL 10/24/2024 20:49:32'!
test01

	| system |
	system := self createSystem.
	
	self deny: system hasCarts.! !

!TusLibrosSystemTest methodsFor: 'as yet unclassified' stamp: 'LL 10/28/2024 20:28:16'!
test02

	| system user |
	system := self createSystem.
	user := #aUser.
	
	system takeCartFor: user.
	
	self assert: system hasCarts.
	self assert: (system listCartOf: user) isEmpty.
	! !

!TusLibrosSystemTest methodsFor: 'as yet unclassified' stamp: 'LL 10/26/2024 12:15:05'!
test03

	| system |
	system := self createSystem.
	
	self should: [ system listCartOf: #aUser ]
		raise: Error 
		withExceptionDo: [ :anException | 
			self assert: anException messageText equals: TusLibrosSystem cartNotFoundErrorMessage.
		].
	! !

!TusLibrosSystemTest methodsFor: 'as yet unclassified' stamp: 'LL 10/26/2024 12:15:09'!
test04

	| system |
	system := self createSystem.
	
	self should: [ system add: 'ABC123' toCartOf: #aUser ]
		raise: Error 
		withExceptionDo: [ :anException | 
			self assert: anException messageText equals: TusLibrosSystem cartNotFoundErrorMessage.
		]
	
	
	! !

!TusLibrosSystemTest methodsFor: 'as yet unclassified' stamp: 'LL 10/28/2024 20:28:16'!
test05

	| system user |
	system := self createSystem.
	user := #aUser.
	system takeCartFor: user.
	
	system add: 'ABC123' toCartOf: user.
	
	self assert: ((system listCartOf: user) includes: 'ABC123').
	! !

!TusLibrosSystemTest methodsFor: 'as yet unclassified' stamp: 'LL 10/28/2024 20:28:16'!
test06

	| system user anotherUser |
	system := self createSystem.
	user := #aUser.
	anotherUser := #anotherUser.
	system takeCartFor: user.
	system takeCartFor: anotherUser.
	
	system add: 'ABC123' toCartOf: user.
	
	self assert: ((system listCartOf: user) includes: 'ABC123').
	self deny: ((system listCartOf: anotherUser) includes: 'ABC123')
	! !


!classDefinition: #Cart category: #TusLibros!
Object subclass: #Cart
	instanceVariableNames: 'books bookCatalog'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!Cart methodsFor: 'initialization' stamp: 'LL 10/22/2024 19:38:12'!
initializeWithCatalog: aBookCatalog
	
	bookCatalog := aBookCatalog.
	books := Set new! !


!Cart methodsFor: 'adding' stamp: 'LL 10/22/2024 19:38:05'!
add: aBook
	
	self assertIsInCatalog: aBook.
	
	books add: aBook.! !


!Cart methodsFor: 'testing' stamp: 'LL 1/20/2021 21:33:04'!
isEmpty

	^books isEmpty! !


!Cart methodsFor: 'private - assertions' stamp: 'LL 1/21/2021 20:19:45'!
assertIsInCatalog: aBook

	^ (bookCatalog includes: aBook) ifFalse: [ self error: self class bookNotInCatalogErrorMessage ]! !


!Cart methodsFor: 'as yet unclassified' stamp: 'LL 10/24/2024 20:59:23'!
contents
	^books copy! !

!Cart methodsFor: 'as yet unclassified' stamp: 'LL 10/22/2024 20:44:05'!
includes: aBook 
	^books includes: aBook! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'Cart class' category: #TusLibros!
Cart class
	instanceVariableNames: ''!

!Cart class methodsFor: 'instance creation' stamp: 'LL 1/20/2021 21:37:38'!
withCatalog: aBookCatalog
 
	^self new initializeWithCatalog: aBookCatalog ! !


!Cart class methodsFor: 'error messages' stamp: 'LL 1/20/2021 21:45:09'!
bookNotInCatalogErrorMessage

	^'Cannot add a book that is not from the editorial'! !

!Cart class methodsFor: 'error messages' stamp: 'LL 1/21/2021 21:27:21'!
invalidNumberOfCopiesErrorMessage

	^'Cannot add zero books'! !


!classDefinition: #HttpResponse category: #TusLibros!
Object subclass: #HttpResponse
	instanceVariableNames: 'body statusCode'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!HttpResponse methodsFor: 'initialization' stamp: 'LL 10/30/2024 00:05:46'!
initializeBadRequestWith: aBody 
	statusCode := 400.
	body := aBody.! !

!HttpResponse methodsFor: 'initialization' stamp: 'LL 10/28/2024 20:58:47'!
initializeCreatedWith: aBody 
	statusCode := 201.
	body := aBody.! !

!HttpResponse methodsFor: 'initialization' stamp: 'LL 10/28/2024 21:30:53'!
initializeOkWith: aBody 
	statusCode := 200.
	body := aBody.! !

!HttpResponse methodsFor: 'initialization' stamp: 'LL 10/29/2024 20:33:14'!
initializeUnprocessableContentWith: aResponseBody 
	statusCode := 422.
	body := aResponseBody.! !


!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:47:03'!
body
	^body! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 10/26/2024 12:01:15'!
bodyEquals: aPotentialBody 
	^body = aPotentialBody! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 10/26/2024 12:00:54'!
is200
	^statusCode = 200! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 10/28/2024 20:57:17'!
is201
	^statusCode = 201! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 10/30/2024 00:05:58'!
is400
	^statusCode = 400! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 10/29/2024 20:27:11'!
is422
	^statusCode = 422! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:46:54'!
status
	^statusCode! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'HttpResponse class' category: #TusLibros!
HttpResponse class
	instanceVariableNames: ''!

!HttpResponse class methodsFor: 'instance creation' stamp: 'LL 10/30/2024 00:05:36'!
badRequestWith: aBody 
	^self new initializeBadRequestWith: aBody! !

!HttpResponse class methodsFor: 'instance creation' stamp: 'LL 10/28/2024 20:58:47'!
createdWith: aBody 
	
	^self new initializeCreatedWith: aBody ! !

!HttpResponse class methodsFor: 'instance creation' stamp: 'LL 10/28/2024 21:30:53'!
okWith: aBody 
	
	^self new initializeOkWith: aBody! !

!HttpResponse class methodsFor: 'instance creation' stamp: 'LL 10/29/2024 20:32:23'!
unprocessableContentWith: aResponseBody 
	^self new initializeUnprocessableContentWith: aResponseBody! !


!classDefinition: #NameOfSubclass category: #TusLibros!
Object subclass: #NameOfSubclass
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!


!classDefinition: #PublisherTestObjectsFactory category: #TusLibros!
Object subclass: #PublisherTestObjectsFactory
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 10/19/2023 15:20:20'!
aBagWithABookFromTheEditorial

	^OrderedCollection with: self bookFromTheEditorial.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 9/25/2023 00:01:09'!
aCatalog

	^OrderedCollection with: self bookFromTheEditorial.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 20:17:11'!
anEmptyCart

	^Cart acceptingItemsOf: self aCatalog.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 1/24/2021 15:59:26'!
bookFromTheEditorial

	^ 'ABC123'! !


!classDefinition: #RestInterface category: #TusLibros!
Object subclass: #RestInterface
	instanceVariableNames: 'system'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!RestInterface methodsFor: 'initialization' stamp: 'LL 10/26/2024 11:57:36'!
initializeUsing: aSystem 
	system := aSystem.! !


!RestInterface methodsFor: 'public' stamp: 'LL 10/30/2024 00:31:09'!
addToCart: aRequest
	| user book |
	user := self parseUserIdFrom: aRequest ifAbsent: [ ^ self badRequestResponse ].
	book := self parseBookISBNFrom: aRequest ifAbsent: [ ^ self badRequestResponse ].	
	
	^self performSystemAction: [ system add: book toCartOf: user ] withResultDo: [ :result | 'OK' ]! !

!RestInterface methodsFor: 'public' stamp: 'LL 10/30/2024 00:09:43'!
createCart: anHttpRequest
	| user |
	user := self parseUserIdFrom: anHttpRequest ifAbsent: [ ^self badRequestResponse ].
	
	system takeCartFor: user.

	^HttpResponse createdWith: '0|OK'! !

!RestInterface methodsFor: 'public' stamp: 'LL 10/30/2024 00:31:09'!
listCart: anHttpRequest 
	| user |
	user := self parseUserIdFrom: anHttpRequest ifAbsent: [ ^self badRequestResponse ].
	
	^self performSystemAction:  [ system listCartOf: user ] withResultDo: [ :cartContents |
		| body |
		body := ''.
		cartContents do: [ :aCartItem | body := body, aCartItem, '|1|' ].
		body allButLast.
	]! !


!RestInterface methodsFor: 'parse parameters' stamp: 'LL 10/31/2024 21:08:44'!
parseBookISBNFrom: aRequest ifAbsent: doBlock
	^aRequest at: 'bookIsbn' ifAbsent: doBlock! !

!RestInterface methodsFor: 'parse parameters' stamp: 'LL 10/30/2024 00:25:27'!
parseUserIdFrom: aRequest ifAbsent: doBlock
	^(aRequest at: 'userId' ifAbsent: doBlock	) asSymbol! !


!RestInterface methodsFor: 'private' stamp: 'LL 10/30/2024 00:09:38'!
badRequestResponse

	^ HttpResponse badRequestWith: '1|', self class missingParameterErrorMessage! !

!RestInterface methodsFor: 'private' stamp: 'LL 10/30/2024 00:31:09'!
performSystemAction: aSystemAction withResultDo: aClosure 
	| result responseBody |

	result := aSystemAction on: Error do: [ :anException | 
		^HttpResponse unprocessableContentWith: '1|', anException messageText
	].

	responseBody := aClosure value: result.
	^HttpResponse okWith: '0|', responseBody! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'RestInterface class' category: #TusLibros!
RestInterface class
	instanceVariableNames: ''!

!RestInterface class methodsFor: 'instance creation' stamp: 'LL 10/26/2024 11:57:27'!
using: aSystem 
	^self new initializeUsing: aSystem! !


!RestInterface class methodsFor: 'as yet unclassified' stamp: 'LL 10/30/2024 00:05:15'!
missingParameterErrorMessage
	^'Missing parameter'! !


!classDefinition: #TusLibrosSystem category: #TusLibros!
Object subclass: #TusLibrosSystem
	instanceVariableNames: 'cartByUser catalog'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!TusLibrosSystem methodsFor: 'public' stamp: 'LL 10/26/2024 12:14:10'!
add: aBook toCartOf: aUser
	(self cartOf: aUser) add: aBook.! !

!TusLibrosSystem methodsFor: 'public' stamp: 'LL 10/26/2024 12:14:29'!
listCartOf: aUser 
	^(self cartOf: aUser) contents! !

!TusLibrosSystem methodsFor: 'public' stamp: 'LL 10/30/2024 00:32:59'!
takeCartFor: aUser 
	cartByUser at: aUser put: (Cart withCatalog: catalog)! !


!TusLibrosSystem methodsFor: 'private' stamp: 'LL 10/30/2024 00:33:12'!
cartOf: aUser

	^ cartByUser at: aUser ifAbsent: [ self error: self class cartNotFoundErrorMessage ]! !


!TusLibrosSystem methodsFor: 'testing' stamp: 'LL 10/26/2024 12:14:02'!
hasCarts
	^cartByUser notEmpty! !


!TusLibrosSystem methodsFor: 'initialization' stamp: 'LL 10/26/2024 12:14:02'!
initializeWith: aCatalog

	cartByUser := Dictionary new.
	catalog := aCatalog.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosSystem class' category: #TusLibros!
TusLibrosSystem class
	instanceVariableNames: ''!

!TusLibrosSystem class methodsFor: 'as yet unclassified' stamp: 'LL 10/31/2024 20:41:50'!
acceptingItemsOf: aCatalog

	^self new initializeWith: aCatalog! !

!TusLibrosSystem class methodsFor: 'as yet unclassified' stamp: 'LL 10/24/2024 20:44:42'!
cartNotFoundErrorMessage
	^'Cart not found'! !


!classDefinition: #TusLibrosWebServer category: #TusLibros!
Object subclass: #TusLibrosWebServer
	instanceVariableNames: 'server restInterface'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros'!

!TusLibrosWebServer methodsFor: 'stopping' stamp: 'LL 2/20/2021 02:00:46'!
stop

	^ server destroy.! !


!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 9/25/2023 00:35:38'!
registerServices

	server addService: '/createCart' action: [ :request | self send: #createCart: toInterfaceAdapting: request ].
	server addService: '/addToCart' action: [ :request | self send: #addToCart: toInterfaceAdapting: request ].
	server addService: '/listCart' action: [ :request | self send: #listCart: toInterfaceAdapting: request ].! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 10/31/2024 20:40:52'!
send: anTusLibrosRestInterfaceMessage toInterfaceAdapting: aWebRequest

	| response |
	response := restInterface perform: anTusLibrosRestInterfaceMessage with: (aWebRequest fields).
	
	aWebRequest sendResponseCode: response status content: response body type: 'text/plain; charset=utf-8' do: [ :aResponse | ].! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 2/20/2021 01:36:51'!
startServerOn: aPort

	server := WebServer reset default.
	server listenOn: aPort.
	server useDebugErrorHandler! !


!TusLibrosWebServer methodsFor: 'initialization' stamp: 'LL 10/31/2024 20:46:06'!
initializeListeningOn: aPort

	| latestCatalog |
	latestCatalog := OrderedCollection 
		withAll: #('9780137314942'. '9780321278654'. '9780201710915'. '9780321125217'. '9780735619654'. '9780321146533').

	restInterface := RestInterface using: (TusLibrosSystem acceptingItemsOf: latestCatalog).

	self startServerOn: aPort.
	
	self registerServices.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosWebServer class' category: #TusLibros!
TusLibrosWebServer class
	instanceVariableNames: ''!

!TusLibrosWebServer class methodsFor: 'as yet unclassified' stamp: 'LL 2/6/2021 18:29:32'!
listeningOn: aPort

	^self new initializeListeningOn: aPort! !
