!classDefinition: #CartTest category: 'TusLibros-Model'!
TestCase subclass: #CartTest
	instanceVariableNames: 'objectsFactory'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!CartTest methodsFor: 'tests' stamp: 'LL 2/16/2021 19:50:24'!
setUp

	objectsFactory := PublisherTestObjectsFactory new! !

!CartTest methodsFor: 'tests' stamp: 'LL 1/21/2021 20:09:58'!
test01NewCartIsEmpty

	| cart |
	
	cart := self createCart.
	
	self assert: cart isEmpty.! !

!CartTest methodsFor: 'tests' stamp: 'LL 2/16/2021 19:50:24'!
test02CanAddABookToACart

	| cart |
	cart := self createCart.
	
	cart add: objectsFactory bookFromTheEditorial.
	
	self deny: cart isEmpty.! !

!CartTest methodsFor: 'tests' stamp: 'LL 1/21/2021 21:25:18'!
test03CannotAddABookNotInCatalog

	| cart bookNotInCatalog |
	cart := self createCart.
	bookNotInCatalog := 'DEF456'.

	self assert: cart isEmptyAfter: [ cart add: bookNotInCatalog ] raisesErrorWithMessage: Cart bookNotInCatalogErrorMessage.
	! !

!CartTest methodsFor: 'tests' stamp: 'LL 9/25/2023 01:07:50'!
test04CartRemembersAddedBooks

	| cart |
	cart := self createCart.
	
	cart add: objectsFactory bookFromTheEditorial.
	
	self assert: (cart includes: objectsFactory bookFromTheEditorial).! !

!CartTest methodsFor: 'tests' stamp: 'LL 10/20/2023 17:57:42'!
test05CanAddMultipleCopiesOfABook

	| cart |
	cart := self createCart.
	
	cart add: objectsFactory bookFromTheEditorial quantity: 2.
	
	self assert: (cart quantityOf: objectsFactory bookFromTheEditorial) equals: 2.! !

!CartTest methodsFor: 'tests' stamp: 'LL 10/20/2023 18:07:40'!
test06CannotAddANonPositiveNumberOfCopiesOfABook

	| cart |
	cart := self createCart.
	
	self should: [ cart add: objectsFactory bookFromTheEditorial quantity: 0 ] raise: Error withExceptionDo: [ :anException |
		self assert: cart isEmpty.
		self assert: anException messageText equals: Cart invalidNumberOfCopiesErrorMessage
	].! !


!CartTest methodsFor: 'private' stamp: 'LL 1/21/2021 21:24:13'!
assert: cart isEmptyAfter: blockExpectedToFail raisesErrorWithMessage: errorMessage
	
	self should: blockExpectedToFail raise: Error withExceptionDo: [ :anException |
		self assert: anException messageText equals: errorMessage.
		self assert: cart isEmpty.
	]! !

!CartTest methodsFor: 'private' stamp: 'LL 2/16/2021 19:50:24'!
createCart

	^ objectsFactory anEmptyCart! !


!classDefinition: #Cart category: 'TusLibros-Model'!
Object subclass: #Cart
	instanceVariableNames: 'books bookCatalog'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!Cart methodsFor: 'initialization' stamp: 'LL 10/20/2023 17:58:45'!
initializeWithCatalog: aBookCatalog
	
	bookCatalog := aBookCatalog.
	books := Bag new.! !


!Cart methodsFor: 'adding' stamp: 'LL 10/20/2023 18:06:10'!
add: aBook
	
	self add: aBook quantity: 1.! !

!Cart methodsFor: 'adding' stamp: 'LL 10/20/2023 18:14:29'!
add: aBook quantity: aQuantity

	self assertCanAdd: aBook quantity: aQuantity.
	books add: aBook withOccurrences: aQuantity.! !


!Cart methodsFor: 'accessing' stamp: 'LL 2/16/2021 20:17:50'!
contents

	^books copy! !


!Cart methodsFor: 'testing' stamp: 'LL 1/21/2021 21:39:26'!
includes: aBook

	^ books includes: aBook! !

!Cart methodsFor: 'testing' stamp: 'LL 1/20/2021 21:33:04'!
isEmpty

	^books isEmpty! !


!Cart methodsFor: 'private - assertions' stamp: 'LL 10/20/2023 18:14:35'!
assertCanAdd: aBook quantity: aQuantity  

	self assertIsInCatalog: aBook.
	self assertValidQuantity: aQuantity.
! !

!Cart methodsFor: 'private - assertions' stamp: 'LL 9/25/2023 00:01:24'!
assertIsInCatalog: aBook

	^ (bookCatalog includes: aBook) ifFalse: [ self error: self class bookNotInCatalogErrorMessage ]! !

!Cart methodsFor: 'private - assertions' stamp: 'LL 10/20/2023 18:08:18'!
assertValidQuantity: aQuantity

	^ (aQuantity > 0) ifFalse: [ self error: self class invalidNumberOfCopiesErrorMessage ]! !


!Cart methodsFor: 'as yet unclassified' stamp: 'LL 10/20/2023 17:59:59'!
quantityOf: aBook 
	^books occurrencesOf: aBook! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'Cart class' category: 'TusLibros-Model'!
Cart class
	instanceVariableNames: ''!

!Cart class methodsFor: 'instance creation' stamp: 'LL 1/25/2021 18:29:34'!
acceptingItemsOf: aBookCatalog
 
	^self new initializeWithCatalog: aBookCatalog ! !


!Cart class methodsFor: 'error messages' stamp: 'LL 2/21/2021 21:11:37'!
bookNotInCatalogErrorMessage

	^'Cannot add a book that is not from the publisher'! !


!Cart class methodsFor: 'as yet unclassified' stamp: 'LL 10/20/2023 18:07:15'!
invalidNumberOfCopiesErrorMessage
	
	^'Cannot add less than one book'! !


!classDefinition: #PublisherTestObjectsFactory category: 'TusLibros-Model'!
Object subclass: #PublisherTestObjectsFactory
	instanceVariableNames: ''
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-Model'!

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 10/20/2023 18:01:02'!
aBagWithABookFromTheEditorial

	^Bag with: self bookFromTheEditorial.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 9/25/2023 00:01:09'!
aCatalog

	^OrderedCollection with: self bookFromTheEditorial.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 2/16/2021 20:17:11'!
anEmptyCart

	^Cart acceptingItemsOf: self aCatalog.! !

!PublisherTestObjectsFactory methodsFor: 'as yet unclassified' stamp: 'LL 1/24/2021 15:59:26'!
bookFromTheEditorial

	^ 'ABC123'! !
!classDefinition: #TusLibrosWebServer category: 'TusLibros-ProductionServer'!
Object subclass: #TusLibrosWebServer
	instanceVariableNames: 'server restInterface'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-ProductionServer'!

!TusLibrosWebServer methodsFor: 'initialization' stamp: 'LL 11/5/2023 20:27:40'!
initializeListeningOn: aPort

	restInterface := TusLibrosRestApi for: self configureSystem.

	self startServerOn: aPort.
	
	self registerServices.! !


!TusLibrosWebServer methodsFor: 'stopping' stamp: 'LL 2/20/2021 02:00:46'!
stop

	^ server destroy.! !


!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 11/5/2023 20:22:21'!
configureSystem

	| latestCatalog |
	
	latestCatalog := OrderedCollection 
		withAll: #('9780137314942'. '9780321278654'. '9780201710915'. '9780321125217'. '9780735619654'. '9780321146533').
	
	^TusLibrosInterface acceptingItemsOf: latestCatalog.
! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 9/25/2023 00:35:38'!
registerServices

	server addService: '/createCart' action: [ :request | self send: #createCart: toInterfaceAdapting: request ].
	server addService: '/addToCart' action: [ :request | self send: #addToCart: toInterfaceAdapting: request ].
	server addService: '/listCart' action: [ :request | self send: #listCart: toInterfaceAdapting: request ].! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 2/21/2021 20:47:12'!
send: anTusLibrosRestInterfaceMessage toInterfaceAdapting: aWebRequest

	| response |
	response := restInterface perform: anTusLibrosRestInterfaceMessage with: (HttpRequest withFields: aWebRequest fields).
	
	aWebRequest sendResponseCode: response status content: response body type: 'text/plain; charset=utf-8' do: [ :aResponse | ].! !

!TusLibrosWebServer methodsFor: 'private' stamp: 'LL 2/20/2021 01:36:51'!
startServerOn: aPort

	server := WebServer reset default.
	server listenOn: aPort.
	server useDebugErrorHandler! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosWebServer class' category: 'TusLibros-ProductionServer'!
TusLibrosWebServer class
	instanceVariableNames: ''!

!TusLibrosWebServer class methodsFor: 'as yet unclassified' stamp: 'LL 2/6/2021 18:29:32'!
listeningOn: aPort

	^self new initializeListeningOn: aPort! !
!classDefinition: #TusLibrosInterfaceTest category: 'TusLibros-RestApi'!
TestCase subclass: #TusLibrosInterfaceTest
	instanceVariableNames: 'objectsFactory clock authenticationSystem interface'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!TusLibrosInterfaceTest methodsFor: 'tests' stamp: 'LL 11/5/2023 20:22:21'!
setUp

	objectsFactory := PublisherTestObjectsFactory new.
	interface := TusLibrosInterface 	acceptingItemsOf: objectsFactory aCatalog.! !

!TusLibrosInterfaceTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:00:19'!
test01CanListAnEmptyCart

	| cartId |
	cartId := self createCart.
	
	self assert: (self listCart: cartId) isEmpty.! !

!TusLibrosInterfaceTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:01:00'!
test02CanAddBooksToCartAndListItsContent

	| cartId |
	cartId := self createCart.
	
	self addBookToCart: cartId.
	
	self assert: (self listCart: cartId) equals: objectsFactory aBagWithABookFromTheEditorial.! !

!TusLibrosInterfaceTest methodsFor: 'tests' stamp: 'LL 10/19/2023 16:49:20'!
test03CannotAddToCartNotCreatedBefore

	self shouldRaiseCartNotFoundWhen: [ self addBookToCart: Object new ]
	
	! !

!TusLibrosInterfaceTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:01:12'!
test04CannotListCartNotCreatedBefore

	self shouldRaiseCartNotFoundWhen: [ self listCart: Object new ]
	
	! !

!TusLibrosInterfaceTest methodsFor: 'tests' stamp: 'LL 10/20/2023 15:02:38'!
test05CanCreateDifferentCarts

	| aCartId anotherCartId |
	aCartId := self createCart.
	
	anotherCartId := self createCart.
	
	self addBookToCart: aCartId.
	
	self assert: (self listCart: anotherCartId) isEmpty.
	! !

!TusLibrosInterfaceTest methodsFor: 'tests' stamp: 'LL 10/20/2023 18:28:25'!
test06CanAddMultipleCopiesOfABook

	| cartId |
	cartId := self createCart.
	
	self addBookToCart: cartId withCopies: 2.
	
	self assert: (self listCart: cartId) equals: (Bag new add: objectsFactory bookFromTheEditorial withOccurrences: 2; yourself).! !


!TusLibrosInterfaceTest methodsFor: 'assertions' stamp: 'LL 11/5/2023 20:22:21'!
shouldRaiseCartNotFoundWhen: aBlock
	
	self should: aBlock
		raise: Error - MessageNotUnderstood 
		withExceptionDo: [ :exceptionRaised | 
			self assert: exceptionRaised messageText equals: TusLibrosInterface cartNotFoundErrorMessage.
		]
	
	! !


!TusLibrosInterfaceTest methodsFor: 'private' stamp: 'LL 10/20/2023 18:29:39'!
addBookToCart: cartId 
	
	self addBookToCart: cartId withCopies: 1.
	
	! !

!TusLibrosInterfaceTest methodsFor: 'private' stamp: 'LL 11/5/2023 20:22:13'!
addBookToCart: cartId withCopies: aNumberOfCopies
	
	interface addToCart: cartId book: objectsFactory bookFromTheEditorial quantity: aNumberOfCopies.
	
	! !

!TusLibrosInterfaceTest methodsFor: 'private' stamp: 'LL 11/5/2023 20:22:13'!
createCart

	^interface createCartFor: Object new withPassword: Object new.! !

!TusLibrosInterfaceTest methodsFor: 'private' stamp: 'LL 11/5/2023 20:22:13'!
listCart: cartId 
	
	^interface listCart: cartId.
	
	! !


!classDefinition: #TusLibrosRestApiTest category: 'TusLibros-RestApi'!
TestCase subclass: #TusLibrosRestApiTest
	instanceVariableNames: 'restInterface objectsFactory'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!TusLibrosRestApiTest methodsFor: 'assertions' stamp: 'LL 2/21/2021 17:45:05'!
assert: response isOkAndBodyBeginsWith: aString

	self assert: response status equals: 200.
	self assert: (response body beginsWith: aString)! !

!TusLibrosRestApiTest methodsFor: 'assertions' stamp: 'LL 2/20/2021 02:17:34'!
assert: response isOkAndBodyEquals: expectedBody

	self assert: response status equals: 200.
	self assert: response body equals: expectedBody! !

!TusLibrosRestApiTest methodsFor: 'assertions' stamp: 'LL 10/22/2023 18:39:08'!
assertCannotParseUUIDFrom: response

	self assert: response isOkAndBodyEquals: '1|', restInterface cannotParseUUIDErrorMessage.
	! !


!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 11/5/2023 20:22:34'!
setUp

	objectsFactory := PublisherTestObjectsFactory new.
	restInterface := TusLibrosRestApi for: (TusLibrosInterface acceptingItemsOf: objectsFactory aCatalog).
! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/19/2023 16:04:09'!
test01CreateCartReturnsCartIdOnSuccess

	| response |

	response := self createCart.

	self assert: response isOkAndBodyBeginsWith: '0|'.
	self assert: response body size >= 3.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:32'!
test02AddToCartReturnsOKOnSuccess

	| response cartId |
	
	cartId := self createCartAndReturnId.
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: cartId.

	self assert: response isOkAndBodyEquals: '0|OK'.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:39'!
test03AddToCartReturnsErrorOnASemanticError

	| response cartId |
	
	cartId := self createCartAndReturnId.
	
	response := self addBook: 'book ISBN not in catalog' toCart: cartId.

	self assert: response isOkAndBodyBeginsWith: '1|'.! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:44'!
test04ListCartReturnsCartContentsOnSuccess

	| response cartId |
	
	cartId := self createCartAndReturnId.
	
	self addBook: objectsFactory bookFromTheEditorial toCart: cartId.
	
	response := self listCart: cartId.

	self assert: response isOkAndBodyEquals: '0|', objectsFactory bookFromTheEditorial, '|1'.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 17:47:51'!
test05ListCartReturnsErrorOnASemanticError

	| response |
	
	response := self listNonExistantCart.

	self assert: response isOkAndBodyBeginsWith: '1|'
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 18:39:53'!
test06AddToCartReturnErrorWhenCannotParseCartId

	| response |
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: Object new asString.

	self assertCannotParseUUIDFrom: response.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 18:40:02'!
test07ListCartReturnErrorWhenCannotParseCartId

	| response |
	
	response := self listCart: Object new asString.

	self assertCannotParseUUIDFrom: response.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:08:58'!
test08AddToCartParsesQuantityParameterCorrectly
	
	| response cartId |

	cartId := self createCartAndReturnId.
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: cartId withQuantity: '2'.
	
	response := self listCart: cartId.
	self assert: (self quantityFrom: response) equals: 2.
	! !

!TusLibrosRestApiTest methodsFor: 'tests' stamp: 'LL 10/22/2023 19:09:09'!
test09AddToCartReturnsErrorOnUnparsableQuantity
	
	| response cartId |

	cartId := self createCartAndReturnId.
	
	response := self addBook: objectsFactory bookFromTheEditorial toCart: cartId withQuantity: 'x'.
	
	self assert: response isOkAndBodyEquals: '1|', restInterface cannotParseBookQuantityErrorMessage.
	! !


!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/22/2023 18:57:03'!
addBook: aBookISBN toCart: cartIdAsString
	
	^self addBook: aBookISBN toCart: cartIdAsString withQuantity: '1'. ! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/22/2023 18:55:02'!
addBook: aBookISBN toCart: cartIdAsString withQuantity: aQuantityAsString 
	| aRequest |
	
	aRequest := HttpRequest withFields: (Dictionary newFromPairs: {'cartId'. cartIdAsString. 'bookIsbn'. aBookISBN. 'bookQuantity'. aQuantityAsString}).
	
	^restInterface addToCart: aRequest.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 9/25/2023 01:06:11'!
createCart

	| aRequest |

	aRequest := HttpRequest withFields: (Dictionary new).
	
	^restInterface createCart: aRequest.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/22/2023 19:08:02'!
createCartAndReturnId

	| response |

	response := self createCart.
	
	^self cartIdFrom: response.! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/20/2023 15:32:01'!
listCart: cartIdAsString

	| aRequest |
	
	aRequest := HttpRequest withFields: (Dictionary newFromPairs: {'cartId'. cartIdAsString}).
	
	^restInterface listCart: aRequest.
	! !

!TusLibrosRestApiTest methodsFor: 'private - test requests' stamp: 'LL 10/22/2023 18:11:26'!
listNonExistantCart

	^self listCart: UUID new asString.
	! !


!TusLibrosRestApiTest methodsFor: 'private' stamp: 'LL 10/20/2023 16:18:19'!
cartIdFrom: response

	^ response body allButFirst: 2! !

!TusLibrosRestApiTest methodsFor: 'private' stamp: 'LL 10/22/2023 18:53:48'!
quantityFrom: response 
	^response body last asString asNumber.! !


!classDefinition: #HttpRequest category: 'TusLibros-RestApi'!
Object subclass: #HttpRequest
	instanceVariableNames: 'fields'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!HttpRequest methodsFor: 'initialization' stamp: 'LL 2/6/2021 17:48:39'!
initializeWithFields: requestFields 
	fields := requestFields.! !


!HttpRequest methodsFor: 'accessing' stamp: 'LL 2/6/2021 17:48:52'!
fields

	^fields! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'HttpRequest class' category: 'TusLibros-RestApi'!
HttpRequest class
	instanceVariableNames: ''!

!HttpRequest class methodsFor: 'instance creation' stamp: 'LL 2/6/2021 17:48:17'!
withFields: requestFields
	^self new initializeWithFields: requestFields ! !


!classDefinition: #HttpResponse category: 'TusLibros-RestApi'!
Object subclass: #HttpResponse
	instanceVariableNames: 'responseBody status'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!HttpResponse methodsFor: 'initialization' stamp: 'LL 2/6/2021 17:52:59'!
initializeOkResponseWith: aResponseBody 
	
	status := 200.
	responseBody := aResponseBody.! !


!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 2/6/2021 17:53:48'!
body

	^responseBody! !

!HttpResponse methodsFor: 'as yet unclassified' stamp: 'LL 2/6/2021 17:53:36'!
status
	
	^status! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'HttpResponse class' category: 'TusLibros-RestApi'!
HttpResponse class
	instanceVariableNames: ''!

!HttpResponse class methodsFor: 'instance creation' stamp: 'LL 2/6/2021 17:52:10'!
okResponseWith: aResponseBody

	^self new initializeOkResponseWith: aResponseBody! !


!classDefinition: #TusLibrosInterface category: 'TusLibros-RestApi'!
Object subclass: #TusLibrosInterface
	instanceVariableNames: 'catalog cartsById'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!TusLibrosInterface methodsFor: 'initialization' stamp: 'LL 10/20/2023 13:15:28'!
initializeWithCatalog: aCatalog  

	catalog := aCatalog.
	cartsById := Dictionary new.! !


!TusLibrosInterface methodsFor: 'public' stamp: 'LL 10/20/2023 18:29:12'!
addToCart: aCartId book: aBook quantity: aQuantity  

	cartsById at: aCartId ifAbsent: [ self error: self class cartNotFoundErrorMessage ].
	(cartsById at: aCartId) add: aBook quantity: aQuantity.
	
	! !

!TusLibrosInterface methodsFor: 'public' stamp: 'LL 10/20/2023 14:37:31'!
createCartFor: aClientId withPassword: aPassword 
	
	| newId |
	newId := UUID new.
	cartsById at: newId put: (Cart acceptingItemsOf: catalog).
	^newId
	! !

!TusLibrosInterface methodsFor: 'public' stamp: 'LL 10/20/2023 13:16:28'!
listCart: aCartId

	cartsById at: aCartId ifAbsent: [ self error: self class cartNotFoundErrorMessage ].
	^(cartsById at: aCartId) contents.! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosInterface class' category: 'TusLibros-RestApi'!
TusLibrosInterface class
	instanceVariableNames: ''!

!TusLibrosInterface class methodsFor: 'as yet unclassified' stamp: 'LL 1/26/2021 22:00:15'!
cartNotFoundErrorMessage

	^'Cart not found'! !


!TusLibrosInterface class methodsFor: 'instance creation' stamp: 'LL 9/25/2023 00:29:28'!
acceptingItemsOf: aCatalog    

	^ self new initializeWithCatalog: aCatalog ! !


!classDefinition: #TusLibrosRestApi category: 'TusLibros-RestApi'!
Object subclass: #TusLibrosRestApi
	instanceVariableNames: 'interface'
	classVariableNames: ''
	poolDictionaries: ''
	category: 'TusLibros-RestApi'!

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 11/5/2023 20:22:06'!
addToCart: aHttpRequest 
	
	| bookIsbn cartId quantity |
	bookIsbn := aHttpRequest fields at: 'bookIsbn'.
	
	^ self answer: [
		cartId := self parseCartIdFrom: aHttpRequest.
		quantity := self parseBookQuantityFrom: aHttpRequest.
		interface addToCart: cartId book: bookIsbn quantity: quantity.
		'0|OK'.
	]! !

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 11/5/2023 20:22:06'!
createCart: aHttpRequest
	
	| cartId |
	cartId := interface createCartFor: Object new withPassword: Object new.
	^HttpResponse okResponseWith: '0|', cartId asString.! !

!TusLibrosRestApi methodsFor: 'public' stamp: 'LL 11/5/2023 20:22:06'!
listCart: aHttpRequest 
	
	| cartContents cartId |
	
	^ self answer: [ 
		cartId := self parseCartIdFrom: aHttpRequest.
		cartContents := interface listCart: cartId.
		'0|', (self cartContentAsString: cartContents).
	]! !


!TusLibrosRestApi methodsFor: 'private' stamp: 'LL 2/21/2021 19:43:28'!
answer: aBlock
	
	| response |
	
	[ response := aBlock value ] on: Error do: [ :anException | 
		^HttpResponse okResponseWith: '1|', anException messageText
	].
	
	^HttpResponse okResponseWith: response! !

!TusLibrosRestApi methodsFor: 'private' stamp: 'LL 10/20/2023 18:52:57'!
cartContentAsString: aBag
	
	^ String streamContents: [:stream |
		aBag contents associations
			do: [ :itemAndQuantity | stream nextPutAll: itemAndQuantity key; nextPut: $|; nextPutAll: itemAndQuantity value asString ]
			separatedBy: [ stream nextPut: $| ]
	]! !

!TusLibrosRestApi methodsFor: 'private' stamp: 'LL 10/22/2023 19:05:07'!
parseBookQuantityFrom: aHttpRequest
	
	| bookQuantityAsString |
	
	bookQuantityAsString := aHttpRequest fields at: 'bookQuantity'.
	
	[ ^bookQuantityAsString asNumber ] on: Error do: [ :anException | 
		self error: self cannotParseBookQuantityErrorMessage.
	].! !

!TusLibrosRestApi methodsFor: 'private' stamp: 'LL 10/22/2023 19:02:34'!
parseCartIdFrom: aHttpRequest
	
	| cartIdAsString |
	
	cartIdAsString := aHttpRequest fields at: 'cartId'.
	
	[ ^UUID fromString: cartIdAsString ] on: Error do: [ :anException | 
		self error: self cannotParseUUIDErrorMessage
	].! !


!TusLibrosRestApi methodsFor: 'initialization' stamp: 'LL 11/5/2023 20:22:06'!
initializeFor: aTusLibrosSystem 

	interface := aTusLibrosSystem.! !


!TusLibrosRestApi methodsFor: 'error handling' stamp: 'LL 10/22/2023 19:05:07'!
cannotParseBookQuantityErrorMessage

	^ 'Cannot parse book quantity'! !

!TusLibrosRestApi methodsFor: 'error handling' stamp: 'LL 10/22/2023 18:38:49'!
cannotParseUUIDErrorMessage

	^ 'Cannot parse UUID'! !

"-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- "!

!classDefinition: 'TusLibrosRestApi class' category: 'TusLibros-RestApi'!
TusLibrosRestApi class
	instanceVariableNames: ''!

!TusLibrosRestApi class methodsFor: 'instance creation' stamp: 'LL 2/6/2021 18:26:34'!
for: aTusLibrosSystem 

	^self new initializeFor: aTusLibrosSystem ! !
